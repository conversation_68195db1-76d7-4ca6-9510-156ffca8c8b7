<?php

/**
 * PDO wrapper class with a query builder.
 */
namespace App\models;

class Db_helper
{
	private $_last_query;
	private $_row_count;
	private $_where;
	private $_set;
	private $_like;
	private $_join;
	private $_select;
	private $_from;
	private $_group;
	private $_order;
	private $_limit;
	private $_paginate;
	private $_parameters;
	private $_raw_sql;
	private $_assoc;
	private $_distinct;
	private $_current_query;
	private $_result_mode;
	private $_temp_result_mode;
	private $_run_hooks;
	private $_temp_hook;
	private $_error;
	private $_paginator_select;
	private $_simulation_mode;
	private $_having;
	private $_errorMode;

	/**
	 * @param string $fetchMode    Optional, Accepted values are "array" or "object"
     * @param int    $PdoErrorMode Optional, PDO:ERRMODE
	 */
	function __construct($fetchMode='array', $PdoErrorMode=\PDO::ERRMODE_WARNING)
	{
	    $this->_errorMode = $PdoErrorMode;
		$this->db = get_dbh();
		//$this->db->setAttribute( \PDO::ATTR_ERRMODE, $PdoErrorMode );
		$this->signs = [ '<', '>', '!', '!=', '<=', '>=', "IS", "NOT", 'LIKE', 'IN' ];
		$this->reset();
		$this->_last_query = $this->_raw_sql = '';
		$this->_row_count = 0;
		$this->set_fetch_mode($fetchMode);
		$this->_temp_result_mode = [$this->_result_mode];
		$this->_run_hooks = false;
	}

	/**
	 * @return bool
	 */
	public function getSimulationMode(): bool
	{
		return $this->_simulation_mode;
	}

	/**
	 * @param bool $simulation_mode
	 * @return Db_helper
	 */
	public function setSimulationMode($simulation_mode): Db_helper
	{
		$this->_simulation_mode = $simulation_mode;
		if($simulation_mode===true){
			$this->enable_hooks(false);
			$this->_temp_hook = false;
		}
		return $this;
	}

	/**
	 * Retrieves the last error.
	 *
	 * Warning: This may fetch an old error message as it is not reset after a successful query.
	 * However, it may be useful if a there was an error somewhere in you query stack.
	 *
	 * @return mixed
	 */
	public function getError()
	{
		return $this->_error;
	}

	/**
	 * @param mixed $error
	 * @return Db_helper
	 */
	private function setError($error)
	{
		$this->_error = $error;
		return $this;
	}

	/**
	 * Speed up your query by eliminating unnecessary selects in the pagination query.
	 *
	 * @param string $select_fields String containing the only necessary fields for counting e.g. table_name.id
	 * @return Db_helper
	 */
	public function setPaginatorSelect($select_fields)
	{
		$this->_paginator_select = $select_fields;
		return $this;
	}


	/**
	 * Sets PDO::FETCH_MODE
	 *
	 * @param string $mode Accepted values are "array" or "object"
	 * @return $this
	 */
	function set_fetch_mode($mode='array'): Db_helper
	{
		if(is_string($mode)){
			$mode = strtolower($mode);
			switch($mode){
				case 'array' : $this->_result_mode = \PDO::FETCH_ASSOC;
					break;
				case 'object' : $this->_result_mode = \PDO::FETCH_OBJ;
					break;
				default : $this->_result_mode = \PDO::FETCH_ASSOC;
					break;
			}
		}
		elseif(is_numeric($mode)){
			$this->_result_mode = $mode;
		}
		else{
			$this->_result_mode = \PDO::FETCH_ASSOC;
		}
		return $this;
	}

	/**
	 * Sets a temporary PDO::FETCH_MODE
	 *
	 * @param string $mode Accepted values are "array" or "object"
	 * @return $this
	 */
	function set_temp_fetch_mode($mode='object'): Db_helper
	{
		array_push( $this->_temp_result_mode, $this->_result_mode );
		return $this->set_fetch_mode($mode);
	}

	/**
	 * Exists the temporarily set PDO FETCH_MODE
	 *
	 * @return $this
	 */
	function exit_temp_fetch_mode(): Db_helper
	{
		return $this->set_fetch_mode( array_pop($this->_temp_result_mode) );
	}

	/**
	 * If enabled, will run hooks for all subsequent queries until explicitly disabled
	 * @param bool $true
	 */
	function enable_hooks($true=true): Db_helper
	{
		$this->_run_hooks = $true;
		return $this;
	}

	/**
	 * Disables hooks
	 *
	 * @return $this
	 */
	function disable_hooks(): Db_helper
	{
		$this->_run_hooks = false;
		return $this;
	}

	/**
	 * Checks whether hooks are enabled or not
	 *
	 * @return bool
	 */
	function hooks_status(): bool
	{
		return $this->_run_hooks;
	}

	/**
	 *  Will run hooks in the current query only
	 */
	function run_hooks() : Db_helper
	{
		$this->_temp_hook = true;
		return $this;
	}

	/**
	 * Resets the query builder. Usually not necessary to explicitly call this function as the query builder is reset after every query execution
	 *
	 * @param bool $params
	 * @return $this
	 */
	public function reset($params=true): Db_helper
	{
		$this->_select = $this->_from = $this->_join = $this->_where = $this->_group = $this->_order = $this->_like = $this->_set = [];
		$this->_limit = $this->_paginate = $this->_assoc = $this->_distinct = $this->_temp_hook = false;
		$this->_current_query = $this->_paginator_select = '';
		if($params) $this->_parameters = [];
		return $this;
	}

	/**
	 * Returns the current PDO connection object
	 *
	 * @return \Doctrine\DBAL\Connection|\PDO|string|void
	 */
	function get_dbh(){
		return $this->db;
	}

	/**
	 * Runs a SQL query that returns data
	 * @param string $sql
	 * @param array $params Optional array with values to be replaced in the query
	 * @param bool $fetchAll
	 * @return array|false|mixed|mixed[]
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	function get_query($sql, $params=null, $fetchAll=true){
	    $this->set_last_query($sql, $params);
		$stmt = $this->db->prepare($sql);
		$stmt->execute($params);
		return $fetchAll ? $stmt->fetchAll($this->_result_mode) : $stmt->fetch($this->_result_mode);
	}

	/**
	 * Runs a SQL query that does not return data e.g. UPDATE/INSERT/ALTER etc.
	 * @param string $sql
	 * @param array $params Optional array with values to be replaced in the query
	 * @return bool
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	function set_query($sql, $params=null): bool
	{
		$stmt = $this->db->prepare($sql);
		return $stmt->execute($params);
	}

	/**
	 * Get a single row from table.
	 *
	 * Recommended that you use the query builder to build your WHERE clause, e.g. $this->db->where or $this->db->join
	 * @param string|array|bool $fields Accepts string of DB table fields or array. If array is associative, the key will be returned as the "AS" field
	 * @param string|bool $table the name of the table
	 * @param array|string $where Accepts string containing WHERE clause or array with key representing field name and value being the value in table.
	 *          You can add comparison sign in the key
	 *          e.g $where = [ 'id >'=>50 ] will produce "WHERE id > 50";
	 *          $where = [ 'name LIKE'=>'%jason%' ] will produce "WHERE name LIKE '%jason%'";.
	 *          You can also change WHERE  combination word
	 *          e.g $where = [ 'category'=>15, 'OR category >='=>19 ] will produce "WHERE category = 15 OR category >= 19";
	 *          You can also add joins in the $where array
	 *          e.g $where = [ 'LEFT JOIN categories c' => 'c.id = p.category_id', 'c.id !='=>18 ] will produce "LEFT JOIN categories c ON c.id = p.id WHERE c.id != 18";
	 * @param array|string $order Accepts an a string of order clauses e.g "id desc, name asc" or array e.g $order = [ 'id desc', 'name asc' ]
	 * @param array|int $limit Accepts a int value specifying the limit e.g $limit = 100 produces "LIMIT 0, 100".
	 *          If array, first index must be the start value and second index the limit, e.g $limit = [10,50] produces "LIMIT 10, 50".
	 * @param false $paginate If true, your query will be paginated automatically using the global paginator - see admin/app/models/paginator.php
	 * @return array|false|mixed[] An array/object depending on fetch_mode containing rows from the queried table. Returns false on failure
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	function get_row( $fields=false, $table=false, $where=[], $order=[] ){
		$data = $this->get_rows( $fields, $table, $where, $order, 1 );
		return isset($data[0])?$data[0]:FALSE;
	}

	/**
	 * Fetches a specific field from a table record.
	 *
	 * Recommended that you use the query builder to build your WHERE clause, e.g. $this->db->where or $this->db->join
	 * @param string $field Accepts string with a DB table field or a complex select statement.
	 * @param string|bool $table the name of the table
	 * @param array|string $where Accepts string containing WHERE clause or array with key representing field name and value being the value in table.
	 *          You can add comparison sign in the key
	 *          e.g $where = [ 'id >'=>50 ] will produce "WHERE id > 50";
	 *          $where = [ 'name LIKE'=>'%jason%' ] will produce "WHERE name LIKE '%jason%'";.
	 *          You can also change WHERE  combination word
	 *          e.g $where = [ 'category'=>15, 'OR category >='=>19 ] will produce "WHERE category = 15 OR category >= 19";
	 *          You can also add joins in the $where array
	 *          e.g $where = [ 'LEFT JOIN categories c' => 'c.id = p.category_id', 'c.id !='=>18 ] will produce "LEFT JOIN categories c ON c.id = p.id WHERE c.id != 18";
	 * @param array|string $order Accepts an a string of order clauses e.g "id desc, name asc" or array e.g $order = [ 'id desc', 'name asc' ]
	 * @param array|int $limit Accepts a int value specifying the limit e.g $limit = 100 produces "LIMIT 0, 100".
	 *          If array, first index must be the start value and second index the limit, e.g $limit = [10,50] produces "LIMIT 10, 50".
	 * @param false $paginate If true, your query will be paginated automatically using the global paginator - see admin/app/models/paginator.php
	 * @return string|int|bool|mixed A string / int / bool depending on data type of field being fetched. Returns false on failure
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	function fetch_field( $field, $table=false, $where=[], $order=[] ){
		$data = $this->get_rows( $field, $table, $where, $order, 1 );
		return isset($data[0])?current($data[0]):FALSE;
	}

	/**
	 * Get rows from table.
	 *
	 * Recommended that you use the query builder to build your WHERE clause, e.g. $this->db->where or $this->db->join
	 * @param string|array|bool $fields Accepts string of DB table fields or array. If array is associative, the key will be returned as the "AS" field
	 * @param string|bool $table the name of the table
	 * @param array|string $where Accepts string containing WHERE clause or array with key representing field name and value being the value in table.
	 *          You can add comparison sign in the key
	 *          e.g $where = [ 'id >'=>50 ] will produce "WHERE id > 50";
	 *          $where = [ 'name LIKE'=>'%jason%' ] will produce "WHERE name LIKE '%jason%'";.
	 *          You can also change WHERE  combination word
	 *          e.g $where = [ 'category'=>15, 'OR category >='=>19 ] will produce "WHERE category = 15 OR category >= 19";
	 *          You can also add joins in the $where array
	 *          e.g $where = [ 'LEFT JOIN categories c' => 'c.id = p.category_id', 'c.id !='=>18 ] will produce "LEFT JOIN categories c ON c.id = p.id WHERE c.id != 18";
	 * @param array|string $order Accepts an a string of order clauses e.g "id desc, name asc" or array e.g $order = [ 'id desc', 'name asc' ]
	 * @param array|int $limit Accepts a int value specifying the limit e.g $limit = 100 produces "LIMIT 0, 100".
	 *          If array, first index must be the start value and second index the limit, e.g $limit = [10,50] produces "LIMIT 10, 50".
	 * @param false $paginate If true, your query will be paginated automatically using the global paginator - see admin/app/models/paginator.php
	 * @return array|false|mixed[] An array/object depending on fetch_mode containing rows from the queried table. Returns false on failure
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	function get_rows($fields=false, $table=false, $where=[], $order=[], $limit=[], $paginate=FALSE ){
        $this->db->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );
		try {
			if($table === false && empty($this->_from) && $fields) {
				$this->from($fields);
				if(empty($this->_select)) $this->select();
			} elseif($fields) $this->select($fields);
			if($table) $this->from($table);

			if($where && !empty($where)) {
				is_string($where) ? $this->where($where, false, true) : $this->where($where);
			}
			if($order && !empty($order)) $this->order_by($order);
			if($limit && !empty($limit)) {
				if(is_array($limit)) {
					(count($limit) == 2) ? $this->limit($limit[1], $limit[0]) : $this->limit($limit[0]);
				} elseif(is_numeric($limit)) $this->limit($limit);
			}

			if($paginate) $this->paginate($paginate);

			$sql = $this->_build_query();
			if( $this->_simulation_mode ) return $this->get_last_query();
			//echo $this->get_compiled_query();
			//$this->set_last_query($sql,$this->_parameters);
			//echo "<br><br>$sql <br><br/>";
			//echo "<br>RAW: ".$this->_raw_sql."<br>";
			//print_r($this->_parameters);
			//return [];
			// echo var_dump($args);
			$stmt = $this->db->prepare($sql);
			$stmt->execute($this->_parameters);
			$data = $stmt->fetchAll($this->_result_mode);
			$this->_row_count = $data ? count($data) : 0;
			$this->reset();
			return $data;
		}
		catch(PDOException $e){
			$this->setError($e->getMessage());
			$this->reset();
			return false;
		}
		finally {
            $this->db->setAttribute( PDO::ATTR_ERRMODE, $this->_errorMode );
        }
	}

	/**
	 * Whether to run the system pagination or not. See the /admin/app/models/paginator.php class.
	 * @param bool $run_pagination
	 * @return $this
	 */
	function paginate($run_pagination=true): Db_helper
	{
		$this->_paginate = $run_pagination;
		return $this;
	}

	/**
	 * Creates the SELECT portion of your query:
	 *
	 * <pre>
	 * $this->db->select('title, content, date');
	 * $data = $this->db->get_rows('mytable');
	 * // Executes: SELECT title, content, date FROM mytable
	 * </pre>
	 *
	 * @param string $fields
	 * @param false  $as optional second argument e.g. $this->db->select('db123', 'surname') produces SELECT db123 AS `surname`
	 * @return $this
	 */
	function select($fields='*', $as=false ): Db_helper
	{
		if(is_array($fields)){
			$f = [];
			foreach($fields as $k=>$v){
				$f[] = is_numeric($k)?$v:"{$v} AS `{$k}`";
			}
			$this->_select = array_merge( $this->_select, $f );
		}
		elseif(is_string($fields) && $as){
			$this->_select[$as] = $fields;
		}
		elseif(is_string($fields)){
			$this->_select[] = $fields;
		}
		return $this;
	}

	/**
	 * Adds a DISTINCT clause to your SELECT statement where true
	 *
	 * @param bool $true_or_false
	 * @return $this
	 */
	function distinct($true_or_false=true): Db_helper
	{
		$this->_distinct = $true_or_false;
		return $this;
	}

	/**
	 * Builds the WHERE clause of your query
	 *
	 * <b>Examples:</b>
	 * <ul>
	 * <li>$this->db->where( 'id', 25 ) produces WHERE id = 25</li>
	 * <li>$this->db->where( 'id >', 25 ) produces WHERE id > 25</li>
	 * <li>$this->db->where( 'id IS NOT', null ) produces WHERE id IS NOT NULL</li>
	 * <li>$this->db->where( 'id', 25 )->where('active', 'yes') produces WHERE id = 25 AND active = 'yes'</li>
	 * <li>$this->db->where("(rec_archive IS NULL OR rec_archive='')") produces WHERE (rec_archive IS NULL OR rec_archive='')</li>
	 * </ul>
	 * @param string|array $field The database table field name or a custom complex string where statement without the WHERE keyword. Can also be an associative array of key value pairs or a numerically indexed array with complex/simple where clauses.
	 * @param int|string|false $value right hand side of your where statement e.g. $this->db->where('id', 100) produces WHERE id = 100
	 * @param bool $reset If true, this will clear previously set WHERE clauses
	 * @return $this
	 */
	function where($field, $value=false, $reset=false): Db_helper
	{
		if(is_array($field)) $this->_where = array_merge( $this->_where, $field );
		elseif( is_string($field) && $value !== false ) $this->_where = array_merge( $this->_where, [$field=>$value] );
		elseif(is_string($field)){
			$this->_where = $reset ? $field : array_merge( $this->_where, [$field] );
		}
		return $this;
	}

	/**
	 * Builds the OR WHERE clause of your query
	 *
	 * <b>Examples:</b>
	 * <pre>
	 * $this->db->where( 'level', 6 )->or_where('is_admin', 'yes')
	 * produces WHERE level = 6 OR is_admin = 'yes'
	 * </pre>
	 * @param string $field The database table field name or a custom complex where statement without the WHERE keyword
	 * @param int|string|false $value right hand side of your where statement e.g. $this->db->where('id', 100) produces WHERE id = 100
	 * @param bool $reset If true, this will clear previously set WHERE clauses
	 * @return $this
	 */
	function or_where($field,$value=false): Db_helper
	{
		if(is_array($field)){
			foreach($field as $k=>$v){
				$this->where( "OR ".$k, $v );
			}
		}
		elseif($value===false) $this->where("OR ".$field);
		else $this->where("OR ".$field, $value);
		return $this;
	}

	/**
	 * Creates a WHERE IN clause
	 *
	 * <b>Example:</b>
	 * <pre>
	 * $this->db->where_in( 'roles', [1,2,3,4,'guest'] )
	 * produces WHERE roles IN ( 1,2,3,4,'guest' )
	 * </pre>
	 * @param string $field The field being queried
	 * @param array $values This must be a numerically indexed array of values
	 * @return $this
	 */
	function where_in($field, $values): Db_helper
	{
		return $this->prepare_in($field, $values);
	}

    /**
     * Creates a WHERE NOT IN clause
     *
     * <b>Example:</b>
     * <pre>
     * $this->db->where_not_in( 'roles', [1,2,3,4,'guest'] )
     * produces WHERE roles NOT IN ( 1,2,3,4,'guest' )
     * </pre>
     * @param string $field The field being queried
     * @param array $values This must be a numerically indexed array of values
     * @return $this
     */
    function where_not_in($field, $values): Db_helper
    {
        return $this->prepare_in($field, $values, true);
    }

    private function prepare_in($field, $values, $not=false){
        if($field && $values && is_string($field) && is_array($values)){
            $data = [];
            foreach($values as $v){
                $data[] = is_numeric($v) ? $v : "'{$v}'";
            }
            $in = implode(',', $data);
            $not ? $this->where( "{$field} NOT IN ($in)" ) : $this->where( "{$field} IN ($in)" );
        }
        return $this;
    }

	/**
	 * Creates a OR WHERE IN clause
	 *
	 * <b>Example:</b>
	 * <pre>
	 * $this->db->where('id', 1)->where_in( 'roles', [1,2,3,4,'guest'] )
	 * produces WHERE id = 1 OR roles IN ( 1,2,3,4,'guest' )
	 * </pre>
	 * @param string $field The field being queried
	 * @param array $values This must be a numerically indexed array of values
	 * @return $this
	 */
	function or_where_in($field, $values): Db_helper
	{
		return $this->where_in( "OR {$field}", $values );
	}

    /**
     * Creates a OR WHERE NOT IN clause
     *
     * <b>Example:</b>
     * <pre>
     * $this->db->where('id', 1)->where_not_in( 'roles', [1,2,3,4,'guest'] )
     * produces WHERE id = 1 OR roles NOT IN ( 1,2,3,4,'guest' )
     * </pre>
     * @param string $field The field being queried
     * @param array $values This must be a numerically indexed array of values
     * @return $this
     */
    function or_where_not_in($field, $values): Db_helper
    {
        return $this->where_not_in( "OR {$field}", $values );
    }

	/**
	 * Produces a WHERE LIKE clause
	 *
	 * @param string $field
	 * @param string|false $value
	 * @param string $wildcard  supported values: both, before/left, after/right, none or ''
	 * @return $this
	 */
	function like($field, $value=false, $wildcard='both' ): Db_helper
	{
		$wildcard = strtolower(trim($wildcard));
		if(is_array($field)){
			foreach($field as $k=>$v){
				$this->like( $k, $v, $wildcard );
			}
		}
		elseif($field && $value===false){
			$this->where( $field );
		}
		else{
			$this->where( "{$field} LIKE", $this->add_wildcard($value, $wildcard) );
		}
		return $this;
	}

	/**
	 * Produces an OR WHERE LIKE clause
	 *
	 * @param string $field
	 * @param string|false $value
	 * @param string $wildcard  supported values: both, before/left, after/right, none or ''
	 * @return $this
	 */
	function or_like($field, $value=false, $wildcard='both'): Db_helper
	{
		if(is_array($field)){
			foreach($field as $k=>$v){
				$this->like( "OR ".$k, $v, $wildcard );
			}
		}
		else $this->like( "OR ".$field, $value, $wildcard );
		return $this;
	}

	/**
	 * Creates a SET statement for an UPDATE query
	 *
	 * @param string $field
	 * @param string|int|bool|null $value
	 * @param bool  $escape whether to escape the field name with backticks or not
	 * @param bool  $has_value allows setting of empty values when true e.g. $this->db->set('rec_archive', null, true, true) produces SET rec_archive  = NULL
	 * @return $this
	 */
	function set($field, $value=false, $escape=true, $has_value=true): Db_helper
	{
		if(is_array($field)){
			$this->_set = array_merge( $this->_set, $field );
		}
		else{
			$this->_set = array_merge( $this->_set, [$field=>['value'=>$value,'escape'=>$escape,'has_value'=>$has_value]] );
		}
		return $this;
	}

	/**
	 * Creates an opening brace "(" in your query
	 *
	 * @return $this
	 */
	function group_start(): Db_helper
	{
		$this->where( '/*GROUPSTART*/' );
		return $this;
	}

	/**
	 * Creates an opening brace "OR (" in your query
	 *
	 * @return $this
	 */
	function or_group_start(): Db_helper
	{
		$this->where('OR /*GROUPSTART*/');
		return $this;
	}

	/**
	 * Creates a closing brace ")" in your query
	 *
	 * @return $this
	 */
	function group_end(): Db_helper
	{
		$this->where('/*GROUPEND*/');
		return $this;
	}

	/**
	 * Create a JOIN statement
	 *
	 * @param string $table
	 * @param string $condition
	 * @param string|false $join_type Supported value are left, right, inner, outer etc. Check MySQL documentation about JOIN types.
	 * @param bool  $escape_table If false, the $table argument will not be escaped with backticks
	 * @return $this
	 */
	function join($table, $condition, $join_type=false, $escape_table=true ): Db_helper
	{
		$jt = $join_type ? strtoupper($join_type).' ' : '';
		if($escape_table) {
			$table = trim($table);
			$t = explode(' ', $table);
			$table = isset($t[0]) ? $t[0] : $table;
			$as = isset($t[1]) ? " {$t[1]}" : '';
			$alias = isset($t[2]) ? " {$t[2]}" : '';
			$this->_join[] = "{$jt}JOIN `{$table}`{$as}{$alias} ON {$condition}";
		}
		else{
			$this->_join[] = "{$jt}JOIN {$table} ON {$condition}";
		}
		return $this;
	}

	/**
	 * Creates the FROM statement
	 *
	 * @param string $table Table name
	 * @param string|false $as Optional table alias
	 * @param bool $quote whether to add backticks on the table name or not
	 * @return $this
	 */
	function from($table, $as=false, $quote=false): Db_helper
	{
		if(is_array($table)){
			foreach($table as $v){
				$t = trim($v);
				$pieces = explode(' ', $t);
				if( isset($pieces[0]) ){
					$name = "`{$pieces[0]}`";
					if(isset($pieces[1])) $name .= " ".$pieces[1];
					if(isset($pieces[2])) $name .= " ".$pieces[2];
					$this->_from[] = $name;
				}
			}
		}
		elseif(is_string($table)){
			if($quote){
				$table = trim($table);
				$t = explode(' ', $table);
				$table = isset($t[0]) ? $t[0] : $table;
				$as1 = isset($t[1]) ? " {$t[1]}" : '';
				$alias = isset($t[2]) ? " {$t[2]}" : '';
				$this->_from[] = $as ? "`".trim($table)."` AS `".trim($as)."`" : "`".trim($table)."`{$as1}{$alias}";
			}
			else{
				$this->_from[] = $as ? trim($table)." AS ".trim($as) : trim($table);
			}
		}
		return $this;
	}

	/**
	 * Creates the FROM statement. This is an alias of the from method
	 *
	 * @param string $table Table name
	 * @param string|false $as Optional table alias
	 * @param bool $quote whether to add backticks on the table name or not
	 * @return $this
	 */
	function table($table, $as=false, $quote=true): Db_helper
	{
		return $this->from($table,$as,$quote);
	}

	/**
	 * Creates the ORDER BY clause
	 *
	 * @param string|array $order String e.g date desc, name asc OR array ['date desc', 'name asc'] to produce ORDER BY date desc, name asc
	 * @return $this
	 */
	function order_by($order=[]): Db_helper
	{
		if( is_array($order) ) $this->_order = array_merge( $this->_order, $order );
		elseif($order && is_string($order)) $this->_order[] = $order;
		return $this;
	}

	/**
	 * Creates the GROUP BY clause
	 *
	 * @param string|array $group String e.g date, name OR array ['date', 'name'] to produce GROUP BY date, name
	 * @return $this
	 */
	function group_by($group=[]): Db_helper
	{
		if( is_array($group) ) $this->_group = array_merge( $this->_group, $group );
		elseif($group && is_string($group)) $this->_group[] = $group;
		return $this;
	}

	/**
	 * Creates the LIMIT clause
	 *
	 * @param int $limit the upper limit value
	 * @param int $start optional starting index
	 * @return $this
	 */
	function limit($limit, $start=0): Db_helper
	{
		$this->_limit = "{$start}, {$limit}";
		return $this;
	}

	private function _build_where(&$where, $assoc=false, $finalize=true){
		$WHERE = '';
		if(!empty($where)){
			if(is_string($where)){
				$WHERE = $where;
				// IMPORTANT: you must submit your own "WHERE" keyword. Would have used stripos to automatically insert the "WHERE"
				// if it's missing but stripos was returning bool(false) in all test cases.
				// You can also include other queries e.g. joins inside this $where argument.
			}
			else{
				$w = [];
				$sign = FALSE;
				$join = false;
				$joins = [];
				foreach($where as $k=>$v){
					if(is_numeric($k)){
						$w[] = $v;
						if($finalize) unset($where[$k]);
					}
					else {
						$signs = explode(' ', $k);
						if($signs && count($signs) > 1) {
							for($i = 1; $i < count($signs); $i++) {
								$s = strtoupper($signs[$i]);
								if(in_array($s, $this->signs)) $sign = $s;
								elseif($s == 'JOIN') $join = TRUE;
							}
						}
						if($join){
							$joins[] = "$k ON $v ";
							if($finalize) unset($where[$k]);
						}
						else{
							if($assoc) {
								$dk = str_replace('.','_dot_', $k);
								$w[] = $sign ? "{$k} :{$dk}" : "{$k} = :{$dk}";
							}
							else{
								$w[] = $sign ? "{$k} ?" : "{$k} = ?";
							}
						}
						$sign = FALSE;
						$join = FALSE;
					}
				}
				$WHERE = "WHERE ". implode(' AND ', $w);
				$WHERE = str_ireplace( ['/*GROUPSTART*/ AND', '/*GROUPSTART*/ OR', '/*GROUPSTART*/'], '(', $WHERE );
				$WHERE = str_ireplace(['AND /*GROUPEND*/', 'OR /*GROUPEND*/', '/*GROUPEND*/'], ')', $WHERE);
				$WHERE = preg_replace( "/\sAND\s+AND\s/i", ' AND ', $WHERE );
				$WHERE = preg_replace( "/\sOR\s+OR\s/i", ' OR ', $WHERE );
				$WHERE = str_ireplace( ' AND OR ', ' OR ', $WHERE );
				$WHERE = str_ireplace( ['WHERE OR ', 'WHERE AND '], 'WHERE ', $WHERE );
				$WHERE = $joins ? implode( ' ', $joins ) . $WHERE : $WHERE;
			}
		}
		return $WHERE;
	}

	private function add_wildcard($value, $wildcard){
		switch($wildcard){
			case "both" : $value = "%{$value}%";
				break;
			case 'before':
			case 'left': $value = "%{$value}";
				break;
			case 'after':
			case 'right': $value = "{$value}%";
				break;
		}
		return $value;
	}

	private function _build_query($assoc=false, $finalize=true){
		if($this->_current_query) return $this->_current_query;
		$sql = "";
		$SELECT = $this->_build_select($sql);
		$TABLE = $this->_build_from($sql);
		$JOIN = $this->_build_join($sql);
		$WHERE = "";
		$HAVING = "";
		$pending_parameters = []; // use this if we're not yet finalizing the query. We don't want to mess up $this->_parameters just yet.
		if( !empty($this->_where) ){
			$parameters = $this->_where;
			$WHERE = $this->_build_where($parameters, $assoc, $finalize);
			$sql .= $WHERE." \n";
			$formatted_parameters = [];
			if(is_array($parameters)){
				if($assoc) {
					foreach($parameters as $k => $v) {
						$f = str_replace('.', '_dot_', $k);
						$formatted_parameters[$f] = $v;
					}
				}
				else $formatted_parameters = array_values($parameters);
			}
			if($finalize) $this->_parameters = array_merge( $this->_parameters, $formatted_parameters );
			else $pending_parameters = array_merge( $this->_parameters, $formatted_parameters );
		}
		$GROUP = $this->_build_group_by($sql);
		$HAVING = $this->_build_having($sql);
		$this->_build_order_by($sql);


		if($this->_paginate && $finalize){
			global $paginator;
			if( $this->_paginator_select ) $select = $this->_paginator_select;
			else{
				$select = isset($this->_select['id'])?"{$this->_select['id']}":( isset($this->_select["{$TABLE}.id"]) ? $this->_select["{$TABLE}.id"] : $SELECT);
			}
			$psql = "SELECT {$select} FROM {$TABLE} {$JOIN} {$WHERE}";
			if($GROUP) $psql .= " GROUP BY {$GROUP}";
			if($HAVING) $psql .= " HAVING {$HAVING}";
			$paginator->calculate_total_entries($psql, $this->_parameters);
			$LIMIT = $paginator->limit_sql();
			$sql .= " ".$LIMIT;
		}
		elseif( !empty($this->_limit) ) $sql .= "LIMIT ".$this->_limit;
		if($finalize){
			$this->set_last_query( $sql, $this->_parameters, $assoc );
			$this->_current_query = $sql;
		}
		else{
			$sql = $this->compile_query($sql, $pending_parameters, $assoc);
		}
		return $sql;
	}

	/**
	 * Inserts a new record into a table
	 *
	 * @param string $table
	 * @param array $data
	 * @param bool $default_fields Will insert default fields when true. These are username_id, usergroup, rec_id, rec_lstup_id, rec_lstup and date
	 * @return false|int The last insert ID or false on failure
	 * @throws \Doctrine\DBAL\Driver\Exception
	 */
	function insert($table, $data=[], $default_fields=TRUE, $dry_run=false)
	{

		if($data){
			foreach($data as $k=>$v){
				if ($data[$k]=='') {
					unset($data[$k]);
				}
			}
		}
		if($default_fields){
			if(!isset($data['username_id'])) $data['username_id'] = random();
//			if(!isset($data['usergroup'])) $data['usergroup'] = $_SESSION['usergroup'];
//			if(!isset($data['rec_id'])) $data['rec_id'] = isset($_SESSION['uid'])?$_SESSION['uid']:0;
//			if(!isset($data['rec_lstup_id'])) $data['rec_lstup_id'] = isset($_SESSION['uid'])?$_SESSION['uid']:0;
			if(!isset($data['rec_lstup'])) $data['rec_lstup'] = date("Y-m-d H:i:s");
			if(!isset($data['date'])) $data['date'] = date("Y-m-d H:i:s");
		}

		$hook_message = '';
		$hooks = [];
		if($this->_run_hooks || $this->_temp_hook){
			dev_debug("Hooks Data before: ".json_encode($data,JSON_PRETTY_PRINT));
			$hooks = $this->get_hooks($table);
			$data = $this->call_hooks( $hooks, 2, $data, $hook_message ); // 2. PRE_INSERT HOOKS
			if($hook_message) dev_debug($hook_message);
			$hook_message = '';
			dev_debug("Hooks Data AFTER: ".json_encode($data,JSON_PRETTY_PRINT));
			dev_debug("Hooks Data:".json_encode($hooks,JSON_PRETTY_PRINT));
		}


		if($data){
			foreach($data as $k=>$v){
				$this->set( $k, $v );
			}
		}

		$values = [];
		$FIELDS = $this->_build_set( 'insert', $values );
		$sql = "INSERT INTO {$table} {$FIELDS}";
		$this->set_last_query($sql,$values);
		if($dry_run) return $this->get_last_query();
//        $this->db->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );
		try{
			$stmt = $this->db->prepare($sql);
			$stmt->execute($values);
			$this->_row_count = $stmt->rowCount();
			$insert_id = $this->db->lastInsertId();

			if($this->_run_hooks || $this->_temp_hook){
				$data['id'] = $insert_id;
				$data = $this->call_hooks( $hooks, 3, $data, $hook_message ); // 3. POST_INSERT HOOKS
				if($hook_message) dev_debug($hook_message);
			}

			$this->reset();

			return $insert_id;
		}
		catch(Exception $e){

			dev_debug($e->getMessage());
			$this->setError($e->getMessage());
			$this->_row_count = 0;
			$this->reset();
			return FALSE;
		} finally {
//            $this->db->setAttribute( PDO::ATTR_ERRMODE, $this->_errorMode );
        }
	}

	/**
	 * Performs an UPDATE operation
	 *
	 * Also works with the set/where/like/join methods to build the update query.
	 *
	 * @param string $table The name of the table to update, required
	 * @param array $data An associative array of key value pairs where key is the table field name and value is the value to be updated.
	 *                    Can be used together with the set() method.
	 * @param string|array|false $where The Where statements, provided as either a string or a mixed array. Can be used together with any of the where/like/join methods
	 * @param bool  $default_fields Will update default fields when true. These are rec_lstup_id and rec_lstup
	 * @return bool True on success, false on failure
	 * @throws \Doctrine\DBAL\Driver\Exception
	 */
	function update($table, $data=[], $where=false, $default_fields=TRUE ){
		if($default_fields){
			if(!isset($data['rec_lstup_id'])) $data['rec_lstup_id'] = isset($_SESSION['uid'])?$_SESSION['uid']:0;
			if(!isset($data['rec_lstup'])) $data['rec_lstup'] = date("Y-m-d H:i:s");
		}

		$hook_message = '';
		$hooks = [];
		if($this->_run_hooks || $this->_temp_hook){
			$hooks = $this->get_hooks($table);
			$data = $this->call_hooks( $hooks, 4, $data, $hook_message ); // 4. PRE_UPDATE HOOKS
			if($hook_message) dev_debug($hook_message);
			$hook_message = '';
		}

		if($data){
			foreach($data as $k=>$v){
				$this->set( $k, $v );
			}
		}

		if($where && !empty($where)){
			is_string($where) ? $this->where($where,false, true) : $this->where($where);
		}
		$values = [];
		$SET = $this->_build_set('update', $values);
		$WHERE = $this->_build_query(false);
		$values = array_merge($values, $this->_parameters );
		$sql = "UPDATE {$table} {$SET} {$WHERE}";
		$this->set_last_query($sql,$values);
		if( $this->_simulation_mode ) return $this->get_last_query();
//        $this->db->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );
		try
		{
			$stmt = $this->db->prepare($sql);
			$e = $stmt->execute($values);
			$this->_row_count = $stmt->rowCount();

			if($this->_run_hooks || $this->_temp_hook){
				$data = $this->call_hooks( $hooks, 5, $data, $hook_message ); // 5. POST_UPDATE HOOKS
				if($hook_message) dev_debug($hook_message);
			}

			$this->reset();

			return $e && $this->_row_count;
		}
		catch(Exception $e){
			dev_debug($e->getMessage());
			$this->setError($e->getMessage());
			$this->_row_count = 0;
			$this->reset();
			return FALSE;
		} finally {
//            $this->db->setAttribute( PDO::ATTR_ERRMODE, $this->_errorMode );
        }
	}

	/**
	 * Deletes a record from the database
	 *
	 * @param string $table Name of the table, required
	 * @param array $where The Where statements, provided as either a string or a mixed array. Can be used together with any of the where/like/join methods
	 * @return bool True on success, false on failure
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	function delete($table, $where=[]){
        $this->db->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );
        try {
            if($where && !empty($where)){
                is_string($where) ? $this->where($where,false, true) : $this->where($where);
            }
            $WHERE = $this->_build_query(false);
            $values = $this->_parameters;
            $sql = "DELETE FROM {$table} {$WHERE}";
            $this->set_last_query($sql,$values);
            if( $this->_simulation_mode ) return $this->get_last_query();
            $stmt = $this->db->prepare($sql);
            $e = $stmt->execute($values);
            $this->_row_count = $stmt->rowCount();
            $this->reset();
            return $e && $this->_row_count;
        }
        catch(Exception $e){
            dev_debug($e->getMessage());
            $this->setError($e->getMessage());
            $this->_row_count = 0;
            $this->reset();
            return FALSE;
        } finally {
            $this->db->setAttribute( PDO::ATTR_ERRMODE, $this->_errorMode );
        }
	}

	/**
	 * Creates and runs a simple DELETE WHERE IN query.
	 *
	 * N.B. This method does not work with the query builder where methods
	 *
	 * @param string $table Name of the table, required
	 * @param string $key Name of the field to be used in the WHERE clause, required
	 * @param array $in A numerically indexed array of values which will go inside the IN statement, required
	 * @return bool True on success, false on failure
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	function delete_where_in($table, $key, $in): bool
	{
        $this->db->setAttribute( PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION );
        try {
            $IN = implode(',', array_fill(0, count($in), '?'));
            $values = array_values($in);
            $sql = "DELETE FROM {$table} WHERE {$key} IN ({$IN})";
            $this->set_last_query($sql,$values);
            if( $this->_simulation_mode ) return $this->get_last_query();
            $stmt = $this->db->prepare($sql);
            $e = $stmt->execute($values);
            $this->_row_count = $stmt->rowCount();
            $this->reset();
            return $e && $this->_row_count;
        }
        catch(Exception $e){
            dev_debug($e->getMessage());
            $this->setError($e->getMessage());
            $this->_row_count = 0;
            $this->reset();
            return FALSE;
        } finally {
            $this->db->setAttribute( PDO::ATTR_ERRMODE, $this->_errorMode );
        }
	}

	/**
	 * Converts field titles to corresponding db field names. e.g. $data['email'] will be converted to $data['db112']
	 *
	 * @param array $fields                 An array with db field names indexed by field titles e.g. $fields = ['email'=>'db112','first_name'=>'db123']
	 * @param array $data                   An array of data indexed either by field titles or db field names e.g. $data = ['email'=>'<EMAIL>','db123'=>'Test']
	 * @param array $tables                 An array of tables names if $fields contains fields that are prefixed by a table name.
	 * @param bool $update_missing_fields   if true, fields not contained in the $data array will be included in the update/insert query with a value of null.
	 * @return array
	 */
	function prepare_data($fields, $data, $tables=[], $update_missing_fields=false ): array
	{
		$template = [];
		// add . suffix to table names
		$tabs = count($tables)>0 ? array_map(function($t){return $t.'.';}, $tables) : FALSE;
		foreach($fields as $field_title=>$db_field){
			// remove table name prefix from field names
			if($tabs){
				$field_title = str_replace($tabs, '', $field_title);
				$db_field = str_replace($tabs, '', $db_field);
			}
			// if field title is a number (a numeric array index), then field title = db field name
			if(is_numeric($field_title)) $field_title = $db_field;
			if( isset($data[$field_title]) ){
				if($update_missing_fields) $template[$db_field] = $data[$field_title];
				elseif(isset($data[$field_title])) $template[$db_field] = $data[$field_title];
				elseif( isset($data[$db_field]) ) $template[$db_field] = $data[$db_field]; // removed && !empty($data[$db_field])
			}
			elseif( isset($data[$db_field]) ){
				if($update_missing_fields) $template[$db_field] = $data[$db_field];
				else $template[$db_field] = $data[$db_field]; // removed if( !empty($data[$db_field]) )
			}
		}
		return $template;
	}

	/**
	 * Adds table name as prefix to a db field
	 *
	 * @param array $fields An associative array of key/value pairs
	 * @param string $table The table name
	 * @return array
	 */
	function prepend_fields($fields, $table ): array
	{
		$data = [];
		foreach($fields as $k=>$v) {
			if(preg_match('/\./', $v)) $data[$k] = $v;
			else $data[$k] = $table.'.'.$v;
		}
		return $data;
	}

	/**
	 * Returns the last query that was executed
	 *
	 * @return string
	 */
	public function get_last_query(): string
	{
		return $this->_last_query;
	}

	/**
	 * Returns the compiled SQL based on the current state of the query builder. Same as get_compiled_query()
	 *
	 * @param bool $assoc whether to create a query with named parameters or not
	 * @return string
	 */
	public function  get_current_query($assoc=false): string
	{
		return $this->_build_query($assoc, false);
	}

	/**
	 * Returns the raw SQL statement for the last operation
	 *
	 * @return string
	 */
	public function get_raw_query(): string
	{
		return $this->_raw_sql;
	}

	/**
	 * Returns the compiled SQL based on the current state of the query builder. Same as get_current_query()
	 *
	 * @param bool $assoc whether to create a query with named parameters or not
	 * @return string
	 */
	public function get_compiled_query($assoc=false){
		return $this->_build_query($assoc, false);
	}


	/**
	 * Allows for a safer way to generate a query without affecting the actual query that you may need to run later.
	 *
	 * Gives similar results to get_compiled_query but is more reliable
	 *
	 * @param string $method The method you wish to execute, e.g get_rows, get_row, fetch_field etc.
	 * @return string|false
	 */
	public function simulate_query($method="get_rows"){
		$copy = clone $this;
		$copy->setSimulationMode(true);
		if(method_exists( $copy, $method )){
			call_user_func([$copy, $method]);
			return $copy->get_last_query();
		}
		return false;
	}

	/**
	 * Returns the number of rows affected by the last query.
	 *
	 * @return int
	 */
	public function affected_rows(): int
	{
		return $this->_row_count;
	}


	/**
	 * Returns the number of rows from the last query [normally used after a get operation]
	 *
	 * @return int
	 */
	public function rowCount(): int
	{
		return $this->_row_count;
	}

	private function set_last_query($debug_sql,$args, $assoc=false){
		$this->_raw_sql = $debug_sql;
		$debug_sql = $this->compile_query($debug_sql, $args, $assoc);
		dev_debug($debug_sql);
		$this->_last_query = $debug_sql;
	}

	private function compile_query($sql, $args, $assoc=false){
		if( is_array($args) && count($args)){
			if($assoc){
				$keys = array_keys($args);
				$values = array_values($args);
				foreach($keys as $k=>$v){
					$keys[$k] = ":".str_replace( '.', '_dot_', $v);
				}
				$sql = str_replace( $keys, $values, $sql );
			}
			else {
				foreach($args as $k => $v) {
					if(is_string($v) && !empty($v)){
						$v = $this->db->quote($v);
					}
					$sql = preg_replace("/\?/", is_numeric($v)?$v:$v, $sql, 1);
				}
			}
		}
		return $sql;
	}

	/**
	 * Returns field details of a table that is defined in system_table
	 *
	 * @param int $table_id The id of the table as defined in system_table
	 * @param int|false $usergroup Optional usergroup ID
	 * @return array|false|mixed[]
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	public function get_table_fields($table_id, $usergroup=FALSE){
		$where = [ 'pg_id'=>$table_id ];
		if($usergroup) {
			$where['usergroup'] = $usergroup;
			$rows = $this->get_rows('*', 'system_table', $where, ['form_order ASC']);
			if( is_array($rows) && count($rows) > 0 ) return $rows;
			$where = [ 'pg_id'=>$table_id ];
		}
		$where[] = "(usergroup IS NULL OR usergroup = '' OR usergroup = 1)";
		return $this->get_rows('*', 'system_table', $where, ['form_order ASC']);
	}

	/**
	 * removes fields that do not exist in the given table. Useful in removing fields in table definitions with fields from multiple tables.
	 *
	 * @param       $table_id
	 * @param       $data
	 * @param false $usergroup
	 * @return array
	 */
	public function validate_fields($table_id, $data, $usergroup=FALSE ): array
	{
		$valid = $fields = [];
		$this->set_temp_fetch_mode('array');
		// Method 1: get table fields using DESCRIBE table
		$table_name = $this->get_table_name($table_id);
		if($table_name){
			$fields = $this->get_table_input_fields($table_name, true);
		}

		// Method 2: get table fields using system_table.
		if(empty($fields)) {
			$rows = $this->get_table_fields($table_id,$usergroup);
			$exclude = ['title', 'instruction', 'textonly', 'info_message', 'info_msg', 'instruction4', 'instructions', 'instruction_box_blue', 'not specified', 'page_break_end', 'page_break_start', 'subtitle', 'warning'];
			foreach($rows as $row) {
				if($row['db_field_name'] && !in_array($row['type'], $exclude)) $fields[] = $row['db_field_name'];
			}
		}
		foreach( $data as $k=>$v ){
			if( in_array($k,$fields) ) $valid[$k] = $v;
		}
		$this->exit_temp_fetch_mode();
		return $valid;
	}


	/**
	 * Retrieves the name of a table given it's system_table ID
	 *
	 * @param int $id
	 * @param bool $include_prefix Whether to return the table name with it's prefix or not
	 * @return false|string Returns the table name or false on failure
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	function get_table_name($id, $include_prefix=true){
		$this->set_temp_fetch_mode('array');
		$select = [ 'prefix'=>'system_cat.sys_cat_abv', 'name'=>'system_pages.page_name', 'id'=>'system_pages.page_id' ];
		$where = ['system_pages.page_id'=>$id];
		$where['INNER JOIN system_cat'] = "system_cat.sys_cat_id = system_pages.project";
		$table = $this->get_row( $select, 'system_pages', $where );
		if(isset($table['id'])){
			$this->exit_temp_fetch_mode();
			return $include_prefix ? $table['prefix'].'_'.$table['name'] : $table['name'];
		}
		$this->exit_temp_fetch_mode();
		return false;
	}


	/**
	 * Retrieves all fields of a table that can be used as input fields ina form.
	 *
	 * @param string $table_name The fully qualified name of the database table
	 * @param bool $mapped Whether to return the field names only or the complete field details
	 * @return array
	 */
	function get_table_input_fields($table_name, $mapped=false){
		$this->set_temp_fetch_mode('array');
		$table_fields = $this->get_query("DESCRIBE {$table_name}");
		if( is_array($table_fields) && $mapped ){
			$fields = array_map( function($row){ return $row['Field']; }, $table_fields );
			$this->exit_temp_fetch_mode();
			return $fields;
		}
		$this->exit_temp_fetch_mode();
		return $table_fields;
	}

	private function _build_select(&$sql){
		$SELECT = '';
		if( !empty($this->_select) ){
			$FIELDS = [];
			foreach($this->_select as $k=>$v){
				if(is_numeric($k)) $FIELDS[] = $v;
				else $FIELDS[] = "{$v} AS `{$k}`";
			}
			$SELECT = implode( ', ', $FIELDS );
			$sql .= $this->_distinct ? "SELECT DISTINCT ".$SELECT." \n" : "SELECT ".$SELECT." \n";
		}
		return $SELECT;
	}

	private function _build_update_set($f, $v, &$values){
		$field = $f;
		$escape = true;
		if( array_key_exists('escape', $v) ){
			$escape = $v['escape'];
			$field = $escape ? "`{$f}`" : $f;
		}
		if( array_key_exists('has_value', $v) && array_key_exists('value', $v) ){
			if($v['has_value']){
				$field .= $escape ? " = ?" : " = {$v['value']}";
				if($escape) $values[] = $v['value'];
			}
		}
		return $field;
	}

	private function _build_insert_set($f, $v, &$values){
		$field = "`$f`";
		if( array_key_exists('value', $v) ){
			$values[] = $v['value'];
		}
		else{
			$values[] = null;
		}
		return $field;
	}

	private function _build_set($mode, &$values){
		$SET = '';
		if( !empty($this->_set) ){
			$fields= [];
			foreach($this->_set as $f=>$v){
				$field = $f;
				if( is_array($v) ){
					$field = $mode==='update' ? $this->_build_update_set( $f, $v, $values ) : $this->_build_insert_set( $f, $v, $values );
				}
				else {
					$array = [ 'value'=>$v, 'escape'=>true, 'has_value'=>true ];
					$field = $mode==='update' ? $this->_build_update_set( $f, $array, $values ) : $this->_build_insert_set( $f, $array, $values );
				}
				$fields[] = $field;
			}
			$FIELDS = implode(', ', $fields);
			if($mode==='update'){
				$SET = "SET {$FIELDS}";
			}
			elseif($mode==='insert'){
				$q = implode(', ', array_fill(0, count($values), '?'));
				$SET = "({$FIELDS}) VALUES ({$q})";
			}
		}
		return $SET;
	}

	private function _build_from(&$sql){
		$TABLE = "";
		if( !empty($this->_from) ){
			$TABLE = implode( ', ', $this->_from );
			$sql .= "FROM ".$TABLE." \n";
		}
		return $TABLE;
	}

	private function _build_join(&$sql){
		$JOIN = "";
		if( !empty($this->_join) ){
			$JOIN = implode( " \n", $this->_join );
			$sql .= $JOIN." \n";
		}
		return $JOIN;
	}

	private function _build_group_by(&$sql){
		$GROUP = "";
		if( !empty($this->_group) ){
			$GROUP = implode( ', ', $this->_group );
			$sql .= "GROUP BY ".$GROUP." ";
		}
		return $GROUP;
	}

	private function _build_order_by(&$sql){
		$ORDER = "";
		if( !empty($this->_order) ){
			$ORDER = implode( ', ', $this->_order );
			$sql .= "ORDER BY ".$ORDER." \n";
		}
		return $ORDER;
	}

	private function _build_having(&$sql){
		$HAVING = "";
		if( !empty($this->_having) ){
			$HAVING = $this->_having;
			$sql .= "HAVING ".$HAVING." \n";
		}
		return $HAVING;
	}

	/**
	 * Manually starts a transaction. You will need to manually commit it
	 *
	 * @return bool True / false on success / failure
	 */
	public function trans_start(): bool
	{
		try {
			$e = $this->db->beginTransaction();
			return $e;
		}
		catch(PDOException $e){
			$this->setError($e->getMessage());
			throw new PDOException($e->getMessage(), $e->getCode(), $e->getPrevious() );
		}
	}

	/**
	 * Commits a transaction
	 *
	 * @return bool True / false on success / failure
	 */
	public function  trans_commit(): bool
	{
		try {
			$e = $this->db->commit();
			return $e;
		}
		catch(PDOException $e){
			$this->setError($e->getMessage());
			throw new PDOException($e->getMessage(), $e->getCode(), $e->getPrevious() );
		}
	}

	/**
	 * Rolls back a transaction that has not been committed yet
	 *
	 * @return bool True / false on success / failure
	 * @throws \Doctrine\DBAL\ConnectionException
	 */
	public function trans_rollback(): bool
	{
		try {
			$e = $this->db->rollBack();
			return $e;
		}
		catch(PDOException $e){
			$this->setError($e->getMessage());
			throw new PDOException($e->getMessage(), $e->getCode(), $e->getPrevious() );
		}
	}

	/**
	 * Checks whether there's currently a transaction that hasn't been committed or rolled back yet
	 *
	 * @return bool true / false
	 */
	public function in_transaction(): bool
	{
		return $this->db->inTransaction();
	}

	/**
	 * Retrieves a list of hooks defined for the given table name
	 *
	 * @param string $table The name of the table including the table prefix
	 * @return array|false|mixed|mixed[]
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	public function get_hooks($table){
		$parts = explode('_', $table);
		if($parts && count($parts)>1){
			$prefix = $parts[0];
			unset($parts[0]);
			$table = implode('_', array_values($parts));
			$sql = "SELECT page_id FROM system_pages INNER JOIN system_cat ON sys_cat_id = system_pages.project WHERE page_name=? AND sys_cat_abv = ?";
			$page = $this->get_query($sql,[$table,$prefix],false);
			if(!isset($page['page_id'])) return [];
			$sql = "select * from system_model_hooks where model_id = ? order by exec_order";
			$hooks = $this->get_query($sql,[$page['page_id']]);
			return $hooks?:[];
		}
		return [];
	}

	private function call_hooks($model_hooks, $hook_id, $args, &$msg_response) {
		foreach ($this->filter_hooks($model_hooks, $hook_id) as $hook) {
			$args = $this->call_hook($hook, $args);
			// msg is just simple message, nothing to worry about
			if (isset($args['msg'])) {
				$msg_response .= $args['msg']."\n";
				unset($args['msg']);
			}
			// 'error' means finish hooks, but do not process further
			if (isset($args['error'])) {
				$msg_response .= $args['error_msg']."\n";
				unset($args['error_msg']);
			}
			// 'stop' means critical error, do not process any further
			if (isset($args['stop'])) {
				$msg_response .= $args['stop_msg']."\n";
				unset($args['stop_msg']);
				break;
			}
		}
		return $args;
	}

	private function filter_hooks($hooks, $type) {
		return array_filter($hooks, function ($hook) use ($type) {
			return ($hook['hook_type'] == $type);
		});
	}

	private function call_hook($hook, $args) {
		error_log('Running hook function '.$hook['namespace'].'\\'.$hook['function_name']);

		$base_path = defined('base_path') ? base_path : ( defined('ABSPATH') ? ABSPATH : realpath(__DIR__.'./../../../') );

		require_once($base_path.'/engine/models/custom/'.$hook['script_path']);

		// Grab and process static and mapping arguments
		$static = json_decode($hook['static'], true) or array();

		// Manipulation of mappings
		$col_mappings = json_decode($hook['mappings'], true) or array();

		$mappings = array();
		if(!empty($col_mappings)){
            foreach ($col_mappings as $key => $col_name) {
                $mappings[$key] = $args[$col_name];
            }
        }


		// Call the hook function
		list($args, $mappings) = call_user_func(
			$hook['namespace'].'\\'.$hook['function_name'],
			$args, $static, $mappings);

		// Put whatever changed in mappings back to $args and return
        if(!empty($col_mappings)){
            foreach ($col_mappings as $key => $col_name) {
                $args[$col_name] = $mappings[$key];
            }
		}

		if(!empty($args['stop'])){
			$msg = $args['stop_msg'] ?? "An unspecified error happened in the hook: ".json_encode($hook);
			throw new Exception($msg);
		}

		return $args;
	}

	public function having($sql)
    {
        // forkaround for an old operation api
        $this->_having = $sql;
        return $this;
	}
}
