<?php

namespace App\models;

use Aws\S3\S3Client;

class FileStorage
{
    public string $vaultEndPoint = 'https://vault.ecloud.co.uk';
    public string $bucket = 'heiapply';
    public S3Client $client;

    public function __construct()
    {
        $this->connectToS3();
    }

    public function getFromLocalDisk()
    {
    }

    public function putToLocalDisk()
    {

    }

    public function getFromS3($args = [])
    {
        $args['Bucket'] = $this->bucket;
        try {
            $response = $this->client->getObject($args);
            return $response;
        } catch (\Exception $e) {
            die($e->getMessage());
        }
    }

    public function putToS3($args = [])
    {

        try {
            $args['Bucket'] = $this->bucket;
            $response = $this->client->putObject($args);
            return $response;
        } catch (\Exception $e) {
            die($e->getMessage());
        }
    }
    public function copyObject($args = [])
    {

        try {
            $args['Bucket'] = $this->bucket;
            $response = $this->client->copyObject($args);
            return $response;
        } catch (Exception $e) {
            die($e->getMessage());
            return ($e->getMessage());
        }
    }
    public function deleteFromS3()
    {

        try {
            $response = $this->client->deleteObject([
                'Bucket' => 'heiapply',
                'Key' => 'hello.txt',
                'Body' => "Hello World!"
            ]);
        } catch (\Exception $e) {

        }
    }

    public function getSignedUrlFromS3()
    {

        try {
            $request = $this->client->createPresignedRequest($this->client->getCommand('GetObject', ['Bucket' => 'heiapply', 'Key' => 'secret_plans.txt']), '+1 hour');
            $response = $request->geturi();
        } catch (\Exception $e) {

        }
    }

    public function getS3Buckets()
    {

        try {
            $response = $this->client->listBuckets();
            return $response['Buckets'];
        } catch (\Exception $e) {

        }
    }

    public function getS3BucketContents()
    {

        try {
            $response = $this->client->listObjects(
                ['Bucket' => 'test']
            );
            return $response['Contents'] ?? [];
        } catch (\Exception $e) {

        }
    }

    public function connectToS3()
    {
        // Instantiate the S3 class and point it at the desired host
        try {
            $client = new S3Client([
                'region' => 'us-west-2',
                'version' => '2006-03-01',
                'endpoint' => $this->vaultEndPoint,
                'credentials' => [
                    'key' => env('VAULT_ACCESS_KEY'),
                    'secret' => env('VAULT_SECRET_KEY')
                ],
                'use_path_style_endpoint' => true
            ]);
            $this->client = $client;
        } catch (\Exception $e) {

        }
    }
	public function checkFileExistence($bucket, $filePath)
	{
		return $this->client->doesObjectExist($bucket, $filePath);
	}

	public function getFileSize($bucket, $key)
	{
		try {
			$object = $this->client->headObject([
				'Bucket' => $bucket,
				'Key' => $key
			]);
			$fileSizeBytes = $object['ContentLength'];
			return $this->formatFileSize($fileSizeBytes);
		} catch (Exception $e) {
			return 0;
		}
	}

	// Function to format file size in KB, MB, or GB
	public function formatFileSize($size)
	{
		//return in KB for now
		return round($size / 1024, 2) . ' KB';
		if ($size >= 1_073_741_824) {  // 1 GB = 1_073_741_824 bytes
			return round($size / 1_073_741_824, 2) . ' GB';
		} elseif ($size >= 1_048_576) {  // 1 MB = 1_048_576 bytes
			return round($size / 1_048_576, 2) . ' MB';
		} elseif ($size >= 1024) {  // 1 KB = 1024 bytes
			return round($size / 1024, 2) . ' KB';
		} else {
			return $size . ' bytes';
		}
	}
}
