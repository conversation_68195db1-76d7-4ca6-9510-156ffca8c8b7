<?php


namespace App\console\commands;


use App\models\FileStorage;
use Carbon\Carbon;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class FixVaultFiles extends Command
{
	protected static $defaultName = 'files:fix-vault-files';

	protected function configure()
	{
		$this->setDescription('Fixes files that are  in vault but no vault key in the database')
			->setHelp('This command is used to fix files that are  in vault but no vault key in the database');
		#$this->addArgument('size', InputArgument::REQUIRED, 'The minimum size to move files');
		$this->addArgument('table', InputArgument::OPTIONAL, 'The table where the files are stored');
		$this->addArgument('usergroup', InputArgument::OPTIONAL, 'The usergroup');
		$this->addArgument('id', InputArgument::OPTIONAL, 'The file id');
	}

	protected function execute(InputInterface $input, OutputInterface $output)
	{
		$usergroup = $input->getArgument('usergroup');
		$table = $input->getArgument('table') ?? 'form_file';
		$id = $input->getArgument('id') ?? '';
		$fileStorage = new FileStorage();
		$output->writeln([
			'Command is running for usergroup:' . $usergroup . ', Table:' . $table,
			'============',
			'',
		]);
		if ($table === 'form_file_large_format') {
			$query = dbh()->createQueryBuilder();
			$query->from($table);
			$query->where("db159275!='vault' or db159275 is null");
			if ($usergroup) {
				$query->andWhere("usergroup=$usergroup");
			}
			if (!empty($id)) {
				$query->andWhere("id='$id'");
			}

			$query->select("*");
			$files = $query->execute()->fetchAll(5);
		}
		if ($table === 'form_file') {
			$query = dbh()->createQueryBuilder();
			$query->from($table);
			$query->where("db159272!='vault' or db159272 is null");
			if ($usergroup) {
				$query->andWhere("usergroup=$usergroup");
			}
			if (!empty($id)) {
				$query->andWhere("id='$id'");
			}
			$query->select("*");
			$files = $query->execute()->fetchAll(5);
		}
		$output->writeln($query->getSQL());
		$output->writeln("Found " . count($files) . ' files');
		$movedFiles = 0;
		//create main zip
		$oneQuery = '';
		$ids = [];
		foreach ($files as $file) {
			if ($table === 'form_file_large_format') {
				if ($fileStorage->checkFileExistence('heiapply', 'media/' . $file->db26071) || $fileStorage->checkFileExistence('heiapply', str_replace('//', '/', 'media/' . $file->db26071))) {
					//$query = "UPDATE form_file_large_format set db159275='vault' where id=" . $file->id;
					$oneQuery .= "UPDATE form_file_large_format set db159275='vault' where id=" . $file->id . ";";
					$ids[] = $file->id;
					//dbh()->query($query);
					$output->writeln(" File " . $file->db26071 . ' updated successfully.');
					$movedFiles++;
				}
			}
			if ($table === 'form_file') {
				if ($fileStorage->checkFileExistence('heiapply', 'media/' . $file->db204) || $fileStorage->checkFileExistence('heiapply', str_replace('//', '/', 'media/' . $file->db204))) {
					//$query = "UPDATE form_file set db159272='vault' where id=" . $file->id;
					$oneQuery .= "UPDATE form_file set db159272='vault' where id=" . $file->id . ";";
					$ids[] = $file->id;
					//dbh()->query($query);
					$output->writeln(" File " . $file->db204 . ' updated successfully.');
					$movedFiles++;
				}
			}
		}
		if (!empty($ids)) {
			$chunks = array_chunk($ids, 50); // Split into chunks of 1000
			foreach ($chunks as $chunk) {
				if ($table === 'form_file_large_format') {
					$query = "UPDATE form_file_large_format set db159275='vault' where id in (" . implode(',', $chunk) . ")";
				}
				if ($table === 'form_file') {
					$query = "UPDATE form_file set db159272='vault' where id in (" . implode(',', $chunk) . ")";
				}
				try {
					dbh()->query($query);
					$output->writeln("Files batch updated successfully.");
				} catch (\Exception $e) {
					$output->writeln($e->getMessage());
				}
			}

		}
		$output->writeln($movedFiles . " files successfully updated");
	}
}
