<?php


namespace App\console\commands;


use App\models\OnlineLearning;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ProcessReferenceReminder extends Command
{
    protected static $defaultName = 'notifications:process-reference-reminder';

    protected function configure()
    {
        $this->setDescription('Sends emails saved in form_email_log')
            ->setHelp('This command allows you to send emails saved in form_email_log and update their status');
        $this->addArgument('run', InputArgument::OPTIONAL, 'The limit of the rows.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $dbh = dbh();
        $cron_start_date = date('Y-m-d H:i:s');

        //limit to Solihull for now
        //$ug = sanitise(empty($input->getArgument('usergroup')) ? 42 : $input->getArgument('usergroup'));
        //$run = sanitise(empty($input->getArgument('run')) ? 0 : $input->getArgument('run'));
        $run = sanitise($input->getArgument('run') ?: 1);
        //$_SESSION['usergroup'] = $ug;
        ($run) ? $run = 1 : $run = 0;


        $emails = [];
        //$output->writeln("<pre>" . print_r($ug, 1) . "</pre>");
        $query = "SELECT form_schools.id
                      FROM form_schools LEFT JOIN form_notifications_config ON form_notifications_config.usergroup=form_schools.id
                      WHERE (form_notifications_config.db37245 not like '%triweekly_referees_reminder%')  group by form_schools.id";
        $sth = $dbh->prepare($query);
        $sth->execute();
        $output->writeln(print_r($query));
        $results = $sth->fetchAll(\PDO::FETCH_OBJ);
        foreach ($results as $school) {
            echo "running school";
            echo $school->id;

            app()->system_forms = collect(app()->all_system_forms->get($school->id));
            set_app_school_info($school->id);
            echo "run is " . $run . "<br>";
            $result = $this->student_start_course_reminder($school->id, $output, $run);
            array_push($emails, $result);
        }


        if ($run == 0) {
            $output->writeln(print_r($emails));
        }
        $cron_end_date = date('Y-m-d H:i:s');

        #Update the crons jobs table table
        $output->writeln('Processed');

    }


    /**
     * @param int $usergroup School
     * @return array Logged emails
     */
    function student_start_course_reminder($usergroup, $output, $run = 0)
    {
        $schools = new \App\models\Schools();
        $school_info = $schools->get(array('id' => $usergroup));
        $dbh = dbh();
        if (107==$usergroup) {
           $exclude_types_sql = "AND db11360 not in ('_Secondary_Supervisor_(Student_Led_only)','Secondary_Mentor_(Post_Doctoral_Fellowship_only)')";
        }else{
            $exclude_types_sql="";
        }
        $query = "
			SELECT core_students.id,
            core_students.db39 AS first_name,
            core_students.db40 AS last_name,
            concat(core_students.db39,' ',core_students.db40) AS full_name,
            dir_reference.db11357  AS email,
            dir_reference.db11354 AS refree,
            dir_reference.date,
            dir_reference.username_id,
            db232 AS  programme,
            db11356,db33617
            FROM dir_reference
            INNER JOIN core_students core_students ON core_students.id = dir_reference.rel_id
            LEFT join dir_reference_questions on db33617=dir_reference.username_id
            LEFT join core_courses on core_courses.id=db889
            WHERE ((db33617 is null or db33617='')
				and (db11356 is null or db11356='')
                )
              AND (dir_reference.rec_archive IS NULL OR dir_reference.rec_archive='')
              AND (core_students.rec_archive IS NULL OR core_students.rec_archive='')
              AND core_students.usergroup='" . $usergroup . "'
              AND dir_reference.db19274='yes'
              AND dir_reference.date < DATE_ADD(CURDATE(),INTERVAL -2 WEEK)
              AND db890=".$school_info['cohort']."
              AND db41 NOT IN ('-1', '-2')
              AND db41='12'
               AND  IF(db237680 IS NOT NULL AND db237680 != '', CURDATE()<= db237680, 1)
               AND db340='on'
               AND db235 = 'public'
              $exclude_types_sql
            ";


        //if ($run=0) {
        echo $query;
        $output->writeln("Query $query");
        //}
        $stmt = $dbh->prepare($query);
        $stmt->execute();
        $results = $stmt->fetchAll(2);
        $students = [];
        $stmt = $dbh->prepare("SELECT * FROM coms_template where usergroup=$usergroup AND db1147='110' ");
        $stmt->execute();
        $coms_template = $stmt->fetch(2);
        if (empty($coms_template)) {
            $stmt = $dbh->prepare("SELECT * FROM coms_template where usergroup=1 AND db1147='110'");
            $stmt->execute();
            $coms_template = $stmt->fetch(2);
        }


        if (!empty($coms_template)) {
            $emails = [];

            
            foreach ($results as $result) {
                //figure out which template to use
              //get 1 week email template
                    $email_template = $coms_template['db1085'];
                    $subject = $coms_template['db1086'];


                    $email_template = str_replace('{{first_name}}', $result['first_name'], $email_template);
                    $email_template = str_replace('{{last_name}}', $result['last_name'], $email_template);
                    $email_template = str_replace('{{full_name}}', $result['full_name'], $email_template);
                    $email_template = str_replace('{{programme}}', $result['programme'], $email_template);
                    $email_template = str_replace('{{first_name_of_applicant}}', $result['first_name'], $email_template);
                    $email_template = str_replace('{{last_name_of_applicant}}', $result['last_name'], $email_template);
                    $email_template = str_replace('{{full_name_of_applicant}}', $result['full_name'], $email_template);
                    $email_template = str_replace('{{programme}}', $result['programme'], $email_template);
                    $email_template = str_replace('{{referee}}', $result['refree'], $email_template);

                    $email_template = str_replace('{{referee_link}}', "<a href='https://" . $school_info['subdomain'] . '.heiapply.com/application/reference-submission/' . $result['username_id'] . "''>this link</a>", $email_template);
                    $email_template = str_replace('{{school_name}}', $school_info['title'], $email_template);


                    //store  email_log
                    $students[] = $email_template;
                    if ($run == 1) {
                        $dbh->insert('form_email_log', [
                            "db1153" => $result['email'],
                            "db1154" => $school_info['email_address'],
                            "db1151" => $email_template,
                            //"rel_id" => $result['id'],
                            "db1150" => $email_template,
                            "db1149" => $subject,
                            "db1152" => 'alert',
                            'db1161' => 'no',
                            "usergroup" => $usergroup,
                            "username_id" => random(),
                        ]);
                    }

                
            }

            return $emails;
        } else {
            return [];
        }
    }
}
