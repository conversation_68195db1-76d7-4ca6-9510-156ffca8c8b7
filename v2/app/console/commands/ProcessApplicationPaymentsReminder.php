<?php


namespace App\console\commands;


use App\models\OnlineLearning;
use Carbon\Carbon;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class ProcessApplicationPaymentsReminder extends Command
{
    protected static $defaultName = 'payments:application-payments-reminder';

    protected function configure()
    {
        $this->setDescription('Sends payment reminders')
            ->setHelp('This command allows you to send application payment reminders');
        $this->addArgument('usergroup', InputArgument::OPTIONAL, 'The usergroup to run for.');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $dbh = dbh();
        $cron_start_date = date('Y-m-d H:i:s');

        //limit to J1 for now
        $ug = $input->getArgument('usergroup') ?? 254;
        $output->writeln("Running for usergroup " . $ug);

        //2nd payment query
        $query = "select core_students.*
from core_students
         LEFT JOIN `dir_stage_tracker` ON `dir_stage_tracker`.`rel_id` = `core_students`.`id`
where core_students.usergroup = $ug
  AND dir_stage_tracker.db1142 = '12'
  and exists(select count(*) as total
             from sis_student_fees
             where sis_student_fees.rel_id = core_students.id
             having total = 1)
  and exists(SELECT date
             FROM dir_stage_tracker
             WHERE db1142 = '12'
               and date_format(date_add(dir_stage_tracker.date, INTERVAL 14 DAY), '%Y %m %d') =
                   date_format(now(), '%Y %m %d')
               AND (rec_archive IS NULL OR rec_archive = '')
               AND dir_stage_tracker.rel_id = core_students.id
               AND dir_stage_tracker.usergroup = $ug)";
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results = $sth->fetchAll(\PDO::FETCH_OBJ);
        $output->writeln("Found " . count($results) . " records");
        foreach ($results as $student) {
            $this->insertTask($student);
        }
        //3rd payment query
        $date=Carbon::parse("2023-02-28")->subDays(8)->format('Y-m-d');
        $query = "select core_students.*
from core_students
         LEFT JOIN `dir_stage_tracker` ON `dir_stage_tracker`.`rel_id` = `core_students`.`id`
where core_students.usergroup = $ug
  AND dir_stage_tracker.db1142 = '12'
  and exists(select count(*) as total
             from sis_student_fees
             where sis_student_fees.rel_id = core_students.id
             having total = 2)
  and exists(SELECT date
             FROM dir_stage_tracker
             WHERE db1142 = '12'
               and date_format(now(), '%Y %m %d') =$date
               AND (rec_archive IS NULL OR rec_archive = '')
               AND dir_stage_tracker.rel_id = core_students.id
               AND dir_stage_tracker.usergroup = $ug)";
        $sth = $dbh->prepare($query);
        $sth->execute();
        $results = $sth->fetchAll(\PDO::FETCH_OBJ);
        $output->writeln("Found " . count($results) . " records");
        foreach ($results as $student) {
            $this->insertTask($student);
        }
        $output->writeln('Processed');

    }

    public function insertTask($student)
    {
        $dbh = dbh();
        //get template
        $ug = $student->usergroup;
        $stmt = $dbh->prepare("SELECT*FROM coms_template where usergroup=$ug AND db1147='113'");
        $stmt->execute();
        $template = $stmt->fetch(5);
        if (empty($template)) {
            $stmt = $dbh->prepare("SELECT*FROM coms_template where usergroup=1 AND db1147='113'");
            $stmt->execute();
            $template = $stmt->fetch(5);
        }
        $body = $template->db1085;
        //replace some tags
        $body = str_replace(
            [
                '{{first_name}}',
                '{{last_name}}',
                '{{name}}',
                '{{surname}}',
            ],
            [
                $student->db39,
                $student->db40,
                $student->db39 . ' ' . $student->db40,
                $student->db40,
            ],
            $body
        );
        $taks_args = [
            'username_id' => random(),
            'rec_id' => '',
            'usergroup' => $student->usergroup,
            'rel_id' => $student->id,
            'date' => Carbon::now(),
            'title' => $template->db1086,
            'task_description' => $body,
            'due_by' => Carbon::today()->addWeek()->format("Y-m-d 23:00:00"),
            'status' => 'active',
            'assign_to_applicant' => $student->id,
            'person_normally_responsible' => 'Applicant',
            'notify_applicant' => 'on',
            'type' => 'general',
            'task_type' => 'application_payment_reminder',
            'release_date' => Carbon::today()->addDay()->format("Y-m-d"),
        ];
        $stmt = $dbh->prepare("INSERT into 
    core_tasks(username_id,rec_id,usergroup,rel_id,date,db1741,db1743,db1744,db1746,db22441,db22444,db22445,db22580,db23147,db37730) 
values (:username_id,:rec_id,:usergroup,:rel_id,:date,:title,:task_description,:due_by,:status,:assign_to_applicant,:person_normally_responsible,:notify_applicant,:type,:task_type,:release_date)");
        $stmt->execute($taks_args);


    }
}