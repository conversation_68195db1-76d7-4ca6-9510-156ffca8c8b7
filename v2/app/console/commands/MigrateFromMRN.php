<?php

//DOne!
namespace App\console\commands;
use App\console\commands\mrn_field_map;
use Carbon\Carbon;
use P<PERSON>Unit\Runner\Exception;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use App\models\Db_helper;
class MigrateFromMRN extends Command
{
    protected static $defaultName = 'database:import-mrn';
    private $map = [];
    private $base_fields = "username_id,rec_id,usergroup,rel_id,group_key,active,rec_lstup,rec_lstup_id,rec_archive,date";
    private $v1_dbh;
    private $v2_dbh;
    private $tables = ["sis_course_booking"];
    private $record_counter = 0;
    private $num_records = 0;
    private $users = [];

    const sched_booking_detail = [ 'id'=>'id', 'rel_id'=>'rel_id', 'rec_id'=>'rec_id','first_name'=>'db15054', 'surname'=>'db15055', 'email'=>'db15056', 'gender'=>'db15057', 'date_of_birth'=>'db15058', 'scheduled_booking_id'=>'db15052','booking_status'=>'db59978'];
//Questionable tables = "form_user_levels,form_submodules,form_user_access,sis_disabilities,coms_custom_fields"
//    private $lead_profile_dependencies = ['lead_profiles,sis_ind_learner_plan'];
    private $lead_profile_dependencies = [
        "form_users",
        "sis_employment_status",
        "core_ethnicity",
        "core_student_types",
        "sis_primary_groups",
        "sis_student_groups",
        "sis_disabilities",
        "sis_course_geog_area",
        "sis_course_geog_coverage",
        "sis_tutor_stages",
        "sis_tutor_group",
        "sis_tutor_type",
        "sis_course_stages",
        "sis_course_tutors",
        "sis_course_groups",
        "core_course_level",
        "core_faith_affiliations",
        "core_source_of_referrals",
        "sis_course_management",
        "sis_course_venues",
        "sis_booking_status",
        "sis_course_schedule_stages",
        "sis_attendance_status",
        "sis_course_schedule",
        "lead_partner",
        "lead_stages",
        "lead_profiles",
        "lead_convert",
        "sis_course_feedback",
        "sis_course_booking",
        "sis_tutor_checklist",
        "sis_course_waiting_list",
        "sis_tutor_comms",
        "sis_tutor_course_feedback",
        "sis_booking_status_track",
        "sis_tutor_status_tracker",
        "sis_sch_crs_status_track",
//        "sis_course_status_track",
        'sis_course_sessions',
        'sis_schedule_sessions',
        'sis_session_booking',
        "lead_tasks",
        "sis_learner_goal_groups",
        "sis_learner_goal_status",
        "sis_ind_learner_goals",
        "sis_ind_learner_plan",
        "sis_ilp_scoring",
        "core_contact_types",
        "lead_interactions",
        "enrol_induction_checklist",
        "sis_next_of_kin",
        "sis_reqol",
        "assist_categories",
        "assist_articles",
        "core_letter_library",
        "form_profile_settings",
        "form_password_reset",
        "form_knowlegebase",
        "form_knowlegebase_groups",
        "coms_template",
        "core_contact",
        "form_bulk_actions",
        "form_bulk_action_link",
        "form_support_ticket",
        "form_support_notes",
        "coms_custom_fields",
        "form_folder",
        "form_file",
        "sis_reqol_requests"
    ];
    private $rel_id_relationships = [
        "lead_convert" => "lead_profiles",
        "sis_ind_learner_plan" => "lead_profiles",
        "core_notes" => "lead_profiles",
        "dir_internal_notes" => "lead_profiles",
        "form_folder" => "lead_profiles",
        "form_file" => "lead_profiles",
        "form_email_log" => "lead_profiles",
        "form_letter_log" => "lead_profiles",
        "form_sms_log" => "lead_profiles",
        "form_cron_send_log" => "lead_profiles",
        "sis_ind_learner_goals" => "lead_profiles",
        "lead_interactions" => "lead_profiles",
        "sis_student_contact" => "lead_profiles",
        "enrol_induction_checklist" => "lead_profiles",
        "sis_next_of_kin" => "lead_profiles",
        "sis_session_booking" => "lead_profiles",
        "sis_course_waiting_list" => "lead_profiles",
        "sis_reqol" => "lead_profiles",
        "form_support_notes" => "form_support_ticket",
        "sis_course_feedback" => "sis_course_schedule",
        "sis_tutor_checklist" => "sis_course_tutor",
        "sis_tutor_comms" => "sis_course_tutor",
        "sis_tutor_course_feedback" => "sis_course_tutor",
        "sis_course_booking" => "lead_profiles",
        "sis_booking_status_track" => "sis_course_booking",
        "sis_tutor_status_tracker" => "sis_course_tutor",
        "sis_sch_crs_status_track" => "sis_course_schedule",
        "sis_course_status_track" => "sis_course_management",
        "lead_tasks" => "lead_profiles",
        'sis_schedule_sessions' => "sis_course_schedule",
        'sis_course_sessions' => "sis_course_management",
        "form_profile_settings" => "form_users",
        "sis_reqol_requests" => "lead_profiles",

    ];

//    Foreign key Fields
//    new_field_name => old_table_name
    private $foreign_key_fields = [
        'db1317'=>'system_pages',
        'db22'=>'form_users',
        'db23'=>'form_users',
        'db97'=>'form_modules',
        'db29329'=>'form_create_view',
        'db29330'=>'form_bulk_actions',
        'db19360'=>'sis_primary_groups',
        'db201'=>'form_users',
        'db997'=>'core_hei_roles',
        'db1087'=>'coms_template',
        'db1197'=>'form_modules',
        'db1193'=>'form_knowlegebase_groups',
        'db1235'=>'form_account_status',
        'db1729'=>'assist_categories',
        'db1692'=>'coms_custom_fields',
        'db1701'=>'form_user_levels',
        'db66520'=>'sis_course_schedule',
        'db63029'=>'lead_partner',
        'db16146'=>'sis_course_management',
        'db62923'=>'sis_course_geog_coverage',
        'db52624'=>'form_coms_types',
        'db52626'=>'form_users',
        'db52628'=>'core_note_priority',
        'db66778'=>'sis_course_geog_coverage',
        'db48837'=>'sis_course_geog_area',
        'db52599'=>'form_users',
        'db52601'=>'sis_tutor_type',
        'db52602'=>'sis_tutor_group',
        'db66774'=>'sis_course_groups',
        'db1424'=>'sis_course_tutors',
        'db16995'=>'sis_course_tutors',
        'db66734'=>'sis_course_management',
        'db66732'=>'sis_course_management',
        'db66730'=>'sis_course_management',
        'db48581' => 'lead_stages',
        'db48595' => 'sis_course_management',
        'db48593' =>'sis_course_management',
        'db48594' =>'sis_course_management',
        'db48592' =>'sis_course_management',
        'db48570' => 'lead_partner',
        'db48574' => 'core_source_of_referrals',
        'db48578' => 'core_course_level',
        'db48609' => 'sis_primary_groups',
        'db48591' =>'core_faith_affiliations',
        'db48564' =>'form_users',
        'db48643' =>'sis_disabilities',
        'db48649' =>'sis_course_schedule',
        'db48650' =>'sis_course_schedule',
        'db48653' =>'sis_employment_status',
        'db48657' =>'core_student_types',
        'db52605' => 'sis_course_management',
        'db52606' => 'sis_course_geog_coverage',
        'db48610' => 'sis_student_groups',
        'db48602' => 'sis_course_management',
        'db48600' => 'sis_course_geog_coverage',
        'db16901' => 'sis_course_tutors',
        'db14946' => 'sis_course_management',
        'db16888' => 'sis_course_tutors',
        'db14954' => 'sis_course_venues',
        'db66793' => 'sis_course_geog_coverage',
        'db17213' => 'sis_course_tutors',

        'db14983' => 'sis_booking_status',
        'db16136' => 'sis_course_management',
        'db14977' => 'sis_course_schedule',

        'db17984' => 'sis_course_tutors',
        'db17985' => 'sis_course_tutors',
        'db59839' => 'sis_course_venues',
        'db59840' => 'sis_course_sessions',

        'db1745' => 'form_users',

        'db61555' => 'sis_learner_goal_groups',
        'db61557' => 'sis_course_management',
        'db61562' => 'sis_learner_goal_status',

        'db1403' => 'core_contact_types',
        'db17043' => 'lead_stages',
        'db1454' => 'core_contact_types',
        'db63840' => 'lead_stages',

        'db59901' => 'sis_schedule_sessions',
        'db59900' => 'sis_course_sessions',
        'db59976' => 'sis_course_booking',

        'db73' => 'form_coms_types',
        'db19828' => 'coms_template',
        'db19831' => 'coms_template',
        'db77' => 'form_users',
        'db91' => 'core_note_priority',
        'db66791' => 'sis_primary_groups',
        'db201' => 'form_users'

    ];
    protected function configure()
    {
        $this->setDescription('Imports data from MRN and maps it to corresponding heiapply tables')
            ->setHelp('This command imports data from MRN and maps it to corresponding heiapply tables');
        $this->addArgument('old_usergroup', InputArgument::REQUIRED, 'The Old Usergroup of the rows.');
        $this->addArgument('new_usergroup', InputArgument::REQUIRED, 'The New Usergroup of the rows.');
        $this->addArgument('table', InputArgument::OPTIONAL, 'The table to migrate');
        $this->addArgument('update', InputArgument::OPTIONAL, 'Whether to run a fresh migration or to update existing table');
    }
//ToDo: Change Primary Groups to foreign keys in sis_profiles
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        set_time_limit(0);
        $f_map = new mrn_field_map();
        $this->map = json_decode($f_map->field_map, 2);
        $old_usergroup = $input->getArgument('old_usergroup');
        $new_usergroup = $input->getArgument('new_usergroup');
        $table = $input->getArgument('table');
        $update = $input->getArgument('update');
        $update = !empty($update);

        $this->v1_dbh = $this->db_connect('v1', $input, $output);
        $this->v2_dbh = $this->db_connect('v2', $input, $output);
        if($old_usergroup==8){
            $this->foreign_key_fields['db48601'] = 'sis_course_geog_area';
        }else{
            $this->foreign_key_fields['db48601'] = 'sis_course_geog_coverage';
        }
        $progress_file = fopen(__DIR__."/../../../storage/mrn_migration_table_progress.json", "w") or die("Unable to open file!");
        fwrite($progress_file, count($this->lead_profile_dependencies));
        fclose($progress_file);

        if(isset($old_usergroup) && isset($new_usergroup)){
            $output_file = fopen(__DIR__."../../../../storage/mrn_migration_output.txt", "w") or die("Unable to open file!");
            $dbh = get_dbh();
            if(!$table){
                $index = 0;
                foreach ($this->lead_profile_dependencies as $table) {
                    $this->migrate_table($table, $input, $output, $output_file);
                    $index ++;
                    $progress_file = fopen(__DIR__."/../../../storage/mrn_migration_table_progress.json", "w") or die("Unable to open file!");
                    fwrite($progress_file, $index);
                    fclose($progress_file);
                }
                $dbh = get_dbh();
                $dbh->query("UPDATE sis_profiles SET db48581 =1 WHERE db48581 IS NULL AND usergroup={$new_usergroup} ");
                $dbh->query("UPDATE sis_sched_booking_detail SET db15052 = (SELECT id FROM sis_scheduled_booking WHERE rel_id= CAST(sis_sched_booking_detail.rel_id as char)) WHERE usergroup={$new_usergroup} ");
            }elseif (is_numeric($table)){
                $index = $table;
                foreach (array_slice($this->lead_profile_dependencies, $table) as $tbl) {
                    $this->migrate_table($tbl, $input, $output, $output_file);
                    $index ++;
                    $progress_file = fopen(__DIR__."/../../../storage/mrn_migration_table_progress.json", "w") or die("Unable to open file!");
                    fwrite($progress_file, $index);
                    fclose($progress_file);
                }
                $dbh = get_dbh();
                $dbh->query("UPDATE sis_profiles SET db48581 =1 WHERE db48581 IS NULL AND usergroup={$new_usergroup} ");
                $dbh->query("UPDATE sis_sched_booking_detail SET db15052 = (SELECT id FROM sis_scheduled_booking WHERE rel_id= CAST(sis_sched_booking_detail.rel_id as char)) WHERE usergroup={$new_usergroup} ");
            }else{
                $this->migrate_table($table, $input, $output, $output_file, $update);
            }
            if(empty($table)){
                $query = "SELECT 
            sis_scheduled_booking.id as booking_id, 
            sis_sched_booking_detail.id as detail_id,
            db14983 as booking_status, 
            db14981 as attendance_status 
            FROM sis_session_bookings
             LEFT JOIN sis_scheduled_booking ON sis_session_bookings.db59976 = sis_scheduled_booking.id
            LEFT JOIN sis_sched_booking_detail ON sis_sched_booking_detail.db15052 = sis_scheduled_booking.id
            WHERE sis_session_bookings.usergroup='{$new_usergroup}'";
                $stmt = $dbh->prepare($query);
                $stmt->execute();
                $session_bookings = $stmt->fetchAll(2);
                $current_detail_id = 0;
                $dbh->query("UPDATE sis_sched_booking_detail SET db64664 = (SELECT db14981 FROM sis_scheduled_booking WHERE sis_sched_booking_detail.db15052=sis_scheduled_booking.id) WHERE usergroup='{$new_usergroup}'");
                foreach ($session_bookings as $booking){
                    $session_booking_update_query = "UPDATE sis_session_bookings SET db59977='{$booking['detail_id']}', db59902 ='{$booking['booking_status']}' WHERE id='{$booking['id']}'";
                    $dbh->query($session_booking_update_query);
                }
                fclose($output_file);
            }

        }

    }

    function migrate_table($table, InputInterface $input, OutputInterface $output, $output_file, $update = false){
        $db_helper = new Db_helper();
        $old_usergroup = $input->getArgument('old_usergroup');
        $new_usergroup = $input->getArgument('new_usergroup');
        $output->writeln("Migrating Table: $table");
        $txt ="=====================================================\n=====================================================\n\n\nTable is . {$table}\n";
        $this->log($txt, $output, $output_file);
        $tutors = [];
        $dbh = get_dbh();

        if($table!=="form_users" && empty($this->users)){
            $query = "SELECT db67121 as new_id, db67119 as old_id FROM form_migration WHERE db67118='form_users' AND usergroup='{$new_usergroup}'";
            $stmt = $dbh->prepare($query);
            $stmt->execute();
            $this->users = $stmt->fetchAll(2);
            $this->log("Migrated Users ===  ... ".json_encode($this->users), $output, $output_file);
            $this->log("New user id for 282 ===  ... ".json_encode($this->getUserId(["rec_id"=>473], "rec_id")), $output, $output_file);

        }
            if($table=="sis_course_schedule"){
                $query = "SELECT db67121 as new_id, db67119 as old_id FROM form_migration WHERE db67118='sis_course_tutors' AND usergroup='{$new_usergroup}'";
                $stmt = $dbh->prepare($query);
                $stmt->execute();
                $tutors = $stmt->fetchAll(2);
            }
        $this->log("COUNT Users ===  ... ".count($this->users), $output, $output_file);
        $field_map = [];
        foreach ($this->map as $item) {
            if($item['v1tablename'] == $table){
                array_push($field_map,$item);
            }
        }
        $fields_v1 = $this->base_fields.',date';
        $fields_v2 = [];
        foreach(explode(",",$this->base_fields.',date') as $item){
            $fields_v2[$item] = $item;
        }

        foreach ($field_map as $map_for_field){
            $fields_v1 .= ",".$map_for_field['v1db_field_name'];
            $fields_v2[$map_for_field['v1db_field_name']] = $map_for_field['v2db_field_name'];
        }
        $v2_table = $field_map[0]['v2tablename'];

        $sth = $this->v1_dbh->prepare("SELECT COUNT(*) FROM $table WHERE usergroup = {$old_usergroup}");
        $sth->execute();
        $total = $sth->fetchColumn();
        $this->log("FOUND TOTAL:=> {$total}", $output, $output_file);
        $this->num_records = $total;

        if($total>0){
            $offset = 0;
            $step = 100;
            $quotient = $total/$step;
            $mod = $total%$step;
            $num_pages = $mod>0?intval($quotient)+1:$quotient;
            $this->record_counter = 0;

            foreach (range(1, $num_pages) as $page) {
                $sth = null;
                $this->v1_dbh = null;
                $this->v1_dbh = $this->db_connect('v1', $input, $output);
                $sth = $this->v1_dbh->prepare("SELECT id, $fields_v1 FROM $table WHERE usergroup = {$old_usergroup} LIMIT $offset, $step");
                $sth->execute();
                if(!$sth){
                    $this->log(json_encode($this->v1_dbh->errorInfo()));
                }
                $imported_records = $sth->fetchAll(2);
                if($this->num_records==0){
                    $progress_file = fopen(__DIR__."/../../../storage/mrn_migration_progress.json", "w") or die("Unable to open file!");
                    $output->writeln("Table has no records to import");
                    fwrite($progress_file, 100);
                    fclose($progress_file);
                }else{
                        $count_first = pull_field("form_migration", "COUNT(db67121)", "WHERE db67119='{$imported_records[0]['id']}' AND db67118='{$table}' AND usergroup={$new_usergroup}");
                        $count_last = pull_field("form_migration", "COUNT(db67121)", "WHERE db67119='{$imported_records[($this->num_records-1)]['id']}' AND db67118='{$table}' AND usergroup={$new_usergroup}");
                        if ($count_first > 0 && $count_last > 0) {
                            $output->writeln("SUBSET has been wholly imported previously");
                            continue;
                        }
                    }
                foreach ($imported_records as $reckey=> $record){
                    $count = pull_field("form_migration", "COUNT(db67121)", "WHERE db67119='{$record['id']}' AND db67118='{$table}' AND usergroup={$new_usergroup}" );
                    $this->log("Record Data:=> ".json_encode($record), $output, $output_file);
                    if($count>0 && !$update){
                        $this->log("Record already imported", $output, $output_file);
                        $this->record_counter++;
                        $progress_file = fopen(__DIR__."/../../../storage/mrn_migration_progress.json", "w") or die("Unable to open file!");
                        $progress = ceil($this->record_counter * 100/$this->num_records);
                        fwrite($progress_file, $progress);
                        fclose($progress_file);
                        unset($imported_records[$reckey]);
                        continue;
                    }
                    if($table=="sis_course_management") {
                        $db341 = pull_field("core_course_level", "id", "WHERE usergroup = {$new_usergroup}");
                        $insert_values = ["db340"=> "on", "db235"=> "public", "db341" => $db341];
                    }else{
                        $insert_values = ($table=="form_users"?["db1034"=> "{$new_usergroup}"]: []);
                    }
                    foreach(explode(",",$fields_v1) as $item) {
                        if($item =="db16888" || $item =="db17213"){
                            continue;
                        }
                        $v2_field_name = $fields_v2[$item];
                        if ($item == 'usergroup') {
                            $insert_values['usergroup'] = $new_usergroup;
                        }else{
                            $insert_values[$v2_field_name] = trim(addslashes($record[$item]));
                        }
                        //Setting Null for empty
                        if(empty($record[$item])){
                            $insert_values[$v2_field_name] = null;
                        }
                        if($item == "db21307"){
                            $new_val = $item??($record["db21308"]?? null);
                            $insert_values[$v2_field_name] = $new_val;
                        }
                        if($item == "db21308"){
                            $new_val = $item??($record["db21307"]?? null);
                            $insert_values[$v2_field_name] = $new_val;
                        }
                    }
                    //Execute Pre Hook
                    if($table=="lead_profiles") {
                        $this->log("\n\nCREATING FORM USERS ...", $output, $output_file);
                        $insert_values["rec_id"] = $this->create_form_users($insert_values, $input, $output, $output_file);
                    }
                    $insert_values = $this->setForeignKeys($table, $insert_values, $input, $output, $output_file);

                    if($table=="sis_schedule_sessions") {
                        $insert_values["db59906"] = pull_field("sis_course_sessions", "db59829", "WHERE id='{$insert_values['db59840']}'");
                        $insert_values["db59908"] = pull_field("sis_course_schedule", "db14959", "WHERE id='{$insert_values['rel_id']}'");
                    }

                    $query = $db_helper->insert($v2_table, $insert_values, true, true);
                    $this->log($query, $output, $output_file);
                    $last_insert_id = null;
                    if($update){
                        $last_insert_id = pull_field("form_migration", "db67121", "WHERE db67119 = '{$record['id']}' AND db67118='{$table}'");
                        $db_helper->update($v2_table, $insert_values, array('id'=>$last_insert_id,"usergroup"=>$new_usergroup));
                    }else{
                        $last_insert_id = $db_helper->insert($v2_table, $insert_values, false);
                    }
                    //Execute POST Hook
                    if($table=="lead_profiles") {
                        $l_student_array = $this->create_core_student($last_insert_id, $old_usergroup, $new_usergroup, $output, $output_file);
                        $sth = $this->v2_dbh->prepare("UPDATE sis_profiles SET rel_id={$l_student_array['id']} WHERE id={$last_insert_id}");
                        $this->log("UPDATING sis_profiles___  UPDATE sis_profiles SET rel_id={$l_student_array['id']} WHERE id={$last_insert_id}", $output, $output_file);
                        $sth->execute();
                    }else if($table=='sis_scheduled_sessions'){
                        $foreign_key = pull_field("sis_course_schedule","db14959", "WHERE id={$insert_values['rel_id']}");
                        $dbh->query("UPDATE sis_scheduled_sessions SET db14959='{$foreign_key}', db59906 = (SELECT db232 FROM core_courses WHERE id = (SELECT db14946 FROM sis_course_schedule WHERE id = {$insert_values['rel_id']})) WHERE usergroup={$new_usergroup} ");
                    }else if($table=='sis_course_schedule'){
                        if(!empty($record["db16888"])){
                            $args1 = ['rec_id' => $insert_values["rec_id"], 'rel_id' => $last_insert_id, 'username_id' => random(), 'usergroup' => $new_usergroup, "db59798" => $this->getNewId($tutors, $record["db16888"])];
                            $q1 = $db_helper->insert("sis_scheduled_tutors", $args1, false, true);
                            $this->log("INSERTING sis_scheduled_tutors___  {$q1}", $output, $output_file);
                            $db_helper->insert("sis_scheduled_tutors", $args1);
                        }
                        if(!empty($record["db17213"])){
                            $args2 = ['rec_id' => $insert_values["rec_id"], 'rel_id' => $last_insert_id, 'username_id' => random(), 'usergroup' => $new_usergroup, "db59798" => $this->getNewId($tutors, $record["db17213"])];
                            $q2 = $db_helper->insert("sis_scheduled_tutors", $args2, false, true);
                            $this->log("INSERTING sis_scheduled_tutors___  {$q2}", $output, $output_file);
                            $db_helper->insert("sis_scheduled_tutors", $args2);
                        }

                    }else if($table=='sis_course_booking'){
                        $sis_profiles_id = null;
                        $core_student_id = null;
                        $sis_profiles_id = pull_field("form_migration", "db67121", "WHERE db67119 = '{$record['rel_id']}' AND db67118='lead_profiles'");
                        if(!empty($sis_profiles_id)) $core_student_id = pull_field("sis_profiles", "rel_id", "WHERE id = '{$sis_profiles_id}'");

                        $this->log("Sis_profiles ID for Booking is: {$sis_profiles_id} And core students id = {$core_student_id}\n", $output, $output_file);

                        $rec_id = pull_field("sis_profiles", "rec_id", "WHERE id = '{$insert_values['rel_id']}'");
                        if(!empty($sis_profiles_id) && !empty($last_insert_id)){
                            $student_array = $this->create_core_student_for_booking($sis_profiles_id, $rec_id, $new_usergroup);
                            $sth = $this->v2_dbh->prepare("UPDATE sis_scheduled_booking SET rel_id={$student_array['booking_core_student_id']} WHERE id={$last_insert_id}");
                            $this->log("UPDATING sis_scheduled_booking___  UPDATE sis_scheduled_booking SET rel_id={$student_array['booking_core_student_id']} WHERE id={$last_insert_id}", $output, $output_file);
                            $sth->execute();
                            if (!empty($student_array['booking_core_student_id'])) {
                                $this->log("The main record we got is: ".json_encode($record), $output, $output_file);
                                ###third create a sched_booking_detail record
                                $user_data = ['rel_id' => $last_insert_id];
                                $user_data['scheduled_booking_id'] = pull_field("sis_scheduled_booking", "id", "WHERE rel_id={$student_array['booking_core_student_id']}");
                                $user_data['booking_status'] = $insert_values['db14983'];
                                $this->create_sched_booking_detail($user_data, $student_array, $output, $output_file);
                            }
                            $student_array = null;
                        }
                    }
                    if($last_insert_id){
                        $data = [
                            "usergroup" => $new_usergroup,
                            "db67118" => $table,
                            "db67119" => $record['id'],
                            "db67120" => $v2_table,
                            "db67121" => $last_insert_id,
                        ];
                        $db_helper->insert("form_migration", $data);
                    }
                    $this->record_counter ++;
                    if($this->num_records === $this->record_counter) {
                        $txt ="Finished Importing {$table}\n";
                        $output->writeln($txt);
                        fwrite($output_file, $txt);
                    }
                    $progress_file = fopen(__DIR__."/../../../storage/mrn_migration_progress.json", "w") or die("Unable to open file!");
                    fwrite($progress_file, ceil($this->record_counter * 100/$this->num_records));
                    $output->writeln("Progress is: ".ceil($this->record_counter * 100/$this->num_records));
                    fclose($progress_file);
                    unset($imported_records[$reckey]);

                }
                $offset = $page * $step;
            }
        }

        return true;
    }

    function log($text, OutputInterface $output, $output_file, $limit100 = true){
        if ($this->record_counter<=100 || !$limit100) {
            $output->writeln($text);
            fwrite($output_file, $text."\n");
        }
    }

    function create_form_users($args, InputInterface $input, OutputInterface $output, $output_file){
        $dbh = get_dbh();

        ### hook runs first creation of form users
        $db112 = 4; //position parent
        $db111 = trim(addslashes($args["db48568"])); //surname
        $db106 = trim(addslashes($args["db48566"])); //first name
        $db119 = trim(addslashes($args["db48583"])); //email
        $pass2_encrypted = ''; // encrypt password
        $db222 = ''; //password
        $db223 = "no"; //can delete
        $db307 = 'yes'; //yes is suspended
        $db308 = trim(addslashes($args["db48566"])) .  trim(addslashes($args["db48568"])); //username
        $db1034 = $args['usergroup']; //user group manager
        $randomvalue = random();
        $db62967 = trim(addslashes($args["db48619"]));

        //check if email already exists
        $email_check  =  pull_field("form_users", "id", "WHERE db119 ='{$db119}' AND usergroup='{$args['usergroup']}'");

        if (!empty($email_check)) {
            return $email_check;

        }else{
            $form_users_sql = "INSERT INTO form_users (username_id, rec_id, usergroup, rel_id, db112, db111, db106, db119, db222, db223, db307, db308, db1034,db62967) VALUES ('" . $randomvalue . "', '1', '$db1034', '" . floating_info("ref") . "', '$db112', '$db111', '$db106', '$db119', '$db222', '$db223', '$db307', '$db308', '$db1034', '$db62967')";
            $this->log("FORM USERS Insert Query ... {$form_users_sql}", $output, $output_file);

            dev_debug($form_users_sql);
            $sql = $dbh->prepare($form_users_sql);
            $sql->execute();
            $id = pull_field("form_users", "id", "WHERE username_id = '{$randomvalue}'");
            $this->log("NEW rec_id is == {$id}", $output, $output_file);
            return $id;
        }
    }

    function setForeignKeys($table, $data, InputInterface $input, OutputInterface $output, $output_file){
        foreach ($data as $key=> $value){
            if(array_key_exists($key, $this->foreign_key_fields)){
                $new_values = [];
                foreach (explode(",", $value) as $old_value){
                    array_push($new_values, pull_field("form_migration", "db67121", "WHERE db67119='{$old_value}' AND db67118='{$this->foreign_key_fields[$key]}'" ));
                }
                $new_values = implode(",", $new_values);
//                $this->log("Foreign key for {$key} was {$data[$key]} and now is {$new_values}", $output, $output_file );
                $data[$key] = $new_values;
            }
        }
        $data = $this->setRecId($data);
        $data = $this->setRelId($table, $data, $input,  $output, $output_file);

        if($table=="sis_course_booking") {
            $data["db14978"] = pull_field("sis_course_schedule","db14947","WHERE id='{$data['db14977']}'");
        }
        return $data;
    }
    function getUserId($data, $prop = 'rec_id', $debug=false){
        $users = array_filter($this->users, function ($user) use ($data, $prop){
            return $user["old_id"]==$data[$prop];
        });
        if(!empty($users)) {
            if ($debug) return array_values($users);
            return array_values($users)[0]["new_id"];
        }
        return $data[$prop];
    }
    function getNewId($data, $old_id){
        $items = array_filter($data, function ($row) use ($old_id){
            return $row["old_id"]==$old_id;
        });
        if(!empty($items)) {
            return array_values($items)[0]["new_id"];
        }
        return $old_id;
    }
    function setRecId( $data){
        if(!empty($data['rec_id'])){
            $data['rec_id'] = $this->getUserId($data);
        }
        if(!empty($data['rec_lstup_id'])){

            $data['rec_lstup_id'] = $this->getUserId($data, 'rec_lstup_id');
        }
        if(!empty($data['rec_archive'])){
            $data['rec_archive'] = $this->getUserId($data, 'rec_archive');
        }
        return $data;
    }
    function setRelId($table, $data, InputInterface $input, OutputInterface $output, $output_file){
        if(array_key_exists($table, $this->rel_id_relationships) && !empty($data['rel_id'])){

            $this->log("Related table is ... {$this->rel_id_relationships[$table]}", $output, $output_file);
            $newId = pull_field("form_migration", "db67121", "WHERE db67119='{$data['rel_id']}' AND db67118='{$this->rel_id_relationships[$table]}'" );

            if(($table=="form_sms_log" || $table=="form_email_log") && $this->rel_id_relationships[$table]=="lead_profiles"){
                $newId = pull_field("sis_profiles", "rec_id", "WHERE id='{$newId}'" );
            }else if($table=="sis_session_booking" && $this->rel_id_relationships[$table]=="lead_profiles"){
                $newId = pull_field("sis_scheduled_booking", "rel_id", "WHERE id='{$data['db59976']}'" );
            }else if($this->rel_id_relationships[$table]=="lead_profiles"){
                $newId = pull_field("sis_profiles", "rel_id", "WHERE id='{$newId}'" );
            }else if($table=="sis_reqol_requests"){
                $newId = pull_field("sis_profiles", "rel_id", "WHERE id='$newId'");
            }
            $this->log("setting rel_id for {$data['rel_id']} to {$newId}", $output, $output_file);
            $data['rel_id'] = $newId;
        }else{
            $this->log("No rel_id found. \n", $output, $output_file);
        }
        return $data;
    }
    function create_core_student($profile_id, $old_usergroup, $new_usergroup, OutputInterface $output, $output_file) {


        ///now get the details of this student to add to core_student
        $dbh = get_dbh();
        $sql = $dbh->prepare("SELECT rec_id,rel_id,db48566,db48568,db48567,db48573,db48572,db48574,db48593,db48583,db48571,db48597 from sis_profiles where id = {$profile_id}");
        $sql->execute();
        $student_infos = $sql->fetchAll(\PDO::FETCH_OBJ);
        $student_info = $student_infos[0];

        $rel_id = $student_info->rel_id;
        $_db39_first_name = $student_info->db48566;
        $_db40_surname = $student_info->db48568;
        $_db46_middle_name = $student_info->db48567;
        $_db41_application_status = 0;
        $_db44_gender = $student_info->db48573;
        $_db53_dob = $student_info->db48572;
        $_db510_source_applicant = $student_info->db48574;
        $_db889_first_course = $student_info->db48593;
        $_db764_email = $student_info->db48583;
        $_db765_telephone = $student_info->db48571;
        $_db28467_mobile = $student_info->db48597;
        $db50 = pull_field("core_course_level", "id", "WHERE db16595='short' AND usergroup='$new_usergroup' and (rec_archive IS NULL or rec_archive='')");
        $random = random();
        $student_array = array(
            "username_id" => $random,
            "rec_id" => $student_info->rec_id,
            "usergroup" => $new_usergroup,
            "rel_id" => 0,
            "db39_first_name" => $_db39_first_name,
            "db40_surname" => $_db40_surname,
            "db41_application_status" => $_db41_application_status,
            "db44_gender" => $_db44_gender,
            "db46_middle_name" => $_db46_middle_name,
            "db53_dob" => $_db53_dob,
            "db510_source_applicant" => $_db510_source_applicant,
            "db889_first_course" => $_db889_first_course,
            "db764_email" => $_db764_email,
            "db765_telephone" => $_db765_telephone,
            "db28467_mobile" => $_db28467_mobile,
            "db50" => $db50);
        $sql = "INSERT INTO core_students (username_id,rec_id,usergroup,rel_id,db39,db40,db41,db44,db46,db53,db510,db889,db764,db765,db28467,db50) 
    					VALUES (:username_id,:rec_id,:usergroup,:rel_id, :db39_first_name,:db40_surname,:db41_application_status,:db44_gender,:db46_middle_name,:db53_dob,:db510_source_applicant,:db889_first_course,:db764_email,:db765_telephone,:db28467_mobile,:db50)";

        $sql = $dbh->prepare($sql);
        $sql->execute($student_array);
        $id = $dbh->lastInsertId();
        $sth = $dbh->prepare("SELECT * FROM core_students WHERE id='{$id}'");
        $sth->execute();
        $data = $sth->fetch(2);
        $this->log("Core Students Data== ".json_encode($data), $output, $output_file);

        return $data;

    }

    function create_core_student_for_booking($profile_id, $rec_id, $usergroup) {

        ///now get the details of this student to add to core_student
        $dbh = get_dbh();
        $sql = $dbh->prepare("SELECT rel_id,db48566,db48568,db48567,db48573,db48572,db48574,db48593,db48583,db48571,db48597 from sis_profiles where id ='{$profile_id}'");
        $sql->execute();
        $student_infos = $sql->fetchAll(\PDO::FETCH_OBJ);
        $student_info = $student_infos[0];

        $rel_id = $student_info->rel_id;
        $_db39_first_name = $student_info->db48566;
        $_db40_surname = $student_info->db48568;
        $_db46_middle_name = $student_info->db48567;
        $_db41_application_status = 0;
        $_db44_gender = $student_info->db48573;
        $_db53_dob = $student_info->db48572;
        $_db510_source_applicant = $student_info->db48574;
        $_db889_first_course = $student_info->db48593;
        $_db764_email = $student_info->db48583;
        $_db765_telephone = $student_info->db48571;
        $_db28467_mobile = $student_info->db48597;
        $db50 = pull_field("core_course_level", "id", "WHERE db16595='short' AND usergroup='{$usergroup}' and (rec_archive IS NULL or rec_archive='')");

        $student_array = array(
            "username_id" => random(),
            "rec_id" => $rec_id,
            "usergroup" => $usergroup,
            "rel_id" => $rel_id,
            "db39_first_name" => $_db39_first_name,
            "db40_surname" => $_db40_surname,
            "db41_application_status" => $_db41_application_status,
            "db44_gender" => $_db44_gender,
            "db46_middle_name" => $_db46_middle_name,
            "db53_dob" => $_db53_dob,
            "db510_source_applicant" => $_db510_source_applicant,
            "db889_first_course" => $_db889_first_course,
            "db764_email" => $_db764_email,
            "db765_telephone" => $_db765_telephone,
            "db28467_mobile" => $_db28467_mobile,
            "db50" => $db50);
        $sql = "INSERT INTO core_students (username_id,rec_id,usergroup,rel_id,db39,db40,db41,db44,db46,db53,db510,db889,db764,db765,db28467,db50) 
    					VALUES (:username_id,:rec_id,:usergroup,:rel_id, :db39_first_name,:db40_surname,:db41_application_status,:db44_gender,:db46_middle_name,:db53_dob,:db510_source_applicant,:db889_first_course,:db764_email,:db765_telephone,:db28467_mobile,:db50)";
        dev_debug($sql);
        $sql = $dbh->prepare($sql);
        $sql->execute($student_array);

        $new_core_student_id = $dbh->lastInsertId();
        $student_array['booking_core_student_id'] = $new_core_student_id;

        return $student_array;

    }

    function create_sched_booking_detail($user_data, $student_array, $output, $output_file) {
        $this->log("user data =>> ".json_encode($user_data), $output,$output_file);
        $db_helper = new Db_helper();
        $attendance_status = (!empty($user_data['attendance_status'])? $user_data['attendance_status'] : '');
        $booking_detail = [
            'rel_id' => $student_array['booking_core_student_id'],
            'username_id' => random(),
            'first_name'=>$student_array['db39_first_name'],
            'surname'=>$student_array['db40_surname'],
            'email'=>$student_array['db764_email'],
            'gender'=>$student_array['db44_gender'],
            'date_of_birth'=>$student_array['db53_dob'],
            'scheduled_booking_id'=>$user_data['scheduled_booking_id'],
            'booking_status'=>$user_data['booking_status'],
            'attendance_status' => $attendance_status
        ];

        $data = $db_helper->prepare_data( self::sched_booking_detail, $booking_detail );
        $data["usergroup"] = $student_array["usergroup"];

        return $db_helper->insert( 'sis_sched_booking_detail', $data );

    }

    function create_profile_session_booking($detail_id,$scheduled_booking_id,$course_schedule_id, $rec_id, $usergroup) {
        $dbh = get_dbh();
        list($scheduled_course,$core_student_id) = explode(',',pull_field ("sis_scheduled_booking","CONCAT(db14977,',',rel_id)","WHERE id = $scheduled_booking_id"));
        list($db59902_booking_status,$db59977_scheduled_booking_detail) = explode(',',pull_field ("sis_sched_booking_detail","CONCAT(db59978,',',id)","WHERE id = $detail_id"));

        //check to see if the course has sessions
        $sql = "SELECT id, db59840 as 'course_session'  from sis_scheduled_sessions WHERE rel_id ='$course_schedule_id' AND (rec_archive IS NULL or rec_archive ='') AND db59908 = '3' ORDER by id ASC"; //only book those sessions that are available for booking
        $sth = $dbh->prepare($sql);
        $sth->execute();
        $schedule_sessions = $sth->fetchAll(2);
        foreach ($schedule_sessions as $schedule_session) {
            //create a session_booking
            $db59900_course_session = $schedule_session['course_session'];
            $db59901_scheduled_session = $schedule_session['id'];
//		$db59902_booking_status = $args['db59978'];
            $db59976_schedule_booking = $scheduled_booking_id;
//		$db59977_scheduled_booking_detail = $args['id'];

            $sql = "INSERT INTO sis_session_bookings (username_id, rec_id, usergroup, rel_id, db59900, db59901, db59902,db59976,db59977) VALUES (:username_id,:rec_id,:usergroup,:rel_id,:course_session,:scheduled_session,:booking_status,:schedule_booking,:scheduled_booking_detail)";
            dev_debug($sql);
            $sth = $dbh->prepare($sql);
            $sth->execute(array(
                "username_id" => random(),
                "rec_id" => $rec_id,
                "usergroup" => $usergroup,
                "rel_id" => $core_student_id,
                "course_session" => $db59900_course_session,
                "scheduled_session" => $db59901_scheduled_session,
                "booking_status" => $db59902_booking_status,
                "schedule_booking" => $db59976_schedule_booking,
                "scheduled_booking_detail" => $db59977_scheduled_booking_detail));
        }

    }

    function migrate_user_data($school, InputInterface $input, OutputInterface $output){
        $user_fields = array_filter($this->map, function ($item){
            return $item['v1tablename'] == 'form_users';
        });
        $qn_marks = "?,?,?,?,?,?,?,?,?";
        $user_fields_v1 = $this->base_fields; $user_fields_v2 = $this->base_fields;
        foreach ($user_fields as $map_for_field){
            $user_fields_v1 .= ",".$map_for_field['v1db_field_name'];
            $user_fields_v2 .= ",".$map_for_field['v2db_field_name'];
            $qn_marks .= ",?";
        }
//        $output->writeln("SELECT id, $user_fields_v1 FROM form_users WHERE usergroup = $school[id]");
        $sth = $this->v1_dbh->prepare("SELECT id, $user_fields_v1 FROM form_users WHERE usergroup = $school[id]");
        $sth->execute();
        $imported_records = $sth->fetchAll(2);
        foreach ($imported_records as $user){
            $insert_values = [];
            foreach(explode(",",$user_fields_v1) as $item) {
                if ($item == 'usergroup') {
                    if ($user['usergroup'] == 4 || $user['usergroup'] == 1) {
                        $insert_values['usergroup'] = 75;
                    }else {
                        $insert_values['usergroup'] = $school['new_id'];
                    }
                }else{
                    $insert_values[$item] = $user[$item];
                }
            }
            $qn_marks = implode(",", array_map(function ($item){ return ":".$item;}, explode(",",$user_fields_v1)));
            $insert_user_sql = "INSERT INTO form_users ($user_fields_v2,date)
VALUES ($qn_marks, CURRENT_TIMESTAMP)";
            try{
                $output->writeln("Inserting user $user[db106] into form_users");
                $sth = $this->v2_dbh->prepare($insert_user_sql);
                $sth->execute($insert_values);
                $last_insert_id = $this->v2_dbh->lastInsertId();
                $this->v2_dbh->exec("INSERT INTO form_migration (db67118,db67119,db67120,db67121) VALUES ('form_users', $user[id], 'form_users',$last_insert_id)");
            } catch(PDOException $e) {
                die ($e->getMessage());
            }
        }

    }

    // grab some data
    function describe_table($db_version, $table, InputInterface $input, OutputInterface $output){

        if(!$db_version){
            $output->writeln("db not declared");
        }

        if(!$table){
            $output->writeln("table not declared");
        }
        $databases = ['v1' => 'registration', 'v2' => 'heiapply_dev'];
        $dbh = $this->db_connect($db_version, $input, $output);
        $query1 = "DESCRIBE $databases[$db_version].$table";
        $data="";
        $sth = $dbh->prepare($query1);
        $sth->execute();
        $results = $sth->fetchAll(2);
        foreach($results as $row ) {
            $data.=$row['Field'].',';
        }
        return $data;

    }

    function db_connect($db_version, InputInterface $input, OutputInterface $output){
        if($db_version == 'v1'){
            $host=env('DB_HOST');
            $username=env('DB_USERNAME');
            $password=env('DB_PASSWORD');
            $database='registration';
            //$dbh = new \PDO("mysql:host=**************;dbname=registration", 'regist-admin', '*h893wh298!mhwm89');
            $dbh = new \PDO("mysql:host=$host;dbname=$database", $username, $password);
        }elseif ($db_version == 'v2'){
            $dbh = get_dbh();
        }else{
            $output->writeln("Please specify database version: v1 or v2");
            return null;
        }
        return $dbh;

    }
}