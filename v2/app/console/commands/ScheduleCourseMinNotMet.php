<?php

	namespace App\console\commands;

	use App\core\helpers\Collection;
    use App\models\CommunicationMessages;
    use App\models\Emails;
	use App\models\Enquiries;
	use App\models\Fields;
	use App\models\FormTemplates;
	use App\models\ShortCourses;
	use Doctrine\DBAL\Driver\Exception;
	use Symfony\Component\Console\Command\Command;
	use Symfony\Component\Console\Input\InputArgument;
	use Symfony\Component\Console\Input\InputInterface;
	use Symfony\Component\Console\Output\OutputInterface;
	use App\console\commands\reports\ExportAllStudentsDataJob;
	use Twilio\Rest\Client;

//require_once __DIR__ . '/twilio-php-master/Twilio/autoload.php';

	class ScheduleCourseMinNotMet extends Command
	{
		protected static $defaultName = 'alert:send--schedule-course-minimum-not-met';
		public $processed_jobs = 0;
		public $failed_jobs = 0;
		public $pending_jobs = 0;
		
		protected $debug_mode;
		
		
		protected function configure()
		{
			$this->setDescription('Process alert to tutors and admin if minimum attendees not met for the scheduled course')
				->setHelp('Process alert to tutors and admin if minimum attendees not met for the scheduled course');
			
		}
		
		/**
		 * @throws Exception
		 * @throws \Doctrine\DBAL\Exception
		 */
		protected function execute(InputInterface $input, OutputInterface $output)
        {
            $output->writeln('Starting alter to admin/tutor on min attendees not met');
            $dbh = dbh();
            $_SESSION['uid'] = 1;
            $school_type_filter = " db30='12' AND ";//db=12 if for MRN schools only

            $sql_alert = "SELECT db333589,db333709,db30,db1118,db985, 
                sis_course_schedule.id as 'scheduled_course_id',
                sis_course_schedule.username_id as 'scheduled_course_username_id',
                sis_course_schedule.usergroup as 'scheduled_course_usergroup' ,
                (SELECT db232 from core_courses where id = db14946) as 'scheduled_course_name',
                IFNULL(DATE_FORMAT(db14947,'%d %M %Y'), '') AS 'scheduled_course_start_date',
                IFNULL(CONCAT(SUBSTRING(db14948,1,2),':',SUBSTRING(db14948,3,2)), '') AS 'scheduled_course_start_time'
                
		        FROM  sis_course_schedule 
		        LEFT JOIN form_schools ON form_schools.id = CAST(sis_course_schedule.usergroup AS SIGNED)
                WHERE $school_type_filter
                (sis_course_schedule.rec_archive is null OR sis_course_schedule.rec_archive = '') 
		        AND (
                    db14947 > CURDATE()
                    AND db14947 = DATE_ADD(CURDATE(), INTERVAL 5 DAY)
	            ) 
	            AND (SELECT count(sis_sched_booking_detail.id) 
		    	    FROM sis_sched_booking_detail
    		        LEFT JOIN sis_scheduled_booking ON sis_scheduled_booking.id = db15052
    		        LEFT JOIN core_students on core_students.id = CAST(db16135 as SIGNED)
    	            LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id
    	    
    	        	WHERE 
    		            sis_sched_booking_detail.usergroup = sis_course_schedule.usergroup
                        AND db14977 = CAST(sis_course_schedule.id AS CHAR)
                        AND (sis_scheduled_booking.rec_archive is NULL or sis_scheduled_booking.rec_archive = '')
    		            AND (sis_sched_booking_detail.rec_archive is NULL or sis_sched_booking_detail.rec_archive = '')
    		            AND (core_students.rec_archive is NULL or core_students.rec_archive = '')
    		            AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive = '')
			            AND (db59978 = '2' OR db59978 = '3')
    		    )  < db14953
    		    AND ((db333589 IS NULL OR db333589 !='yes') OR (db333709 IS NULL OR db333709 !='yes')) ";
            $sth = $dbh->prepare("$sql_alert");
            $sth->execute();
            $all_sched_courses_min_attendees_not_met = $sth->fetchAll(\PDO::FETCH_ASSOC);
            $server_domain_name = env('APP_URL');
            $coms_template_subject_line = 'Minimum Number of Attendees NOT Reached';
            $emails = new Emails();
            foreach ($all_sched_courses_min_attendees_not_met as $sched_courses_min_attendees_not_met) {
                if (empty($sched_courses_min_attendees_not_met['db333589']) || $sched_courses_min_attendees_not_met['db333589'] != 'yes' ) {

                    $sub_domain = $sched_courses_min_attendees_not_met['db985'];
                    $course_schedule_id = $sched_courses_min_attendees_not_met['scheduled_course_id'];
                    $course_username_id = $sched_courses_min_attendees_not_met['scheduled_course_username_id'];
                    $scheduled_course_name = $sched_courses_min_attendees_not_met['scheduled_course_name'];
                    $scheduled_course_start_date = $sched_courses_min_attendees_not_met['scheduled_course_start_date'];
                    $scheduled_course_start_time = $sched_courses_min_attendees_not_met['scheduled_course_start_time'];
                    $scheduled_course_usergroup = $sched_courses_min_attendees_not_met['scheduled_course_usergroup'];

                    $website_url = "https://" . $sub_domain . '.' . $server_domain_name;

                    $scheduleCourseLink = $website_url . '/admin/shortcourses/scheduled/' . $course_schedule_id . '?vw=' . $course_username_id . '&ref=' . $course_schedule_id . '&event=1';


                    $disabled_notifications = pull_field("form_notifications_config", "db37245", "WHERE usergroup='" . $scheduled_course_usergroup . "'");
                    if (!in_array('alert_admin_on_minimum_bookings_not_reached', explode(',', $disabled_notifications))) {

                        //message to admin
                        $min_email_message = 'Dear Admin
    
                        The minimum number of attendees specified on the '. $scheduled_course_name.' course, starting on '.$scheduled_course_start_date.' at '.$scheduled_course_start_time.' has not been reached.
                        
                        Link to course: <a href="'.$scheduleCourseLink.'" rel="noopener" target="_blank">'.$scheduled_course_name.'</a>
    
                        Regards
                        
                        Mind Recovery Net Team
    
                        ';

                        //notify the admin
                        $admin_email = $sched_courses_min_attendees_not_met['db1118'];

                        $email_args = [
                            'to' => $admin_email,
                            'subject' => $coms_template_subject_line,
                            'text' => $min_email_message,
                            'html' => nl2br($min_email_message),
                        ];
                       $emails->send($email_args);
                        $update_sql = "UPDATE sis_course_schedule SET db333589 = 'yes' WHERE id = {$course_schedule_id}";
                        $sth_update = $dbh->prepare($update_sql);
                        $sth_update->execute();
                    }
                }

                if (!in_array('alert_tutor_on_minimum_bookings_not_reached', explode(',', $disabled_notifications))) {
                    if (empty($sched_courses_min_attendees_not_met['db333709']) || $sched_courses_min_attendees_not_met['db333709'] != 'yes' ) {
                        $tutorSql = "
                            SELECT
                                CONCAT(db52597,' ',db52598) as 'tutor_name',
                                (select db119 from form_users where id =db52599) as 'tutor_email'
                                
                            FROM sis_scheduled_tutors
                            INNER JOIN sis_course_tutors ON sis_course_tutors.id = db59798
                            WHERE sis_scheduled_tutors.rel_id= {$course_schedule_id}
                            AND (sis_scheduled_tutors.rec_archive IS NULL OR sis_scheduled_tutors.rec_archive = '')
                            AND (sis_course_tutors.rec_archive IS NULL OR sis_course_tutors.rec_archive = '')
                            AND sis_scheduled_tutors.usergroup={$scheduled_course_usergroup}
                        ";
                        dev_debug($tutorSql);
                        $sth_tutors = $dbh->prepare($tutorSql);
                        $sth_tutors->execute();
                        $tutors = $sth_tutors->fetchAll(\PDO::FETCH_ASSOC);
                        foreach ($tutors as $tutor) {
                            $tutor_email_message = "Dear Tutor
    
                            The minimum number of attendees specified on the {$scheduled_course_name} course, starting on {$scheduled_course_start_date} at {$scheduled_course_start_time} has not been reached.
                            
                            Regards
                            
                            Mind Recovery Net Team
                            ";

                            $email_args = [
                                'to' => $tutor['tutor_email'],
                                'subject' => $coms_template_subject_line,
                                'text' => $tutor_email_message,
                                'html' => nl2br($tutor_email_message),
                            ];

                            $emails->send($email_args);


                        }
                        $update_sql = "UPDATE sis_course_schedule SET db333709 = 'yes' WHERE id = {$course_schedule_id}";
                        $sth_update = $dbh->prepare($update_sql);
                        $sth_update->execute();
                    }
                }
            }

        }
		
		

	}
