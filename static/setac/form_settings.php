<?php
$button_name = "Signup";
//get the domain info for the subdomain
$school_logo = "logo.png";
$page_title = "Society of Environmental Toxicology and Chemistry (SETAC)";
$page_description = "A simple convenient way to apply to us direct.";
//$page_key_title="International Admissions Portal"; // also used in main header

//this account comes with multi applications allowed
if (!$_SESSION['multi_application']) {
    $_SESSION['multi_application'] = 'yes';
}
// Get info about student
list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake, $core_students_deleted, $core_student_application_ucas, $core_student_ucas_pass) = get_core_students($_SESSION['student_id']);

//get rules for usergroup
$dbh = get_dbh();
$sql = $dbh->prepare("SELECT db_field_name AS 'name', pr.id AS 'rule_id', db19262 AS 'condition' FROM dir_page_rules pr INNER JOIN system_table ON form_id=db19266
						WHERE (pr.rec_archive IS NULL or pr.rec_archive='')
						AND pr.usergroup = $_SESSION[usergroup] ORDER BY db19264 ASC ");
$sql->execute();
$page_rules = $sql->fetchAll(PDO::FETCH_OBJ);
//loop through rules for usergroup
$my_page_rule = "";

foreach ($page_rules as $page_rule) {
    //check if page has already been found
    //if($_SESSION['student_id'] == 20218){
    // echo " condition = $page_rule->condition";
    //}
    if ($my_page_rule == "") {
        //pull the actual field value
        $page_rule_value = pull_field("core_students", "$page_rule->name", "WHERE id = $core_students_id");


        //test the rule
        $sql_val = "SELECT db19262 FROM dir_page_rules pr INNER JOIN core_students cs ON db19262 = '$page_rule_value'
                                    WHERE pr.usergroup = $_SESSION[usergroup] AND cs.id=$core_students_id AND pr.id = $page_rule->rule_id AND (pr.rec_archive IS NULL or pr.rec_archive='')
                                    ORDER BY db19264 ASC";
        dev_debug($sql_val);
        $sql = $dbh->prepare("$sql_val");
        $sql->execute();
        $result = $sql->fetchColumn();
        if ($result) {
            $my_page_rule = $result;
        }


    }

}

/* //personal link
 if($application_form["db50"] == 61){
    $personal_info = "Award"; //self awards
 }elseif($application_form["db50"] == 63){
    $personal_info = "grant_application_form"; //grant
 }else{
     $personal_info = "personal-information"; //all other
 }*/

//get the correct landing page to send them to
//$page_to_navigate_to = pull_field("core_recruitment_campaign a INNER JOIN form_cms b ON a.db28379=b.id Inner JOIN dir_page_rules c ON a.db28376=c.id","db647","WHERE db28379=b.id AND a.usergroup = $_SESSION[usergroup] AND db19262=$application_form[db50]");
$page_to_navigate_to = pull_field("core_recruitment_campaign a INNER JOIN form_cms b ON a.db28379=b.id Inner JOIN dir_page_rules c ON a.db28376=c.id", "db647", "WHERE db28379=b.id AND a.usergroup = $_SESSION[usergroup] AND db19262=$my_page_rule");

dev_debug("core_recruitment_campaign a INNER JOIN form_cms b ON a.db28379=b.id Inner JOIN dir_page_rules c ON a.db28376=c.id db647 WHERE db28379=b.id AND a.usergroup = $_SESSION[usergroup] AND db19262=$my_page_rule");

//personal link
if ($page_to_navigate_to !== '') {
    $personal_info = $page_to_navigate_to;
} else {
    $personal_info = "personal-information"; //all other
}
$page_link = $personal_info;


/////////////////////////////////////////////////////
// THIS FUNCTION AUTOMATICALLY SHOWS AND HIDES BASED ON THE SESSION
/////////////////////////////////////////////////////
function session_based_show_hide($og_content)
{
    /*
    [IFSESSION]
    [[program_type\345]]
    {{contentadasdsadsa[else]ssdsds}}
    [/IFSESSION]

    if($_SESSION[program_type]==345){

        echo "x";
    }
    */
    ///////////////////////////////////////////////////////////
    // simply replace the get value with the live value
    $content = get_string_between($og_content, "[IFSESSION]", "[/IFSESSION]");
    $if_statement = get_string_between($content, "[[", "]]");
    $core_content = explode("[else]", get_string_between($content, "{{", "}}"));

    $core_content1 = $core_content[0]; //part one
    $core_content2 = $core_content[1]; //part two

    //Purify
    $session_info = explode("|", $if_statement);

    //echo "$session_info[0] - $session_info[1]";
    //echo $_SESSION[$session_info[0]];
    if ($_SESSION["$session_info[0]"] == $session_info[1]) {
        $final_content = str_replace($content, $core_content1, $og_content);
    } else {
        $final_content = str_replace($content, $core_content2, $og_content);
    }

    //remove tages
    $final_content = str_replace('[IFSESSION]', '', $final_content);//".floating_info("ref")."
    $final_content = str_replace('[/IFSESSION]', '', $final_content);

    return $final_content;

}


?>
<style>
    .form_wrapper {
        background: #58b74e;
        margin: 0;
        text_align: left;
        font-family: Arial
    }

    .form_style_bkg {
        color: #666;
        padding: 0;
        margin: 10px auto;
        max-width: 850px;
        background-color: #fff;
        -webkit-box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
        -moz-box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
        box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
    }

    .top_logo {
        padding: 5px 10px 5px 5px;
        max-height: 50px;
        float: left
    }

    .form_detail {
        padding: 10px
    }

    .slogan {
        background: #58b74e;
        color: #fff
    }
</style>