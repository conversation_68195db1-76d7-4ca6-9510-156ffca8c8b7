<?php

/**
 * Created by PhpStorm.
 * User: anita
 * Date: 26/09/2020
 * Time: 19:35
 */

$dbh = get_dbh();
$sql = "SELECT 
(SELECT CONCAT(db52597,' ',db52598) FROM sis_course_tutors WHERE id = db59798) as 'tutor_name',
IF(IFNULL(db63006,'no')='yes','yes', 'no') AS 'confirmed',
IF(IFNULL(db59801,'no')=IFNULL(db59802,'no'),'yes', 'no') as 'accommodation_complete',
IF(IFNULL(db59807,'no')=IFNULL(db59808,'no'),'yes', 'no') as 'travel_complete'
FROM sis_scheduled_tutors WHERE rel_id=:scheduled_course  AND (rec_archive IS NULL OR rec_archive = '') AND usergroup=:usergroup";
dev_debug("$sql**$_GET[ref]**$_SESSION[usergroup]");
$sth = $dbh->prepare($sql);
$sth->execute(array(":scheduled_course" => $_GET['ref'], ":usergroup" => $_SESSION['usergroup']));
$tutors = array();
$no_confirmed_tutors_required = pull_field("sis_course_schedule JOIN core_courses on core_courses.id = db14946", "db67076", "WHERE sis_course_schedule.id = $_GET[ref]");
$confirmed_count = 0;
$confirmed = 'yes';
$accommodation_complete = 'no';
$accommodation_complete_count = 0;
$travel_complete = 'no';
$travel_complete_count = 0;
$tutor_names = '';
while ($row = $sth->fetch(PDO::FETCH_ASSOC)) $tutors[] = $row;
foreach ($tutors as $tutor) {
    $tutor_names = $tutor_names . $tutor['tutor_name'] . ' ';
    if ($tutor['confirmed'] == 'yes') {
        $confirmed_count++;

        if ($tutor['accommodation_complete'] == 'yes') {
            $accommodation_complete_count++;
        }
        if ($tutor['travel_complete'] == 'yes') {
            $travel_complete_count++;
        }
    }
}

$tutor_requirements_complete = "yes";
if ($accommodation_complete_count < $no_confirmed_tutors_required || $travel_complete_count < $no_confirmed_tutors_required) {
    $tutor_requirements_complete = 'no';
}
if ($confirmed_count < $no_confirmed_tutors_required || $confirmed_count == 0) {
    $confirmed = 'no';
    $tutor_requirements_complete = 'no';
}
dev_debug("confirmed_count $confirmed_count no_confirmed_tutors_required $no_confirmed_tutors_required");

?>
<div class="panel panel-default no-padding grey profile_wrap">
    <div class="panel-heading">
        <h2 class="next-heading">Scheduled Course Profile
            <button class="btn btn-default pull-right" ng-click="toggleEditMode()">{{editBtn}}</button>
        </h2>
    </div>
    <div class="panel-body">
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="Object.keys(course.selected_course).length === 0" class="fa fa-circle"></i>
                    Scheduled Course ID:</label></div>
            <div class="col-sm-8">
                <p ng-show="true">{{course.id}}</p>
            </div>
        </div>
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="Object.keys(course.selected_venue).length === 0"
                          class="fa fa-circle"></i>Venue:</label></div>
            <div class="col-sm-8">
                <p ng-show="!editMode">
                    <a title="Click here to view more information about the venue" class="thickbox"
                       href="<?php echo engine_url() ?>view_only.php?pg=250&rec={{course.venue}}&width=850&height=600&jqmRefresh=true<?php echo $hideEdit ?>">
                        {{course.selected_venue.title}}
                    </a>
                </p>
                <ui-select ng-show="editMode" ng-model="course.selected_venue" theme="bootstrap"
                           on-select="updateField('db14954',$item.id);" title="Select a Epic">
                    <ui-select-match placeholder="Select a venue...">{{course.selected_venue.title}}</ui-select-match>
                    <ui-select-choices
                            repeat="venue in course.venue_options | propsFilter: {title: $select.search, id: $select.search}">
                        <div ng-bind-html="venue.title | highlight: $select.search"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
        </div>
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="Object.keys(course.selected_status).length === 0"
                          class="fa fa-circle"></i>Status:</label></div>
            <div class="col-sm-8">
                <p ng-show="!editMode">{{course.selected_status.title}}</p>
                <ui-select ng-show="editMode" ng-model="course.selected_status" theme="bootstrap"
                           on-select="updateField('db14959',$item.id);" title="Select a status">
                    <ui-select-match placeholder="Select a status...">{{course.selected_status.title}}</ui-select-match>
                    <ui-select-choices
                            repeat="status in course.status_options | propsFilter: {title: $select.search, id: $select.search}">
                        <div ng-bind-html="status.title | highlight: $select.search"></div>
                    </ui-select-choices>
                </ui-select>
            </div>
        </div>
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="false" class="fa fa-circle"></i>
                    Start Date:</label></div>
            <div class="col-sm-8">
                <p ng-show="!editMode">{{course.start_date}}</p>
                <div ng-show="editMode" class="input-group date" data-provide="datepicker"
                     data-date-format="{{course.date_picker_date_format}}">
                    <input id="start_date" type="text" class="form-control" ng-model="course.start_date">
                    <div class="input-group-addon">
                        <span class="glyphicon glyphicon-th"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="false" class="fa fa-circle"></i>
                    Start Time:</label></div>
            <div class="col-sm-8">
                <p ng-show="!editMode">{{course.start_time_to_view}}</p>
                <input ng-show="editMode" type="time" ng-model="course.start_time" class="form-control">
            </div>
        </div>
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="false" class="fa fa-circle"></i>
                    End Date:</label></div>
            <div class="col-sm-8">
                <p ng-show="!editMode">{{course.end_date}}</p>
                <div ng-show="editMode" class="input-group date" data-provide="datepicker"
                     data-date-format="{{course.date_picker_date_format}}">
                    <input id="end_date" type="text" class="form-control" ng-model="course.end_date">
                    <div class="input-group-addon">
                        <span class="glyphicon glyphicon-th"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="false" class="fa fa-circle"></i>
                    End Time:</label></div>
            <div class="col-sm-8">
                <p ng-show="!editMode">{{course.end_time_to_view}}</p>
                <input ng-show="editMode" type="time" ng-model="course.end_time" class="form-control">
            </div>
        </div>
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="false" class="fa fa-circle"></i>
                    Last Booking Date:</label></div>
            <div class="col-sm-8">
                <p ng-show="!editMode">{{course.last_booking_date}}</p>
                <div ng-show="editMode" class="input-group date" data-provide="datepicker"
                     data-date-format="{{course.date_picker_date_format}}}">
                    <input id="last_booking_date" type="text" class="form-control" ng-model="course.last_booking_date">
                    <div class="input-group-addon">
                        <span class="glyphicon glyphicon-th"></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="false" class="fa fa-circle"></i>
                    Max Attendees:</label></div>
            <div class="col-sm-8">
                <p ng-show="!editMode">{{course.max_attendees}}</p>
                <input ng-show="editMode" type="number" min="0" ng-model="course.max_attendees" class="form-control"
                       ng-blur="updateField('db14952',course.max_attendees);">
            </div>
        </div>
        <div class="row ip_profile">
            <div class="col-sm-4">
                <label><i ng-if="false" class="fa fa-circle"></i>
                    Min Attendees:</label></div>
            <div class="col-sm-8">
                <p ng-show="!editMode">{{course.min_attendees}}</p>
                <input ng-show="editMode" type="number" min="0" ng-model="course.min_attendees" class="form-control"
                       ng-blur="updateField('db14953',course.min_attendees);">
            </div>
        </div>

        <div class="row ip_profile">
            <div class="col-sm-4"><label>Notes:</label></div>
            <div class="col-sm-8">
                <p style=" overflow-wrap: break-word; word-break: break-word;" ng-show="!editMode">{{course.notes}}</p>
                <textarea ng-show="editMode" ng-model="course.notes" class="form-control" rows="3"
                          ng-blur="updateField('db73811',course.notes);"></textarea>
            </div>
        </div>
    </div>
</div>
