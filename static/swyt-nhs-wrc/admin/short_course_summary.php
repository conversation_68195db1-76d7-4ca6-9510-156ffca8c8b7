<?php

/**
 * Created by PhpStorm.
 * User: anita
 * Date: 26/09/2020
 * Time: 19:35
 */

$dbh = get_dbh();
$sql = "SELECT 
(SELECT CONCAT(db52597,' ',db52598) FROM sis_course_tutors WHERE id = db59798) as 'tutor_name',
IF(IFNULL(db63006,'no')='yes','yes', 'no') AS 'confirmed',
IF(IFNULL(db59801,'no')=IFNULL(db59802,'no'),'yes', 'no') as 'accommodation_complete',
IF(IFNULL(db59807,'no')=IFNULL(db59808,'no'),'yes', 'no') as 'travel_complete'
FROM sis_scheduled_tutors WHERE rel_id=:scheduled_course  AND (rec_archive IS NULL OR rec_archive = '') AND usergroup=:usergroup";
dev_debug("$sql**$_GET[ref]**$_SESSION[usergroup]");
$sth = $dbh->prepare($sql);
$sth->execute(array(":scheduled_course" => $_GET['ref'], ":usergroup" => $_SESSION['usergroup']));
$tutors = array();
$no_confirmed_tutors_required = pull_field("sis_course_schedule JOIN core_courses on core_courses.id = db14946", "db67076", "WHERE sis_course_schedule.id = $_GET[ref]");
$confirmed_count = 0;
$confirmed = 'yes';
$accommodation_complete = 'no';
$accommodation_complete_count = 0;
$travel_complete = 'no';
$travel_complete_count = 0;
$tutor_names = '';
while ($row = $sth->fetch(PDO::FETCH_ASSOC)) $tutors[] = $row;
foreach ($tutors as $tutor) {
    $tutor_names = $tutor_names . $tutor['tutor_name'] . ' ';
    if ($tutor['confirmed'] == 'yes') {
        $confirmed_count++;

        if ($tutor['accommodation_complete'] == 'yes') {
            $accommodation_complete_count++;
        }
        if ($tutor['travel_complete'] == 'yes') {
            $travel_complete_count++;
        }
    }
}

$tutor_requirements_complete = "yes";
if ($accommodation_complete_count < $no_confirmed_tutors_required || $travel_complete_count < $no_confirmed_tutors_required) {
    $tutor_requirements_complete = 'no';
}
if ($confirmed_count < $no_confirmed_tutors_required || $confirmed_count == 0) {
    $confirmed = 'no';
    $tutor_requirements_complete = 'no';
}
dev_debug("confirmed_count $confirmed_count no_confirmed_tutors_required $no_confirmed_tutors_required");

?>
<div class="panel panel-default no-padding ">
    <div class="panel-body">
        <h2 class="next-heading">Summary</h2>
        <div ng-show="book_msg" class="alert alert-info" role="alert"><i class="fa fa-info-circle"
                                                                         aria-hidden="true"></i> {{book_msg}}
        </div>

        <table class="table table-responsive">
            <tr>
                <td>Course:</td>
                <td>{{course.course_name}}</td>
            </tr>
            <tr>
                <td>All tutors associated with this training:</td>
                <td><?php echo $tutor_names; ?></td>
            </tr>
            <tr>
                <td>Status:</td>
                <td>{{course.selected_status.title}}</td>
            </tr>
            <tr>
                <td>COUNT of Bookings for this Scheduled Course:</td>
                <td>{{course.count_bookings}}</td>
            </tr>
            <tr>
                <td>Max Attendees:</td>
                <td>{{course.max_attendees}}</td>
            </tr>
            <tr>
                <td>Min Attendees:</td>
                <td>{{course.min_attendees}}</td>
            </tr>
            <tr>
                <td>Last Booking Date:</td>
                <td>{{course.last_booking_date}}</td>
            </tr>
            <tr>
                <td>Additional Information:</td>
                <td>{{course.additional_information}}</td>
            </tr>

        </table>
    </div>
</div>