<?php
//error_log("student_id: $_SESSION[student_id]");

// Get info about student
list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake) = get_core_students($_SESSION['student_id']);

// what's the curse level?
// this mess is because course is not the id but the name...
/*
$sql = "SELECT core_course_level.db343
    FROM core_students
    INNER JOIN core_courses ON core_courses.id=core_students.db889
    INNER JOIN core_course_level ON core_course_level.id = core_courses.db341
    WHERE core_students.id = '$core_students_id'";
$res = mysqli_query($dbcon,$sql);
list($course_level) = mysqli_fetch_row($res);
*/

$avatar = get_student_avatar($core_students_id);
$course_level = pull_field("core_course_level", "db343", "WHERE id=$core_students_level_of_entry");

// GET PERCENTAGE AND DISPLAY
list($required_fields, $required_filled) = filled_percentage($core_students_id);

$filled_percentage = round(100 * $required_filled / $required_fields, 0, PHP_ROUND_HALF_DOWN);
// error_log("Filled: $filled_percentage%");

// error_log("Total required: $required_fields");
// error_log("Filled in: $required_filled");
$dbh = get_dbh();
// Fetch application stages
$sql = "SELECT db1131 FROM dir_appli_stages WHERE id = '$core_students_application_status'";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$application_status = $stmt->fetchColumn();
$application_status = explode('-', $application_status);
$application_status = trim($application_status[1]);

// Compares stage student is in to argument
function stage_complete($stage_id, $type = 0)
{
    global $core_students_application_status;

    if ($type !== 0) {
        return ($core_students_application_status > $stage_id) ? ' <span class="glyphicon glyphicon-ok"></span>' : '';
    } else {
        return ($core_students_application_status > $stage_id) ? ' completed' : '';
    }
}

// add new comment
if ($_POST["new_message"]) {

    $message = sanitise($_POST['new_message']);

    $random_id = random();
    $sql = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db76, db77, db80, db139) VALUES ('" . $random_id . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . floating_info("ref") . "', '$message', '" . session_info('uid') . "', '" . session_info('uid') . "', 'no')";
    $stmt = $dbh->prepare($sql);
    $stmt->execute();

    //add tracker
    track_use("Sent a private message", "$random_id");
}

// Grab messages
$sql = "SELECT * FROM core_notes WHERE rel_id='$core_students_id' AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') ORDER BY date DESC";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$messages = array();
foreach ($results as $row) $messages[] = $row;

//Grab Blogs
$sql = "SELECT * FROM assist_articles WHERE usergroup='$_SESSION[usergroup]' AND LOCATE('$_SESSION[ulevel]',db1701) ORDER BY date DESC";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$blog_assist = array();
foreach ($results as $row_assist) $blog_assist[] = $row_assist;

// Also grab student user id to know who sent the message
$student_uid = $_SESSION['uid'];

include("form_settings.php");
?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en-US">
<head>
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="robots" content="index,follow"/>
    <script type='text/javascript' src='<?php echo $front_web_url_file_loc; ?>/js/jquery-1.10.2.min.js'></script>
    <link rel="shortcut icon" href="<?php echo $front_web_url_file_loc; ?>/resources/img/favicon.ico"
          type="image/vnd.microsoft.icon"/>
    <link rel="icon" href="<?php echo $front_web_url_file_loc; ?>/resources/img/favicon.ico"
          type="image/vnd.microsoft.icon"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="stylesheet" href="<?php echo $front_web_url_file_loc; ?>/css/fontend.css" type="text/css" media="screen">
    <!-- Bootstrap core CSS -->
    <link href="<?= $front_web_url_file_loc ?>/resources/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,400,700,600,300"
          rel="stylesheet" type="text/css">
    <link href="<?= $front_web_url_file_loc ?>/resources/css/custom.css" rel="stylesheet">

    <!--REQUIRED IN HEADER FOR EACH CUSTOM HEADER START-->
    <script type="text/javascript" src="<?php echo $front_web_url_file_loc; ?>/js/sorttable.js"></script>
    <link rel="stylesheet" href="<?php echo $front_web_url_file_loc; ?>/css/sortable.css" type="text/css"
          media="screen">
    <link rel="stylesheet" href="<?php echo $front_website_url; ?>/css/style.css" type="text/css" media="screen">
    <script src="<?php echo $front_web_url_file_loc; ?>/js/datedropdown.js" type="text/javascript"
            charset="utf-8"></script>
    <script src="<?php echo $front_web_url_file_loc; ?>/js/gotonextpage_check.js" type="text/javascript"
            charset="utf-8"></script>
    <script src="<?php echo $front_web_url_file_loc; ?>/js/submit_appli_check.js" type="text/javascript"
            charset="utf-8"></script>
    <script src="/js/autologout_v1.js" type="text/javascript" charset="utf-8"></script>
    <!--REQUIRED IN HEADER FOR EACH CUSTOM HEADER END-->


</head>

<body>

<div id="everything" class="container">

    <div id="topbar" class="row">
        <div class="col-md-10">
            <a href="<?= website_url_applicant ?>"><img
                        src="<?php echo $front_web_url_file_loc; ?>/images/<?= $school_logo ?>" class="top_logo"></a>
            <h2><?php echo $page_title; ?></h2>
        </div>
        <div class="col-md-2 backbutton"><a href="http://<?= $schools_website_address ?>">Back to main site</a></div>
    </div> <!-- .row -->

    <div class="row">
        <div id="wrapper" class="col-xs-12">

            <!-- for custom nav, portal etc. -->
