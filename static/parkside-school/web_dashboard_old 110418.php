<div class="row">
    <div class="col-xs-12 col-md-12">
        <div class="splash">

            <div class="row">

                <div class="col-md-12" style="border-bottom:1px solid #ccc">
                	<h3>Welcome <?=$core_students_first_name?></h3>
                </div>
                <div class="col-md-3 profile_image text-center">
                    <img src="<?=$avatar?>" class="img-circle" style="width:100%">
                    <button class="label label-default" data-toggle="modal" data-target="#photo_update">Manage Profile</button>
                </div>

                <div class="col-md-4">
                    
                	<h4>Long Course</h4>
                                        <?php
                                        // show a different view for short courses
                                        if ($course_level=="Short Course") {                
                                            ?>
                                            <div class="alert alert-warning">
                                            <h3>Interested in an long course? </h3>
                                            <p>If you wish to apply to a longer course please contact us through the communication inbox and we will be happy to help you. </p><br/><p>Alternatively <NAME_EMAIL></p>
                                            </div>
                                            <?php
                                        }else{
                                            ?>
                                        <table class="table">
                                                <tr>
                                                    <th>Programme </th>
                                                    <td><?=$core_students_course_of_study?></td>
                                                </tr>
                                                <tr>
                                                    <th>Intake </th>
                                                    <td><?php echo $core_students_cohort_intake; ?></td>
                                                </tr>
                                                <tr>
                                                    <th>Status</th>
                                                    <td>
                                                        <?=($appli_submited_check<1?'Application Not Yet Submitted':'Application Submitted'); ?>
                                                    </td>
                                                </tr>
                                                
                                                <tr>
                                                <td colspan="2">
                                                        <div class="alert <?php echo ($filled_percentage=="100"?'alert-success':'alert-info') ?>" align="center">
                                                                <h3>Your Application is now <b><?=$filled_percentage?>%</b> complete.</h3>
                                                                <!--START PROGRESS BAR-->
                                                                <div class="progress">
                                                                    <div class="progress-bar <?php echo ($filled_percentage=="100"?'progress-bar-success':'progress-bar-info') ?>" role="progressbar" aria-valuenow="<?=$filled_percentage?>" aria-valuemin="0" aria-valuemax="100" style="width: <?=$filled_percentage?>%">
                                                                        <span class="sr-only"><?=$filled_percentage?>% Complete</span>
                                                                    </div>
                                                                </div>
                                                                <!--END PROGRESS BAR-->
                                                                <hr/>
                                                                <?php
                                                                if($filled_percentage=="100"){
                                                                    ?>
                                                                    <a href="<?php echo website_url_applicant; ?>/application_form" class="btn btn-success">View Your Application</a>
                                                                    <?php
                                                                    // check if the user has submitted
                                                                    if($appli_submited_check<1){
                                                                        ?>
                                                                        <br/> or <br/>
                                                                        <a href="<?php echo website_url_applicant; ?>/submit_application" class="btn btn-success">Submit Your Application</a>
                                                                    <?php } ?>
                                    
                                                                    <?php
                                                                }else{
                                                                    ?>
                                                                    <a href="<?php echo website_url_applicant; ?>/application_form" class="btn btn-info">Finish your application</a>
                                                                    <?php
                                                                } ?>
                                    
                                    
                                                                <?php
                                    
                                                                if($appli_submited_check==1){
                                                                    //check if Diploma that the college chalenge has been submitted.. if not add the button
                                                                    if ($course_level=="Diploma" || $course_level=="Certificate") {
                                                                        //$college_challenge_done = pull_field("dir_stage_tracker","count(*)","WHERE rel_id = '$_SESSION[student_id]' AND db1142='db14551'");
                                                                        //safer to get the checklist and see if that is updated
                                                                        $checklist_table_name = 'chk_'.pull_field(
                                                                                'system_pages INNER JOIN core_students ON core_students.db2280 = system_pages.page_id',
                                                                                'page_name', "WHERE core_students.id = '$_SESSION[student_id]'");
                                                                        //$college_challenge_done = pull_field($checklist_table_name, "db14553", "WHERE rel_id = $_SESSION[student_id]" );
                                                                        $college_challenge_done = pull_field('form_file', "count(*)", "WHERE rel_id = $_SESSION[student_id] AND db200 = 36" );
                                    
                                                                        if ($college_challenge_done == 0) {?>
                                                                            <br/><br/><a href="<?php echo website_url_applicant; ?>/college_challenge" class="btn btn-info">Upload your College Challenge</a>
                                                                        <?php }
                                    
                                    
                                                                    }
                                                                    if ($course_level=="Degree" ) {
                                                                        //$college_challenge_done = pull_field("dir_stage_tracker","count(*)","WHERE rel_id = '$_SESSION[student_id]' AND db1142='db14551'");
                                                                        //safer to get the checklist and see if that is updated
                                                                        $checklist_table_name = 'chk_'.pull_field(
                                                                                'system_pages INNER JOIN core_students ON core_students.db2280 = system_pages.page_id',
                                                                                'page_name', "WHERE core_students.id = '$_SESSION[student_id]'");
                                                                        $portfolio_upload_done = pull_field('form_file', "count(*)", "WHERE rel_id = $_SESSION[student_id] AND db200 = 27" );
                                                                        $college_challenge_done_2 = pull_field('form_file', "count(*)", "WHERE rel_id = $_SESSION[student_id] AND db200 = 36" );
                                    
                                    
                                    
                                                                        if ($portfolio_upload_done == 0 || $college_challenge_done_2 == 0) {?>
                                                                            <br/><br/><a href="<?php echo website_url_applicant; ?>/upload_portfolio" class="btn btn-info">Upload Portfolio/College Challenge</a>
                                                                        <?php }
                                                                    }
                                    
                                                                }
                                                                $check_offer_status = pull_field("dir_offers","count(*)","WHERE rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]' AND db1827='active'");
                                                                // check if the user has submitted
                                                                $show_payment_button = 0;
                                                                if($check_offer_status>0){
                                                                    //check payments
                                                                    global $show_features;
                                                                    $payments_option = 0;
                                    // check if they have this feature
                                                                    if (preg_match_search($show_features, 'payments') == true) {
                                                                        $payments_option = 1;
                                                                    }
                                    
                                                                    if ($payments_option == 1) {
                                                                        //get the latest accepted offer
                                                                        $latest_accepted_offer_invoice = pull_field("dir_offers","db16369","WHERE rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]' AND (db16369 IS NOT NULL AND db16369 !='') ORDER BY id DESC LIMIT 1");
                                                                        if ($latest_accepted_offer_invoice && $latest_accepted_offer_invoice !='') {
                                                                            $payoptions =pull_field("dir_offers","db15880","WHERE db16369='$latest_accepted_offer_invoice'");
                                                                            $lead_invoice_username_id = pull_field("lead_invoice_settings", "username_id", "WHERE id =" . $latest_accepted_offer_invoice);
                                    
                                                                            $currency = pull_field("system_currency", "abv", "WHERE id =" . pull_field("lead_invoice_settings", "db14987", "WHERE id='$latest_accepted_offer_invoice'"));
                                    
                                                                            //take info from the invoice in case invoice has changed the form_payment_options may no longer be valid
                                                                            $overall_price = pull_field("lead_invoice_settings", "db15006", "WHERE id='$latest_accepted_offer_invoice'");
                                                                            $overall_deposit = pull_field("lead_invoice_settings", "db15003", "WHERE id='$latest_accepted_offer_invoice'");
                                                                            $number_of_installments_allowed = pull_field("lead_invoice_settings", "db14996", "WHERE id='$latest_accepted_offer_invoice'");
                                                                            $include_vat = pull_field("lead_invoice_settings", "db14998", "WHERE id='$latest_accepted_offer_invoice'");
                                    
                                                                            if ($include_vat == 'yes') {
                                                                                //get the preferences
                                                                                $sql_preferences = "SELECT * FROM lead_preferences WHERE usergroup = '$_SESSION[usergroup]'";
                                                                                $sql = $dbh->prepare($sql_preferences);
                                                                                $sql->execute();
                                                                                $pdo_preferences_rows = $sql->fetchAll(PDO::FETCH_OBJ);
                                                                                $pdo_preferences = $pdo_preferences_rows[0];
                                                                                $overall_price += ($overall_price * pull_field("lead_preferences","db15030","WHERE usergroup = '$_SESSION[usergroup]'" )) / 100;
                                                                                $overall_deposit += ($overall_deposit * pull_field("lead_preferences","db15030","WHERE usergroup = '$_SESSION[usergroup]'" )) / 100;
                                    
                                                                            }
                                    
                                                                            if (!$number_of_installments_allowed || $number_of_installments_allowed == '' || $number_of_installments_allowed == "not specified") {
                                                                                $number_of_installments_allowed = 0;
                                                                            }
                                    
                                    
                                                                            if (!$payoptions || $payoptions == '') {
                                                                                $payoptions = 'Pay_online_-_full_amount';
                                                                            }
                                    
                                                                            $number_of_payments_made = pull_field("sis_student_fees", "count(*)","WHERE rel_id=$_SESSION[student_id] and db1494='$lead_invoice_username_id'");
                                                                            $total_amount_paid = pull_field("sis_student_fees", "SUM(db1495)","WHERE rel_id=$_SESSION[student_id] and db1494='$lead_invoice_username_id'");
                                    
                                    //echo 'Number_of_payments_made'.'**'.$number_of_payments_made.'**'."WHERE rel_id=$_SESSION[student_id] and db1494='$lead_invoice_username_id'";
                                                                            //pay all
                                                                            ?>
                                    
                                                                            <!--View Invoice Button
                                                                        <button type="button" class="btn btn-info btn-sm"><a
                                                                                href="<?= website_url_applicant . '/application/invoice/' . $lead_invoice_username_id ?>"
                                                                                id="button" target="_blank"
                                                                                title="View Invoice">View Invoice </a></button>-->
                                                                            <!-- The first line of code specifies the URL for our test environment.-->
                                    
                                    
                                                                            <?php if ($payoptions == 'Pay_online_-_full_amount') {
                                                                                if ($number_of_payments_made < 1) {
                                                                                    $button_title = ' Pay total £' . number_format($overall_price, 2) . ' now';
                                                                                    $amount = $overall_price;
                                                                                    $my_payment = "All";
                                                                                    $show_payment_button = 1;
                                                                                } else {
                                                                                    //echo 'Fully Paid';
                                                                                    //check to see if total amount paid is equal tp the overall price
                                    
                                                                                    if ($overall_price > $total_amount_paid) {
                                                                                        $to_pay = $overall_price - $total_amount_paid;
                                                                                        $button_title = ' Pay balance £' . number_format($to_pay, 2) . ' now';
                                                                                        $amount = $to_pay;
                                                                                        $my_payment = "Balance";
                                                                                        $show_payment_button = 1;
                                                                                    }
                                                                                }
                                    
                                    
                                                                            } ?>
                                    
                                                                            <?php if ($payoptions == 'Pay_online_-_deposit_then_all') {
                                                                                $deposit_and_installments = 2; //deposit then all
                                    
                                                                                if ($number_of_payments_made < $deposit_and_installments) {
                                                                                    //is there a deposit
                                                                                    $deposit = false;
                                                                                    if ($overall_deposit && $overall_deposit > 0) {
                                                                                        $deposit = true;
                                                                                    }
                                                                                    //echo 'Payments made ' . $number_of_payments_made;
                                                                                    //echo 'Deposit ' . $overall_deposit;
                                                                                    if ($deposit == true) {
                                    
                                                                                        if ($number_of_payments_made == 0) {
                                                                                            $amount = $overall_deposit;
                                                                                            $button_title = ' Pay deposit £' . number_format($overall_deposit, 2) . ' now';
                                                                                            $my_payment = "Deposit";
                                                                                        } else {
                                                                                            $installment_to_pay = $number_of_payments_made;
                                                                                            $amount = ($overall_price - $overall_deposit);
                                                                                            $button_title = ' Pay remaining ' . ' £' . number_format($amount, 2) . ' now';
                                                                                            $my_payment = "Instalment";
                                                                                        }
                                    
                                    
                                                                                    } else {
                                    
                                                                                        $installment_to_pay = $number_of_payments_made + 1;
                                                                                        $amount = $overall_price;
                                                                                        $button_title = ' Pay total ' . ' £' . number_format($amount, 2) . ' now';
                                                                                        $my_payment = "All";
                                    
                                                                                    }
                                                                                    $show_payment_button = 1;
                                    
                                                                                } else {
                                                                                    //echo 'Fully Paid';
                                                                                    //check to see if the full payment has been made.. if not add the rest here
                                                                                    if ($overall_price > $total_amount_paid) {
                                                                                        $to_pay = $overall_price - $total_amount_paid;
                                                                                        $button_title = ' Pay balance £' . number_format($to_pay, 2) . ' now';
                                                                                        $amount = $to_pay;
                                                                                        $my_payment = "Balance";
                                                                                        $show_payment_button = 1;
                                                                                    }
                                    
                                                                                }
                                    
                                                                                ?>
                                    
                                                                            <?php } ?>
                                                                            <?php if ($payoptions == 'Pay_online_-_deposit_and_instalments' || $payoptions == 'Pay_online_-_deposit_and_installments') {
                                    
                                                                                $deposit_and_installments = 0;
                                                                                //is there a deposit
                                                                                $deposit_paid = false;
                                                                                $deposit = false;
                                                                                if ($overall_deposit && $overall_deposit > 0) {
                                                                                    $deposit_and_installments++;
                                                                                    $deposit = true;
                                                                                }
                                    
                                                                                $db20215_intake = pull_field("lead_invoice_items", "db20215", "WHERE rel_id='" . $latest_accepted_offer_invoice . "' AND (rec_archive IS NULL OR rec_archive = '') LIMIT 1");
                                                                                //show the manual instalments
                                                                                if ($db20215_intake && $db20215_intake != '') {
                                                                                    $number_of_installments_allowed = pull_field("dir_cohort_instalments", "COUNT(*)", "WHERE rel_id = '".$db20215_intake."' AND (rec_archive IS NULL or rec_archive ='')");
                                                                                    $deposit_and_installments += $number_of_installments_allowed;
                                                                                    if ($number_of_payments_made < $deposit_and_installments) {
                                    
                                                                                        if ($deposit == true) {
                                                                                            if ($number_of_payments_made == 0) {
                                                                                                $amount = $overall_deposit;
                                                                                                $button_title = ' Pay deposit £' . number_format($overall_deposit, 2) . ' now';
                                                                                                $my_payment = "Deposit";
                                    
                                                                                            } else {
                                    
                                                                                                $installment_to_pay = $number_of_payments_made;
                                                                                                //go through the manual instalments to find the instalment to pay
                                                                                                //read the cohort_instalments and put them on the screen
                                                                                                $sql = "SELECT db20171 as 'due_date', db20172 as 'name', db20214 AS 'amount' FROM dir_cohort_instalments WHERE rel_id = ? AND (rec_archive IS NULL or rec_archive ='')";
                                                                                                $sql = $dbh->prepare($sql);
                                                                                                $sql->execute(array($db20215_intake));
                                                                                                $instalments = $sql->fetchAll(PDO::FETCH_ASSOC);
                                    
                                                                                                if ($instalments) {
                                                                                                    $count = 1;
                                                                                                    foreach ($instalments as $instalment) {
                                                                                                        if ($count == $installment_to_pay) {
                                    
                                                                                                            $cohort_installment_amount = $instalment['amount'];
                                                                                                            if ($include_vat == 'yes') {
                                                                                                                $installment_vat_amount = $cohort_installment_amount - ($cohort_installment_amount / (1 + ($pdo_preferences->db15030 / 100)));
                                                                                                                $amount = $cohort_installment_amount + $installment_vat_amount;
                                                                                                            } else {
                                                                                                                $installment_vat_amount = '';
                                                                                                                $amount = $cohort_installment_amount;
                                                                                                            }
                                                                                                            $button_title = ' Pay '. $instalment['name']. ' £' . number_format($amount, 2) . ' now';
                                                                                                            $my_payment = $instalment['name'];
                                    
                                                                                                            break;
                                                                                                        }
                                    
                                                                                                    }
                                                                                                }
                                                                                            }
                                    
                                    
                                                                                        }
                                                                                        else {
                                                                                            $installment_to_pay = $number_of_payments_made + 1;
                                                                                            //go through the manual instalments to find the instalment to pay
                                                                                            //read the cohort_instalments and put them on the screen
                                                                                            $sql = "SELECT db20171 as 'due_date', db20172 as 'name', db20214 AS 'amount' FROM dir_cohort_instalments WHERE rel_id = ? AND (rec_archive IS NULL or rec_archive ='')";
                                                                                            $sql = $dbh->prepare($sql);
                                                                                            $sql->execute(array($db20215_intake));
                                                                                            $instalments = $sql->fetchAll(PDO::FETCH_ASSOC);
                                                                                            if ($instalments) {
                                                                                                $count = 1;
                                                                                                foreach ($instalments as $instalment) {
                                                                                                    if ($count == $installment_to_pay) {
                                                                                                        $cohort_installment_amount = $instalment['amount'];
                                                                                                        if ($include_vat == 'yes') {
                                                                                                            $installment_vat_amount = $cohort_installment_amount - ($cohort_installment_amount / (1 + ($pdo_preferences->db15030 / 100)));
                                                                                                            $amount = $cohort_installment_amount + $installment_vat_amount;
                                                                                                        } else {
                                                                                                            $installment_vat_amount = '';
                                                                                                            $amount = $cohort_installment_amount;
                                                                                                        }
                                                                                                        $button_title = ' Pay '. $instalment['name']. ' £' . number_format($amount, 2) . ' now';
                                                                                                        $my_payment = $instalment['name'];
                                    
                                                                                                        break;
                                                                                                    }
                                    
                                                                                                }
                                                                                            }
                                    
                                                                                        }
                                                                                        $show_payment_button = 1;
                                                                                    } else {
                                                                                        if ($overall_price > $total_amount_paid) {
                                                                                            $to_pay = $overall_price - $total_amount_paid;
                                                                                            $button_title = ' Pay balance £' . number_format($to_pay, 2) . ' now';
                                                                                            $amount = $to_pay;
                                                                                            $my_payment = "Balance";
                                                                                            $show_payment_button = 1;
                                                                                        }
                                                                                        //echo 'Fully Paid';
                                                                                    }
                                    
                                    
                                    
                                                                                } else {
                                    
                                                                                    $deposit_and_installments += $number_of_installments_allowed;
                                    
                                    
                                                                                    if ($number_of_payments_made < $deposit_and_installments) {
                                    
                                                                                        if ($deposit == true) {
                                                                                            if ($number_of_payments_made == 0) {
                                                                                                $amount = $overall_deposit;
                                                                                                $button_title = ' Pay deposit £' . number_format($overall_deposit, 2) . ' now';
                                                                                                $my_payment = "Deposit";
                                                                                            } else {
                                                                                                $installment_to_pay = $number_of_payments_made;
                                                                                                $amount = ($overall_price - $overall_deposit) / $number_of_installments_allowed;
                                                                                                $button_title = ' Pay instalment ' . $installment_to_pay . ' £' . number_format($amount, 2) . ' now';
                                                                                                $my_payment = "Instalment $installment_to_pay";
                                    
                                                                                            }
                                                                                        } else {
                                                                                            $installment_to_pay = $number_of_payments_made + 1;
                                                                                            $amount = ($overall_price - $overall_deposit) / $number_of_installments_allowed;
                                                                                            $button_title = ' Pay instalment ' . $installment_to_pay . ' £' . number_format($amount, 2) . ' now';
                                                                                            $my_payment = "Instalment $installment_to_pay";
                                    
                                                                                        }
                                                                                        $show_payment_button = 1;
                                                                                    } else {
                                                                                        if ($overall_price > $total_amount_paid) {
                                                                                            $to_pay = $overall_price - $total_amount_paid;
                                                                                            $button_title = ' Pay balance £' . number_format($to_pay, 2) . ' now';
                                                                                            $amount = $to_pay;
                                                                                            $my_payment = "Balance";
                                                                                            $show_payment_button = 1;
                                                                                        }
                                                                                        //echo 'Fully Paid';
                                                                                    }
                                                                                }
                                                                                ?>
                                    
                                    
                                                                            <?php } ?>
                                    
                                                                            <?php if ($show_payment_button == 1) {
                                                                                $student_username_id = pull_field("core_students", "username_id", "WHERE id='$_SESSION[student_id]'");
                                                                                $user_username_id = pull_field("form_users", "username_id", "WHERE id='$_SESSION[uid]'");
                                                                                ?>
                                                                                <br/> or <br/>
                                                                                <form style="margin-bottom:0px;" action="https://secure.worldpay.com/wcc/purchase" method=POST>
                                    
                                                                                    <!-- This next line contains the testMode parameter - it specifies that the submission is a test submission -->
                                                                                     <!-- <input type="hidden" name="testMode" value="100">-->
                                    
                                    
                                                                                    <!-- This next line contains a mandatory parameter. Put your Installation ID inside the quotes after value= -->
                                                                                    <input type="hidden" name="instId" value="276728">
                                    												<input type="hidden" name = "accId1" value="CONDENASTPUBM1">
                                                                                    <input type="hidden" name="MC_callback"
                                                                                           value="<?= website_url ?>/worldpay_callback.php">
                                    
                                                                                    <!-- Another mandatory parameter. Put your own reference identifier for the item purchased inside the quotes after value= -->
                                                                                    <!--<input type="hidden" name="cartId" value="<?= pull_field("dir_offers","username_id","WHERE db16369='$latest_accepted_offer_invoice'"); ?>">-->
                                                                                    <input type="hidden" name="cartId" value="<?= pull_field("lead_invoice_settings","db25617","WHERE id='$latest_accepted_offer_invoice'"); ?>">
                                                                                    <input type="hidden" name="MC_student_id" value="<?= $student_username_id ?>">
                                                                                    <input type="hidden" name="MC_payment_type" value="<?= $payoptions ?>">
                                                                                    <input type="hidden" name="MC_website" value="<?=$_SERVER['HTTP_HOST']?>">
                                                                                    <input type="hidden" name="MC_whoami" value="<?= session_info('subdomain') ?>">
                                                                                    <input type="hidden" name="MC_slogan" value='<?= pull_field("form_schools","db1093","WHERE id = $_SESSION[access]") ?>'>
                                                                                    <input type="hidden" name="MC_course_type" value="long">
                                                                                    <!-- Another mandatory parameter. Put the code for the purchase currency inside the quotes after value= -->
                                                                                    <input type="hidden" name="currency" value="<?= $currency ?>">
                                    
                                                                                    <!-- Another mandatory parameter. Put the total cost of the item inside the quotes after value= -->
                                                                                    <input type="hidden" name="amount" value="<?= $amount ?>">
                                                                                    <input type="hidden" name="MC_my_payment_type" value="<?= $my_payment ?>">
                                                                                    <input type="hidden" name="MC_password" value="<?= md5('actionstarterpassword') ?>">
                                                                                    <input type="hidden" name="MC_uid" value="<?= $user_username_id ?>">
                                                                                    <input type="hidden" name="MC_access" value="<?= session_info('access') ?>">
                                    
                                    
                                                                                    <!-- This creates the button. When it is selected in the browser, the form submits the purchase details to us. -->
                                                                                    <!-- <input type="submit" class="btn btn-info btn-sm" value=" Make Payment">-->
                                                                                    <button type="submit" class="btn btn-info btn-sm" title="<?= $button_title ?>"><i
                                                                                            class="fa fa-credit-card"></i><?= $button_title ?></button>
                                    
                                                                                </form>
                                    
                                                                            <?php } ?>
                                                                            <?php
                                                                        }
                                    
                                                                    }
                                    
                                                                    if ($show_payment_button == 0) {
                                                                        echo '<br/> or <br/>';
                                                                    }
                                                                    else {
                                                                        echo 'or <br/>';
                                                                    }
                                                                    ?>
                                    
                                                                    <a href="<?php echo website_url_applicant; ?>/offer" class="btn btn-danger">View Your Offer Letter And Invoice</a>
                                                                <?php } ?>
                                   
                                                       
                                                    </div><!-- end of alert bkg-->
                                                </td>
                                                </tr>
                                        </table>
                                                           <?php
                                                            }//end
                                                            ?>
                </div><!--end of col-4 -->
                
                <div class="col-md-5">
                	<h4>Short Course</h4>


                            <?php

                            //show amount outstanding in short courses
                            //Get the sum of all fees if booking status is 'Awaiting payment"
                            $outstanding_amount = pull_field('sis_scheduled_booking','ifnull(Concat("£", FORMAT(SUM(db15104),2)),"£0.00")',"WHERE db14979 ='2' AND db16135 = '$_SESSION[student_id]' AND (rec_archive IS NULL or rec_archive='')" );

                            //calculate the total number of courses booked
                            $short_courses_applied = pull_field("sis_scheduled_booking","count(*)","WHERE db16135 = '$_SESSION[student_id]' AND (rec_archive IS NULL or rec_archive='')");
                            ?>
                            
                    <?PHP if($short_courses_applied < 1){ ?>
                    <p>
                    This booking form will allow you to reserve a place on an upcoming short course at the Condé Nast College by submitting your details and paying the tuition fee.</p>
                    <ul>
                    <li>One Week Fashion Styling Course</li>
                    <li>One Week Fashion Journalism Course</li>
                    <li>One Week Fashion Business Course</li>
                    <li>Vogue Teen Weekend (16 and 17 year olds only)</li>
                    </ul>
                    <p>Please note that your place is only reserved once the tuition fee has been paid, and make sure you read through our refund policy prior to booking: <a href="https://www.condenastcollege.ac.uk/how-to-apply/terms-conditions-of-business/" target="_blank">https://www.condenastcollege.ac.uk/how-to-apply/terms-conditions-of-business/</a></p>
                    <?php } ?>
                    
                    <table class="table">
                            <tr>
                                <td colspan="2"> <a href="<?php echo website_url_applicant; ?>/shortcoursebooking?ref1=&ref=" class="btn btn-info" style="width: 100%; margin-bottom: 15px;">Make a booking</a>
                                </td>
                            </tr>
                            
                            <?php 
							// short courses check
							// only show this section if they ave applied for at least one course
							if($short_courses_applied > 0 ){ 
							?>
                            <tr>
                                <th>Number of Short Course Bookings: </th>
                                <td><a href="<?php echo website_url_applicant; ?>/shortcoursebookings" class="btn btn-info" style="width: 100%;">View Your <?=$short_courses_applied?> Booking(s)</a></td>
                            </tr>
                            <tr>
                                <th>Payments Outstanding For Short Courses</th>
                                <td><?=$outstanding_amount?></td>
                            </tr>
                            <?php
                            // show a different view for short courses

                            //show amount outstanding in short courses
                            //Get the sum of all fees if booking status is 'Awaiting payment"
                            $outstanding_amount = pull_field('sis_scheduled_booking','ifnull(Concat("£", FORMAT(SUM(db15104),2)),"£0.00")',"WHERE db14979 ='2' AND db16135 = '$_SESSION[student_id]' AND (rec_archive IS NULL or rec_archive='')" );

                            //calculate the total number of courses booked
                            $short_courses_applied = pull_field("sis_scheduled_booking","count(*)","WHERE db16135 = '$_SESSION[student_id]' AND (rec_archive IS NULL or rec_archive='')");
                            ?>
                            <tr>
                                <th>Number of Short Course Bookings: </th>
                                <td><a href="<?php echo website_url_applicant; ?>/shortcoursebookings"><?=$short_courses_applied?></a></td>
                            </tr>
                            <tr>
                                <th>Payments Outstanding </th>
                                <td><?=$outstanding_amount?></td>
                            </tr>


                        <tr>
                            <th>&nbsp;</th>
                            <td>&nbsp;</td>
                        </tr>
                            <?php 
							// END only show this section if they ave applied for at least one course
							}
							?>
                        
                    </table>

                </div>
            </div>

        </div>
    </div>
</div>


<!--CHAT RELATED-->
<script src="<?php echo $front_web_url_file_loc; ?>/js/dashboard.js" type="text/javascript" charset="utf-8"></script>

<script type="text/javascript">
    $(document).ready(function () {

        $('.star').on('click', function () {
            $(this).toggleClass('star-checked');
        });

        $('.ckbox label').on('click', function () {
            $(this).parents('tr').toggleClass('selected');
        });

        $('.btn-filter').on('click', function () {
            var $target = $(this).data('target');
            if ($target != 'all') {
                $('.table_comms tbody tr').css('display', 'none');
                $('.table_comms tbody tr[data-status="' + $target + '"]').fadeIn('slow');
            } else {
                $('.table_comms tbody tr').css('display', 'none').fadeIn('slow');
            }
        });

    });
</script>



<div class="row">
    <div class="col-xs-12 col-lg-6">
        <h2>Communication Inbox</h2>
        <p>Send and receive communication from our admissions team here</p>

        <!--CHAT WIZARD-->
<!--        <div class="box widget-chat">-->

<!--            <div class="widget-actions">-->
<!--                <form class="form-inline" action="--><?//=website_url?><!--/static/inc/inc_dir_messages_process.php" method="POST">-->
<!--                    <button class="btn btn-custom">-->
<!--                        Send-->
<!--                    </button>-->
<!--                    <div>-->
<!--                        <textarea name="new_message" rows="1" id="textarea-chat-example" style="overflow: hidden; word-wrap: break-word; resize: vertical; height: 100px;"></textarea>-->
<!--                    </div>-->
<!--                </form>-->
<!--            </div>-->
<!--            <div class="clearfix"></div>-->







            <section class="content">
                <div class="col-md-12">
                    <div class="panel panel-default">
                        <div class="panel-body">




                            <div id="coms" class="box widget-chat">
                                <div class="widget-actions">
                                    <form class="form-inline" action="<?= website_url ?>/static/inc/inc_dir_messages_process.php" method="POST">
                                        <button class="btn btn-primary">
                                            Send
                                        </button>
                                        <div>
                                                    <textarea name="new_message" rows="1" id="textarea-chat-example"
                                                              style="overflow: hidden; word-wrap: break-word; resize: vertical; height: 100px;"></textarea>
                                        </div>
                                        <input type="hidden" name="child_id" id="hdn_child_id" value="">
                                    </form>
                                </div>
                            </div>

                            View messages relating to<br/>
                            <div class="btn-group">
                                <button type="button" class="btn btn-default btn-filter" id="all" data-target="all">All
                                </button>
                                <?php
                                // loop through kids
                                ?>
                                <button type="button" class="btn btn-default btn-filter"
                                        id="emails"
                                        data-target="emails">Emails
                                    <?php if ($new_msgs > 0) { ?>
                                        <span class="label label-danger"><?= $new_msgs ?></span>
                                    <?php } ?>
                                </button>

                                <button type="button" class="btn btn-default btn-filter"
                                        id="messages"
                                        data-target="messages">Messages
                                    <?php if ($new_msgs > 0) { ?>
                                        <span class="label label-danger"><?= $new_msgs ?></span>
                                    <?php } ?>
                                </button>


                                <?php
                                // loop through kids end

                                ?>

                            </div>

                            <div class="clearfix"></div>

                            <div class="table-container">
                                <table class="table table-filter table_comms" style="overflow:scroll; height:500px">
                                    <thead class="hidden">
                                    <tr>
                                        <th style="width:10%">Marked as read</th>
                                        <th>Message</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php
                                    $count_messages = 0;
                                    foreach ($messages as $msg) {
                                        $count_messages++;
                                        $content = text_to_html($msg["msginfo"]);
                                        $date = format_date("d M y",$msg["date"]);
                                        $child_name = pull_field("core_students","db39","WHERE id='$msg[rel_id]'");


                                        //logic to check between emails and messages
                                        //filter using data target
//                                        echo"<tr data-status=\"".$msg['rel_id']."\">

//                                        if email
//                                            $data_status = 'email'
//                                                else
//                                                    $data_status = 'messages'
////                                        "<tr data-status=\"".$data_status."\">

                                        if ($msg['type'] == "form_email_log") {
                                            $data_status = "emails";
//                                            echo 'EMAILS';
                                        }
                                        else if ($msg['type'] == "core_notes") {
                                            $data_status = "messages";
//                                            echo 'MESSAGES';
                                        }




                                        if($msg['rec_id'] === $student_uid){
                                            $sender = 'You';//
                                            $checked = 'checked="checked"';
                                            //echo "<br> marked as read: ".$msg['db139']."<br>";
                                            if($msg['db139'] == 'no'){
                                                $checked = '';
                                            }
//                                            echo 'TEST!!!!';
                                            echo"<tr data-status=\"".$data_status."\">
                                                    <td>
                                                        <div class=\"\">
                                                            <input type=\"checkbox\" id=\"$msg[id]\" $checked disabled=\"disabled\" title=\"mark as read\">
                                                            <label for=\"checkbox1\"></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class=\"media\">
                                                            <a href=\"#\" class=\"pull-left\">
                                                                <img src=\"$_SESSION[domain]/engine/images/blank_user_icon.png\" width=\"40px\" height=\"40px\" class=\"media-photo\">
                                                            </a>
                                                            <div class=\"media-body\">
                                                                <span class=\"media-meta pull-right\">$date</span>
                                                                <h4 class=\"title\">
                                                                    $sender 
                                                                    
                                                                </h4>
                                                                <p class=\"summary\">$content</p>
                                                                
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>";

                                    echo'<div style="display:none">';
                                            print_r($content);
                                            echo'<pre></pre></div>';
                                        }
                                        else{
//                                            $sender = pull_field("form_users","db106","WHERE id='$msg[rec_id]'");
                                            $sender = $_SESSION['school_name'];
                                            $checked = 'checked="checked"';
                                            if($msg['db139'] == 'no'){
                                                $checked = '';
                                            }
                                            echo"<tr data-status=\"".$data_status."\">
                                                    <td>
                                                        <div class=\"\">
														
                                                            <input type=\"checkbox\" id=\"$msg[id]\" $checked title=\"mark as read\">
                                                            <label for=\"checkbox1\"></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class=\"media\">
                                                            <a href=\"#\" class=\"pull-right\">
                                                                <img src=\"$_SESSION[domain]/engine/images/blank_user_icon.png\" width=\"40px\" height=\"40px\" class=\"media-photo\">
                                                            </a>
                                                            <div class=\"media-body\">
                                                                <span class=\"media-meta pull-right\">$date</span>
                                                                <h4 class=\"title\">
                                                                    $sender 
                                                                    
                                                                </h4>
                                                                <p class=\"summary\">$content</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>";
                                            echo'<div style="display:none">';
                                            print_r($content);
                                            echo'<pre></pre></div>';


                                        }
                                    }
                                    if($count_messages == 0){
                                        echo "<tr><td></td><td>Sorry, no messages found</td></tr>";
                                    }
                                    ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </section>




    </div><!--/span-->


                        <?php

                        // show a different view for short courses
                        if($course_level!=="Short Course"){
                            $letters_sql = "SELECT * from dir_letters_sent WHERE (rec_archive IS NULL or rec_archive ='') AND rel_id = ?";
                            $dbh = get_dbh();
                            $sth = $dbh->prepare($letters_sql);
                            $sth->execute(array($_SESSION['student_id']));
                            $letters = $sth->fetchAll(PDO::FETCH_ASSOC);
							
							//end
							if(count($letters)>0){
                            ?>
                            <div class="col-xs-12 col-lg-6">
                                <h2>Letters</h2>
                                <p>View your letters here.</p>
                                
                                <div class="table-responsive">
                                    <table class="table">

                                        <thead class="hidden">
                                        <tr>
                                            <th>Subject</th>
                                            <th>Letter</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <?php
                                            foreach ($letters as $letter) { ?>
                                                <tr>
                                                    <td><?php echo $letter['db20301']?></td>
                                                    <td><a href="<?php echo $letter['db20300']?>" title='View Letter'>View Letter</a></td>
                                                </tr>

                                            <?php }
                                        ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <?php
								}// letters check end
								
							}//
                         ?>
                         
    <div class="col-xs-12 col-lg-6">
        <h2>News and Updates</h2>
        <p>Various information to help you through your application</p>

        <ul class="list-inline">
            <?php
            $i=0;
            foreach ($blog_assist as $msg) {
                ?>
                <li class="col-lg-12">
                    <h4><?php echo $msg["db1695"]; ?></h4>
                    <p><?php echo $msg["db1698"]; ?></p>
                    <a href="<?php echo website_url_applicant.'/blog/'.$msg["id"]; ?>" class="label label-info">Read More</a>
                    <hr/>
                </li>
            <?php } // end of loop ?>
        </ul>
    </div>
</div> <!-- .row -->

<!--
<div class="row">
    <div class="col-xs-12">
    <h3>Please read this important information before you begin your application</h3>
        <div style="width:650px; height:400px; overflow:auto; border:4px solid #f0f0f0; padding:5px; background-color:#f4f4f4; margin-bottom:10px">
          <?php
// FUNCTION TO GET_CMS
//list($page_id,$cms_category,$cms_page_name,$cms_heading,$cms_brief,$cms_article)=get_cms('page_name',"Tier4_Information");
//echo $cms_article;//pull_field("form_cms","db650","WHERE db647='Tier4_Information'");
?>
        </div>
    </div>
</div>
-->
