<?php
include_once("../admin/inc/lib.inc.php");
chk_login();
?>
<?php
$dbh = get_dbh();
//get the preferences
$sql_preferences = "SELECT * FROM lead_preferences WHERE usergroup = '$_SESSION[usergroup]' and (rec_archive IS NULL or rec_archive='')";
$sth = $dbh->prepare($sql_preferences);
$sth->execute();
$pdo_preferences = $sth->fetch(PDO::FETCH_ASSOC);

if ($_POST['process'] == 1) {
    if (!$_POST['db14977'] || !$_POST['db14978']) {
        echo "Fill in all the information";
        $url = "shortcoursebooking?ref=" . $username_id . "&ref1=";
        echo '<META HTTP-EQUIV="refresh" content="0;URL=' . $url . '">';
        echo "<script type='text/javascript'>document.location.href='{$url}';</script>";
        exit();
    }

    $db14977_scheduled_course_id = $_POST['db14978'];
    $db14980_notes_about_booking = $_POST['db14980'];

    $include_VAT = pull_field('sis_course_schedule', 'db15434', "WHERE id=$db14977_scheduled_course_id");

    $db14978_scheduled_course_start_date = pull_field('sis_course_schedule', 'db14947', "WHERE id=$db14977_scheduled_course_id");
    $db14946_course = pull_field('sis_course_schedule', 'db14946', "WHERE id=$db14977_scheduled_course_id");
    $db14982_course_title = pull_field('core_courses', 'db232', "WHERE id=$db14946_course");
    $db14979_booking_status = "1";
    $db14984_payment_method = $_POST['db14984'];
    $price_per_applicant = pull_field('sis_course_schedule', 'db14951', "WHERE id=$db14977_scheduled_course_id");
    $deposit_per_applicant = pull_field('sis_course_schedule', 'db15110', "WHERE id=$db14977_scheduled_course_id");
    if ($include_VAT == "yes") {
        $db15104_price_per_applicant = $price_per_applicant * (1 + ($pdo_preferences['db15030'] / 100));
        $db15111_deposit_per_applicant = $deposit_per_applicant * (1 + ($pdo_preferences['db15030'] / 100));
    } else {
        $db15104_price_per_applicant = $price_per_applicant;
        $db15111_deposit_per_applicant = $deposit_per_applicant;
    }
    $db15111_deposit_per_applicant = pull_field('sis_course_schedule', 'db15110', "WHERE id=$db14977_scheduled_course_id");
    $db15871_number_of_installments = pull_field('sis_course_schedule', 'db15432', "WHERE id=$db14977_scheduled_course_id");

    $student_id = $_SESSION['student_id'];
    $uname_id = $_SESSION['student_id'];//id

    $short_course_booking_id = $_SESSION["scheduled_course_booking"];

    if ($short_course_booking_id && $short_course_booking_id != '') {
        //Update
        $sql = "UPDATE sis_scheduled_booking SET db14977 = '$db14977_scheduled_course_id', db14978 = '$db14978_scheduled_course_start_date', db14979 = '$db14979_booking_status', db14980 = '$db14980_notes_about_booking', db14982 = '$db14982_course_title', db14984 = '$db14984_payment_method', db15104 = '$db15104_price_per_applicant', db15111 = '$db15111_deposit_per_applicant', db15871 = $db15871_number_of_installments,db16136='$db14946_course' WHERE id = $short_course_booking_id";

        $sth = $dbh->prepare($sql);
        $sth->execute();
        echo '<div class="alert alert-success"><b>Short Course Booking Updated Successfully  </b></div>';
    } else {
        $username_id = random();
        $sql = "INSERT INTO sis_scheduled_booking (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db14977,db14978,db14979,db14980,db14982,db14984,db15104,db15111,db15871,db16135,db16136) VALUES ('" . $username_id . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$uname_id', CURRENT_TIMESTAMP(), '1', '$db14977_scheduled_course_id', '$db14978_scheduled_course_start_date', '$db14979_booking_status','$db14980_notes_about_booking', '$db14982_course_title', '$db14984_payment_method','$db15104_price_per_applicant','$db15111_deposit_per_applicant','$db15871_number_of_installments', '$uname_id','$db14946_course')";

        $sth = $dbh->prepare($sql);
        $sth->execute();
        //$_SESSION["scheduled_course_booking"] = $dbh->lastInsertId();
        //echo '<div class="alert alert-success"><b>Short Course Booking Added Successfully  </b></div>';
        //die("Please close (this) window to proceed");
        //header("Location: shortcourses_detail?ref=".$username_id."&ref1=",true);
        //die();
        $url = "shortcourses_detail?ref=" . $username_id . "&ref1=";

        echo '<META HTTP-EQUIV="refresh" content="0;URL=' . $url . '">';
        echo "<script type='text/javascript'>document.location.href='{$url}';</script>";
        //echo '<form  action="shortcourses_detail?ref='.$username_id.'&ref1=">';
        //echo '<input type="submit" value="Add Applicant" name="mem_type" border="0">';
        //echo '</form>';

    }


}//end process
?>


<?php

// get all scheduled courses

$usergroups = usergroups_management();
$sched_course_id = '';
$course_name = '';
$notes = '';
$payment_type = '';
$all_payment_type = '';

if ($_SESSION["scheduled_course_booking"] && $_SESSION["scheduled_course_booking"] != '') {

    $scheduled_course_booking_id = $_SESSION["scheduled_course_booking"];
    $sql_scheduled_course_booking = "SELECT username_id, db14977 as 'sched_course', db14982 as 'course_name',db14979 as 'status', db14980 as 'notes',db14984 as 'payment_type',db15104 as 'price_per_applicant',db15111 as 'deposit_per_applicant', db15871 as 'number_of_installments' FROM sis_scheduled_booking WHERE id =  $scheduled_course_booking_id";
    $sth = $dbh->prepare($sql_scheduled_course_booking);
    $sth->execute();
    $results = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach ($results as $rows) {

        $sched_course_id = $rows['sched_course'];
        $course_name = $rows['course_name'];
        $notes = $rows['notes'];
        $payment_type = $rows['payment_type'];
        $price_per_applicant = $rows['price_per_applicant'];
        $deposit_per_applicant = $rows['deposit_per_applicant'];
        $number_of_installments = $rows['number_of_installments'];
        $all_payment_types = pull_field('sis_course_schedule', 'db15431', "WHERE id = $sched_course_id");
        $booking_status = $rows['status'];
        if ($booking_status == 3) {
            $disabled = 'disabled=disabled';

        }
        $booking_username_id = $rows['username_id'];
    }
} else {
    global $core_students_course_of_study;
    $course_id = pull_field('core_students', 'db889', "WHERE id = $_SESSION[student_id]");
    $course_name = pull_field("core_courses", "db232", "WHERE id = '" . pull_field('core_students', 'db889', "WHERE id = $_SESSION[student_id]") . "'");
}

$sql = "SELECT id, db232 as 'title' from core_courses WHERE $usergroups AND (rec_archive IS NULL OR rec_archive = '')  AND db235='public' AND db340='on' AND db341='27' ";
//$sth = $dbh->prepare($sql);
//$sth->execute();
//$course_results = $sth->fetchALL(PDO::FETCH_ASSOC);
$course_results = mysqli_query($dbcon, $sql);

?>

    <div>
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <link rel="stylesheet" href="<?= baseline_url ?>/<?= front_header_file_location ?>/css/fontend.css"
              type="text/css" media="screen">
        <!-- Bootstrap core CSS -->
        <link href="<?= baseline_url ?>/<?= front_header_file_location ?>/resources/css/bootstrap.min.css"
              rel="stylesheet">
        <!-- Custom styles for this template -->
        <link href="https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,400,700,600,300"
              rel="stylesheet" type="text/css">
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Lato&subset=latin,latin-ext"
              type="text/css">
        <link href="<?= baseline_url ?>/<?= front_header_file_location ?>/resources/css/custom.css" rel="stylesheet">
        <link href="<?= baseline_url ?>/<?= front_header_file_location ?>/resources/font-awesome-4.1.0/css/font-awesome.min.css"
              rel="stylesheet" type="text/css" media="screen">
        <link href="<?= baseline_url ?>/<?= front_header_file_location ?>/resources/datatables/css/jquery.dataTables.min.css"
              rel="stylesheet" type="text/css" media="screen">
        <link href="<?= engine_url ?>/css/theme_controller.css" rel="stylesheet">

        <script src="<?= baseline_url ?>/<?= front_header_file_location ?>/js/jquery-1.10.2.min.js"
                type="text/javascript" charset="utf-8"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/jquery-ui.min.js" type="text/javascript"
                charset="utf-8"></script>
        <script type="text/javascript" language="JavaScript"
                src="<?= engine_url ?>/js/pop_msg.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.4/js/bootstrap.min.js" type="text/javascript"
                charset="utf-8"></script>
        <script src="<?= engine_url ?>/js/global.js"></script>
        <script src="<?= baseline_url ?>/<?= front_header_file_location ?>/js/form_check.js"></script>
        <script>
            setTimeout(function () {
                $('#img_processing').fadeOut('fast');
                $('.img_processing').fadeOut('fast');
                $('.entry_success').fadeOut('fast');
            }, 5000); // <-- time in milliseconds

            $(function () {
                //testing
                var activeTab = null;
                $('a[data-toggle="tab"]').on('show', function (e) {
                    activeTab = e.target;
                })
            });

        </script>
        <script>
            // function used to produce page breaks
            $(function () {
                // jump to first tab initially
                $('.tabbable li > a:first').tab('show');

                var tabhist = [],
                    curtab = $('.tabbable li.active').attr('id'),
                    percentage_done = 0,
                    // count all the mandatory fields
                    req_count = $('.form-group .required').length;

                $('#button').toggle($('.tabbable li.active').next('li').length === 0);

                $('#prevtab').click(function () {
                    // pop one from history and show it
                    curtab = tabhist.pop() || curtab;
                    $('#' + curtab + ' > a').tab('show');
                    // update tabcounter
                    $('[name=tabcounter]').val(tabhist.length);

                    // check if the new tab is last and possibly show submit button
                    $('#button').toggle($('.tabbable li.active').next('li').length === 0);
                    $('#nexttab').toggle($('.tabbable li.active').next('li').length !== 0);
                });

                $('#nexttab').click(function () {
                    // do not allow proceeding if not all required fields are filled
                    var active_req_count = $('.tab-pane.active .form-group .required').length,
                        active_req_filled = $('.tab-pane.active .form-group .required.required-filled').length;
                    if (active_req_count !== active_req_filled) {
                        alert('Please fill all the required fields!');
                    } else {
                        var active_tab = $('.tabbable li.active a'),
                            decisions_el = $(active_tab.attr('href') + ' > .decision-data'),
                            // grab decision cases
                            decisions = decisions_el.data('decisions') || {},
                            // what is the decision making field here?
                            decisions_field = decisions_el.data('decisions-field'),
                            // where should we switch to?
                            eltype = $('[name="' + decisions_field + '"]').attr("type"),
                            selector = '[name="' + decisions_field + '"]' + ((eltype === 'radio') ? ':checked' : ''),
                            decision = decisions[$(selector).val()];
                        // default can be provided in JSON, if it's not then we can go to next tab
                        decision = decision || decisions['default'] || $('.tabbable li.active').next('li').attr('id');

                        // record our current tab and update to new decision
                        tabhist.push(curtab);
                        $('[name=tabcounter]').val(tabhist.length);
                        curtab = decision;
                        // change the tab
                        $('#' + curtab + ' > a').tab('show');

                        // check if the new tab is last and possibly show submit button
                        $('#button').toggle($('.tabbable li.active').next('li').length === 0);
                        $('#nexttab').toggle($('.tabbable li.active').next('li').length !== 0);
                    }
                });

                if (req_count > 0) {
                    $('.form-group .required').map(function () {
                        var req_element = this;
                        $(this).closest('.form-group').find('input,textarea,select').change(function () {
                            var field_tag = $(this).prop('tagName'),
                                field_type = $(this).attr('type'),
                                field_name = $(this).attr('name'),
                                field_value = $(this).val(),
                                // is the field marked as filled already?
                                is_filled = $(req_element).hasClass('required-filled'),
                                // is the field filled in?
                                field_empty = !field_value;

                            // check all date select fields for "empty" values
                            field_empty = field_empty || (field_name.endsWith('_day') && field_value === '00');
                            field_empty = field_empty || (field_name.endsWith('_month') && field_value === '00');
                            field_empty = field_empty || (field_name.endsWith('_year') && field_value === '0000');
                            field_empty = field_empty || field_value === '0000-00-00';
                            // selects default to "not specified"
                            field_empty = field_empty || (field_tag === 'SELECT' && field_value === 'not specified');
                            // if radio need to find if any of the same-name radios are checked
                            field_empty = field_empty || (field_type === 'radio' && $('[name=' + field_name + ']:checked').length === 0);

                            // add/remove class signifying the field is filled/empty
                            $(req_element).toggleClass('required-filled', !field_empty);

                            percentage_done = Math.ceil(100 * $('.tab-pane.active .form-group .required.required-filled').length / req_count);
                            // update the progress bar
                            $('.progress-bar').css('width', percentage_done + '%')
                                .attr('aria-valuenow', percentage_done)
                                .text(percentage_done + '%');
                        });
                        $(this).closest('.form-group').find('input,textarea,select').change();
                    });
                    $('.progress-bar').css('width', percentage_done + '%')
                        .attr('aria-valuenow', percentage_done)
                        .text(percentage_done + '%');
                }
            });

        </script>
        <script>
            $(function () {

                // THIS SCRIPT ADDS A COUNTER NEXT TO THE FIELDS
                $('.field-counter').on('input', function () {
                    var total_allowed = 4000,
                        val = $(this).val(),
                        count = val.length,
                        remaining = total_allowed - count;
                    if (count > total_allowed) {
                        $(this).val(val.substr(0, total_allowed));
                        remaining = 0;
                    }
                    $('#' + $(this).attr('id') + '-counter').text(remaining + ' characters left');

                });
                //END
            });
        </script>

        <div id="pageloading"><i class="icon-spinner icon-spin icon-large"></i></div>
        <form class="form-horizontal" role="form"
              action='<?= website_url_applicant ?>/shortcoursebooking??ref=<?= $booking_username_id ?>&ref1='
              method="post" enctype="multipart/form-data" onsubmit="return formCheck_shortcourse_booking(this)">
            <div>
                <input name="option4" type="hidden" value="253">
                <div class="required"><strong>Required field exist on this form </strong></div>
                <br>
                <div class="tabbable">
                    <ul class="nav nav-tabs"></ul>
                    <div>
                        <div class=""><h2>Course Booking Form</h2><a name="anc_" id="anc_"></a></div>
                        <script>
                            function rv(el) {
                                return el.value;
                            }

                            function parentsChange(parentName, childName, activeId) {
                                var parents = $('input[name="' + parentName + '"]');
                                var values = (parents.toArray().length == 1) ?
                                    parents.toArray().map(rv) :
                                    parents.filter(':checked').toArray().map(rv);

                                if (values.indexOf(activeId) > -1) {
                                    $('input[name="' + childName + '"]').closest('div.form-group').show();
                                    $('input[name="' + childName + '[]"]').closest('div.form-group').show();
                                    $('textarea[name="' + childName + '"]').closest('div.form-group').show();
                                    $('select[name="' + childName + '"]').closest('div.form-group').show();

                                    // if field is switched on make sure we check if it's filled
                                    $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                } else {
                                    $('input[name="' + childName + '"]').closest('div.form-group').hide();
                                    $('input[name="' + childName + '[]"]').closest('div.form-group').hide();
                                    $('textarea[name="' + childName + '"]').closest('div.form-group').hide();
                                    $('select[name="' + childName + '"]').closest('div.form-group').hide();

                                    // if field is switched off consider it filled
                                    $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                        .closest('div.form-group')
                                        .find('.required')
                                        .addClass('required-filled');
                                }
                            }

                            $(function () {
                                // Parent(s) of current child element
                                var parents = $('input[name="sis_course_schedule WHERE db14959=3"]');
                                // Bind to change event
                                parents.change(function () {
                                    parentsChange('sis_course_schedule WHERE db14959=3',
                                        'db14977',
                                        'db14946');
                                });
                                // Trigger change event initially to have the correct
                                // initial state
                                parents.change();

                                // The same version, but if parents have [] in the name
                                // Parent(s) of current child element
                                var parents = $('input[name="sis_course_schedule WHERE db14959=3[]"]');
                                // Bind to change event
                                parents.change(function () {
                                    parentsChange('sis_course_schedule WHERE db14959=3[]',
                                        'db14977',
                                        'db14946');
                                });
                                // Trigger change event initially to have the correct
                                // initial state
                                parents.change();
                            });
                        </script>
                        <input class="form-control" name="reqhid[]" type="hidden" id="reqhid[]"
                               value="db14977_Scheduled Course">
                        <script>

                            $(function () {
                                $('#db14977').change(function () {
                                    var link_id;
                                    if (this.value) {
                                        link_id = this.value;
                                    }

                                    $('#db14978').html($());
                                    $.ajax({
                                        url: window.location.origin + '/engine/inc/link_api.php',
                                        data: "cat_id=id,DATE_FORMAT(db14947,'%d/%m/%Y')&tb=sis_course_schedule WHERE (rec_archive is null or rec_archive='') AND db14947 >= NOW() AND <?=$usergroups?> AND db14959=3&fld=db14946&where=" + link_id + "",
                                        dataType: 'json',
                                        success: function (rows) {
                                            var elements = $();
                                            elements = elements.add('<option value="">Please select....</option>');
                                            var selected = "";
                                            for (var i in rows) {
                                                var row = rows[i];
                                                if (row[0] == '<?php echo $sched_course_id?>') {
                                                    selected = " selected=selected";
                                                }
                                                if (row.length == 3) {
                                                    elements = elements.add('<option value=' + row[0] + selected + '>' + row[1] + ' ' + row[2] + '</option>');
                                                } else {
                                                    elements = elements.add('<option value=' + row[0] + selected + '>' + row[1] + '</option>');
                                                }
                                                selected = "";

                                            }
                                            $('#db14978').html(elements);
                                        }
                                    });
                                });
                            });

                        </script>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                    <span class="required required-filled"></span>
                                    Scheduled course<a name="anc_db14977" id="anc_db14977"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <select class="form-control" name="db14977" id="db14977" <?= $disabled ?>>
                                        <option value="" selected="selected">Select</option>
                                        <?php while ($rows = mysqli_fetch_assoc($course_results)) { ?>
                                            <option value="<?= $rows['id'] ?>" <?php if ($course_name == $rows['title']) {
                                                echo 'selected="selected"';
                                            } ?> ><?= $rows['title'] ?></option>
                                        <?php } ?>
                                    </select></div>
                            </div>

                        </div>
                        <input class="form-control" name="reqhid[]" type="hidden" id="reqhid[]"
                               value="db14978_Scheduled Course start date">
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                    <span class="required required-filled"></span>
                                    Scheduled course start date<a name="anc_db14978" id="anc_db14978"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <select name="db14978" id="db14978" class="form-control" <?= $disabled ?>>
                                        <option value="">Select above...</option>
                                    </select>
                                </div>
                            </div>
                            <script>

                                /*$(function () {
                                    $('#db14978').change(function () {
                                        if (this.value) {
                                            var link_id = this.value;
                                        }
                                        $.ajax({
                                            url: '<?=engine_url?>/inc/link_api.php',
                                            data: "cat_id=db14951,db15110,db15431,db15434,db15432&tb=sis_course_schedule WHERE db14959=3&fld=id&where=" + link_id + "",
                                            dataType: 'json',
                                            success: function (rows) {
                                                for (var i in rows) {
                                                    var row = rows[i];
                                                    if (row[3] =="yes") {//include vat
                                                        $('#db15104').val( row[0] * (1 + (<?=$pdo_preferences['db15030']?> / 100)));
                                                    }
                                                    else {
                                                        $('#db15104').val(row[0]);
                                                    }
                                                    //if (row[3] =="yes") {//include vat
                                                        //$('#db15111').val( row[1] * (1 + (<?=$pdo_preferences['db15030']?> / 100)));
                                                    //}
                                                    //else {
                                                        $('#db15111').val(row[1]);
                                                    //}
                                                    var payment_methods = row[2];
                                                    var payment_methods_array = payment_methods.split(',');
                                                    var arrayLength = payment_methods_array.length;
                                                    var elements = $();
                                                    elements = elements.add('<option value="">Please select....</option>');
                                                    var selected = '';
                                                    for (var j = 0; j < arrayLength; j++) {
                                                        if (payment_methods_array[j] == '<?php echo $payment_type?>') { selected = " selected=selected";}
                                                        elements = elements.add('<option value="' + payment_methods_array[j]+'" ' +  selected + '>' + payment_methods_array[j] + '</option>');
                                                        selected = "";
                                                    }
                                                    $('#db14984').html(elements);
                                                    $('#db15871').val(row[4]);//number of installments


                                                }
                                            }
                                        });
                                    });
                                });*/

                            </script>

                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">Price per applicant<a
                                            name="anc_db15104" id="anc_db15104"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <input name="db15104" id="db15104" class="form-control" disabled="disabled"
                                           value="<?= $price_per_applicant ?>">
                                </div>
                            </div>
                        </div>
                        <!--<div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">Deposit<a name="anc_db15111" id="anc_db15111"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <input  name="db15111" id="db15111"  class="form-control" disabled="disabled" value="<?= $deposit_per_applicant ?>">
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">Number of Installments<a name="anc_db15871" id="anc_db15871"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <input class="form-control" type="number" name="db15871" id="db15871" disabled="disabled" value="<?= $number_of_installments ?>">
                                </div>
                            </div>
                        </div>-->
                        <input value="" type="hidden" name="db14979" id="db14979">
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">Notes about booking
                                    <span class="pull-right text-warning" id="db14980-counter"></span>
                                    <a name="anc_db14980" id="anc_db14980"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <textarea type="text" name="db14980" id="db14980" size="db14980"
                                              class="form-control field-counter" rows="3"><?= $notes ?></textarea>
                                </div>
                            </div>
                        </div>
                        <input value="" type="hidden" name="db14981" id="db14981">
                        <input class="form-control" value="" type="hidden" name="db14982" id="db14982">
                        <input value="" type="hidden" name="db14983" id="db14983">
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">Payment method<a
                                            name="anc_db14984" id="anc_db14984"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <select class="form-control" name="db14984" id="db14984" <?= $disabled ?>>
                                        <?php
                                        if ($all_payment_types && $all_payment_types != '') {

                                            $all_types = explode(',', $all_payment_types);

                                            if (count($all_types) > 1) {
                                                echo '<option value="">Select...</option>';
                                            }
                                            foreach ($all_types as $type) { ?>
                                                <option value="<?= $type ?>" <?php if ($type == $payment_type) echo ' selected=selected'; ?> ><?= $type ?></option>
                                            <?php } ?>
                                        <?php } else { ?>
                                            <option value="">Select above..</option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <input class="form-control" value="<?= $pdo_preferences['db15030'] ?>" type="hidden"
                               name="db17743" id="db17743">
                        <input value="" type="hidden" name="db14985" id="db14985"></div>
                </div>
                <div class="col-md-12 text-center"><br>
                    <span style="padding-top:5px; color:#333; font-size:12px">Click this button to save your information &gt;&gt;</span>
                    <br><br>

                    <input name="process" type="hidden" value="1">
                    <input name="tabcounter" type="hidden" value="">
                    <?php if ($_SESSION['scheduled_course_booking'] && $_SESSION['scheduled_course_booking'] != '') { ?>
                        <button type="Submit" name="Submit" class="btn btn-info"> Save</button>
                    <?php } else { ?>
                        <button type="Submit" name="Submit" class="btn btn-info"> Step 2: Confirm Your Details</button>
                    <?php } ?>
                </div>


            </div>
        </form>


    </div>
    <script>
        jQuery(document).ready(function ($) {
            $('#db14977').change();
            $('#db14978').change();
        });
    </script>


<?php if ($_SESSION["scheduled_course_booking"] && $_SESSION["scheduled_course_booking"] != '') {
    $dbh = get_dbh();
    $scheduled_course_username_id = pull_field('sis_scheduled_booking', 'username_id', "WHERE id='" . $_SESSION["scheduled_course_booking"] . "'");

    $usergroups = usergroups_management();

// get all short_course applicants for this booking

    $sql = "SELECT id as 'manage', username_id as 'unique_name', db15054 as 'first_name', db15055 as 'last_name', db15056 as 'email',  db15059 as 'nationality' FROM sis_sched_booking_detail WHERE $usergroups AND (rec_archive is null or rec_archive ='') AND rel_id = '$_SESSION[scheduled_course_booking]'";
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    ?>
    <?php if ($booking_status != 3) { //if not paid?>
        <div class="pull-right">
            <a href="shortcourses_detail?ref=<?= $scheduled_course_username_id ?>&ref1=" class="btn btn-info"
               id="button" onclick="return confirmGoToNextPage()">Add new applicant details</a>
        </div>
        <br><br>
    <?php } ?>
    <?php if (count($results) > 0) { ?>
        <table class="table table-shaded">
        <tr>

            <td>First Name</td>
            <td>Last Name</td>
            <td>Email</td>
            <td>Nationality</td>
        </tr>

        <?php


        foreach ($results as $rows) {
            $uid = $rows['id'];

            ?>
            <tr>

                <td><?= $rows['first_name'] ?></td>
                <td><?= $rows['last_name'] ?></td>
                <td><?= $rows['email'] ?></td>
                <td><?= $rows['nationality'] ?></td>
                <td>

                    <a href="shortcourses_detail?ref=<?= $scheduled_course_username_id ?>&ref1=<?= $rows['unique_name'] ?>"
                       id="button"
                       title="Edit short course booking detail" class="btn btn-info btn-sm"><i class="fa fa-pencil"></i></a>

                </td>

            </tr>
            <?php
        }
    }
    ?>
    </table>

<?php }