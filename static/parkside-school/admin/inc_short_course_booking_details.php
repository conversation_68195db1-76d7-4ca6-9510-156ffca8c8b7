<?php
include_once("../../../engine/admin/inc/lib.inc.php");
chk_login();
?>

<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">

<link rel="stylesheet" href="<?= baseline_url ?>/<?= front_header_file_location ?>/css/fontend.css" type="text/css"
      media="screen">
<!-- Bootstrap core CSS -->
<link href="<?= baseline_url ?>/<?= front_header_file_location ?>/resources/css/bootstrap.min.css" rel="stylesheet">
<!-- Custom styles for this template -->
<link href="https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,400,700,600,300"
      rel="stylesheet" type="text/css">
<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Lato&subset=latin,latin-ext" type="text/css">
<link href="<?= baseline_url ?>/<?= front_header_file_location ?>/resources/css/custom.css" rel="stylesheet">
<link href="<?= baseline_url ?>/<?= front_header_file_location ?>/resources/font-awesome-4.1.0/css/font-awesome.min.css"
      rel="stylesheet" type="text/css" media="screen">
<link href="<?= baseline_url ?>/<?= front_header_file_location ?>/resources/datatables/css/jquery.dataTables.min.css"
      rel="stylesheet" type="text/css" media="screen">
<link href="<?= engine_url ?>/css/theme_controller.css" rel="stylesheet">

<script src="<?= baseline_url ?>/<?= front_header_file_location ?>/js/jquery-1.10.2.min.js"
        type="text/javascript" charset="utf-8"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/jquery-ui.min.js" type="text/javascript"
        charset="utf-8"></script>
<script type="text/javascript" language="JavaScript"
        src="<?= engine_url ?>/js/pop_msg.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.4/js/bootstrap.min.js" type="text/javascript"
        charset="utf-8"></script>
<script src="<?= engine_url ?>/js/global.js"></script>
<script src="<?= baseline_url ?>/<?= front_header_file_location ?>/js/form_check.js"></script>
<script>
    setTimeout(function () {
        $('#img_processing').fadeOut('fast');
        $('.img_processing').fadeOut('fast');
        $('.entry_success').fadeOut('fast');
    }, 5000); // <-- time in milliseconds
</script>

<div class="container">
    <?php
    if ($_POST['process'] == 1) {
        $dbh = get_dbh();
        //get the values

        //// check if submission is an upload. If so then inclue upload module
        if (isset($_POST['uploadsNeeded']) && $_POST['uploadsNeeded'] !== '') {
            //error_log('this uses scpt_custom_uploader');
            include(base_path."/engine/models/scpt_custom_uploader.php");
            track_use("uploaded file...$_POST[uploadsNeeded]");//tracking use
            $db204_location_box = sanitise($_POST['location_box'][0]);
            $db201_uploaded_by = session_info("uid");
            $db199_title = "Passport upload: (scan of passport)";
            $db200_file_description = pull_field("form_file_category", "id", "WHERE usergroup='" . $_SESSION['access'] . "' AND db743 LIKE '%passport%'");
            $sql = "INSERT INTO form_file (username_id, rec_id, usergroup, rel_id, db199, db200, db201, db202, db203, db204) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . session_info("student_id") . "', '$db199_title', '$db200_file_description', '$db201_uploaded_by', '', '', '$db204_location_box')";
            $sth = $dbh->prepare($sql);
            $sth->execute();
        }

        $rel_id = $_SESSION['scheduled_course_booking'];

        $scheduled_course_booking_detail = $_SESSION['scheduled_course_booking_detail'];

        $db15052_scheduled_booking = $rel_id;
        $db15054_first_name = sanitise($_POST["db15054"]);
        $db15055_last_name = sanitise($_POST["db15055"]);
        $db15056_email_address = sanitise($_POST["db15056"]);
        $db15057_gender = sanitise($_POST["db15057"]);
        $db15058_date_of_birth = sanitise($_POST["db15058"]);
        $db15059_nationality = sanitise($_POST["db15059"]);
        $db15060_passport_number = sanitise($_POST["db15060"]);
        $db15061_passport_expiry = sanitise($_POST["db15061"]);
        $db15062_country_of_origin = sanitise($_POST["db15062"]);
        $db15063_country_of_permanent_residence = sanitise($_POST["db15063"]);
        $db15064_passport_upload = sanitise($_POST["db15064"]);
        $db15067_address1 = sanitise($_POST["db15067"]);
        $db15068_address_town = sanitise($_POST["db15068"]);
        $db15069_address_county = sanitise($_POST["db15069"]);
        $db15070_country = sanitise($_POST["db15070"]);
        $db15071_postcode = sanitise($_POST["db15071"]);
        $db15073_contact_telephone = sanitise($_POST["db15073"]);
        $db15075_emergency_first_name = sanitise($_POST["db15075"]);
        $db15076_emergency_last_name = sanitise($_POST["db15076"]);
        $db15077_emergency_relationship = sanitise($_POST["db15077"]);
        $db15078_emergency_email_address = sanitise($_POST["db15078"]);
        $db15079_emergency_telephone = sanitise($_POST["db15079"]);
        $db15083_learning_disability = sanitise($_POST["db15083"]);
        $db15084_learning_disability_type = sanitise($_POST["db15084"]);
        $db15085_hear_about_us = sanitise($_POST["db15085"]);
        $db15086_terms_and_conditions = sanitise($_POST["db15086"]);
        $db15081_qualifications = implode(',', sanitise($_POST["db15081"]));
        $db15439_invoice_address = sanitise($_POST["db15439"]);
        $db19347_invoice_fao = sanitise($_POST["db19347"]);
        $db19348_invoice_address1 = sanitise($_POST["db19348"]);
        $db19349_invoice_town = sanitise($_POST["db19349"]);
        $db19350_invoice_county = sanitise($_POST["db19350"]);
        $db19351_invoice_country = sanitise($_POST["db19351"]);
        $db19352_invoice_postcode = sanitise($_POST["db19352"]);
        $db19353_invoice_address_record = sanitise($_POST["db19353"]);

        if ($scheduled_course_booking_detail && $scheduled_course_booking_detail != '') {
            $db15440_address_record = pull_field('sis_sched_booking_detail', 'db15440', "WHERE id = $scheduled_course_booking_detail");

            if (!$db15440_address_record || $db15440_address_record == '') {
                $sql = "INSERT INTO sis_address (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db215,db216,db217,db218,db219,db220,db221,db15441,db19354) VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . session_info("student_id") . "', CURRENT_TIMESTAMP(), '1','$db15067_address1','','$db15068_address_town','$db15069_address_county','$db15070_country','$db15071_postcode','',$db15439_invoice_address, $db15054_first_name.' '.$db15054_last_name)";

                $sth = $dbh->prepare($sql);
                $sth->execute();
                $db15440_address_record = $dbh->lastInsertId();

            }
            $db19353_invoice_address_record = pull_field('sis_sched_booking_detail', 'db19353', "WHERE id = $scheduled_course_booking_detail");
            if ((!$db19353_invoice_address_record || $db19353_invoice_address_record == '') && $db15439_invoice_address == 'no') {
                $sql = "INSERT INTO sis_address (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db215,db216,db217,db218,db219,db220,db221,db15441,db19354) VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . session_info("student_id") . "', CURRENT_TIMESTAMP(), '1','$db19348_invoice_address1','','$db19349_invoice_town','$db19350_invoice_county','$db19351_invoice_country','$db19352_invoice_postcode','','yes','$db19347_invoice_fao')";

                $sth = $dbh->prepare($sql);
                $sth->execute();
                $db19353_invoice_address_record = $dbh->lastInsertId();

            }

            if ($db19353_invoice_address_record && $db19353_invoice_address_record != '') {
                //update invoice address in sis_address
                $sql = "UPDATE sis_address SET db215 = '$db19348_invoice_address1', db216 = '', db217 = '$db19349_invoice_town', db218 = '$db19350_invoice_county', db219 = '$db19351_invoice_country', db220 = '$db19352_invoice_postcode', db19354='$db19347_invoice_fao' WHERE id = $db19353_invoice_address_record";
                $sth = $dbh->prepare($sql);
                $sth->execute();
            }

            //update address in sis_address
            $sql = "UPDATE sis_address SET db215 = '$db15067_address1', db216 = '', db217 = '$db15068_address_town', db218 = '$db15069_address_county', db219 = '$db15070_country', db220 = '$db15071_postcode', db15441 = '$db15439_invoice_address' WHERE id = $db15440_address_record";
            $sth = $dbh->prepare($sql);
            $sth->execute();

            //Update
            $sql = "UPDATE sis_sched_booking_detail SET 
                        db15054 = '$db15054_first_name',
                        db15055 = '$db15055_last_name',
                        db15056 = '$db15056_email_address',
                        db15057 = '$db15057_gender',
                        db15058 = '$db15058_date_of_birth',
                        db15059 = '$db15059_nationality',
                        db15060 = '$db15060_passport_number',
                        db15061 = '$db15061_passport_expiry',
                        db15062 = '$db15062_country_of_origin',
                        db15063 = '$db15063_country_of_permanent_residence',
                        db15064 = '$db15064_passport_upload',
                        db15067 = '$db15067_address1',
                        db15068 = '$db15068_address_town',
                        db15069 = '$db15069_address_county',
                        db15070 = '$db15070_country',
                        db15071 = '$db15071_postcode',
                        db15439 = '$db15439_invoice_address',
                        db15440 = '$db15440_address_record',
                        db19347 = '$db19347_invoice_fao',
                        db19348 = '$db19348_invoice_address1',
                        db19349 = '$db19349_invoice_town',
                        db19350 = '$db19350_invoice_county',
                        db19351 = '$db19351_invoice_country',
                        db19352 = '$db19352_invoice_postcode',
                        db19353 = '$db19353_invoice_address_record',
                        
                        db15073 = '$db15073_contact_telephone',
                        db15075 = '$db15075_emergency_first_name',
                        db15076 = '$db15076_emergency_last_name',
                        db15077 = '$db15077_emergency_relationship',
                        db15078 = '$db15078_emergency_email_address',
                        db15079 = '$db15079_emergency_telephone',
                        db15083 = '$db15083_learning_disability',
                        db15084 = '$db15084_learning_disability_type',
                        db15085 = '$db15085_hear_about_us',
                        db15086 = '$db15086_terms_and_conditions',
                        db15081 = '$db15081_qualifications'
              WHERE id = $scheduled_course_booking_detail";
            $sth = $dbh->prepare($sql);
            $sth->execute();

            //update the record in sis_address as well

            echo '<div class="alert alert-success"><b>Applicant Details Updated Successfully  </b></div>';
        } else {

            $sql = "INSERT INTO sis_address (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db215,db216,db217,db218,db219,db220,db221,db15441) VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . session_info("student_id") . "', CURRENT_TIMESTAMP(), '1','$db15067_address1','','$db15068_address_town','$db15069_address_county','$db15070_country','$db15071_postcode','','$db15439_invoice_address')";
            $sth = $dbh->prepare($sql);
            $sth->execute();
            $db15440_address_record = $dbh->lastInsertId();

            if ($db15439_invoice_address == 'no') {
                $sql = "INSERT INTO sis_address (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db215,db216,db217,db218,db219,db220,db221,db15441,db19354) VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . session_info("student_id") . "', CURRENT_TIMESTAMP(), '1','$db19348_invoice_address1','','$db19349_invoice_town','$db19350_invoice_county','$db19351_invoice_country','$db19352_invoice_postcode','','yes','$db19347_invoice_fao')";
                $sth = $dbh->prepare($sql);
                $sth->execute();
                $db19353_invoice_address_record = $dbh->lastInsertId();
            }
            $sid = session_info('student_id');
            $sql = "INSERT INTO sis_sched_booking_detail 
                (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db15052, db15054, db15055, db15056, db15057, db15058, db15059, db15060, db15061, db15062, db15063, db15064, db15069, db15068, db15067, db15070, db15071, db15439, db15440,db19347,db19348,db19349,db19350,db19351,db19352,db19353, db15073, db15075, db15076, db15077, db15078, db15079, db15086, db15083, db15084, db15085,db15081)
                VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$sid', CURRENT_TIMESTAMP(), '1', 
                '$db15052_scheduled_booking', 
                '$db15054_first_name', 
                '$db15055_last_name',
                '$db15056_email_address',
                '$db15057_gender',
                '$db15058_date_of_birth',
                '$db15059_nationality',
                '$db15060_passport_number',
                '$db15061_passport_expiry',
                '$db15062_country_of_origin',
                '$db15063_country_of_permanent_residence',
                '$db15064_passport_upload',
                '$db15069_address_county',
                '$db15068_address_town',
                '$db15067_address1',
                '$db15070_country',
                '$db15071_postcode',
                '$db15439_invoice_address',
                '$db15440_address_record',
                '$db19347_invoice_fao',
                '$db19348_invoice_address1',
                '$db19349_invoice_town',
                '$db19350_invoice_county',
                '$db19351_invoice_country',
                '$db19352_invoice_postcode',
                '$db19353_invoice_address_record',
                '$db15073_contact_telephone',
                '$db15075_emergency_first_name',
                '$db15076_emergency_last_name',
                '$db15077_emergency_relationship',
                '$db15078_emergency_email_address',
                '$db15079_emergency_telephone',
                '$db15086_terms_and_conditions',
                '$db15083_learning_disability',
                '$db15084_learning_disability_type',
                '$db15085_hear_about_us',
                '$db15081_qualifications'
                )";

            $sth = $dbh->prepare($sql);
            $sth->execute();
            $_SESSION["scheduled_course_booking_detail"] = $dbh->lastInsertId();

            //update the status on the booking
            //Update
            $sql = "UPDATE sis_scheduled_booking SET db14979 = '2' WHERE id = $rel_id";
            $sth = $dbh->prepare($sql);
            $sth->execute();


            echo '<div class="alert alert-success"><b>Applicant Added Successfully to Booking. Please close this window</b></div><br><div class="text-center"><a href="#" class="btn btn-primary continue_btn_1">Continue</a></div> <script type="text/javascript"> $(document).ready(function(){ $(".continue_btn_1").click(function(){  window.parent.$("#short_course_registration").modal("hide"); }); });</script>';
            exit();
        }
    }
    ?>

    <script type="text/javascript">

    </script>

    <?php
    // get all scheduled courses
    $dbh = get_dbh();
    $usergroups = usergroups_management();

    $scheduled_course_booking = $_SESSION["scheduled_course_booking"];
    $booking_username_id = pull_field("sis_scheduled_booking", "username_id", "WHERE id=$scheduled_course_booking");

    if ($_SESSION["scheduled_course_booking_detail"] && $_SESSION["scheduled_course_booking_detail"] != '') {

        $scheduled_course_booking_detail_id = $_SESSION["scheduled_course_booking_detail"];
        $scheduled_course_booking_detail_sql = "SELECT * FROM sis_sched_booking_detail WHERE id=$scheduled_course_booking_detail_id";
        $sth = $dbh->prepare($scheduled_course_booking_detail_sql);
        $sth->execute();
        $results = $sth->fetchAll(PDO::FETCH_ASSOC);
        foreach ($results as $rows) {
            $db15054_first_name = $rows['db15054'];
            $db15055_last_name = $rows['db15055'];
            $db15056_email_address = $rows['db15056'];
            $db15057_gender = $rows['db15057'];
            $db15058_date_of_birth = $rows['db15058'];
            $db15059_nationality = $rows['db15059'];
            $db15060_passport_number = $rows['db15060'];
            $db15061_passport_expiry = $rows['db15061'];
            $db15062_country_of_origin = $rows['db15062'];
            $db15063_country_of_permanent_residence = $rows['db15063'];
            $db15064_passport_upload = $rows['db15064'];
            $db15069_address_county = $rows['db15069'];
            $db15068_address_town = $rows['db15068'];
            $db15067_address1 = $rows['db15067'];
            $db15070_country = $rows['db15070'];
            $db15071_postcode = $rows['db15071'];
            $db15440_address_record = $rows['db15440'];
            $db15073_contact_telephone = $rows['db15073'];
            $db15075_emergency_first_name = $rows['db15075'];
            $db15076_emergency_last_name = $rows['db15076'];
            $db15077_emergency_relationship = $rows['db15077'];
            $db15078_emergency_email_address = $rows['db15078'];
            $db15079_emergency_telephone = $rows['db15079'];
            $db15086_terms_and_conditions = $rows['db15086'];
            $db15083_learning_disability = $rows['db15083'];
            $db15084_learning_disability_type = $rows['db15084'];
            $db15085_hear_about_us = $rows['db15085'];
            $db15081_qualifications = $rows['db15081'];
            $booking_detail_username_id = $rows['username_id'];
            $db19347_invoice_fao = $rows['db19347'];
            $db19348_invoice_address1 = $rows['db19348'];
            $db19349_invoice_town = $rows['db19349'];
            $db19350_invoice_county = $rows['db19350'];
            $db19351_invoice_country = $rows['db19351'];
            $db19352_invoice_postcode = $rows['db19352'];
            $db19353_invoice_address_record = $rows['db19353'];
            $db15439_invoice_address = $rows['db15439'];

        }
    } else {
        //see if this person has make a booking before and use these details as default if they have
        $sql = "SELECT * FROM sis_scheduled_booking WHERE $usergroups AND (rec_archive is null or rec_archive ='') AND db16135 = '$_SESSION[student_id]' AND db14979 > 1 ORDER BY date DESC LIMIT 1";
        $sth = $dbh->prepare($sql);
        $sth->execute();
        $temp_results = $sth->fetchAll(PDO::FETCH_ASSOC);

        if ($temp_results) {
            $scb = $temp_results[0];
            $sql = "SELECT * FROM sis_sched_booking_detail WHERE db15052 = $scb[id]  ORDER BY date DESC LIMIT 1";

            $sth = $dbh->prepare($sql);
            $sth->execute();
            $temp_results = $sth->fetchAll(PDO::FETCH_ASSOC);
            foreach ($temp_results as $rows) {
                $db15054_first_name = $rows['db15054'];
                $db15055_last_name = $rows['db15055'];
                $db15056_email_address = $rows['db15056'];
                $db15057_gender = $rows['db15057'];
                $db15058_date_of_birth = $rows['db15058'];
                $db15059_nationality = $rows['db15059'];
                $db15060_passport_number = $rows['db15060'];
                $db15061_passport_expiry = $rows['db15061'];
                $db15062_country_of_origin = $rows['db15062'];
                $db15063_country_of_permanent_residence = $rows['db15063'];
                $db15064_passport_upload = $rows['db15064'];
                $db15069_address_county = $rows['db15069'];
                $db15068_address_town = $rows['db15068'];
                $db15067_address1 = $rows['db15067'];
                $db15070_country = $rows['db15070'];
                $db15071_postcode = $rows['db15071'];
                $db15073_contact_telephone = $rows['db15073'];
                $db15075_emergency_first_name = $rows['db15075'];
                $db15076_emergency_last_name = $rows['db15076'];
                $db15077_emergency_relationship = $rows['db15077'];
                $db15078_emergency_email_address = $rows['db15078'];
                $db15079_emergency_telephone = $rows['db15079'];
                $db15086_terms_and_conditions = $rows['db15086'];
                $db15083_learning_disability = $rows['db15083'];
                $db15084_learning_disability_type = $rows['db15084'];
                $db15085_hear_about_us = $rows['db15085'];
                $db15081_qualifications = $rows['db15081'];
                $db19347_invoice_fao = $rows['db19347'];
                $db19348_invoice_address1 = $rows['db19348'];
                $db19349_invoice_town = $rows['db19349'];
                $db19350_invoice_county = $rows['db19350'];
                $db19351_invoice_country = $rows['db19351'];
                $db19352_invoice_postcode = $rows['db19352'];
                $db15439_invoice_address = $rows['db15439'];

            }
        } else {
            //get the values from the student
            // Get info about student
            list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake, $core_students_deleted, $core_student_application_ucas, $core_student_ucas_pass) = get_core_students($_SESSION['student_id']);


            $db15054_first_name = $core_students_first_name;
            $db15055_last_name = $core_students_surname;
            $db15056_email_address = $core_students_email_address;
            $db15057_gender = $core_students_gender;
            $db15058_date_of_birth = $core_students_date_of_birth;
            $db15062_country_of_origin = $core_students_country_of_origin;
            $db15073_contact_telephone = $core_students_telephone_number;

        }//end else
    }


    $sql_hear_about_us = "SELECT id as 'manage', db16997 as 'name' FROM core_hear_about_us WHERE (rec_archive IS NULL OR rec_archive = '') AND usergroup='$_SESSION[access]' AND db16999='yes' ORDER BY id desc LIMIT 0, 1000";
    dev_debug($sql_hear_about_us);
    $sth = $dbh->prepare($sql_hear_about_us);
    $sth->execute();
    $hear_about_us_results = $sth->fetchAll(PDO::FETCH_ASSOC);

    ?>

    <div class="row">
        <link href="<?= engine_url ?>/css/theme_controller.css" rel="stylesheet">
        <script src="<?= baseline_url ?>/<?= front_header_file_location ?>/js/jquery-1.10.2.min.js"
                type="text/javascript" charset="utf-8"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.9.2/jquery-ui.min.js" type="text/javascript"
                charset="utf-8"></script>
        <script type="text/javascript" language="JavaScript"
                src="<?= engine_url ?>/js/pop_msg.js"></script>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.4/js/bootstrap.min.js" type="text/javascript"
                charset="utf-8"></script>
        <script src="<?= engine_url ?>/js/global.js"></script>
        <script src="<?= baseline_url ?>/<?= front_header_file_location ?>/js/form_check.js"></script>
        <script>
            setTimeout(function () {
                $('#img_processing').fadeOut('fast');
                $('.img_processing').fadeOut('fast');
                $('.entry_success').fadeOut('fast');
            }, 5000); // <-- time in milliseconds

            $(function () {
                //testing
                var activeTab = null;
                $('a[data-toggle="tab"]').on('show', function (e) {
                    activeTab = e.target;
                })
            });

        </script>
        <script>
            // function used to produce page breaks
            $(function () {
                // jump to first tab initially
                $('.tabbable li > a:first').tab('show');

                var tabhist = [],
                    curtab = $('.tabbable li.active').attr('id'),
                    percentage_done = 0,
                    // count all the mandatory fields
                    req_count = $('.form-group .required').length;

                $('#button').toggle($('.tabbable li.active').next('li').length === 0);

                $('#prevtab').click(function () {
                    // pop one from history and show it
                    curtab = tabhist.pop() || curtab;
                    $('#' + curtab + ' > a').tab('show');
                    // update tabcounter
                    $('[name=tabcounter]').val(tabhist.length);

                    // check if the new tab is last and possibly show submit button
                    $('#button').toggle($('.tabbable li.active').next('li').length === 0);
                    $('#nexttab').toggle($('.tabbable li.active').next('li').length !== 0);
                });

                $('#nexttab').click(function () {
                    // do not allow proceeding if not all required fields are filled
                    var active_req_count = $('.tab-pane.active .form-group .required').length,
                        active_req_filled = $('.tab-pane.active .form-group .required.required-filled').length;
                    if (active_req_count !== active_req_filled) {
                        alert('Please fill all the required fields!');
                    } else {
                        var active_tab = $('.tabbable li.active a'),
                            decisions_el = $(active_tab.attr('href') + ' > .decision-data'),
                            // grab decision cases
                            decisions = decisions_el.data('decisions') || {},
                            // what is the decision making field here?
                            decisions_field = decisions_el.data('decisions-field'),
                            // where should we switch to?
                            eltype = $('[name="' + decisions_field + '"]').attr("type"),
                            selector = '[name="' + decisions_field + '"]' + ((eltype === 'radio') ? ':checked' : ''),
                            decision = decisions[$(selector).val()];
                        // default can be provided in JSON, if it's not then we can go to next tab
                        decision = decision || decisions['default'] || $('.tabbable li.active').next('li').attr('id');

                        // record our current tab and update to new decision
                        tabhist.push(curtab);
                        $('[name=tabcounter]').val(tabhist.length);
                        curtab = decision;
                        // change the tab
                        $('#' + curtab + ' > a').tab('show');

                        // check if the new tab is last and possibly show submit button
                        $('#button').toggle($('.tabbable li.active').next('li').length === 0);
                        $('#nexttab').toggle($('.tabbable li.active').next('li').length !== 0);
                    }
                });

                if (req_count > 0) {
                    $('.form-group .required').map(function () {
                        var req_element = this;
                        $(this).closest('.form-group').find('input,textarea,select').change(function () {
                            var field_tag = $(this).prop('tagName'),
                                field_type = $(this).attr('type'),
                                field_name = $(this).attr('name'),
                                field_value = $(this).val(),
                                // is the field marked as filled already?
                                is_filled = $(req_element).hasClass('required-filled'),
                                // is the field filled in?
                                field_empty = !field_value;

                            // check all date select fields for "empty" values
                            field_empty = field_empty || (field_name.endsWith('_day') && field_value === '00');
                            field_empty = field_empty || (field_name.endsWith('_month') && field_value === '00');
                            field_empty = field_empty || (field_name.endsWith('_year') && field_value === '0000');
                            field_empty = field_empty || field_value === '0000-00-00';
                            // selects default to "not specified"
                            field_empty = field_empty || (field_tag === 'SELECT' && field_value === 'not specified');
                            // if radio need to find if any of the same-name radios are checked
                            field_empty = field_empty || (field_type === 'radio' && $('[name=' + field_name + ']:checked').length === 0);

                            // add/remove class signifying the field is filled/empty
                            $(req_element).toggleClass('required-filled', !field_empty);

                            percentage_done = Math.ceil(100 * $('.tab-pane.active .form-group .required.required-filled').length / req_count);
                            // update the progress bar
                            $('.progress-bar').css('width', percentage_done + '%')
                                .attr('aria-valuenow', percentage_done)
                                .text(percentage_done + '%');
                        });
                        $(this).closest('.form-group').find('input,textarea,select').change();
                    });
                    $('.progress-bar').css('width', percentage_done + '%')
                        .attr('aria-valuenow', percentage_done)
                        .text(percentage_done + '%');
                }
            });

        </script>
        <script>
            $(function () {

                // THIS SCRIPT ADDS A COUNTER NEXT TO THE FIELDS
                $('.field-counter').on('input', function () {
                    var total_allowed = 4000,
                        val = $(this).val(),
                        count = val.length,
                        remaining = total_allowed - count;
                    if (count > total_allowed) {
                        $(this).val(val.substr(0, total_allowed));
                        remaining = 0;
                    }
                    $('#' + $(this).attr('id') + '-counter').text(remaining + ' characters left');

                });
                //END
            });
        </script>

        <?php
        //check if the default details exist. if not then set some
        if ($db15054_first_name == '') {
            $db15054_first_name = $_SESSION['name'];
        }
        if ($db15055_last_name == '') {
            $db15055_last_name = $_SESSION['surname'];
        };
        if ($db15056_email_address == '') {
            $db15056_email_address = $_SESSION['user'];
        }
        if ($db15058_date_of_birth == '') {
            $db15058_date_of_birth = $_SESSION['applicant_dob'];
        }
        if ($db15073_contact_telephone == '') {
            $db15073_contact_telephone = $_SESSION['applicant_telephone'];
        }
        ?>

        <div id="pageloading"><i class="icon-spinner icon-spin icon-large"></i></div>
        <form class="form-horizontal" role="form"
              action='<?php echo website_url . "/" . front_header_file_location . "/admin/inc_short_course_booking_details.php"; ?>?ref=<?= $booking_username_id ?>&ref1=<?= $booking_detail_username_id ?>'
              method="post" enctype="multipart/form-data" onsubmit="return formCheck_shortcourse_detail(this)">
            <div>
                <input name="option4" type="hidden" value="261">
                <div class="required"><strong>Required fields exist on this form </strong></div>
                <br>
                <div class="tabbable">
                    <div>
                        <input class="form-control" value="<?= $scheduled_course_booking ?>" type="hidden"
                               name="db15052" id="db15052">
                        <div class=""><h2>Attendee details</h2><a name="anc_" id="anc_"></a></div>
                        <input class="form-control" name="reqhid[]" type="hidden" id="reqhid[]"
                               value="db15054_First name(s)">
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                    <span class="required"></span>
                                    First name(s)<a name="anc_db15054" id="anc_db15054"></a></label>
                                <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 "><input class="form-control"
                                                                                                    value="<?= $db15054_first_name ?>"
                                                                                                    type="text"
                                                                                                    maxlength="240"
                                                                                                    name="db15054"
                                                                                                    id="db15054"></div>
                            </div>
                        </div>
                        <input class="form-control" name="reqhid[]" type="hidden" id="reqhid[]"
                               value="db15055_Last Name">
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                    <span class="required"></span>
                                    Last name<a name="anc_db15055" id="anc_db15055"></a></label>
                                <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 "><input class="form-control"
                                                                                                    value="<?= $db15055_last_name ?>"
                                                                                                    type="text"
                                                                                                    maxlength="240"
                                                                                                    name="db15055"
                                                                                                    id="db15055"></div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                    <span class="required"></span>
                                    Email address<a name="anc_db15056" id="anc_db15056"></a></label>
                                <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 "><input class="form-control"
                                                                                                    value="<?= $db15056_email_address ?>"
                                                                                                    type="text"
                                                                                                    maxlength="240"
                                                                                                    name="db15056"
                                                                                                    id="db15056"></div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                    <span class="required"></span>
                                    Gender<a name="anc_db15057" id="anc_db15057"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <select class="form-control" name="db15057">
                                        <option value="not specified">Select....
                                        </option>
                                        <option value="Male" <?php if ($db15057_gender == "Male") {
                                            echo 'selected=selected';
                                        } ?>>Male
                                        </option>
                                        <option value="Female" <?php if ($db15057_gender == "Female") {
                                            echo 'selected=selected';
                                        } ?>>Female
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                    <span class="required"></span>
                                    Date of birth<a name="anc_db15058" id="anc_db15058"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <?= DateDropdown("60", "past", "db15058", $db15058_date_of_birth); ?>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                    <span class="required"></span>
                                    Nationality<a name="anc_db15059" id="anc_db15059"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 "><select class="form-control" name="db15059">
                                        <option value="not specified">Select....</option>
                                        <?= country_list($db15059_nationality); ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                    <span class="required"></span>
                                    Passport number<a name="anc_db15060" id="anc_db15060"></a></label>
                                <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                    <input class="form-control" value="<?= $db15060_passport_number ?>" type="text"
                                           maxlength="240" name="db15060" id="db15060">
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                    <span class="required"></span>
                                    Pasport expiry date<a name="anc_db15061" id="anc_db15061"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">

                                    <?= DateDropdown("30", "future", "db15061", $db15061_passport_expiry); ?>

                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                    <span class="required"></span>
                                    Country of origin<a name="anc_db15062" id="anc_db15062"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 "><select class="form-control" name="db15062">
                                        <option value="not specified">Select....</option>
                                        <?= country_list($db15062_country_of_origin); ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                    <span class="required"></span>
                                    Country of permanent residence<a name="anc_db15063" id="anc_db15063"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">
                                    <select class="form-control" name="db15063">
                                        <option value="not specified">Select....</option>
                                        <?= country_list($db15063_country_of_permanent_residence); ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div class="form-group">
                                <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                    <span class="required"></span>
                                    Passport upload: (upload scan of passport)<a name="anc_db15064"
                                                                                 id="anc_db15064"></a></label>
                                <div class="col-md-7 col-xs-12 col-sm-6 ">Upload new file<br><input class="form-control"
                                                                                                    name="uploadFile[]"
                                                                                                    id="uploadFile"
                                                                                                    type="file"
                                                                                                    onchange="UpdateUploadFielddb15064(this,'db15064')"><input
                                            name="uploadsNeeded" type="hidden" value="1">
                                    <input name="db15064" type="hidden" id="db15064"
                                           value="<?= $db15064_passport_upload ?>">
                                    <input name="location_box[]" type="hidden" id="location_boxdb15064"
                                           value="bookings">
                                    <input name="client_box" type="hidden" id="client_box"
                                           value="bookings/<?= $scheduled_course_booking ?>"></div>
                            </div>
                        </div>
                        <script type="text/javascript">
                            function UpdateUploadFielddb15064(which, fld) {
                                if (which.files && which.files[0]) {
                                    var cb = document.getElementById("client_box").value;
                                    var lb = document.getElementById("location_boxdb15064").value;
                                    var target_path = ((cb != "-") ? cb : lb) + "/";

                                    var d = new Date();
                                    var weekdays = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
                                    var fname_split = which.value.split(".");
                                    var extension = fname_split[fname_split.length - 1];

                                    var target_file = weekdays[d.getDay()];
                                    target_file += d.getHours();
                                    target_file += d.getMinutes();
                                    target_file += d.getSeconds();
                                    target_file += "-" + "ABCD".charAt(Math.floor(Math.random() * 4));
                                    target_file += Math.floor(Math.random() * 25) + 1;
                                    target_file += "." + extension;

                                    document.getElementById("location_boxdb15064").value = target_path + target_file;
                                    document.getElementById(fld).value = target_path + target_file;
                                }
                            }
                        </script>
                        <div>
                            <div class="form-group">
                                <div class="alert alert-warning"><i class="fa fa-bell" aria-hidden="true"></i> If you
                                    are applying for an on-campus course and you do not have a UK/EU passport, you must
                                    have the appropriate visa that allows you to study in the UK. This does not apply to
                                    online courses<a name="anc_db15065" id="anc_db15065"></a></div>
                                <input class="form-control" value="" type="hidden" name="db15065" id="db15065">
                            </div>
                        </div>
                        <div class=""><h2>Address</h2><a name="anc_db15066" id="anc_db15066"></a></div>
                        <input class="form-control" value="121" type="hidden" name="db15440" id="db15440"/>
                        <div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        Flat/house number or name and road<a name="anc_db15067"
                                                                             id="anc_db15067"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db15067_address1 ?>" type="text"
                                               maxlength="240" name="db15067" id="db15067">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        Town<a name="anc_db15068" id="anc_db15068"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db15068_address_town ?>" type="text"
                                               maxlength="240" name="db15068" id="db15068">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        County/State<a name="anc_db15069" id="anc_db15069"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db15069_address_county ?>" type="text"
                                               maxlength="240" name="db15069" id="db15069">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        Country<a name="anc_db15070" id="anc_db15070"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db15070_country ?>" type="text"
                                               maxlength="240" name="db15070" id="db15070">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        Post code<a name="anc_db15071" id="anc_db15071"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db15071_postcode ?>" type="text"
                                               maxlength="240" name="db15071" id="db15071">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        Contact telephone number: (with international dialing code)<a name="anc_db15073"
                                                                                                      id="anc_db15073"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db15073_contact_telephone ?>"
                                               type="text" maxlength="240" name="db15073" id="db15073">
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                        <span class="hotspot"
                                              onmouseover="tooltip.show('select if this is the invoice address');"
                                              onmouseout="tooltip.hide();"><img
                                                    src="https://adminlite.heiapply.com/images/info.png" width="16"
                                                    height="16"></span>
                                        Is this your invoice address?<a name="anc_db15440" id="anc_db15440"></a></label>
                                    <div class="col-md-7 col-xs-12 col-sm-6 ">
                                        <input name="db15439" class="radios" type="radio"
                                               value="yes" <?php if ($db15439_invoice_address == 'yes') echo 'checked=checked' ?>>
                                        Yes <br>
                                        <input name="db15439" class="radios" type="radio"
                                               value="no" <?php if ($db15439_invoice_address == 'no') echo 'checked=checked' ?>>
                                        No
                                    </div>
                                </div>
                            </div>
                            <input class="form-control" value="<?= $db19353_invoice_address_record ?>" type="hidden"
                                   name="db19353" id="db19353"/>
                            <script>
                                function rv(el) {
                                    return el.value;
                                }

                                function parentsChange(parentName, childName, activeId) {
                                    var parents = $('[name="' + parentName + '"]');
                                    var values = (parents.toArray().length == 1) ?
                                        parents.toArray().map(rv) :
                                        parents.filter(':checked').toArray().map(rv);
                                    if (activeId.includes("-")) {
                                        activeId = activeId.split("-");
                                        var value_active = false;
                                        $.each(activeId, function (index, value) {
                                            if (values.indexOf(value) > -1) {
                                                value_active = true;
                                            } else {

                                            }
                                        });
                                        if (value_active) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    } else {
                                        if (values.indexOf(activeId) > -1) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').show();
//				$('select[name="'+childName+'"]').closest('div.form-group').show();

                                            // if field is switched on make sure we check if it's filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').hide();
//				$('select[name="'+childName+'"]').closest('div.form-group').hide();

                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    }

                                }

                                $(function () {
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439',
                                            'db19347',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();

                                    // The same version, but if parents have [] in the name
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439[]"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439[]',
                                            'db19347',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();
                                });
                            </script>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  "> Invoice FAO<a
                                                name="anc_db19347" id="anc_db19347"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db19347_invoice_fao ?>" type="text"
                                               maxlength="240" name="db19347" id="db19347"/>
                                    </div>
                                </div>
                            </div>
                            <script>
                                function rv(el) {
                                    return el.value;
                                }

                                function parentsChange(parentName, childName, activeId) {
                                    var parents = $('[name="' + parentName + '"]');
                                    var values = (parents.toArray().length == 1) ?
                                        parents.toArray().map(rv) :
                                        parents.filter(':checked').toArray().map(rv);
                                    if (activeId.includes("-")) {
                                        activeId = activeId.split("-");
                                        var value_active = false;
                                        $.each(activeId, function (index, value) {
                                            if (values.indexOf(value) > -1) {
                                                value_active = true;
                                            } else {

                                            }
                                        });
                                        if (value_active) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    } else {
                                        if (values.indexOf(activeId) > -1) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').show();
//				$('select[name="'+childName+'"]').closest('div.form-group').show();

                                            // if field is switched on make sure we check if it's filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').hide();
//				$('select[name="'+childName+'"]').closest('div.form-group').hide();

                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    }

                                }

                                $(function () {
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439',
                                            'db19348',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();

                                    // The same version, but if parents have [] in the name
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439[]"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439[]',
                                            'db19348',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();
                                });
                            </script>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  "> Invoice Flat/house
                                        number or name and road<a name="anc_db19348" id="anc_db19348"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db19348_invoice_address1 ?>" type="text"
                                               maxlength="240" name="db19348" id="db19348"/>
                                    </div>
                                </div>
                            </div>
                            <script>
                                function rv(el) {
                                    return el.value;
                                }

                                function parentsChange(parentName, childName, activeId) {
                                    var parents = $('[name="' + parentName + '"]');
                                    var values = (parents.toArray().length == 1) ?
                                        parents.toArray().map(rv) :
                                        parents.filter(':checked').toArray().map(rv);
                                    if (activeId.includes("-")) {
                                        activeId = activeId.split("-");
                                        var value_active = false;
                                        $.each(activeId, function (index, value) {
                                            if (values.indexOf(value) > -1) {
                                                value_active = true;
                                            } else {

                                            }
                                        });
                                        if (value_active) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    } else {
                                        if (values.indexOf(activeId) > -1) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').show();
//				$('select[name="'+childName+'"]').closest('div.form-group').show();

                                            // if field is switched on make sure we check if it's filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').hide();
//				$('select[name="'+childName+'"]').closest('div.form-group').hide();

                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    }

                                }

                                $(function () {
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439',
                                            'db19349',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();

                                    // The same version, but if parents have [] in the name
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439[]"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439[]',
                                            'db19349',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();
                                });
                            </script>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  "> Invoice Town<a
                                                name="anc_db19349" id="anc_db19349"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db19349_invoice_town ?>" type="text"
                                               maxlength="240" name="db19349" id="db19349"/>
                                    </div>
                                </div>
                            </div>
                            <script>
                                function rv(el) {
                                    return el.value;
                                }

                                function parentsChange(parentName, childName, activeId) {
                                    var parents = $('[name="' + parentName + '"]');
                                    var values = (parents.toArray().length == 1) ?
                                        parents.toArray().map(rv) :
                                        parents.filter(':checked').toArray().map(rv);
                                    if (activeId.includes("-")) {
                                        activeId = activeId.split("-");
                                        var value_active = false;
                                        $.each(activeId, function (index, value) {
                                            if (values.indexOf(value) > -1) {
                                                value_active = true;
                                            } else {

                                            }
                                        });
                                        if (value_active) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    } else {
                                        if (values.indexOf(activeId) > -1) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').show();
//				$('select[name="'+childName+'"]').closest('div.form-group').show();

                                            // if field is switched on make sure we check if it's filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').hide();
//				$('select[name="'+childName+'"]').closest('div.form-group').hide();

                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    }

                                }

                                $(function () {
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439',
                                            'db19350',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();

                                    // The same version, but if parents have [] in the name
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439[]"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439[]',
                                            'db19350',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();
                                });
                            </script>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  "> Invoice County<a
                                                name="anc_db19350" id="anc_db19350"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db19350_invoice_county ?>" type="text"
                                               maxlength="240" name="db19350" id="db19350"/>
                                    </div>
                                </div>
                            </div>
                            <script>
                                function rv(el) {
                                    return el.value;
                                }

                                function parentsChange(parentName, childName, activeId) {
                                    var parents = $('[name="' + parentName + '"]');
                                    var values = (parents.toArray().length == 1) ?
                                        parents.toArray().map(rv) :
                                        parents.filter(':checked').toArray().map(rv);
                                    if (activeId.includes("-")) {
                                        activeId = activeId.split("-");
                                        var value_active = false;
                                        $.each(activeId, function (index, value) {
                                            if (values.indexOf(value) > -1) {
                                                value_active = true;
                                            } else {

                                            }
                                        });
                                        if (value_active) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    } else {
                                        if (values.indexOf(activeId) > -1) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').show();
//				$('select[name="'+childName+'"]').closest('div.form-group').show();

                                            // if field is switched on make sure we check if it's filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').hide();
//				$('select[name="'+childName+'"]').closest('div.form-group').hide();

                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    }

                                }

                                $(function () {
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439',
                                            'db19351',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();

                                    // The same version, but if parents have [] in the name
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439[]"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439[]',
                                            'db19351',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();
                                });
                            </script>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  "> Invoice Country<a
                                                name="anc_db19351" id="anc_db19351"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db19351_invoice_country ?>" type="text"
                                               maxlength="240" name="db19351" id="db19351"/>
                                    </div>
                                </div>
                            </div>
                            <script>
                                function rv(el) {
                                    return el.value;
                                }

                                function parentsChange(parentName, childName, activeId) {
                                    var parents = $('[name="' + parentName + '"]');
                                    var values = (parents.toArray().length == 1) ?
                                        parents.toArray().map(rv) :
                                        parents.filter(':checked').toArray().map(rv);
                                    if (activeId.includes("-")) {
                                        activeId = activeId.split("-");
                                        var value_active = false;
                                        $.each(activeId, function (index, value) {
                                            if (values.indexOf(value) > -1) {
                                                value_active = true;
                                            } else {

                                            }
                                        });
                                        if (value_active) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    } else {
                                        if (values.indexOf(activeId) > -1) {
                                            $('[name="' + childName + '"]').closest('div.form-group').show();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').show();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').show();
//				$('select[name="'+childName+'"]').closest('div.form-group').show();

                                            // if field is switched on make sure we check if it's filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]').change();
                                        } else {
                                            $('[name="' + childName + '"]').closest('div.form-group').hide();
                                            $('[name="' + childName + '[]"]').closest('div.form-group').hide();
//				$('textarea[name="'+childName+'"]').closest('div.form-group').hide();
//				$('select[name="'+childName+'"]').closest('div.form-group').hide();

                                            // if field is switched off consider it filled
                                            $('[name="' + childName + '"],[name="' + childName + '[]"]')
                                                .closest('div.form-group')
                                                .find('.required')
                                                .addClass('required-filled');
                                        }
                                    }

                                }

                                $(function () {
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439',
                                            'db19352',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();

                                    // The same version, but if parents have [] in the name
                                    // Parent(s) of current child element
                                    var parents = $('[name="db15439[]"]');
                                    // Bind to change event
                                    parents.change(function () {
                                        parentsChange('db15439[]',
                                            'db19352',
                                            'no');
                                    });
                                    // Trigger change event initially to have the correct
                                    // initial state
                                    parents.change();
                                });
                            </script>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  "> Invoice Post Code<a
                                                name="anc_db19352" id="anc_db19352"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 ">
                                        <input class="form-control" value="<?= $db19352_invoice_postcode ?>" type="text"
                                               maxlength="240" name="db19352" id="db19352"/>
                                    </div>
                                </div>
                            </div>


                            <div class=""><h2>Emergency contact / Next of kin details</h2><a name="anc_db15072"
                                                                                             id="anc_db15072"></a></div>

                            <div>
                                <div class="form-group">
                                    <input class="form-control" value="" type="hidden" name="db15074" id="db15074">
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        First name(s)<a name="anc_db15075" id="anc_db15075"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 "><input
                                                class="form-control" value="<?= $db15075_emergency_first_name ?>"
                                                type="text" maxlength="240" name="db15075" id="db15075"></div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        Last name<a name="anc_db15076" id="anc_db15076"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 "><input
                                                class="form-control" value="<?= $db15076_emergency_last_name ?>"
                                                type="text" maxlength="240" name="db15076" id="db15076"></div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        Relationship to you<a name="anc_db15077" id="anc_db15077"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 "><input
                                                class="form-control" value="<?= $db15077_emergency_relationship ?>"
                                                type="text" maxlength="240" name="db15077" id="db15077"></div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        Email Address<a name="anc_db15078" id="anc_db15078"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 "><input
                                                class="form-control" value="<?= $db15078_emergency_email_address ?>"
                                                type="text" maxlength="240" name="db15078" id="db15078"></div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label  ">
                                        <span class="required"></span>
                                        Contact telephone number: (with international dialing code)<a name="anc_db15079"
                                                                                                      id="anc_db15079"></a></label>
                                    <div class="col-xs-12 col-sm-6 col-md-7 col-xs-12 col-sm-6 "><input
                                                class="form-control" value="<?= $db15079_emergency_telephone ?>"
                                                type="text" maxlength="240" name="db15079" id="db15079"></div>
                                </div>
                            </div>
                            <div class=""><h2>Qualifications</h2><a name="anc_db15080" id="anc_db15080"></a></div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                        <span class="required"></span>
                                        Language qualifications<a name="anc_db15081" id="anc_db15081"></a></label>
                                    <div class="checklist col-md-7 col-xs-12 col-sm-6">
                                        <ul>
                                            <?php // get the selected
                                            $db15081_qualifications_array = explode(",", $db15081_qualifications);
                                            if (in_array("I have a degree taught in English", $db15081_qualifications_array)) {
                                                echo '<li><input class="checkbox" name="db15081[]" id="db15081[]" value="I have a degree taught in English" type="checkbox"  checked="checked"> I have a degree taught in English</li>';
                                            } else {
                                                echo '<li><input class="checkbox" name="db15081[]" id="db15081[]" value="I have a degree taught in English" type="checkbox"> I have a degree taught in English</li>';
                                            }
                                            if (in_array("I am a national of an English speaking country", $db15081_qualifications_array)) {
                                                echo '<li><input class="checkbox" name="db15081[]" id="db15081[]" value="I am a national of an English speaking country" type="checkbox"  checked="checked"> I am a national of an English speaking country</li>';
                                            } else {
                                                echo '<li><input class="checkbox" name="db15081[]" id="db15081[]" value="I am a national of an English speaking country" type="checkbox">I am a national of an English speaking country</li>';
                                            }
                                            if (in_array("I have taken an English language exam", $db15081_qualifications_array)) {
                                                echo '<li><input class="checkbox" name="db15081[]" id="db15081[]" value="I have taken an English language exam" type="checkbox"  checked="checked"> I have taken an English language exam</li>';
                                            } else {
                                                echo '<li><input class="checkbox" name="db15081[]" id="db15081[]" value="I have taken an English language exam" type="checkbox"> I have taken an English language exam</li>';
                                            }
                                            ?>

                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class=""><h2>Learning difficulties or disabilities</h2><a name="anc_db15082"
                                                                                           id="anc_db15082"></a></div>
                            <!--<div>
                                <div class="form-group">
                                    <div class="">Learning difficulties or disabilities<a name="anc_db15082" id="anc_db15082"></a></div>
                                    <input class="form-control" value="" type="hidden" name="db15082" id="db15082">
                                </div>
                            </div>-->
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                        <span class="required"></span>
                                        Do you have a learning disability?<a name="anc_db15083"
                                                                             id="anc_db15083"></a></label>
                                    <div class="col-md-7 col-xs-12 col-sm-6 ">
                                        <input name="db15083" class="radios" type="radio"
                                               value="yes" <?php if ($db15083_learning_disability == 'yes') {
                                            echo 'checked=checked';
                                        } ?>> Yes <br>
                                        <input name="db15083" class="radios" type="radio"
                                               value="no" <?php if ($db15083_learning_disability == 'no') {
                                            echo 'checked=checked';
                                        } ?>> No
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label ">Please indicate the type
                                        of learning disability
                                        <span class="pull-right text-warning" id="db15084-counter"></span>
                                        <a name="anc_db15084" id="anc_db15084"></a></label>
                                    <div class="col-md-7 col-xs-12 col-sm-6 ">
                                        <textarea type="text" name="db15084" id="db15084" size="db15084"
                                                  class="form-control field-counter"
                                                  rows="3"><?= $db15084_learning_disability_type ?></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class=""><h2>Declaration</h2><a name="anc_" id="anc_"></a></div>
                            <div>
                                <div class="form-group">
                                    <label class="col-md-5 col-xs-12 col-sm-6 control-label ">
                                        <span class="required"></span>
                                        How did you hear about the college?<a name="anc_db15085"
                                                                              id="anc_db15085"></a></label>
                                    <div class="col-md-7 col-xs-12 col-sm-6 ">
                                        <select class="form-control" name="db15085">
                                            <option value="not specified">Select....</option>
                                            <?php foreach ($hear_about_us_results as $hear_about_us_row) {
                                                $value = $hear_about_us_row['manage'];
                                                $name = $hear_about_us_row['name'];
                                                $selected = '';
                                                if ($db15085_hear_about_us == $value) {
                                                    $selected = "selected=selected";
                                                }
                                                echo "<option value='$value' $selected>$name</option>";
                                            } ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <input class="form-control" name="reqhid[]" type="hidden" id="reqhid[]"
                                   value="db15086_I declare that the information I have provided is correct and accurate to the best of my knowledge at the time of completing this application and that I have read the Terms and Conditions ">
                            <div>
                                <div class="form-group">
                                    <div class="col-md-12 col-xs-12 col-sm-12 ">

                                        <label class="">
                                            <input class="checkbox" style="margin-top: 10px;" name="db15086"
                                                   id="db15086" type="checkbox">
                                            <span class="required required-filled"></span>
                                            I declare that the information I have provided is correct and accurate to
                                            the best of my knowledge, at the time of completing this application and
                                            that I have read the Terms and Conditions <a name="anc_db15086"
                                                                                         id="anc_db15086"></a>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12 text-center">
                        <span style="padding-top:5px; color:#333; font-size:12px">Click this button to save your information &gt;&gt;</span>
                        <br><br>
                        <?php if ($_SESSION['scheduled_course_booking_detail'] && $_SESSION['scheduled_course_booking_detail'] != '') { ?>
                            <button type="Submit" name="Submit" class="btn btn-info"> Save</button>
                        <?php } else { ?>
                            <button type="Submit" name="Submit" class="btn btn-info"> Save & Close</button>
                        <?php } ?>
                        <input name="process" type="hidden" value="1">
                        <input name="tabcounter" type="hidden" value=""><br><br>
                    </div>
                </div>
        </form>

    </div>

</div>
