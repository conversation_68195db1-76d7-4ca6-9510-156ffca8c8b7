	<?php
$all_my_applications = explode(",",$application_ids_short);

if(empty($all_my_applications)){
	echo "<div class=\"text-center\">You do not have any short courses booked<br/></div>";
}
	
//start appli loop
$iloop=1;	
foreach ($all_my_applications as $application_form) {
    
 $dbh = get_dbh();
 $usergroups = usergroups_management();
 				
  // get all short_course details for this course

    $sql = "SELECT sis_scheduled_booking.id as 'manage',
	if(sis_scheduled_booking.date,sis_scheduled_booking.date,'') as date,
	sis_scheduled_booking.username_id as 'booking_unique_name',
	db14984 as 'payment_type', 
    core_students.db889 as course_id,
    core_students.id as student_id,
    core_students.db41 as core_student_status_id,
	if(db14982,db14982,(select db232 FROM core_courses WHERE core_courses.id=core_students.db889)) AS 'title',
	if(db14978,DATE_FORMAT(db14978,'%d/%m/%Y'),'') as 'start_date', 
	db14979 as 'status', db15104 as 'price', db15111 as'deposit', 
	(SELECT db14959 FROM sis_course_schedule WHERE sis_course_schedule.id = db14977) AS 'course_status',
	(SELECT db20166 FROM sis_course_schedule WHERE sis_course_schedule.id = db14977) as 'course_code',
	(SELECT (SELECT db14963 FROM sis_course_venues WHERE id = sis_course_schedule.db14954 = sis_course_venues.id) FROM sis_course_schedule WHERE sis_course_schedule.id =db14977) as 'venue', 
(SELECT count(*) FROM sis_sched_booking_detail WHERE core_students.usergroup = '$_SESSION[usergroup]' AND (rec_archive is null or rec_archive ='') AND rel_id = sis_scheduled_booking.id ) as 'bookings', 
db15438 AS 'invoice_no' 
FROM core_students LEFT JOIN
    sis_scheduled_booking ON core_students.id=sis_scheduled_booking.rel_id
WHERE core_students.usergroup = '$_SESSION[usergroup]' AND (sis_scheduled_booking.rec_archive is null or sis_scheduled_booking.rec_archive ='') 
AND core_students.id  IN ($application_form) ORDER BY sis_scheduled_booking.date DESC";

    dev_debug ($sql);
	##echo $sql;
	//echo  "<br/>ahshshhsh = ".	$_SESSION['student_id'];
    $sth = $dbh->prepare($sql);
    $sth->execute();
    $results = $sth->fetchAll(PDO::FETCH_ASSOC);
	
	$num=$sth->rowCount();
		
    foreach ($results AS $rows){

        // echo '<pre>';
        // print_r($sql);
        // echo '</pre>';
        // exit();

        $uid = $rows['manage'];
        $overall_price = $rows['price'] * $rows['bookings'];
        $overall_deposit = $rows['deposit'] * $rows['bookings'];
        $reference = $rows['course_code'].'/'.$uid;
		$invoice_number_value = $rows['invoice_no'];
		
		//calcl status
		if ($rows['status'] >= 3 || $rows['payment_type'] =="Pay on invoice") {
                    switch ($rows['status']) {
                        case 3:
                            $actual_status =  'Payment agreed';
                            break;
                        case 4:
                            $actual_status =  "Deposit paid";
                            break;
                        case 5:
                            $actual_status =  "Instalment paid";
                            break;
                        case 6:
                            $actual_status =  "Paid in Full";
                            break;
                    }

                } else if ($rows['bookings'] < 1) {
                    $actual_status =  'Awaiting applicant details';
                } else {
                    $actual_status =  'Awaiting payment';
                }
		
?>
  
  <div class="row course_list">
  
      <div class="col-sm-8 col-xs-12">    
		  <div style="padding:0 10px">
          <p class="course_title">
			<i class="fa fa-angle-down btn_size" id="chev_sh1<?=$iloop_sh?>"></i>
			<i class="fa fa-angle-up btn_size" id="chev_sh2<?=$iloop_sh?>" style="display:none"></i>
            <a href="#" id="more_sh<?=$iloop?>"> <?=ucfirst($rows['title'])?> <?=$rows['start_date']?" ($rows[start_date]) ":''?></a>
          </p>
          </div>
	  </div>
        
    <div class="col-sm-4 col-xs-12">    
    

    <!--===============---->
                <?php 
                ////if application withdrawn thenno action
                if($rows['core_student_status_id']==="-1"){
                ?>
                <a href="#" id="" class="btn btn-muted btn-block btn-sm">Application Withdrawn</a>
                <?php }else{ ?>
					<?php
					//stop those who have not paid from gping forward if the course if full
					if($rows['course_status']==''){
					?>
					<a href="#" id="<?php echo $rows['title'];?>" student_id="<?php echo $application_form;?>" course_link="<?php echo $rows['course_id'];?>" class="btn btn-custom btn-block btn-sm short_course_booking_link">Select A Date & Book On</a>
					<?php
					//stop those who have not paid from gping forward if the course if full
					}elseif($rows['course_status']==3){
					?>
					<?php if ($rows['bookings'] < 1) { ?>
					
                    <a
                            href="shortcourses_detail?ref=<?= $rows['booking_unique_name'] ?>&ref1=" id="button"
                            title="Add applicant details" class="btn btn-info btn-sm"><i class="fa fa-user"></i> Add applicant details</a>


                <?php } else {	
				

////////start discount code area

					echo 'Have a discount coupon? 
					<form method="POST" action="">
					<div class="input-group">
					  <input type="text" name="coupon" class="form-control" placeholder="enter coupon">
					  <span class="input-group-btn">
						<button class="btn btn-info btn-sm" type="submit" style="margin: 0;">Redeem!</button>
					  </span>
					</div>
					</form>';

					if($_POST['coupon']){
						$coupon = sanitise($_POST['coupon']);
						
						$check_coupon_exists_valid_v1 = pull_field("lead_coupons inner join ols_coupons ON ols_coupons.id = lead_coupons.db41647","concat_ws('|',db21962,db21965,db21964)","WHERE db21962 = '$coupon' AND DATE(NOW()) > db41648 AND DATE(NOW()) < db41649 AND db41651 = '$rows[course_id]' AND lead_coupons.usergroup = '$_SESSION[usergroup]' ");
						
						$check_coupon_exists_valid = explode("|",$check_coupon_exists_valid_v1);
						
						$check_coupon_code = $check_coupon_exists_valid[0];//code
						$check_coupon_amount = $check_coupon_exists_valid[1];//amount
						$check_coupon_type = $check_coupon_exists_valid[2];//type
						
						if($check_coupon_code == $coupon){
							
							$check_coupon_usage = pull_field("lead_coupon_usage_log","count(id)","WHERE db41653 = '$coupon' and rel_id='$application_form' AND usergroup = '$_SESSION[usergroup]' ");
							if($check_coupon_usage<1){
								
								//calculate discount
								if($check_coupon_type=='Percentage'){
									$discount_amount = ($overall_price * $check_coupon_amount)/100;
								}else{
									$discount_amount = $check_coupon_amount;
								}
								
								//influence the overall price
								$overall_price = $overall_price - $discount_amount;
								
								$sql = "INSERT INTO lead_coupon_usage_log (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db41653, db41654, db41655,db41656) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$application_form', CURRENT_TIMESTAMP(), '1','$coupon','$check_coupon_amount','$check_coupon_type','$rows[course_id]')";
								dev_debug($sql);
								$sth = $dbh->prepare($sql);
								$sth->execute();
								$sql='';//reset
								
								//update invoice
								#$sql = "UPDATE lead_invoice_settings SET db14999 = '$check_coupon_amount' WHERE id = '$invoice_number_value' ";
                               # echo "UPDATE Invoice SQL".$sql;
                               ## $sth = $dbh->prepare($sql);
                                ##$sth->execute();								
								echo "<div class=\"alert alert-success\">Coupon accepted and applied to your invoice!</div>";	
								echo '<META HTTP-EQUIV="refresh" content="2;">';
							}else{
								echo "<div class=\"alert alert-success\">Sorry this coupon has already been used</div>";	
							}
						}else{
							echo "<div class=\"alert alert-danger\">Coupon validation failed</div>";
						}
								
					}else{
					
					//origional full amount
					$og_overall_price = $overall_price;
					//calculate discount where coupon already used
					$check_used_state = pull_field("lead_coupon_usage_log","concat_ws('|',db41653|db41654)","WHERE rel_id='$application_form' AND usergroup = '$_SESSION[usergroup]' AND db41656 = '$rows[course_id]' ");
					
						$check_coupon_exists_valids = explode("|",$check_used_state);
						
						$check_coupon_amount = $check_coupon_exists_valids[0];//amount
						$check_coupon_type = $check_coupon_exists_valids[1];//type
					
							    if($check_coupon_type=='Percentage'){
									$discount_amount = ($overall_price * $check_coupon_amount)/100;
								}else{
									$discount_amount = $check_coupon_amount;
								}
								
						//influence the overall price
						$overall_price = $overall_price - $discount_amount;
						
						if($og_overall_price!==$overall_price){
							echo "<small>(£".$discount_amount." Discount has been applied)</small>";	
						}
						
					}//end coupon post end
///////end discount code area
					
                    $student_username_id = pull_field("core_students", "username_id", "WHERE id='$application_form'");
                    $user_username_id = pull_field("form_users", "username_id", "WHERE id='$_SESSION[uid]'");
                    if ($rows['status'] > '2') {//have paid something

                        if ($rows['payment_type'] =='Pay deposit and instalments' ) { //payment has been made
                            //we have made some payment
                            //get the invoice number
                            $invoice_id = $rows['invoice_no'];

                            //get the invoice_settings
                            // FUNCTION TO GET_LEAD_INVOICE_SETTINGS
                            $lead_invoice_settings_deposit_value =  pull_field("lead_invoice_settings","db15003","WHERE id =$invoice_id");

                            $lead_invoice_username_id = pull_field("lead_invoice_settings","username_id","WHERE id =$invoice_id");
                            $lead_invoice_settings_number_of_instalments_allowed = pull_field("lead_invoice_settings","db14996","WHERE id =$invoice_id");

                            $deposit_and_installments = 0;
                            //is there a deposit
                            $deposit_paid=false;
                            if ($lead_invoice_settings_deposit_value &&  $lead_invoice_settings_deposit_value > 0) {
                                $deposit_and_installments++;
                                $deposit_paid=true;
                            }
                            $deposit_and_installments +=$lead_invoice_settings_number_of_instalments_allowed;
                            //echo "HERE**".$lead_invoice_settings_number_of_instalments_allowed."**".$deposit_and_installments."**".$invoice_id;
                            //get the number of payments made\
                            //$sql= "SELECT count(*) from sis_student_fees WHERE rel_id=$_SESSION[student_id] and db1494='$lead_invoice_username_id'";
                            $sql= "SELECT count(*) from sis_student_fees WHERE rel_id=$application_form and db1494='$lead_invoice_username_id'";
                            //echo "HERE1**".$sql;
                            $sth = $dbh->prepare($sql);
                            $sth->execute();
                            $number_of_payments_made = $sth->fetchColumn();

                            if ($number_of_payments_made < $deposit_and_installments ) {
                                //echo "HERE2**".$number_of_payments_made;
                                if ($deposit_paid==true) {

                                    $installment_to_pay = $number_of_payments_made;
                                }
                                else {
                                    $installment_to_pay = $number_of_payments_made + 1;
                                }
                                ?>


                                <form action="https://secure.worldpay.com/wcc/purchase" method=POST>
                                    <!--<input type="hidden" name="testMode" value="100">-->
                                    <!-- This next line contains the testMode parameter - it specifies that the submission is a test submission -->
                                    <input type="hidden" name="name" value="AUTHORISED">
                                    <!-- This next line contains a mandatory parameter. Put your Installation ID inside the quotes after value= -->
                                    <input type="hidden" name="instId" value="276728">
                                    <input type="hidden" name = "accId1" value="CONDENASTPUBM1">
                                    <input type="hidden" name="MC_callback"
                                           value="<?=website_url?>/worldpay_callback.php">

                                    <!-- Another mandatory parameter. Put your own reference identifier for the item purchased inside the quotes after value= -->
                                    <input type="hidden" name="cartId" value="<?= $reference ?>">
                                    <input type="hidden" name="MC_student_id" value="<?=$student_username_id ?>">
                                    <input type="hidden" name="MC_uid" value="<?=$user_username_id ?>">
                                    <input type="hidden" name="MC_access" value="<?= session_info('access') ?>">
                                    <input type="hidden" name="MC_course_type" value="short">

                                    <?php $amount = number_format(($overall_price - $overall_deposit) / $lead_invoice_settings_number_of_instalments_allowed,2);

                                    $button_title = ' Pay instalment '. $installment_to_pay.' £'.$amount.' now'; ?>
                                    <!-- Another mandatory parameter. Put the total cost of the item inside the quotes after value= -->
                                    <input type="hidden" name="amount" value="<?= $amount ?>">
                                    <input type="hidden" name="MC_my_payment_type" value="Instalment <?=$installment_to_pay?>">
                                    <input type="hidden" name="MC_my_book_ref" value="<?= $rows['booking_unique_name'] ?>">
                                    <input type="hidden" name="MC_payment_type" value="<?=$rows['payment_type'] ?>">
                                    <input type="hidden" name="MC_whoami" value="<?= session_info('subdomain') ?>">
                                    <input type="hidden" name="MC_slogan" value='<?= pull_field("form_schools","db1093","WHERE id = $_SESSION[access]") ?>'>

                                    <input type="hidden" name="MC_website" value="<?=$_SERVER['HTTP_HOST']?>">
                                    <input type="hidden" name="MC_password" value="<?= md5('actionstarterpassword') ?>">
                                    <!-- Another mandatory parameter. Put the code for the purchase currency inside the quotes after value= -->
                                    <input type="hidden" name="currency" value="GBP">

                                    <!-- This creates the button. When it is selected in the browser, the form submits the purchase details to us. -->
                                    <!--        <input type="submit" class="btn btn-info btn-sm" value=" Make Payment">-->
                                    <button type="submit" class="btn btn-info btn-sm" title="<?=$button_title?>"><i
                                            class="fa fa-credit-card"></i><?= $button_title?></button>

                                </form>

                            <?php }
                        }
                        $invoice_username_id = pull_field("lead_invoice_settings","username_id","WHERE id =".$rows['invoice_no']);
                        $url = "invoice/".$invoice_username_id;
                        //show the invoice button
                        ?>

                        <a href=<?=$url?> id="button" target="_blank" title="View Invoice" class="btn btn-info btn-sm"><i class="fa fa-user"></i> View Invoice</a>

                    <?php } else {

                        if ($rows['payment_type'] != 'Pay on invoice') { ?>

                        <!-- The first line of code specifies the URL for our test environment.-->

                            <form action="https://secure.worldpay.com/wcc/purchase" method=POST>

                                <!-- This next line contains the testMode parameter - it specifies that the submission is a test submission -->
                                <!--<input type="hidden" name="testMode" value="100">-->
                                <!-- This next line contains a mandatory parameter. Put your Installation ID inside the quotes after value= -->
                                    <input type="hidden" name="instId" value="276728">
                                    <input type="hidden" name = "accId1" value="CONDENASTPUBM1">
                                <input type="hidden" name="MC_callback"
                                       value="<?=website_url?>/worldpay_callback.php">

                                <!-- Another mandatory parameter. Put your own reference identifier for the item purchased inside the quotes after value= -->
                                <input type="hidden" name="cartId" value="<?= $reference ?>">
                                <input type="hidden" name="MC_student_id" value="<?=$student_username_id ?>">
                                <input type="hidden" name="MC_uid" value="<?=$user_username_id ?>">
                                <input type="hidden" name="MC_access" value="<?= session_info('access') ?>">
                                <input type="hidden" name="MC_course_type" value="short">
                                <input type="hidden" name="MC_payment_type" value="<?=$rows['payment_type']?>">
                                <input type="hidden" name="MC_website" value="<?=$_SERVER['HTTP_HOST']?>">
                                <input type="hidden" name="MC_whoami" value="<?= session_info('subdomain') ?>">
                                <input type="hidden" name="MC_slogan" value='<?= pull_field("form_schools","db1093","WHERE id = $_SESSION[access]") ?>'>

                                <?php if (!$rows['payment_type'] || $rows['payment_type'] == '') {
                                    $rows['payment_type']='Pay all now';
                                }?>
                                <?php if ($rows['payment_type'] == 'Pay all now') {
                                    $button_title = ' Pay total £'.number_format($overall_price,2).' now'; ?>
                                    <!-- Another mandatory parameter. Put the total cos of the item inside the quotes after value= -->
                                    <input type="hidden" name="amount" value="<?= $overall_price ?>">
                                    <input type="hidden" name="MC_my_payment_type" value="All">

                                <?php } ?>
                                <?php if ($rows['payment_type'] == 'Pay deposit and instalments') {
                                    $button_title = ' Pay deposit £'.number_format($overall_deposit,2).' now'; ?>
                                    <!-- Another mandatory parameter. Put the total cost of the item inside the quotes after value= -->
                                    <input type="hidden" name="amount" value="<?= $overall_deposit ?>">
                                    <input type="hidden" name="MC_my_payment_type" value="Deposit">
                                <?php } ?>
                                <input type="hidden" name="MC_my_book_ref" value="<?= $rows['booking_unique_name'] ?>">
                                <!-- Another mandatory parameter. Put the code for the purchase currency inside the quotes after value= -->
                                <input type="hidden" name="currency" value="GBP">
                                <input type="hidden" name="MC_password" value="<?= md5('actionstarterpassword') ?>">
                                <!-- This creates the button. When it is selected in the browser, the form submits the purchase details to us. -->
                                <!--        <input type="submit" class="btn btn-info btn-sm" value=" Make Payment">-->
                                <button type="submit" class="btn btn-info btn-sm" title="<?=$button_title?>"><i
                                        class="fa fa-credit-card"></i><?= $button_title?></button>

                            </form>

                        <?php } else {
                            //pay by invoice - need to create the invoice
                            $dbh = get_dbh();

                            $scheduled_course_id = pull_field('sis_scheduled_booking', 'db14977',"WHERE username_id = '$rows[booking_unique_name]'");
                            $sql = "SELECT * FROM sis_course_schedule WHERE id = $scheduled_course_id";

                            $sth = $dbh->prepare($sql);
                            $sth->execute();
                            $scheduled_course = $sth->fetch(PDO::FETCH_ASSOC);

                            $sql = "SELECT *, DATE_FORMAT(db14978,'%d/%m/%Y') as 'start_date' FROM sis_scheduled_booking WHERE username_id = '$rows[booking_unique_name]'";
                            $sth = $dbh->prepare($sql);
                            $sth->execute();
                            $scheduled_booking = $sth->fetch(PDO::FETCH_ASSOC);

                            $invoice_settings_id = $scheduled_booking['db15438'];
                            if (!$invoice_settings_id || $invoice_settings_id = '' ) {

                                //get lead_preferences

                                $sql_preferences = "SELECT * FROM lead_preferences WHERE usergroup = '$_SESSION[usergroup]'";
                                dev_debug($sql_preferences);
                                $sql = $dbh->prepare($sql_preferences);
                                $sql->execute();
                                $pdo_preferences_rows = $sql->fetchAll(PDO::FETCH_ASSOC);
                                //$pdo_preferences_rows = $sql->fetch();
                                $pdo_preferences = $pdo_preferences_rows[0];

                                $no_of_days_after_which_invoice_is_due = $pdo_preferences['db16585'];

                                //create the invoice-setting record
                                $db14987_currency = $scheduled_course['db15435'];
                                //$db14988_issue_date = now();
                                //$db14989_due_date = $scheduled_course[''];
                                $db14990_reminder_date = '';
                                $db14991_office_address = pull_field('lead_company_office', 'id', "WHERE (rec_archive is NULL or rec_archive='') AND usergroup=$_SESSION[usergroup]");
                                $db14992_recipient_type = 'Applicant';
                                $db14993_recipient = $application_form;//$_SESSION['student_id'];
                                $db14995_payment_option = 'Bank Transfer,Cash,Cheque,Pay online';
                                $db14996_number_of_installments_allowed = $scheduled_course['db15432'];
                                $db14997_delivery_option = $scheduled_course['db15433'];
                                $db14998_include_vat = $scheduled_course['db15434'];
                                $db14999_discount_amount = '';
                                $db15000_comment = '';
                                $db15002_deposit = 'amount';
                                $db15003_value = $scheduled_course['db15110'];
                                $db14989_due_date = '';
                                if ($no_of_days_after_which_invoice_is_due && $no_of_days_after_which_invoice_is_due != '' && $no_of_days_after_which_invoice_is_due > 0) {
                                    $db14989_due_date = date('Y-m-d', strtotime("+" . $no_of_days_after_which_invoice_is_due . " days"));
                                }
                                $db15005_status = 'approved';//one sets admin buttons on in invoice
                                $db15006_display_total = $scheduled_course['db14951'];
                                $db342693_vat_rate = $pdo_preferences['db15030'];

                                $sql = "INSERT INTO lead_invoice_settings (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id,db14987,db14988,db14989,db14990,db14991,db14992,db14993,db14995,db14996,db14997,db14998,db14999,db15000,db15002,db15003,db15004,db15005,db15006,db342693)VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$_SESSION[student_id]', CURRENT_TIMESTAMP(), '1', '$db14987_currency', CURRENT_TIMESTAMP(), '$db14989_due_date','$db14990_reminder_date','$db14991_office_address','$db14992_recipient_type','$db14993_recipient','$db14995_payment_option','$db14996_number_of_installments_allowed','$db14997_delivery_option','$db14998_include_vat','$db14999_discount_amount','$db15000_comment',CURRENT_TIMESTAMP(),'$db15003_value','$db15004_due_date','$db15005_status','$db15006_display_total','$db342693_vat_rate')";
                                //echo "INSERT INTO INVOICE SQL DEPOSIT".$sql;
                                //echo 'In here1'.$sql;
                                $update_booking = true;
                                $sth = $dbh->prepare($sql);
                                $sth->execute();
                                $invoice_settings_id = $dbh->lastInsertId();
                                // update the booking with the invoice number
                                $invoice_no = $dbh->lastInsertId();
                                $sql = "UPDATE sis_scheduled_booking SET db14979 = '3', db15438 = '$invoice_settings_id' WHERE username_id = '$rows[booking_unique_name]'";
                                //echo "UPDATE BOOKING SQL".$sql;
                                $sth = $dbh->prepare($sql);
                                $sth->execute();

                                //add the invoice item
                                $db15016_title = $scheduled_booking['db14982'] . ' Fees - Start Date '.$scheduled_booking['start_date'];
                                $db15017_description = '';
                                $db15018_category = 'course';
                                $db15019_type = '1';
                                $db15020_quantity = pull_field("sis_sched_booking_detail", "count(*)", "WHERE usergroup=$_SESSION[usergroup] AND (rec_archive is null or rec_archive ='') AND rel_id = $scheduled_booking[id]");
                                $db15021_units = "Fees";
                                $db15022_unit_price = $scheduled_course['db14951'];
                                $db15024_course = $scheduled_course_id;
                                $db15025_start_date = $scheduled_course['db14947'];
                                $db15026_end_date = $scheduled_course['db14949'];

                                $sql = "INSERT INTO lead_invoice_items (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db15016,db15017,db15018,db15019,db15020,db15021,db15022,db15024,db15025,db15026) VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$invoice_settings_id', CURRENT_TIMESTAMP(), '1','$db15016_title','$db15017_description','$db15018_category','$db15019_type','$db15020_quantity','$db15021_units','$db15022_unit_price','$db15024_course','$db15025_start_date','$db15026_end_date')";
                                //echo "INSERT INVOICE ITEM SQL".$sql;
                                $sth = $dbh->prepare($sql);
                                $sth->execute();
                            }
                            $invoice_username_id = pull_field("lead_invoice_settings","username_id","WHERE id =".$invoice_settings_id);
                            $url = "invoice/".$invoice_username_id;
                            //show the invoice button
                            ?>

                            <a href=<?=$url?> id="button" target="_blank" title="View Invoice" class="btn btn-info btn-sm"><i class="fa fa-user"></i> View Invoice</a>


                        <?php }?>

                    <?php }?>
                <?php } ?>

						<?php
						//end of available for booking check
					}else{
						echo "Sorry this course is no longer available for booking";
					}
					?>
			    <?php }// end of withdrawn check ?>
<!--===============---->
    </div>
                
                <div id="extra_sh<?=$iloop?>" style="display:none">
                <div class="col-sm-8 col-xs-12"> 
                
                <?php
				//check if any data exists
				if($rows['start_date']!=='' && $rows['date']!==''){
				?>                
                <table class="table">      
                <tr>
                    <th>Booking Date</th>
                    <td><?=format_date('d/m/Y',$rows['date'])?></td>
                  </tr>
                <tr>
                    <th>Start Date</th>
                    <td><?=$rows['start_date']?></td>
                  </tr>
                <tr>
                    <th>Overall Price</th>
                    <td><?=number_format($overall_price, 2)?></td>
                  </tr>
                </table>
                <?php
				//check if any data exists end
				}elseif($rows['core_student_status_id']==="-1"){
					//show nothing
				}else{
					echo"<p>Awaiting Your Booking</p>";
				}
				?>  
                </div>
                
                <div class="col-sm-4 col-xs-12">
                   <!--<div class="alert <?php //echo ($filled_percentage=="100"?'alert-default':'alert-default') ?> paynow" align="center">



                  </div> -->
                </div>
                </div><!--extra-->
</div><!--row-->
            
		<script>
         // toggle show hide
         $('#more_sh<?=$iloop?>').click(function() {
         $('#extra_sh<?=$iloop?>').slideToggle('fast');
		 $('#chev_sh1<?=$iloop_sh?>').toggle(0);
		 $('#chev_sh2<?=$iloop_sh?>').toggle(0);
        return false;
        });
        </script>

<?php

// loop through end
}
$iloop++;
}//end appli loop
?>


<!--add course popup-->
<script type="text/javascript">
		$(function() {
			$(document).ready(function(){
				$('#short_course_registration,#short_course_edit_details').on('hidden.bs.modal', function (e) {
					location.reload();
				});
				
				$('.short_course_edit_details_link').click(function (e) {
                    //alert ('test');
                    var booking_unique_name = $(this).attr("id");
                    var iframeURL = "<?=$web_url.'/'.$base_client_loc?>/admin/inc_short_course_booking_details.php?ref1=&ref="+booking_unique_name;
                
                    $("#short_course_edit_details iframe").attr("src",iframeURL);
                    $("#short_course_edit_details").modal("show");
                });

                $('.short_course_booking_link').click(function (e) {
                    //alert ('test');
                    var course_name = $(this).attr("id");
                    var student_id = $(this).attr("student_id");
					var course_link = $(this).attr("course_link");
                    var iframeURL = "<?=$web_url.'/'.$base_client_loc?>/admin/inc_short_course_booking_form.php?course_name="+course_name+"&student_id="+student_id+"&course_link="+course_link;
                    $("#short_course_registration iframe").attr("src",iframeURL);
                    $("#short_course_registration").modal("show");
                });
			});
		});
		</script>
		
		<div id="short_course_registration" class="modal fade">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close hide" data-dismiss="modal" aria-hidden="true">×</button>
						<h4 class="modal-title">Book Onto A Short Course</h4>
					</div>
					<div class="modal-body">
					<iframe style='width:100%; height:500px; border:none;' src=''></iframe>
					</div>
				</div>
			</div>
		</div>
        
        <div id="short_course_edit_details" class="modal fade">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
						<h4 class="modal-title">Add/Edit Attendee Details</h4>
					</div>
					<div class="modal-body">
					<iframe style='width:100%; height:500px; border:none;' src=''></iframe>
					</div>
				</div>
			</div>
		</div>