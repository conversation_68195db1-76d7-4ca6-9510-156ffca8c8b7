<?php
	
	if(count($list_applications)<1){
		echo "<div class=\"text-center\">You do not have any long courses booked</div>";
	}
				// loop through kids
				$iloop=1;
					foreach ($list_applications as $application_form) {
						
						//check if long or short start
						if($application_form['course_type']!=='yes'){
						
						//application status
						//application is not submitted if stage is 0 or 10 i.e profile created or profile dormant
						if($application_form["db41"] == 0 || $application_form["db41"] == 10){
							
							$appli_submited_check = 0;
							
						}else{
							$appli_submited_check = 1;
							//get submission date
							
							$get_submission_date = pull_field("dir_stage_tracker","date","WHERE db1142=12 AND rel_id='".$application_form["applicant_id"]."' AND usergroup='$_SESSION[usergroup]' order by id desc LIMIT 1");
							
							#echo "HERE... ".$get_submission_date." ".$application_form["db41"];
						}
						
						list($required_fields_on_dash,$required_filled_on_dash)=filled_percentage($application_form["applicant_id"]);
						$filled_percentage_on_dash = round(100*$required_filled_on_dash/$required_fields_on_dash, 0, PHP_ROUND_HALF_DOWN);
						
						//check if course is still open
						$check_course_status = pull_field("core_courses","db340","WHERE id='".$application_form["db889"]."' AND usergroup='$_SESSION[usergroup]' ");
						
						// box style
						if($filled_percentage_on_dash=="100"){
							$box_status="alert-success";
							$box_progress="progress-bar-success";
						}else{
							$box_status="alert-default";
							$box_progress="progress-bar-custom";
						}
						
						// if programme is off kill all
						if($check_course_status=="off"){
							$box_status="alert-default";
							$box_progress="progress-bar-default";
						}
						//$appli_status = pull_field("core_stages","db642", "where id= $application_form[db41]" );
						//if (!$appli_status || $appli_status =='') {

							if ($appli_submited_check < 1) {
								$actual_status = "Not Yet Submitted";
							}elseif($application_form["db41"] == "-1"){
								$actual_status = "Application Withdrawn";
							} else {
								$actual_status = "Application Submitted";
							}
						//}else {
						//	$actual_status = $appli_status;
						//}

				//various checks
				$check_offer_status = explode("|",pull_field("dir_offers","concat(db1799,'|',db15880,'|',id)","WHERE rel_id='$application_form[applicant_id]' AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL or rec_archive ='') AND db18742!='' AND db1827='active' order by id desc LIMIT 1"));
							
							
						dev_debug("offer data 0=$check_offer_status[0]:1=$check_offer_status[1]:2=$check_offer_status[2]: app id $application_form[applicant_id]");
							
						dev_debug(json_encode($application_form));
							
				 $check_offer_status_choice= $check_offer_status[0];
				 $check_offer_status_paymethod= $check_offer_status[1];
				 
                 $portfolio_upload_done = pull_field('form_file', "count(*)", "WHERE rel_id = $application_form[applicant_id] AND db200 = 27  AND usergroup=21 AND (rec_archive IS NULL OR rec_archive = '')" );
				 
                 $college_challenge_done_2 = pull_field('form_file', "count(*)", "WHERE rel_id = $application_form[applicant_id] AND db200 = 36  AND usergroup=21 AND (rec_archive IS NULL OR rec_archive = '')" );	 


                        // show a different view for short courses
                            $letters_sql = "SELECT * from dir_letters_sent WHERE (rec_archive IS NULL or rec_archive ='') AND rel_id = ?";
                            $dbh = get_dbh();
                            $sth = $dbh->prepare($letters_sql);
                            $sth->execute(array($application_form["applicant_id"]));
                            $letters = $sth->fetchAll(PDO::FETCH_ASSOC);    	
				?>
                
              <div class="row course_list">

      <div class="col-sm-8 col-xs-12">    
		  <div style="padding:0 10px"><p class="course_title">
          <a href="#" id="more<?=$iloop?>">
			<i class="fa fa-angle-down btn_size" id="che0_sh1<?=$iloop_sh?>"></i>
			<i class="fa fa-angle-up btn_size" id="che0_sh2<?=$iloop_sh?>" style="display:none"></i>
          <?=$application_form["intake"]?>
          </a> </p></div>
	  </div>
        
    <div class="col-sm-4 col-xs-12">
        
					<?php  
					 $personal_info = "application_form"; //all other
					 if($application_form["db41"] == "-1"){
					 ?>					 	
						 <a href="#" id="" class="btn btn-muted btn-block btn-sm">Application Withdrawn</a>
					 <?php }else{ ?>
					 <?php
				    if($check_course_status!=='off'){
					      if($filled_percentage_on_dash=="100"){
						   
						   // check if the user has submitted
						   $submitted = pull_field("core_students", "db41", "WHERE id='".$application_form["applicant_id"]."'");
						   if($submitted==0){
	//                 		?>
								<form method="post" name="chld_view_form1" action="<?php echo website_url_applicant; ?>/submit_application" style="margin: 0;">
								<input type="hidden" name="chld_view" value="<?=$application_form["applicant_id"]?>">
								<button class="btn btn-custom btn-block btn-sm" type="submit">Submit Application</button>
								</form>or <br/>
								<form method="post" name="chld_view_form2" action="<?php echo website_url_applicant; ?>/<?=$personal_info?>" style="margin: 0;">
								<input type="hidden" name="chld_view" value="<?=$application_form["applicant_id"]?>">
								<button class="btn btn-custom btn-block btn-sm" type="submit">Finish Application</button>
								</form>
							<?php 
							 } 	else {
							?>
								<form method="post" name="chld_view_form1" action="<?php echo website_url_applicant; ?>/<?=$personal_info?>" style="margin: 0;">
								<input type="hidden" name="chld_view" value="<?=$application_form["applicant_id"]?>">
								<button class="btn btn-custom btn-block btn-sm" type="submit">View Application</button>
								</form>
					   <?php
							 }//end submit check
					   }else {
						   ?>
						   <form method="post" name="chld_view_form2" action="<?php echo website_url_applicant; ?>/<?=$personal_info?>" style="margin: 0;">
								<input type="hidden" name="chld_view" value="<?=$application_form["applicant_id"]?>">
								<button class="btn btn-custom btn-block btn-sm" type="submit">Finish Application</button>
								</form>
						   <?php
					   }
					   
				   }//end $check_course_status check
					else{
						?>
                        <span>Applications Now Closed</span>
                        <?php
					}
				   ?>
				   <?php }// end withdrawn check ?>

    </div>
                
                <div id="extra<?=$iloop?>" style="display:<?=($iloop==1?"block":"none")?>">
                
                <div class="col-sm-8 col-xs-12">                                
                <table class="table">               
                <?php
				//check if ucas id exists
				 if($core_student_ucas_pass>0){
			    ?>
                <tr>
                    <th>UCAS Personal ID</th>
                    <td><?=($core_student_ucas_pass?$core_student_ucas_pass:'pending')?></td>
                </tr>
                <?php
				//end
				 }
			    ?>

                <?php 
				
				if ($course_level=="Masters" && $application_form["db889"]==1525 && $appli_submited_check>0){//master Communication					
					if($portfolio_upload_done == 0) {?>
                 <tr>
                  <th>Upload Portfolio/Collage Challenge/Written Work</th>
                  <td>                 
                  <a href="<?php echo website_url_applicant; ?>/upload_portfolio" class="btn btn-info">Upload</a>	
                  </td>
                </tr>
                 <?php }
				  $portfolio_upload_done=1;
				 }//end level check 

				if ($course_level=="Degree"  || $course_level=="Masters"  && $appli_submited_check>0){
					
					//if MA then remove the request for portfolio
					if($application_form["db889"]==1526){ $portfolio_upload_done=1;}
					
					if($portfolio_upload_done == 0) {?>
                 <tr>
                  <th>Upload Portfolio</th>
                  <td>                 
                  <a href="<?php echo website_url_applicant; ?>/upload_portfolio" class="btn btn-info">Upload</a>	
                  </td>
                </tr>
                 <?php }
				 }//end level check 
				 
				 ?>
                 
                <?php if ($course_level=="Diploma" || $course_level=="Certificate"  && $appli_submited_check>0){
					if($college_challenge_done_2 == 0) {?>
                 <tr>
                  <th>College Challenge</th>
                  <td>                 
                  <a href="<?php echo website_url_applicant; ?>/upload_portfolio" class="btn btn-info">Upload</a>	
                  </td>
                </tr>
                 <?php }
				}//end level check?>
               
                
                <?php 
						
						
							if ($check_offer_status[2]>0) {?>
                     <tr>
                      <th>View Your Offer Letter And Invoice</th>
                      <td>                 
                      <a href="<?php echo website_url_applicant; ?>/offer" class="btn btn-danger">View</a>
                      </td>
                    </tr>
                  
                  
                     <tr>
                      <th>Payments</th>
                      <td>                 
                                  <?php
								  if($check_offer_status_paymethod=='not specified' || $check_offer_status_paymethod=='' ){
									echo "View offer and accept to make payment";
								  }else{
									$candidate_id = $application_form["applicant_id"];//send this id to the file included below
								    include(base_path."/".front_header_file_location."/resources/views/payment_button.php"); 
								  }?>
                      </td>
                    </tr>
<?php } ?>
                  
                  			 <?php					
							//end
							if(count($letters)>0){
							?>
                             <tr>
                              <th>Letters</th>
                              <td>   
							  <?php foreach ($letters as $letter) { ?>             
                              <a href="<?php echo $letter['db20300']?>" title='click to view letter'><?php echo $letter['db20301']?></a><br/>
							  <?php }?>
                              </td>
                            </tr>
                             <?php
							 }// letters check end
							?>

                <tr>
                    <th>Course</th>
                    <td><?=$application_form["course"]?></td>
                  </tr>
                <tr>
                <tr>
                    <th>Course Level</th>
                    <td><?=$application_form["course_level"]?></td>
                  </tr>
                <tr>
                    <th>Proposed Year Of Study</th>
                    <td><?=$application_form["cohort"]?></td>
                  </tr>
                <tr>
                    <th>Date Application Started</th>
                    <td><?=format_date('d/m/Y',$application_form["application_date"])?></td>
                  </tr>
                <tr>
                <tr>
                    <th>Date Application Submitted</th>
                    <td><?=($get_submission_date?''.format_date('d/m/Y',$get_submission_date).'':'Not Yet Submitted')?></td>
                  </tr>
                <tr>
                  <th>Application Status</th>
                  <td>                 
                  <?=$actual_status?>
                  </td>
                </tr>
                             
                <tr>
                  <td colspan="2">                 
                  <?php
				  //show tracker
				  tracker_track($application_form["applicant_id"],$filled_percentage_on_dash); 
				  ?>
                  </td>
                </tr>
                </table>
                </div>
                
                <div class="col-sm-4 col-xs-12">
                   <div class="alert <?php echo ($filled_percentage_on_dash=="100"?'alert-default':'alert-default') ?> status" align="center">
                   
                   <?php
				   	if($filled_percentage_on_dash=="100"){
				   ?>

						   <?php
						   if($appli_submited_check>0){
						   ?>
						   
						   <h5>Your Application is now <b><span>100%</span></b> complete.</h5>
                   <!--START PROGRESS BAR-->
                    <div class="progress">
                      <div class="progress-bar <?=$box_progress?>" role="progressbar" aria-valuenow="<?=$filled_percentage_on_dash?>" aria-valuemin="0" aria-valuemax="100" style="width: <?=$filled_percentage_on_dash?>%">
                        <span></span>
                      </div>
                    </div>
                   <!--END PROGRESS BAR-->
						   <hr/>
							 
                               <!--<a href="<?php #echo website_url_applicant; ?>/application_form" class="btn btn-lg btn-custom btn-block"><i class="fa fa-lg fa-check-square-o" aria-hidden="true"></i> View Completed Application</a>-->
						   <?php
							}else{
							?>
						   <h5>Your Application is now <b><span><?=$filled_percentage_on_dash?>%</span></b> complete.</h5>
                   <!--START PROGRESS BAR-->
                    <div class="progress">
                      <div class="progress-bar <?=$box_progress?>" role="progressbar" aria-valuenow="<?=$filled_percentage_on_dash?>" aria-valuemin="0" aria-valuemax="100" style="width: <?=$filled_percentage_on_dash?>%">
                        <span></span>
                      </div>
                    </div>
                   <!--END PROGRESS BAR-->
                   
								<form method="post" name="chld_view_form1" action="<?php echo website_url_applicant; ?>/submit_application" style="margin: 0;">
								<input type="hidden" name="chld_view" value="<?=$application_form["applicant_id"]?>">
								<button class="btn btn-success btn-block" type="submit">Submit Your Application</button>
								</form>
						   
						   <?php
							}
						   ?>

                   <?php
				   	}else{
				   ?>
				   <h5>Your Application is now <b><span><?=$filled_percentage_on_dash?>%</span></b> complete</h5>
                   <!--START PROGRESS BAR-->
                    <div class="progress">
                      <div class="progress-bar <?=$box_progress?>" role="progressbar" aria-valuenow="<?=$filled_percentage_on_dash?>" aria-valuemin="0" aria-valuemax="100" style="width: <?=$filled_percentage_on_dash?>%">
                        <span></span>
                      </div>
                    </div>
                   <!--END PROGRESS BAR-->
                                      
                   <?php
				   	}
				   ?>
                  </div>
                </div>
                </div><!--extra-->
          
</div><!--row-->

<script>
 // toggle show hide
$('#more<?=$iloop?>').click(function() {
 $('#extra<?=$iloop?>').slideToggle('fast');
 		 $('#che0_sh1<?=$iloop_sh?>').toggle(0);
		 $('#che0_sh2<?=$iloop_sh?>').toggle(0)
return false;
});
</script>
<?php
		$filled_percentage_on_dash='';//reset
		$submitted='';
		$page_to_navigate_to='';
		$iloop++;
		// loop through kids end
		}
		
	//check if long or short end
	}
?>