<?php
    
 $dbh = get_dbh();
 $usergroups = usergroups_management();
  // get all short_course details for this course
    $sql = "SELECT 
id AS Manage,
DATE_FORMAT(date, '%d/%m/%y %H:%i') AS 'date_created',
(SELECT count(id) FROM lead_com_notes WHERE lead_com_notes.rel_id = lead_profiles.id) AS Notes,
db1043 AS Surname,
db1041 AS 'First name',
db1050 AS 'Cohort',
db1051 AS 'Course',
(SELECT db232 from core_courses WHERE core_courses.id=db1052) AS 'course_of_interest',
(SELECT db343 FROM core_course_level WHERE id = db1053) AS 'Level_of_Entry',
db1055 AS 'Questions',
(SELECT concat(db106, ' ', db111) FROM core_assign,form_users WHERE core_assignations.db69836 = form_users.id AND core_assignations.rel_id = lead_profiles.id AND db69884 = '1') AS 'Assigned To',
(SELECT db1733 FROM lead_stages WHERE id = db1056) AS 'Status' 
FROM `lead_profiles`
        WHERE (rec_archive IS NULL OR rec_archive = '')
AND  db1058 = '$core_students_email_address'  ORDER BY date DESC";
			//echo $sql;
    $sth = $dbh->prepare($sql);
    $sth->execute();
    $results = $sth->fetchAll(PDO::FETCH_ASSOC);
	
	$iloop_sh=1;
    foreach ($results AS $rows){

		if (!$rows['course_of_interest'] || $rows['course_of_interest'] == '') {
            $course_of_intrest = "No specific course selected, see details";
        }
            else {
                $course_of_intrest = $rows['course_of_interest'];

        }
?>
  
  <div class="row course_list">
  
      <div class="col-sm-9 col-xs-12">    
		  <div style="padding:0 10px"><p><a href="#" id="more_enq<?=$iloop_sh?>"><?=$iloop_sh?>: <?=ucfirst($rows['date_created'].' '.$course_of_intrest.' '.$rows['Cohort'])?></a> </p></div>
	  </div>
        
    <div class="col-sm-3 col-xs-12">    
          <div class="label label-info"><?=$rows['Status'];?></div>
    </div>
                
                <div id="extra_enq<?=$iloop_sh?>" style="display:none">
                <div class="col-sm-8 col-xs-12">                 
                <table class="table">
                    <tr>
                        <th>Subject of Interest</th>
                        <td><?=$rows['Course']?></td>
                    </tr>
                <tr>
                    <th>Year of Study</th>
                    <td><?=$rows['Cohort']?></td>
                  </tr>
                    <tr>
                        <th>Level of Entry</th>
                        <td><?=$rows['Level_of_Entry']?></td>
                    </tr>
                    <tr>
                        <th>Questions</th>
                        <td><?=$rows['Questions']?></td>
                    </tr>

                <!--<tr>
                    <th>Edit details</th>
                    <td><a href="/engine/controller.php?pg=97&rec=<?= $rows['Manage']?>&module_id=2&width=850&height=600&jqmRefresh=true" id="button" title="Edit enquiry" class="btn btn-info btn-sm"><i class="fa fa-pencil"></i></a>    </td>
                  </tr>-->
                </table>
                </div>
                
                



                  
                </div><!--extra-->
</div><!--row-->
            
		<script>
         // toggle show hide
        $('#more_enq<?=$iloop_sh?>').click(function() {
         $('#extra_enq<?=$iloop_sh?>').slideToggle('fast');
        return false;
        });
        </script>
<?php
$iloop_sh++;
// loop through end
}
?>

