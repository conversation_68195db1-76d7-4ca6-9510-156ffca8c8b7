<?php //check payments
global $show_features;
$payments_option = 0;
// check if they have this feature
if (preg_match_search($show_features, 'payments') == true) {
    $payments_option = 1;
}


if ($payments_option == 1) {
    //get the latest accepted offer
    $latest_accepted_offer_invoice = pull_field("dir_offers","db16369","WHERE rel_id='$candidate_id' AND usergroup='$_SESSION[usergroup]' AND (db16369 IS NOT NULL AND db16369 !='') AND (rec_archive IS NULL OR rec_archive = '') ORDER BY id DESC LIMIT 1");
    if ($latest_accepted_offer_invoice && $latest_accepted_offer_invoice !='') {
        $payoptions =pull_field("dir_offers","db15880","WHERE db16369='$latest_accepted_offer_invoice'");
        $lead_invoice_username_id = pull_field("lead_invoice_settings", "username_id", "WHERE id =" . $latest_accepted_offer_invoice);

        $currency = pull_field("system_currency", "abv", "WHERE id =" . pull_field("lead_invoice_settings", "db14987", "WHERE id='$latest_accepted_offer_invoice'"));

        //take info from the invoice in case invoice has changed the form_payment_options may no longer be valid
        //$overall_price = pull_field("lead_invoice_settings", "db15006", "WHERE id='$latest_accepted_offer_invoice'");
        $overall_price_excluding_discount = pull_field("lead_invoice_items", "SUM(db15022)","WHERE usergroup = '$_SESSION[usergroup]' AND rel_id ='$latest_accepted_offer_invoice'");
        $discount = pull_field("lead_invoice_settings", "db14999","WHERE usergroup = '$_SESSION[usergroup]' AND id ='$latest_accepted_offer_invoice'");

        $overall_price = ($overall_price_excluding_discount - $discount);

        $overall_deposit = pull_field("lead_invoice_settings", "db15003", "WHERE id='$latest_accepted_offer_invoice'");
        $number_of_installments_allowed = pull_field("lead_invoice_settings", "db14996", "WHERE id='$latest_accepted_offer_invoice'");
        $include_vat = pull_field("lead_invoice_settings", "db14998", "WHERE id='$latest_accepted_offer_invoice'");

        if ($include_vat == 'yes') {
            //get the preferences
            $sql_preferences = "SELECT * FROM lead_preferences WHERE usergroup = '$_SESSION[usergroup]'";
            $sql = $dbh->prepare($sql_preferences);
            $sql->execute();
            $pdo_preferences_rows = $sql->fetchAll(PDO::FETCH_OBJ);
            $pdo_preferences = $pdo_preferences_rows[0];
            $vat_percent = pull_field("lead_preferences","db15030","WHERE usergroup = '$_SESSION[usergroup]'" );
            $overall_price += ($overall_price * $vat_percent) / 100;
            $overall_deposit += ($overall_deposit * $vat_percent) / 100;

        }

        if (!$number_of_installments_allowed || $number_of_installments_allowed == '' || $number_of_installments_allowed == "not specified") {
            $number_of_installments_allowed = 0;
        }


        if (!$payoptions || $payoptions == '') {
            $payoptions = 'Pay_online_-_full_amount';
        }

        $number_of_payments_made = pull_field("sis_student_fees", "count(*)","WHERE rel_id=$candidate_id and db1494='$lead_invoice_username_id'");
        $total_amount_paid = pull_field("sis_student_fees", "SUM(db1495)","WHERE rel_id=$candidate_id and db1494='$lead_invoice_username_id'");
        //add credit notes to the total amount paid
        $total_credit_notes_amount_with_vat = pull_field("lead_credit_notes", "SUM(if (db18737='yes', if ((SELECT db14998 FROM lead_invoice_settings WHERE username_id =db18734)='yes',(SELECT (db15006 + (db15006*$vat_percent)/100) FROM lead_invoice_settings WHERE username_id =db18734),(SELECT db15006 FROM lead_invoice_settings WHERE username_id =db18734)), if (db18736 ='yes',(db18738 + (db18738*$vat_percent)/100) ,db18738)))","WHERE usergroup = '$_SESSION[usergroup]' AND db18734 ='$lead_invoice_username_id'");
        $total_amount_paid = $total_amount_paid + $total_credit_notes_amount_with_vat;
        //echo
        //echo 'Number_of_payments_made'.'**'.$number_of_payments_made.'**'."WHERE rel_id=$candidate_id and db1494='$lead_invoice_username_id'";
        //pay all
		
		dev_debug('pay_options='.$payoptions);
        ?>

        <!--View Invoice Button
                                                                        <button type="button" class="btn btn-info btn-sm"><a
                                                                                href="<?= website_url_applicant . '/application/invoice/' . $lead_invoice_username_id ?>"
                                                                                id="button" target="_blank"
                                                                                title="View Invoice">View Invoice </a></button>-->
        <!-- The first line of code specifies the URL for our test environment.-->


        <?php if ($payoptions =='Pay_online' || $payoptions == 'Pay_online_-_full_amount' || (strpos($payoptions,'-_full_amount' ) > 0)) {
            if ($number_of_payments_made < 1) {
                $button_title = ' Pay total £' . number_format($overall_price, 2) . ' now';
                $amount = $overall_price;
                $my_payment = "All";
                $show_payment_button = 1;
            } else {
                //echo 'Fully Paid';
                //check to see if total amount paid is equal tp the overall price

                if ($overall_price > $total_amount_paid) {
                    $to_pay = $overall_price - $total_amount_paid;
                    $button_title = ' Pay balance £' . number_format($to_pay, 2) . ' now';
                    $amount = $to_pay;
                    $my_payment = "Balance";
                    $show_payment_button = 1;
                }
            }


        } ?>

        <?php if ((strpos($payoptions,'-_deposit_only' ) > 0)) {
            if ($number_of_payments_made < 1) {
                $button_title = ' Pay deposit £' . number_format($overall_price, 2) . ' now';
                $amount = $overall_price;
                $my_payment = "Deposit";
                $show_payment_button = 1;
            } else {
                //echo 'Fully Paid';
                //check to see if total amount paid is equal tp the overall price

                if ($overall_price > $total_amount_paid) {
                    $to_pay = $overall_price - $total_amount_paid;
                    $button_title = ' Pay balance £' . number_format($to_pay, 2) . ' now';
                    $amount = $to_pay;
                    $my_payment = "Balance";
                    $show_payment_button = 1;
                }
            }


        } ?>


        <?php if ($payoptions == 'Pay_online_-_deposit_then_all' || (strpos($payoptions,'-_deposit_then_all' ) !==false) || (strpos($payoptions,'-_deposit_and_then_all' ) !==false)) {
            if ( $overall_price > $total_amount_paid) {

                //is there a deposit
                $deposit = false;
                if ($overall_deposit && $overall_deposit > 0) {
                    $deposit = true;
                }

                if ($deposit == true) {
                    if ($total_amount_paid < $overall_deposit ) {

                        $amount = $overall_deposit - $total_amount_paid;
                        $button_title = ' Pay deposit '.$_SESSION['currency_symbol'].' '. number_format($overall_deposit, 2) . ' now';
                        $my_payment = "Deposit";
                    } else {
                        $amount = ($overall_price - $total_amount_paid);
                        $button_title = ' Pay balance '.$_SESSION['currency_symbol'].' '. number_format($amount, 2) . ' now';
                        $my_payment = "Balance";
                    }


                } else {

                    $amount = $overall_price;
                    $button_title = ' Pay balance '.$_SESSION['currency_symbol'].' '. number_format($amount, 2) . ' now';
                    $my_payment = "All";

                }
                $show_payment_button = 1;

            }
            ?>

        <?php } ?>

        <?php if ($payoptions == 'Pay_online_-_deposit_and_instalments' || $payoptions == 'Pay_online_-_deposit_and_installments'  || (strpos($payoptions,'-_deposit_and_installments' ) !==false) || (strpos($payoptions,'-_deposit_and_instalments' ) !==false)) {
            $have_button = false;
            $deposit_and_installments = 0;
            //is there a deposit
            $deposit_paid = false;
            $deposit = false;
            if ($overall_deposit && $overall_deposit > 0) {
                $deposit_and_installments++;
                $deposit = true;
            }

            $db20215_intake = pull_field("lead_invoice_items", "db20215", "WHERE rel_id='" . $latest_accepted_offer_invoice . "' AND (rec_archive IS NULL OR rec_archive = '') LIMIT 1");

            //show the manual instalments
            if ($db20215_intake && $db20215_intake != '') {

                $number_of_installments_allowed = pull_field("dir_cohort_instalments", "COUNT(*)", "WHERE rel_id = '".$db20215_intake."' AND (rec_archive IS NULL or rec_archive ='')");
                $deposit_and_installments += $number_of_installments_allowed;

                if ( $overall_price > $total_amount_paid) {

                    if ($deposit == true) {
                        if ($total_amount_paid == 0 || $total_amount_paid < $overall_deposit) {
                            $amount = $overall_deposit - $total_amount_paid;
                            $button_title = ' Pay deposit ' . $_SESSION['currency_symbol'] . ' ' . number_format($overall_deposit, 2) . ' now';
                            $my_payment = "Deposit";
                            $have_button = true;

                        } else {
                            $installments_paid_to_date = $total_amount_paid - $overall_deposit;
                        }
                    }
                    else {

                        $installments_paid_to_date = $total_amount_paid;
                    }
                    
                    if ($have_button == false) {
                        //go through the manual instalments to find the instalment to pay
                        //read the cohort_instalments and put them on the screen

                        $sql = "SELECT db20171 as 'due_date', db20172 as 'name', db20214 AS 'amount' FROM dir_cohort_instalments WHERE rel_id = ? AND (rec_archive IS NULL or rec_archive ='') order by db20171";
                        $sql = $dbh->prepare($sql);
                        $sql->execute(array($db20215_intake));
                        $instalments = $sql->fetchAll(PDO::FETCH_ASSOC);

                        if ($instalments) {
                            $underpayment = 0;
                            $underpayment_sorted = false;
                            foreach ($instalments as $instalment) {

                                $cohort_installment_amount = $instalment['amount'];
                                if ($include_vat == 'yes') {
                                    //AFY 04/10/2018 $installment_vat_amount = $cohort_installment_amount - ($cohort_installment_amount / (1 + ($pdo_preferences->db15030 / 100)));
                                    $installment_vat_amount = $cohort_installment_amount * ($pdo_preferences->db15030 / 100);
                                    $amount = $cohort_installment_amount + $installment_vat_amount + $underpayment;
                                    if ($underpayment > 0) {
                                        $underpayment_sorted = true;
                                    }
                                    $underpayment = 0;
                                } else {
                                    $installment_vat_amount = '';
                                    $amount = $cohort_installment_amount + $underpayment;
                                    if ($underpayment > 0) {
                                        $underpayment_sorted = true;
                                    }
                                    $underpayment = 0;
                                }

                                if ($installments_paid_to_date > 0 & $installments_paid_to_date < $amount & $underpayment_sorted == false) { //underpayment
                                    $underpayment = $amount - $installments_paid_to_date;

                                } else {
                                    if ($installments_paid_to_date >= $amount) {
                                        $installments_paid_to_date = $installments_paid_to_date - $amount;

                                    } else {

                                        //$amount = $amount - $instalment['name'];
                                        $button_title = ' Pay ' . $instalment['name'] . ' ' . $_SESSION['currency_symbol'] . ' ' . number_format($amount, 2) . ' now';
                                        $my_payment = $instalment['name'];
                                        break;
                                    }

                                }


                            }
                            if ($underpayment && $underpayment > 0) {
                                $amount = $underpayment;
                                $button_title = ' Pay Balance' . ' ' . $_SESSION['currency_symbol'] . ' ' . number_format($amount, 2) . ' now';
                                $my_payment = 'Balance';


                            }
                        }

                    }

                    $show_payment_button = 1;
                }



            } else {
                //show generated instalments
                $deposit_and_installments += $number_of_installments_allowed;


                if ( $overall_price > $total_amount_paid) {
                    if ($deposit == true) {
                        if ($total_amount_paid == 0 || $total_amount_paid < $overall_deposit) {
                            $amount = $overall_deposit - $total_amount_paid;
                            $button_title = ' Pay deposit ' . $_SESSION['currency_symbol'] . ' ' . number_format($overall_deposit, 2) . ' now';
                            $my_payment = "Deposit";

                        } else {
                            $installments_paid_to_date = $total_amount_paid - $overall_deposit;
                        }
                        $each_installment_amount = ($overall_price - $overall_deposit) / $number_of_installments_allowed;
                    }
                    else {

                        $installments_paid_to_date = $total_amount_paid;
                        $each_installment_amount = $overall_price  / $number_of_installments_allowed;
                    }
                    $installment_count=1;
                    if ($installments_paid_to_date == 0) {
                        $amount = $each_installment_amount;
                        $button_title = ' Pay instalment ' . $installment_count .' '.$_SESSION['currency_symbol'].' ' . number_format($amount, 2) . ' now';
                        $my_payment = "Instalment $installment_count";
                    }
                    else {
                        while ($installment_count <= $number_of_installments_allowed) {
                            if ($installments_paid_to_date > 0 & $installments_paid_to_date < $each_installment_amount) { //underpayment
                                $underpayment = $each_installment_amount - $installments_paid_to_date;
                                break;
                            }
                            else {
                                if ($installments_paid_to_date > $each_installment_amount) {
                                    $installments_paid_to_date = $installments_paid_to_date - $each_installment_amount;
                                }
                                else {
                                    $amount = $each_installment_amount;
                                    $button_title = ' Pay Instalment'. $installment_count.' '.$_SESSION['currency_symbol'].' '. number_format($amount, 2) . ' now';
                                    $my_payment = 'Instalment '.$installment_count;
                                    break;
                                }

                            }
                        }
                    }

                    if ($underpayment && $underpayment > 0) {
                        $amount = $underpayment;
                        $button_title = ' Pay Balance'.' '.$_SESSION['currency_symbol'].' '. number_format($amount, 2) . ' now';
                        $my_payment = 'Balance';

                    }

                    $show_payment_button = 1;
                }
            }
            ?>


        <?php } ?>

        <?php if ($show_payment_button == 1) {
            $student_username_id = pull_field("core_students", "username_id", "WHERE id='$candidate_id'");
            $user_username_id = pull_field("form_users", "username_id", "WHERE id='$_SESSION[uid]'");
            ?>

            <form style="margin-bottom:0px;" action="https://secure.worldpay.com/wcc/purchase" method=POST>

                <!-- This next line contains the testMode parameter - it specifies that the submission is a test submission -->
                <!-- <input type="hidden" name="testMode" value="1000">-->


                <!-- This next line contains a mandatory parameter. Put your Installation ID inside the quotes after value= -->
                <input type="hidden" name="instId" value="276728">
                <input type="hidden" name = "accId1" value="CONDENASTPUBM1">
                <input type="hidden" name="MC_callback"
                       value="<?= website_url ?>/worldpay_callback.php">

                <!-- Another mandatory parameter. Put your own reference identifier for the item purchased inside the quotes after value= -->
                <!--<input type="hidden" name="cartId" value="<?= pull_field("dir_offers","username_id","WHERE db16369='$latest_accepted_offer_invoice'"); ?>">-->
                <input type="hidden" name="cartId" value="<?= pull_field("lead_invoice_settings","db25617","WHERE id='$latest_accepted_offer_invoice'"); ?>">
                <input type="hidden" name="MC_student_id" value="<?= $student_username_id ?>">
                <input type="hidden" name="MC_payment_type" value="<?= $payoptions ?>">
                <input type="hidden" name="MC_website" value="<?=$_SERVER['HTTP_HOST']?>">
                <input type="hidden" name="MC_whoami" value="<?= session_info('subdomain') ?>">
                <input type="hidden" name="MC_slogan" value='<?= pull_field("form_schools","db1093","WHERE id = $_SESSION[access]") ?>'>
                <input type="hidden" name="MC_course_type" value="long">
                <!-- Another mandatory parameter. Put the code for the purchase currency inside the quotes after value= -->
                <input type="hidden" name="currency" value="<?= $currency ?>">

                <!-- Another mandatory parameter. Put the total cost of the item inside the quotes after value= -->
                <input type="hidden" name="amount" value="<?= $amount ?>">
                <input type="hidden" name="MC_my_payment_type" value="<?= $my_payment ?>">
                <input type="hidden" name="MC_password" value="<?= md5('actionstarterpassword') ?>">
                <input type="hidden" name="MC_uid" value="<?= $user_username_id ?>">
                <input type="hidden" name="MC_access" value="<?= session_info('access') ?>">

                <input type="hidden" name="Payment_option" value="<?= $payoptions ?>">
                <input type="hidden" name="Instalments paid to date" value="<?= $installments_paid_to_date ?>">


                <!-- This creates the button. When it is selected in the browser, the form submits the purchase details to us. -->
                <!-- <input type="submit" class="btn btn-info btn-sm" value=" Make Payment">-->
                <button type="submit" class="btn btn-info btn-sm" title="<?= $button_title ?>"><i
                        class="fa fa-credit-card"></i><?= $button_title ?></button>

            </form>

        <?php }else { echo "You have no payments to make"; } ?>
        <?php
    }

}

?>