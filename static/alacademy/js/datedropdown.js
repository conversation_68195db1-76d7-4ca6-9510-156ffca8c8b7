
// these should be in a class really
function updateDateField (strFieldName)
{
    var strDay = document.getElementById(strFieldName + '_day').value;
    var strMonth = document.getElementById(strFieldName + '_month').value;
    var strYear = document.getElementById(strFieldName + '_year').value;

    // since january is 0
    var month = strMonth -1;

    ///new date
    var date = new Date();

    //set date
    var d = new Date(strYear, month, strDay);

    //check if date is valid
    if (d.getFullYear() == strYear && d.getMonth() == month && d.getDate() == strDay) {

        document.getElementById(strFieldName).value = strYear + '-' + strMonth + '-' + strDay;
    } else {

        strDay = date.getDate();
        strMonth = date.getMonth() + 1;
        strYear = date.getFullYear();
        document.getElementById(strFieldName).value = strYear + '-' + strMonth + '-' + strDay;

    }
}

function resetDateField (strFieldName)
{
    document.getElementById(strFieldName + '_day').selectedIndex = 0;
    document.getElementById(strFieldName + '_month').selectedIndex = 0;
    document.getElementById(strFieldName + '_year').selectedIndex = 0;
    document.getElementById(strFieldName).value = '';
}

function disableDateField (strFieldName)
{
    document.getElementById(strFieldName + '_day').style.backgroundColor = '#FAA';
    document.getElementById(strFieldName + '_month').style.backgroundColor = '#FAA';
    document.getElementById(strFieldName + '_year').style.backgroundColor = '#FAA';

    document.getElementById(strFieldName + '_day').disabled = true;
    document.getElementById(strFieldName + '_month').disabled = true;
    document.getElementById(strFieldName + '_year').disabled = true;
}

function enableDateField (strFieldName)
{
    document.getElementById(strFieldName + '_day').style.backgroundColor = '#DCE5F0';
    document.getElementById(strFieldName + '_month').style.backgroundColor = '#DCE5F0';
    document.getElementById(strFieldName + '_year').style.backgroundColor = '#DCE5F0';

    document.getElementById(strFieldName + '_day').disabled = false;
    document.getElementById(strFieldName + '_month').disabled = false;
    document.getElementById(strFieldName + '_year').disabled = false;
}

