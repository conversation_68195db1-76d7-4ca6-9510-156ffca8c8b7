<?php
if ($_SESSION['loggedin']) { // only show to logged in users

    echo '<div class="btn btn-custom btn-lg btn-block"><a href="' . website_url_applicant . '/logout">' . translate("Log out", $_SESSION['lang']) . ' </a></div><br/>';

//only show on the homepage
    if ($cms_page_name == "Checklist") {


        $dbh = get_dbh();

// Get the stages for student's route
        $sql = 'SELECT system_table.form_id, system_table.db_field_name, system_table.name
    FROM system_table
    INNER JOIN core_students ON core_students.db2280 = system_table.pg_id
    WHERE core_students.id = ? AND system_table.type="stage"
    ORDER BY system_table.form_order';
        $sth = $dbh->prepare($sql);
        $sth->execute(array($_SESSION['student_id']));
        $stages = array();
        while ($row = $sth->fetch(PDO::FETCH_ASSOC)) $stages[] = $row;

// pull the statuses of stages for this particular student
        $checklist_table_name = 'chk_' . pull_field(
                'system_pages INNER JOIN core_students ON core_students.db2280 = system_pages.page_id',
                'page_name', "WHERE core_students.id = '$_SESSION[student_id]'");
        error_log('checklist table is called ' . $checklist_table_name);


        $sql = 'SELECT ';
        foreach ($stages as $stage) {
            $sql .= $stage['db_field_name'] . ', ';
        }
        $sql = substr($sql, 0, -2);
        $sql .= " FROM $checklist_table_name WHERE rel_id = ? LIMIT 1";
        error_log('query to get stage statuses: ' . $sql);
        $sth = $dbh->prepare($sql);
        $sth->execute(array($_SESSION['student_id']));
        $stage_status = $sth->fetch(PDO::FETCH_ASSOC);

        ?>
        <div class="row">
            <div class="col-xs-12">
                <ul class="list-group list-group_home">
                    <li class="list-group-item">
                        <h4 class="list-group-item-heading"><?php echo translate("Application Tracker", $_SESSION['lang']) ?></h4>
                        <p class="list-group-item-text"><?php echo translate("A quick summary of how your application is proceeding", $_SESSION['lang']) ?></p>
                    </li>

                    <li class="list-group-item">
                        <div class="circle-text">
                            <span>0</span>
                        </div>
                        Application Completion
                        <?php echo($filled_percentage == '100' ? '<img src="' . engine_url . '/images/icon_tick.png" width="16" height="16" alt="tick" />' : '') ?>
                    </li>
                    <?php
                    $i = 1;
                    foreach ($stages as $stage) {
                        $exp = explode('-', $stage['name']);
                        $sts = $stage_status[$stage['db_field_name']];
                        $sts_image = ($sts === 'on') ? '<img src="' . engine_url . '/images/icon_tick.png" width="16" height="16" alt="tick" />' : '';
                        ?>
                        <li class="list-group-item<?= stage_complete($i) ?>">
                            <div class="circle-text">
                                <span><?= $i ?></span>
                            </div><?= $exp[1] ?> <?= $sts_image ?></li>
                        <?php
                        $i++;
                    } ?>

                </ul>
            </div>
        </div>
        <?php
        //end of checklist
    } else {
        ?>
        <div class="row">
            <div class="col-xs-12">
                <ul class="list-group">
                    <li class="list-group-item"><b><?php echo translate("Your Application", $_SESSION['lang']) ?></b>
                    </li>
                    <?php
                    $okay_for_form2 = pull_field("chk_z_11_default_checklist", "db9514", "WHERE rel_id='$_SESSION[student_id]'");
                    get_cms_nav("application", "private", "id!='330' AND db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");

                    if ($okay_for_form2 == 'on') {
                        get_cms_nav("application", "private", "id IN (330)", "yes", "list-group-item");
                    }


                    ?>
                </ul>
            </div>
        </div>
    <?php } ?>

    <div class="row">
        <div class="col-xs-12">
            <div class="alert alert-warning">
                <h3><?php echo translate("FAQ & Support", $_SESSION['lang']) ?></h3>
                <hr/>
                <p>
                <h4><?php echo translate("Can I track my application after submitting?", $_SESSION['lang']) ?></h4>
                <?php echo translate("Yes, just log in and use Application Tracker in the Quicklinks menu.", $_SESSION['lang']) ?>
                </p><br/>
                <a href="<?php echo website_url_applicant; ?>/applicant-faqs"
                   class="label label-info"><?php echo translate("See all our frequently asked questions", $_SESSION['lang']) ?>
                    > </a>
            </div>
        </div>
    </div>

    <?php
}// end of check if checklist

if (!$_SESSION['loggedin']) { // only show to logged in users
    ?>
    <div class="row col-xs-12">
        <p><b><?php echo translate("Quicklinks", $_SESSION['lang']) ?></b></p>
        <ul class="list-group">
            <?php
            get_cms_nav("application", "public", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");
            ?>
            <li class="list-group-item <?php if ($current_id == 'register') {
                echo 'active';
            } ?>"><a href="<?php echo website_url_applicant; ?>/register" <?php if ($current_id == 'register') {
                    $sty = 'class="nav_selected"';
                } ?>>Start a new application >></a></li>
            <li class="list-group-item <?php if ($current_id == 'login') {
                echo 'active';
            } ?>">
                <a href="<?php echo website_url_applicant; ?>/login"><?php echo translate("Log in to continue an application you have already started", $_SESSION['lang']) ?>
                    >></a></li>
            <li class="list-group-item <?php if ($current_id == 'login') {
                echo 'active';
            } ?>">
                <a href="<?php echo website_url_applicant; ?>/login"><?php echo translate("Log in to view the status of a completed application", $_SESSION['lang']) ?>
                    >></a></li>
        </ul>
    </div>
    <?php
}

?>