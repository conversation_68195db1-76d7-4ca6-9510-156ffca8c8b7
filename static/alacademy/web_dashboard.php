<div class="row">
    <div class="col-xs-12 col-md-12">
        <div class="splash">

            <div class="row">

                <div class="col-md-3 profile_image text-center">
                    <img src="<?= $avatar ?>" class="img-circle" style="width:100%">
                    <button class="label label-default" data-toggle="modal"
                            data-target="#photo_update"><?= translate("Update Profile", $_SESSION['lang']) ?></button>
                </div>

                <div class="table-responsive col-md-5">
                    <h3><?php echo translate("Welcome Back", $_SESSION['lang']) ?>
                        , <?= $core_students_first_name ?></h3>

                    <table class="table">
                        <tr>
                            <th><?= translate("Programme", $_SESSION['lang']) ?></th>
                            <td><?= $core_students_course_of_study ?></td>
                        </tr>

                        <tr>
                            <th><?= translate("Cohort", $_SESSION['lang']) ?></th>
                            <td><?= $core_students_cohort ?></td>
                        </tr>
                        <tr>
                            <th><?= translate("Status", $_SESSION['lang']) ?></th>
                            <td>
                                <?= ($appli_submited_check < 1 ? translate("Application Not Yet Submitted", $_SESSION['lang']) : translate("Application Submitted", $_SESSION['lang'])); ?>
                            </td>
                        </tr>

                        <tr>
                            <th>&nbsp;</th>
                            <td>&nbsp;</td>
                        </tr>
                    </table>
                </div>

                <div class="col-md-4">
                    <div class="alert <?php echo($filled_percentage == "100" ? 'alert-success' : 'alert-info') ?>"
                         align="center">
                        <h3><?= translate("Your Application is now", $_SESSION['lang']) ?> <b><?= $filled_percentage ?>
                                %</b> <?= translate("complete", $_SESSION['lang']) ?>.</h3>
                        <!--START PROGRESS BAR-->
                        <div class="progress">
                            <div class="progress-bar <?php echo($filled_percentage == "100" ? 'progress-bar-success' : 'progress-bar-info') ?>"
                                 role="progressbar" aria-valuenow="<?= $filled_percentage ?>" aria-valuemin="0"
                                 aria-valuemax="100" style="width: <?= $filled_percentage ?>%">
                                <span class="sr-only"><?= $filled_percentage ?>% <?= translate("complete", $_SESSION['lang']) ?></span>
                            </div>
                        </div>
                        <!--END PROGRESS BAR-->
                        <hr/>
                        <?php
                        if ($filled_percentage == "100") {
                            ?>
                            <a href="<?php echo website_url_applicant; ?>/personal-information"
                               class="btn btn-success"><?= translate("View Your Application", $_SESSION['lang']) ?></a>
                            <?php
                            // check if the user has submitted
                            if ($appli_submited_check < 1) {
                                ?>
                                <br/> or <br/>
                                <a href="<?php echo website_url_applicant; ?>/submit_application"
                                   class="btn btn-success"><?= translate("Submit Your Application", $_SESSION['lang']) ?></a>
                            <?php } ?>

                            <?php
                        } else {
                            ?>
                            <a href="<?php echo website_url_applicant; ?>/personal-information"
                               class="btn btn-custom"><?= translate("Edit Your Application", $_SESSION['lang']) ?></a>
                            <?php
                        }
                        ?>
                    </div>
                </div>

            </div>

        </div>
    </div>
</div>


<div class="row">
    <div class="col-xs-12 col-lg-6">
        <h2><?= translate("Communication Inbox", $_SESSION['lang']) ?></h2>
        <p><?= translate("Send and receive communication from our administration team here", $_SESSION['lang']) ?></p>

        <!--CHAT WIZARD-->
        <div class="box widget-chat">

            <div class="widget-actions">
                <form class="form-inline" action="<?= website_url ?>/static/inc/inc_dir_messages_process.php"
                      method="POST">
                    <button class="btn btn-primary">
                        <?= translate("Send", $_SESSION['lang']) ?>
                    </button>
                    <div>
                        <textarea name="new_message" rows="1" id="textarea-chat-example"
                                  style="overflow: hidden; word-wrap: break-word; resize: vertical; height: 100px;"></textarea>
                    </div>
                </form>
            </div>
            <div class="clearfix"></div>

            <!--START CHAT STREAM-->
            <div class="chat_stream">
                <?php foreach ($messages as $msg) {
                    // current logged in person?
                    if ($msg['rec_id'] === $student_uid) { ?>
                        <div class="chat-message">
                            <img src="<?= $avatar ?>" alt="no image" width="150" border="0"/>
                            <div>
                                <b><?php echo $core_students_first_name; ?></b> says:
                                <span class="pull-right"><?php echo format_date("d M y", $msg["date"]); ?></span>
                                <div><?php echo text_to_html($msg["db76"]); ?></div>
                            </div>
                        </div>
                    <?php } else { ?>
                        <div class="chat-message right">
                            <img src="<?= get_avatar($msg['rec_id']); ?>"/>
                            <div>
                                <b><?php echo pull_field("form_users", "db106", "WHERE id='$msg[rec_id]'"); ?></b> <?= translate("Says", $_SESSION['lang']) ?>
                                :
                                <span class="pull-right"><?php echo format_date("d M y", $msg["date"]); ?></span>
                                <div><?php echo text_to_html($msg["db76"]); ?></div>
                            </div>
                        </div>
                    <?php }
                }//end while ?>
            </div>
            <!--END CHAT STREAM-->

        </div>
    </div><!--/span-->
    <?php $class_resources = "col-xs-12 col-lg-6";
    include('static/inc/inc_shared_resources.php'); ?>

    <div class="col-xs-12 col-lg-6">
        <h2><?= translate("News and Updates", $_SESSION['lang']) ?></h2>
        <p><?= translate("Various information to help you through your application", $_SESSION['lang']) ?></p>

        <ul class="list-inline">
            <?php
            $i = 0;
            foreach ($blog_assist as $msg) {
                ?>
                <li class="col-lg-12">
                    <h4><?php echo $msg["db1695"]; ?></h4>
                    <p><?php echo $msg["db1698"]; ?></p>
                    <a href="<?php echo website_url_applicant . '/blog/' . $msg["id"]; ?>"
                       class="label label-info"><?= translate("Read More", $_SESSION['lang']) ?></a>
                    <hr/>
                </li>
            <?php } // end of loop ?>
        </ul>
    </div>
</div> <!-- .row -->

<!--
<div class="row">
    <div class="col-xs-12">
    <h3>Please read this important information before you begin your application</h3>
        <div style="width:650px; height:400px; overflow:auto; border:4px solid #f0f0f0; padding:5px; background-color:#f4f4f4; margin-bottom:10px">
          <?php
// FUNCTION TO GET_CMS
//list($page_id,$cms_category,$cms_page_name,$cms_heading,$cms_brief,$cms_article)=get_cms('page_name',"Tier4_Information");
//echo $cms_article;//pull_field("form_cms","db650","WHERE db647='Tier4_Information'");
?>
        </div>
    </div>
</div>
-->
