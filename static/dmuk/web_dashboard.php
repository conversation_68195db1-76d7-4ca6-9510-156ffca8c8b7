<?php
//check if offer has been accepted before showing letters
$check_offer_status_accepted = pull_field("dir_offers", "count(*)", "WHERE rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]' AND db1813='Accepted' AND db16984='yes'");

if ($check_offer_status_accepted > 0) {
    // show letters if this is the dashboard
    $letters_sql = "SELECT * from dir_letters_sent WHERE (rec_archive IS NULL or rec_archive ='') AND usergroup='" . $_SESSION['usergroup'] . "' AND rel_id = ? ORDER BY date desc";
    dev_debug($letters_sql);
    $dbh = get_dbh();
    $sth = $dbh->prepare($letters_sql);
    $sth->execute(array($_SESSION['student_id']));
    $letters = $sth->fetchAll(PDO::FETCH_ASSOC);
}

dev_debug('session varibales');

dev_debug($_SESSION);

?>
<div class="row">
    <div class="col-xs-12 col-md-12">
        <div class="splash" style="margin-top: 30;">

            <div class="row">

                <div class="col-sm-2 col-xs-12" align="center">
                    <img src="<?= $avatar ?>" class="img-circle" style="width:100%">
                    <button class="btn btn-default btn-block update" data-toggle="modal" data-target="#photo_update"><i
                                class="fa fa-cog" aria-hidden="true"></i> Update
                    </button>
                </div>

                <div class="col-sm-6 col-xs-12">
                    <h3>Welcome Back, <?= $core_students_first_name ?></h3>

                    <table class="table">
                        <tr>
                            <th>Student ID</th>
                            <td><?= $core_students_internal_id ?></td>
                        </tr>
                        <tr>
                            <th>Programme</th>
                            <td><?= $core_students_course_of_study ?></td>
                        </tr>

                        <?php
                        //check if ucas id exists
                        if ($core_student_ucas_pass > 0){
                        ?>
                        <tr>
                            <th>UCAS Personal ID</th>
                            <td><?= ($core_student_ucas_pass ? $core_student_ucas_pass : 'pending') ?></td>
                        </tr>
                        <tr>
                            <?php
                            //end
                            }
                            ?>

                        <tr>
                            <th>Cohort</th>
                            <td><?= $core_students_cohort ?></td>
                        </tr>
                        <tr>
                            <th>Status</th>
                            <td>
                                <?php
                                if ($appli_submited_check < 1) {
                                    ?>
                                    Not Yet Submitted
                                    <?php
                                } else {
                                    ?>
                                    Application Submitted
                                    <?php
                                }
                                ?>
                            </td>
                        </tr>
                        <?php
                        //check and show offers
                        $check_offer_status = explode("|", pull_field("dir_offers", "concat(id,'|',username_id)", "WHERE rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL or rec_archive ='') AND db1827='active' order by id desc LIMIT 1"));

                        dev_debug("offer data 0=$check_offer_status[0]:1=$check_offer_status[1]:2=$check_offer_status[2]: app id $application_form[applicant_id]");

                        // check if the user has submitted
                        if ($check_offer_status[0] > 0) {
                            ?>
                            <tr>
                                <th>Your Offer</th>
                                <td>
                                    <a href="<?php echo website_url_applicant; ?>/offer/<?= $check_offer_status[1] ?>"
                                       class="btn btn-danger">View Your Offer</a>
                                    <a href="https://dmuk.edu.kz/payments/" class="btn btn-danger" target="_blank">Make
                                        Payment</a>
                                </td>
                            </tr>
                        <?php } ?>

                        <?php
                        //check if letters have been sent
                        //end
                        if (count($letters) > 0) {
                            ?>
                            <?php
                            foreach ($letters as $letter) { ?>
                                <tr>
                                    <td><i class="fa fa-envelope"
                                           aria-hidden="true"></i> <?php echo format_date('d/m/Y', $letter['date']) ?>
                                    </td>
                                    <td><a href="<?php echo $letter['db20300'] ?>" title="Click To Download Letter"
                                           target="_blank"><?php echo $letter['db20301'] ?></a></td>
                                </tr>

                            <?php }
                            ?>


                            <?php
                        }// letters check end
                        ?>
                    </table>
                </div>

                <div class="col-sm-4 col-xs-12">
                    <div class="alert <?php echo($filled_percentage == "100" ? 'alert-default' : 'alert-default') ?> status"
                         align="center">

                        <?php
                        if ($filled_percentage == "100") {
                            ?>

                            <?php
                            if ($appli_submited_check > 0) {
                                ?>

                                <h5>Your Application is now <b>
                                        <span>100%</span>
                                    </b> complete.
                                </h5>
                                <div class="progress">
                                    <div class="progress-bar <?php echo($filled_percentage == "100" ? 'progress-bar-success' : 'progress-bar-custom') ?>"
                                         role="progressbar" aria-valuenow="<?= $filled_percentage ?>" aria-valuemin="0"
                                         aria-valuemax="100" style="width: <?= $filled_percentage ?>%">
                                        <span class="sr-only"><?= $filled_percentage ?>% Complete</span>
                                    </div>
                                </div>
                                <hr/>

                                <a href="<?php echo website_url_applicant; ?>/<?= $page_link ?>"
                                   class="btn btn-lg btn-custom btn-block"><i class="fa fa-lg fa-check-square-o"
                                                                              aria-hidden="true"></i> View
                                    Application</a>
                                <?php
                            } else {
                                ?>
                                <h5>Your Application is now <b>
                                        <span><?= $filled_percentage ?>%</span>
                                    </b> complete.
                                </h5>
                                <!--START PROGRESS BAR-->
                                <div class="progress">
                                    <div class="progress-bar <?php echo($filled_percentage == "100" ? 'progress-bar-custom' : 'progress-bar-custom') ?>"
                                         role="progressbar" aria-valuenow="<?= $filled_percentage ?>" aria-valuemin="0"
                                         aria-valuemax="100" style="width: <?= $filled_percentage ?>%">
                                        <span class="sr-only"><?= $filled_percentage ?>% Complete</span>
                                    </div>
                                </div>
                                <!--END PROGRESS BAR-->
                                <hr/>

                                <a href="<?php echo website_url_applicant; ?>/submit_application"
                                   class="btn btn-success btn-block">Submit Your Application</a>
                                <?php
                            }
                            ?>

                            <?php
                        } else {
                            ?>
                            <h5>Your Application is now <b>
                                    <span><?= $filled_percentage ?>%</span>
                                </b> complete
                            </h5>
                            <!--START PROGRESS BAR-->
                            <div class="progress">
                                <div class="progress-bar <?php echo($filled_percentage == "100" ? 'progress-bar-success' : 'progress-bar-custom') ?>"
                                     role="progressbar" aria-valuenow="<?= $filled_percentage ?>" aria-valuemin="0"
                                     aria-valuemax="100" style="width: <?= $filled_percentage ?>%">
                                    <span class="sr-only"><?= $filled_percentage ?>% Complete</span>
                                </div>
                            </div>
                            <!--END PROGRESS BAR-->
                            <hr/>
                            <a href="<?php echo website_url_applicant; ?>/<?= $page_link ?>"
                               class="btn btn-custom btn-block">Complete Application</a>
                            <?php
                        }
                        ?>

                    </div>
                </div>

            </div>

        </div>
    </div>
</div>


<div class="row">
    <?php
    //check if task have been sent
    //end
    get_applicant_tasks($_SESSION["student_id"]);
    ?>
    <div class="clearfix"></div>
</div>


<div class="row">
    <div class="col-xs-12 col-lg-6">
        <h3><?= translate(terminology("Communication Inbox", $_SESSION['url'], '', true), $_SESSION['lang']) ?></h3>
        <p><?= translate(terminology("Communicate with the administration team directly.", $_SESSION['url'], '', true), $_SESSION['lang']) ?></p>

        <!--CHAT WIZARD-->
        <div class="box widget-chat">

            <div class="widget-actions">
                <form class="form-inline" action="<?= website_url ?>/static/inc/inc_dir_messages_process.php"
                      method="POST">
                    <button class="btn btn-custom">
                        Send
                    </button>
                    <div>
                        <textarea name="new_message" rows="1" id="textarea-chat-example"
                                  style="overflow: hidden; word-wrap: break-word; resize: vertical; height: 100px;"></textarea>
                    </div>
                </form>
            </div>
            <div class="clearfix"></div>

            <!--START CHAT STREAM-->
            <div class="chat_stream">
                <?php foreach ($messages as $msg) {
                    // current logged in person?
                    if ($msg['rec_id'] === $student_uid) { ?>
                        <div class="chat-message">
                            <img src="<?= $avatar ?>" alt="no image" width="150" border="0"/>
                            <div>
                                <b><?php echo $core_students_first_name; ?></b> says:
                                <span class="pull-right"><?php echo format_date("d M y h:i:s", $msg["date"]); ?></span>
                                <div><?php echo text_to_html($msg["db76"]); ?></div>
                            </div>
                        </div>
                    <?php } else { ?>
                        <div class="chat-message right">
                            <img src="<?= get_avatar($msg['rec_id']); ?>"/>
                            <div>
                                <b><?php echo pull_field("form_users", "db106", "WHERE id='$msg[rec_id]'"); ?></b> says:
                                <span class="pull-right"><?php echo format_date("d M y h:i:s", $msg["date"]); ?></span>
                                <div><?php echo text_to_html($msg["db76"]); ?></div>
                            </div>
                        </div>
                    <?php }
                }//end while ?>
            </div>
            <!--END CHAT STREAM-->

        </div>
    </div><!--/span-->
    <?php $class_resources = "col-xs-12 col-lg-6";
    include('static/inc/inc_shared_resources.php'); ?>

    <div class="col-xs-12 col-lg-6">
        <h3><?= translate(terminology("News and Updates", $_SESSION['url'], '', true), $_SESSION['lang']) ?></h3>
        <p><?= translate(terminology("Various information to help you through your application", $_SESSION['url'], '', true), $_SESSION['lang']) ?></p>

        <ul class="list-inline">
            <?php
            $i = 0;
            foreach ($blog_assist as $msg) {
                ?>
                <li class="col-lg-12" style="text-align: justify;">
                    <h4><?php echo $msg["db1695"]; ?></h4>

                    <?php
                    //show images
                    if ($msg["db1699"]) { ?>
                        <img src="<?= $front_web_url_file_loc ?>/media/<?php echo $msg["../43airschool - Copy/db1699"]; ?>"
                             class="thumbnail pull-left" alt="nes_image"/>
                    <?php } // end image show  ?>

                    <p><?php echo $msg["db1698"]; ?></p>
                    <a href="<?php echo website_url_applicant . '/blog/' . $msg["id"]; ?>" class="label label-default">Read
                        More</a>
                    <hr/>
                </li>
            <?php } // end of loop ?>
        </ul>
    </div>
</div> <!-- .row -->

<!--
<div class="row">
    <div class="col-xs-12">
    <h3>Please read this important information before you begin your application</h3>
        <div style="width:650px; height:400px; overflow:auto; border:4px solid #f0f0f0; padding:5px; background-color:#f4f4f4; margin-bottom:10px">
          <?php
// FUNCTION TO GET_CMS
//list($page_id,$cms_category,$cms_page_name,$cms_heading,$cms_brief,$cms_article)=get_cms('page_name',"Tier4_Information");
//echo $cms_article;//pull_field("form_cms","db650","WHERE db647='Tier4_Information'");
?>
        </div>
    </div>
</div>
-->
