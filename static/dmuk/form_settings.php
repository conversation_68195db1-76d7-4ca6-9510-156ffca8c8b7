<?php
//hide invoice on offer
$show_invoice = 1;
$offer_dont_show_paymth = 'yes';
$offer_show_signature = 'yes';
$send_back_home = 'yes';

//The message hides the message that says "Click cbutton to continue"
$hide_click_this_button_message = 1;

//Custom thank you message. This message appears on the submission page
function custom_thank_message()
{
    global $website_url_applicant;
    echo '<h1 class="main">Thank you.</h1>
<div class="alert alert-success"><i class="fa-lg fa fa-check-square-o" aria-hidden="true"></i> Thank you for submitting your application. Please now <a href="https://dmuk.edu.kz/payments/" style="text-decoration: underline; font-weight: bold;" target="_blank"> visit the payments page to pay the registration fee</a> so your application can move to the offer stage once it has been fully reviewed by our Admissions team.</div>
<a href="' . $website_url_applicant . '/application/Checklist" class="btn btn-primary"><i class="fa fa-arrow-left" aria-hidden="true"></i> Go back to my account</a>
</div>';
}

//this function wil automatically inject a custom student number into the system at the point at which a student is registered.
//custom_internal_number generator
function custom_internal_number_generator()
{

    $dbh = get_dbh();
    $sql = "SELECT *,CAST(db888 AS SIGNED) as db888 FROM core_students WHERE usergroup='" . $_SESSION['usergroup'] . "' ORDER BY db888 DESC LIMIT 1";
    dev_debug($sql);
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $checks = array();
    foreach ($results as $user_info) {
        $last_number = $user_info['db888'];
    }

    if (!$last_number) {
        $last_number = 1000;
    }

    $internal_ref_number = $last_number + 1;
    dev_debug('internal_ref_number' . $internal_ref_number);
    return $internal_ref_number;
}

// Get info about student
list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_internal_id, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake) = get_core_students($_SESSION['student_id']);

//get the correct landing page to send them to
//check if route is set
$page_to_navigate_to = pull_field("core_recruitment_campaign,form_cms", "db647", "WHERE db28379=form_cms.id AND core_recruitment_campaign.usergroup = $_SESSION[usergroup] AND db28378=$core_student_application_route");

//if no route then check if this is for programme
if (!$page_to_navigate_to) {
    $page_to_navigate_to = pull_field("core_recruitment_campaign,form_cms", "db647", "WHERE db28379=form_cms.id AND core_recruitment_campaign.usergroup = $_SESSION[usergroup] AND db28374=$core_student_application_route");
}

//personal link
if ($page_to_navigate_to !== '') {
    $page_link = $page_to_navigate_to;
} else {
    $page_link = "personal-information"; //all other
}

// what's the curse level?
// this mess is because course is not the id but the name...
/*
$sql = "SELECT core_course_level.db343
    FROM core_students
    INNER JOIN core_courses ON core_courses.id=core_students.db889
    INNER JOIN core_course_level ON core_course_level.id = core_courses.db341
    WHERE core_students.id = '$core_students_id'";
$res = mysqli_query($dbcon,$sql);
list($course_level) = mysqli_fetch_row($res);
*/

$avatar = get_avatar($core_students_rec_id);
$course_level = pull_field("core_course_level", "db343", "WHERE id=$core_students_level_of_entry");

// GET PERCENTAGE AND DISPLAY
list($required_fields, $required_filled) = filled_percentage_on_new_forms($core_students_id);

$filled_percentage = round(100 * $required_filled / $required_fields, 0, PHP_ROUND_HALF_DOWN);

// error_log("Filled: $filled_percentage%");

// error_log("Total required: $required_fields");
// error_log("Filled in: $required_filled");
$dbh = get_dbh();
// Fetch application stages
$sql = "SELECT db1131 FROM dir_appli_stages WHERE id = '$core_students_application_status'";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$application_status = $stmt->fetchColumn();
$application_status = explode('-', $application_status);
$application_status = trim($application_status[1]);

// Compares stage student is in to argument
function stage_complete($stage_id, $type = 0)
{
    global $core_students_application_status;

    if ($type !== 0) {
        return ($core_students_application_status > $stage_id) ? ' <span class="glyphicon glyphicon-ok"></span>' : '';
    } else {
        return ($core_students_application_status > $stage_id) ? ' completed' : '';
    }
}

// add new comment
if ($_POST["new_message"]) {
    $message = sanitise($_POST['new_message']);

    $random_id = random();
    $sql = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db76, db77, db80, db139) VALUES ('" . $random_id . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . floating_info("ref") . "', '$message', '" . session_info('uid') . "', '" . session_info('uid') . "', 'no')";
    $stmt = $dbh->prepare($sql);
    $stmt->execute();

    //add tracker
    track_use("Sent a private message", "$random_id");
}

//Grab children
##$pre_select = $_SESSION['chld_view'];
$sql_children = "SELECT *,core_students.id as applicant_id,(SELECT db232 FROM core_courses WHERE id=db889) AS course  FROM core_students WHERE usergroup='$_SESSION[usergroup]' AND (rec_id='$_SESSION[uid]') AND (core_students.rec_archive IS NULL OR core_students.rec_archive ='' ) ORDER BY id DESC";
$stmt = $dbh->prepare($sql_children);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$list_children = array();

foreach ($results as $row_children) $list_children[] = $row_children;
$child_ids = "";
$i = 0;
foreach ($list_children as $child) {
    if ($i == 0) {
        $child_ids .= "$child[id]";
    } else {
        $child_ids .= ",$child[id]";
    }
    $i++;
}

// Grab messages
$sql = "SELECT * FROM core_notes WHERE rel_id IN($child_ids) AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') ORDER BY date DESC";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$messages = array();
foreach ($results as $row) $messages[] = $row;
//Grab Blogs
$sql = "SELECT * FROM assist_articles WHERE usergroup='$_SESSION[usergroup]' AND LOCATE('$_SESSION[ulevel]',db1701) ORDER BY date DESC";
//echo $sql;

$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
//$blog_assist = array();

foreach ($results as $row_assist) $blog_assist[] = $row_assist;
//print_r($blog_assist);

// Also grab student user id to know who sent the message
$student_uid = $_SESSION['uid'];

$button_name = "Signup";
//get the domain info for the subdomain
$school_logo = "logo.png";
$page_title = "$schools_institution_name";
$page_description = "A simple convenient way to apply to us direct.";
//$page_key_title="International Admissions Portal"; // also used in main header

$custom_enquiry_success_message = "Thank you for your interest";


function get_public_files_multi($core_students_id)
{

    $dbh = get_dbh();

    //get public file uploads
    $sql_file = "SELECT *
      FROM form_file 
      WHERE rel_id='$core_students_id' 
      AND usergroup= '$_SESSION[usergroup]' 
      AND db202!='archived' 
      AND (db203='' || db203 IS NULL) 
      AND rec_id!='$_SESSION[uid]'
      AND (rec_archive='' || rec_archive IS NULL)";
    dev_debug($sql_file);

    $sth2 = $dbh->prepare($sql_file);
    $sth2->execute();
    $results_array = $sth2->fetchAll(PDO::FETCH_ASSOC);
    dev_debug($sql_file);

    return $results_array;
}


// mark as done as soon as the mark as bodone button is pressed.
if (isset($_POST['mark_as_done'])) {

    $task_id = ($_POST['mark_as_done']);
    $random = random();

    $dbh = get_dbh();
    $sql = "UPDATE core_tasks SET db1746 = 'completed',db1747 = now() WHERE id = '$task_id' ";
    dev_debug("---" . $sql);
    $sql = $dbh->prepare($sql);
    $sql->execute();

//get details of task
    $task_student_info = explode(",", pull_field("core_tasks", "group_concat(rel_id,db1741,db1743)", "WHERE id = '$task_id' "));
    $task_student_id = $task_student_info[0];
    $task_title = $task_student_info[1];
    $task_detail = $task_student_info[2];
    $uname_id = pull_field("core_students", "username_id", "WHERE id='$task_student_id' ");
    $student_link_url = engine_url . "/direct/proc?pg=4&vw=$uname_id&ref=$task_student_id";
    $task_message = "Task Completion Alert|" . $task_title . "|" . $task_detail;

    //	send message to owner about the task being marked as done
    $sql = "SELECT count(*) FROM core_notes WHERE username_id='" . $_SESSION['last random'] . "'  AND usergroup = '" . $_SESSION['usergroup'] . "' ";
    #echo $sql;
    $sql = $dbh->prepare($sql);
    $sql->execute();
    $duplicates = $sql->fetchColumn();

    if ($duplicates < 1) { // catchj page refreshes

        $sql = "INSERT INTO core_notes 
			(username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139) 
			VALUES ('" . $random . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$task_student_id', '1', '$task_message', '" . $task_id . "', 'yes', '$student_link_url', '0', 'no')";// if users does not want alerts lock using this
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
    }
    $_SESSION['last random'] = $random;
}

//////////tasks
function get_applicant_tasks($student_id)
{
    global $server_domain_name;

    $dbh = get_dbh();


    $task_sql = "SELECT 
id,
rel_id,
DATE_FORMAT(date, '%d/%m/%Y @%H%i') AS 'crafted',
DATEDIFF(db1744,NOW()) AS 'time_left',
db1741 AS 'title',
db1743 AS 'description',
db22441 AS 'Attached To',
(SELECT concat(db39,' ',db40) FROM core_students WHERE core_students.id=core_tasks.rel_id) AS 'Relating To',
(SELECT db106 FROM form_users WHERE id=db1745) AS 'assigned_to',
db22444 as party_responsible,
'' AS 'Application',
db1746 AS 'status',
db52933 as task_hide_options
FROM core_tasks
WHERE db22444='applicant' and (rec_archive is null or rec_archive ='')  and rel_id = ?
AND db1746 IN ('completed','active','on_hold')
ORDER BY id DESC
";
    dev_debug($task_sql);

    $sth3 = $dbh->prepare($task_sql);
    $sth3->execute(array($student_id));
    $sras_tasks = array();
    while ($row3 = $sth3->fetch(PDO::FETCH_ASSOC)) $task_rows[] = $row3;

    dev_debug("tasks Count " . count($task_rows));

    $task_incomplete_count = pull_field("core_tasks", "count(*)", "WHERE db1746 IN ('active','on_hold') and (rec_archive is null or rec_archive ='') AND rel_id='$student_id' ");

    if (count($task_rows) > 0) {
        ?>
        <div class="col-xs-12 col-lg-12">
            <h3>Tasks..</h3>
            <p>Below you can see a list of all the tasks currently assigned to you. To help us processes your
                applications we need you to complete all these tasks. </p>

            <?php
            //echo generate_table($sql3, array($profile_id),'');
            include(base_path . "/engine/tools/tasks/core_view.php");
            include(base_path . "/engine/tools/tasks/core_footer.php");
            ?>
        </div>
        <?php
    }// task check end
} // end function

///////CREATE TASK AS SOON AS THEY ACCEPT THE OFFER/////
function create_task_after_offer_acceptance($applicant_id, $task_title = '')
{

    $dbh = get_dbh();

    $db1741 = "Offer Acceptance Form";//title
    $db1743 = "Please download and sign the attached Offer Acceptance form.</br>
<b>Step 1:</b> Once signed please scan or take a photo of the signed contract and upload it into this task</br>.
<b>Step 2:</b> Please send the same signed copy over to us in the post. <br/>
https://doyrms.heiapply.com/admin/files/download/213383/";//detail
    $db1744 = date("Y-m-d G:i:s", mktime(date("G"), date("i"), date("s"), date("m"), date("d") + 14, date("Y")));;//date  2 weeks from today
    $db1746 = "active";//active
    $db22444 = "applicant";//applicant
    $db22580 = "general";//general
    $db23147 = "parent_contract";//type

    //create task for ALS and safe_guarding
    //if($task_title == $task_message || $_SESSION['safeguarding'] == 'yes'){
    dev_debug('signoff pack task');
    $sql = "INSERT INTO core_tasks (username_id, rec_id, usergroup,rel_id,
    db1741,db1743,db1744,db1746,db22444,db22580,db23147) 
    values (
    '" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . $applicant_id . "',
    '$db1741',
    '$db1743',
    '$db1744',
    '$db1746',
    '$db22444',
    '$db22580',
    '$db23147')";
    dev_debug($sql);
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    // }
}


function after_submit()
{

    $core_student_application_route = pull_field("core_students", "db2280", "WHERE id='$_SESSION[student_id]'");
    //Update Application Stage 1 to be checked

    $application_stage_1 = "db68302";

    $dbh = get_dbh();
    $checklist_table = get_system_page_name($core_student_application_route);

    $update_sql = "UPDATE $checklist_table SET $application_stage_1 = 'on' WHERE rel_id=?";
    $sth = $dbh->prepare($update_sql);
    $sth->execute(array($_SESSION['student_id']));

}
