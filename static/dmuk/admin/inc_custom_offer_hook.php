<?php
// Only runs for the current customer
// this script will run after an offer is processed on the system
$offer_id = $_POST['offer_id'] ?? false;
if ($_SESSION['usergroup'] == 104 && $offer_id) {
    if (!class_exists('Db_helper')) load_helper('db');
    $dbh = new Db_helper('object');

    $fields = ['student_id' => 'core_students.id', 'accepted' => 'db1813', 'route' => 'db2280',
        'first_name' => 'db39', 'middle_name' => 'db46', 'surname' => 'db40', 'email' => 'db764', 'year' => 'db890',
        'course' => 'db232', 'course_level' => 'db343', 'cohort' => 'db1681', 'intake' => 'db1681'];

    $dbh->join('core_students', 'core_students.id = dir_offers.rel_id', 'inner');
    $dbh->join('core_courses', 'core_courses.id = db889', 'left');
    $dbh->join('core_course_level', 'core_course_level.id = db50', 'left');
    $dbh->join('dir_cohorts', 'dir_cohorts.id = db1682', 'left');
    $latest_offer = $dbh->get_row($fields, 'dir_offers', ['dir_offers.id' => $offer_id]);

    if (empty($latest_offer)) return;

    switch ($latest_offer->route) {
        case 851:
            if ($latest_offer->accepted == 'Accepted') {
                $dbh->update('chk_z_104_default_checklist', ['db79433' => 'on'], ['rel_id' => $latest_offer->student_id]);

                // 31 = Offer Accepted Email - Email sent to applicant after accepting an offer
                $dbh->where("(rec_archive IS NULL or rec_archive='')");
                $template = $dbh->get_row('*', 'coms_template', ['db1147' => 31, 'usergroup' => $_SESSION['usergroup']]);
                if ($template) {
                    $from = $dbh->fetch_field('db1117', 'form_schools', ['id' => $_SESSION['usergroup']]);
                    send_offer_accepted_confirmation_email($latest_offer, $template, $from);
                }
            }
            break;
    }
}


function send_offer_accepted_confirmation_email($student, $template, $from)
{
    $keys = array_map(function ($key) {
        return '{{' . $key . '}}';
    }, array_keys((array)$student));
    $values = array_values((array)$student);
    $html = str_replace($keys, $values, $template->db1085);
    try {
        $plain = convert_html_to_text($html);
    } catch (Html2TextException $e) {
        $plain = strip_tags($html);
    }
    log_email($student->email, $template->db1086, $plain, $html, $from, 'offer_accepted_confirmation', $student->student_id);
}