<?php
// list pull
// FUNCTION TO GET_CMS
$page_id = 3380;

list($page_id, $cms_category, $cms_page_name, $cms_heading, $cms_brief, $cms_article, $cms_data, $cms_publish, $cms_page_title, $cms_keywords, $cms_page_description, $cms_included_form, $cms_privacy, $cms_refreshpage_on_add, $nextpage_url, $cms_introductory_instructions, $cms_pull_file, $cms_save_action, $cms_system_form_id, $cms_system_quiz_id) = get_cms('id', $page_id);

if ($show_reference_form == true) {
// echo admin only instructions
    echo "<h1>$cms_heading</h1>";
    echo $cms_introductory_instructions;
    echo '<b>' . $cms_brief . '</b><br/>';

// do some tag replacements
    $cms_article = str_replace("{{name}}", $ref_fname, $cms_article);
    $cms_article = str_replace("{{surname}}", $ref_surname, $cms_article);

    echo "<h2>Reference data for $ref_fname $ref_surname</h2>";

    //only show for sixthform

    // Expected grades
    $subject_choices = pull_field("dir_custom2", "CONCAT_WS(',',db10501,db10494,db10495,db10496)", "where rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]'");

    //$subject_selection = pull_field("dir_custom","CONCAT_WS(',',db6767,db6768,db6769,db6770,db6771,db6772,db6773,db6774,db6775,db6776)","where rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]'");

    // echo $subject_choices."----";
    //echo "=====";
    //echo $subject_selection;
    $selection_subjects = explode(",", $subject_choices);
    $subject_selection = explode(",", $subject_selection);

    $selection_subjects = str_replace("_", " ", $selection_subjects);
    $subject_selection = str_replace("_", " ", $subject_selection);

    //selection_subjects 
    $selection_subjects_box = 'These are the subjects the student has applied for';
    $selection_subjects_box .= '<table class="table table-bordered">';
    $selection_subjects_box .= "
    <tr>
        <th>Subject 1</th>
        <th>Subject 2</th>
        <th>Subject 3</th>
        <th>Subject 4</th>
    </tr>
    <tr>";
    foreach ($selection_subjects as $selection_subjects_boxes) {
        $selection_subjects_box .= "<td>$selection_subjects_boxes</td>";
    }
    $selection_subjects_box .= '</tr></table>';

    //$subject_selection 
    // $subject_selection_box = '<table class="table table-bordered"><tr>';
    // foreach($subject_selection as $subject_selection_boxes){
    //     $subject_selection_box.="<td>$subject_selection_boxes</td>";
    // }
    //$subject_selection_box.= '</tr></table>';

    //only show for sixform
    if ($_SESSION['course_id_field'] == 9092) {
        $cms_article = str_replace("[[subject_choices]]", $selection_subjects_box, $cms_article);
        $cms_article = str_replace("[[subject_selection]]", $subject_selection_box, $cms_article);
    } else {
        $dbh = get_dbh();
        $query = "SELECT * FROM dir_student_public_results WHERE rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]' AND (rec_archive is NULL or rec_archive ='') ";

        $sth = $dbh->prepare($query);
        $sth->execute();

        //selection_subjects 
        $selection_subjects_box = '';
        $selection_subjects_box = 'These are the subjects the student has added predicated grades for';
        $selection_subjects_box .= '<table class="table table-bordered">';
        $selection_subjects_box .= "
        <tr>
            <th>Subject</th>
            <th>Predicated Grade</th>
        </tr>
        ";
        foreach ($row_result = $sth->fetchAll() as $row) {
            $selection_subjects_box .= "<tr><td>" . $row["db426"] . "</td><td>" . $row["db428"] . "</td></tr>";
            //$row['rec_id'];

        }
        $selection_subjects_box .= '</table>';
        $cms_article = str_replace("[[subject_choices]]", $selection_subjects_box, $cms_article);
        $cms_article = str_replace("[[subject_selection]]", '', $cms_article);
    }

    echo $cms_article;
}
