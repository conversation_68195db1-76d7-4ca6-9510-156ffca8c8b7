<div class="sidebar" style="margin-top: 22px;">
    <?php
    if ($_SESSION['loggedin']) { // only show to logged in users

        echo '<div ><a href="' . website_url_applicant . '/logout" class="btn btn-danger btn-lg btn-block"><i class="fa fa-lg fa-sign-out" aria-hidden="true"></i> Log out >></a></div>';

        error_log('tracker');

//only show on the homepage
        if ($cms_page_name == "Checklist" || $cms_page_name == "checklist" || $cms_page_name == "applicant-faqs" || find_slug(2) == "blog") {


            $dbh = get_dbh();

// Get the stages for student's route
            $sql = 'SELECT system_table.form_id, system_table.db_field_name, system_table.name
    FROM system_table
    INNER JOIN core_students ON core_students.db2280 = system_table.pg_id
    WHERE core_students.id = ? AND system_table.type="stage"
	AND locked IN(2,0)
    ORDER BY system_table.form_order';
            $sth = $dbh->prepare($sql);
            $sth->execute(array($_SESSION['student_id']));
            $stages = array();
            while ($row = $sth->fetch(PDO::FETCH_ASSOC)) $stages[] = $row;

// pull the statuses of stages for this particular student
            $checklist_table_name = 'chk_' . pull_field(
                    'system_pages INNER JOIN core_students ON core_students.db2280 = system_pages.page_id',
                    'page_name', "WHERE core_students.id = '$_SESSION[student_id]'");
            error_log('checklist table is called ' . $checklist_table_name);


            $sql = 'SELECT ';
            foreach ($stages as $stage) {
                $sql .= $stage['db_field_name'] . ', ';
            }
            $sql = substr($sql, 0, -2);
            $sql .= " FROM $checklist_table_name WHERE rel_id = ? LIMIT 1";
            error_log('query to get stage statuses: ' . $sql);
            $sth = $dbh->prepare($sql);
            $sth->execute(array($_SESSION['student_id']));
            $stage_status = $sth->fetch(PDO::FETCH_ASSOC);
            ?>
            <div class="row">
                <div class="col-xs-12">
                    <ul class="list-group list-group_home">
                        <li class="list-group-item">
                            <h4 class="list-group-item-heading">Application Tracker</h4>
                            <p class="list-group-item-text">A quick summary of how your application is proceeding</p>
                        </li>

                        <li class="list-group-item">
                            <div class="circle-text <?php echo($filled_percentage == '100' ? 'completed' : '') ?>">
                                <span>1</span>
                            </div>
                            Application Completion
                        </li>
                        <?php
                        $i = 1;
                        foreach ($stages as $stage) {

                            $exp = explode('-', $stage['name']);
                            $sts = $stage_status[$stage['db_field_name']];
//            $sts_image = ($sts === 'on') ? '<img src="'.engine_url.'/images/icon_tick.png" width="16" height="16" alt="tick" />' : '';

                            if ($sts === 'on') {
                                //$sts_image = '<img src="'.engine_url.'/images/icon_tick.png" width="16" height="16" alt="tick" />';
                                $tick_class = 'tick_class';
                                $number = '<i class="fa fa-lg fa-check" aria-hidden="true"></i>';
                            } else {
                                $sts_image = '';
                                $tick_class = '';
                                $number = $i + 1;
                            }
                            ?>
                            <li class="list-group-item clearfix <?= stage_complete($i) ?><?php echo ' ' . $tick_class; ?>">
                                <div class="circle-text <?php if ($sts === 'on') {
                                    echo "completed";
                                } ?>">
                                    <span><?= $number; ?></span>
                                </div><?= $exp[1] ?> <?= $sts_image ?></li>

                            <?php
                            $i++;
                        } ?>

                    </ul>
                </div>
            </div>
            <?php
            //end of checklist
        } else {
            ?>
            <div class="row">
                <div class="col-xs-12">
                    <ul class="list-group">
                        <li class="list-group-item"><h4>Your Application</h4></li>
                        <?php
                        //get the right pages for this section
                        $pages_id = get_page_list_based_on_rules($_SESSION['student_id']);

                        if ($pages_id != "") {
                            get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq','ucas','product_page') AND id IN($pages_id) ORDER BY FIELD(id,$pages_id)", "yes", "list-group-item", "show_auto_number");
                        } else {
                            get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");
                        }
                        ?>
                    </ul>
                </div>
            </div>
        <?php } ?>

        <div class="row">
            <div class="col-xs-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4>FAQ & Support</h4>
                    </div>
                    <div class="panel-body">
                        <h5><i class="fa fa-question-circle" aria-hidden="true"></i> Can I track my application after
                            submitting?</h5>
                        <p>Yes, just log in and select 'Track your application' in the Quick links menu.</p>
                        <a href="<?php echo website_url_applicant; ?>/applicant-faqs" class="btn btn-primary">See
                            more</a>
                    </div>
                </div>
            </div>
        </div>

        <?php
    }// end of check if checklist

    if (!$_SESSION['loggedin']) { // only show to logged in users
        ?>


        <h3>Quick Links</h3>
        <ul class="list-group">
            <?php
            //hide all second stage pages
            get_cms_nav("application", "public", "db656 IN ('landing_page','forms','information','submit_page','upload','faq')", "yes", "list-group-item");
            ?>

            <li class="list-group-item"><a href="<?php echo website_url_applicant; ?>/login"><i
                            class="fa fa-fw fa-lg fa-check-circle-o" aria-hidden="true"></i> Already Have An
                    Account?</a></li>
        </ul>

        <?php
    }

    ?>
</div>