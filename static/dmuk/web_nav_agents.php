<?php
if ($_SESSION['loggedin']) { // only show to logged in users

    echo '<div class="btn btn-info btn-lg btn-block"><a href="' . website_url_agent . '/logout">Log out >></a></div><br/>';

    error_log('tracker');

//only show on the homepage
    if ($cms_page_name == "Checklist" || find_slug(2) == "blog") {
        ?>
        <div class="row">
            <div class="col-xs-12">
                <div class="alert alert-warning">
                    <h3>Agents Platform</h3>
                    <hr/>
                    <p>
                    <h4>Welcome to your dedicated portal</h4>
                    Welcome to our agents platform. Via this platform you can create, update and submit applications for
                    the applicants you are supporting. You can also use the portal to send and receive communication
                    from the administrators processing the applications.
                </div>
            </div>
        </div>


        <div class="col-xs-12 col-lg-12">
            <h3><?= translate("News and Updates", $_SESSION['lang']) ?></h3>
            <p><?= translate("Various information to support & keep you informed dring the application process", $_SESSION['lang']) ?></p>

            <ul class="list-inline">
                <?php
                $i = 0;
                foreach ($blog_assist as $msg) {
                    ?>
                    <li class="col-lg-12">
                        <h4><?php echo $msg["db1695"]; ?></h4>
                        <h5><?php echo $msg["db1698"]; ?></h5>
                        <!--                    <img src="--><?php //=$front_web_url_file_loc?><!--/media/-->
                        <?php //echo $msg["db1699"]; ?><!--" class="thumbnail pull-left" alt="nes_image" />-->
                        <a href="<?php echo website_url_agent . '/blog/' . $msg["id"]; ?>"
                           class="label label-info"><?= translate("Read More", $_SESSION['lang']) ?></a>
                        <hr/>
                    </li>
                <?php } // end of loop
                echo($msg == '' ? '<small>- ' . translate("Sorry no message to show", $_SESSION['lang']) . '</small>' : '');
                ?>
            </ul>
        </div>
        <?php
    }

    if ($cms_page_name !== "Checklist" && find_slug(2) !== "blog") {

        if ($appli_submited_check > 1) {


            $dbh = get_dbh();

            // Get the stages for student's route
            $sql = 'SELECT system_table.form_id, system_table.db_field_name, system_table.name
		FROM system_table
		INNER JOIN core_students ON core_students.db2280 = system_table.pg_id
		WHERE core_students.id = ? AND system_table.type="stage"
		AND locked IN(2,0)
		ORDER BY system_table.form_order';
            $sth = $dbh->prepare($sql);
            $sth->execute(array($_SESSION['student_id']));
            $stages = array();
            while ($row = $sth->fetch(PDO::FETCH_ASSOC)) $stages[] = $row;

            // pull the statuses of stages for this particular student
            $checklist_table_name = 'chk_' . pull_field(
                    'system_pages INNER JOIN core_students ON core_students.db2280 = system_pages.page_id',
                    'page_name', "WHERE core_students.id = '$_SESSION[student_id]'");
            error_log('checklist table is called ' . $checklist_table_name);


            $sql = 'SELECT ';
            foreach ($stages as $stage) {
                $sql .= $stage['db_field_name'] . ', ';
            }
            $sql = substr($sql, 0, -2);
            $sql .= " FROM $checklist_table_name WHERE rel_id = ? LIMIT 1";
            error_log('query to get stage statuses: ' . $sql);
            $sth = $dbh->prepare($sql);
            $sth->execute(array($_SESSION['student_id']));
            $stage_status = $sth->fetch(PDO::FETCH_ASSOC);

            ?>
            <div class="row">
                <div class="col-xs-12">
                    <ul class="list-group list-group_home">
                        <li class="list-group-item">
                            <h4 class="list-group-item-heading">Application Tracker</h4>
                            <p class="list-group-item-text">A quick summary of how your application is proceeding</p>
                        </li>

                        <li class="list-group-item">
                            <div class="circle-text">
                                <span>0</span>
                            </div>
                            Application Completion
                            <?php echo($filled_percentage == '100' ? '<img src="' . engine_url . '/images/icon_tick.png" width="16" height="16" alt="tick" />' : '') ?>
                        </li>
                        <?php
                        $i = 1;
                        foreach ($stages as $stage) {
                            $exp = explode('-', $stage['name']);
                            $sts = $stage_status[$stage['db_field_name']];
                            $sts_image = ($sts === 'on') ? '<img src="' . engine_url . '/images/icon_tick.png" width="16" height="16" alt="tick" />' : '';
                            ?>
                            <li class="list-group-item<?= stage_complete($i) ?>">
                                <div class="circle-text">
                                    <span><?= $i ?></span>
                                </div><?= $exp[1] ?> <?= $sts_image ?></li>
                            <?php
                            $i++;
                        } ?>

                    </ul>
                </div>
            </div>
            <?php
            //end of checklist
        } else {
            ?>
            <div class="row">
                <div class="col-xs-12">
                    <ul class="list-group">
                        <li class="list-group-item"><b>Your Application</b></li>
                        <?php //get rules for usergroup
                        $dbh = get_dbh();
                        $sql = $dbh->prepare("SELECT db_field_name AS 'name', pr.id AS 'rule_id', db19262 AS 'condition' FROM dir_page_rules pr INNER JOIN system_table ON form_id=db19266
 				WHERE (pr.rec_archive IS NULL or pr.rec_archive='')
				AND pr.usergroup = $_SESSION[usergroup] ORDER BY db19264 ASC ");
                        $sql->execute();
                        $page_rules = $sql->fetchAll(PDO::FETCH_OBJ);
                        //loop through rules for usergroup
                        $pages_id = "";
                        foreach ($page_rules as $page_rule) {
                            //check if page has already been found
                            if ($_SESSION['student_id'] == 5242) {
                                // echo " condition = $page_rule->condition";
                            }
                            if ($pages_id == "") {
                                if ($page_rule->condition == "default") {
                                    $sql = $dbh->prepare("SELECT db19263
                                                FROM dir_page_rules pr
                                                WHERE  pr.id = $page_rule->rule_id
                                                      AND (pr.rec_archive IS NULL or pr.rec_archive='')
                                                ORDER BY db19264 ASC");
                                    $sql->execute();
                                    $result = $sql->fetchColumn();
                                    if ($result) {
                                        $pages_id = $result;
                                    }
                                } else {

                                    //test the rule
                                    $sql = $dbh->prepare("SELECT db19263
                                                FROM dir_page_rules pr
                                                     INNER JOIN core_students cs ON db19262 = $page_rule->name
                                                WHERE $_SESSION[usergroup]
                                                      AND cs.id=$_SESSION[student_id]
                                                      AND pr.id = $page_rule->rule_id
                                                      AND (pr.rec_archive IS NULL or pr.rec_archive='')
                                                ORDER BY db19264 ASC");
                                    $sql->execute();
                                    $result = $sql->fetchColumn();
                                    if ($result) {
                                        $pages_id = $result;
                                    }
                                    //$pages_id = pull_field("dir_page_rules,core_students","db19263","WHERE db19262=$page_rules_set AND dir_page_rules.usergroup=$_SESSION[usergroup] AND core_students.id=$_SESSION[student_id] AND (dir_page_rules.rec_archive IS NULL or dir_page_rules.rec_archive='')");

                                }
                            } else {
                                //do nothing
                            }

                        }
                        if ($_SESSION['student_id'] == 5242) {
                            //  echo "web nav pages id = $pages_id";
                        }

                        if ($pages_id != "") {
                            get_cms_nav("agent", "private", "db656 IN ('forms','information','submit_page','upload','faq','ucas') AND id IN($pages_id)", "yes", "list-group-item");
                        } else {
                            get_cms_nav("agent", "private", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");
                        }
                        ?>
                    </ul>
                </div>
            </div>
        <?php } ?>

        <?php
    }// end of check if checklist

}//end checklist check

if (!$_SESSION['loggedin']) { // only show to logged in users
    ?>
    <div class="row col-xs-12">
        <p><b>Quick-links</b></p>
        <ul class="list-group">
            <?php
            get_cms_nav("agent", "public", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");
            ?>
            <li class="list-group-item"><a
                        href="<?php echo website_url_agent; ?>/parent_register" <?php if ($current_id == 'register') {
                    $sty = 'class="nav_selected"';
                } ?>>Start a new application >></a></li>
            <li class="list-group-item"><a href="<?php echo website_url_agent; ?>/login">Continue an application you
                    have already started >></a></li>
            <li class="list-group-item"><a href="<?php echo website_url_agent; ?>/login">Log in to view the status of a
                    completed application >></a></li>
        </ul>
    </div>
    <?php
}
