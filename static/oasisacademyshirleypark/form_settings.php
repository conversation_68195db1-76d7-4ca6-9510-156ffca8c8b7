<?php
// Get info about student
list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake, $core_students_deleted, $core_student_application_ucas, $core_student_ucas_pass, $core_students_course_of_study_id, $core_students_course_audio) = get_core_students($_SESSION['student_id']);
$dbh = get_dbh();
// Get avatar
$avatar = get_student_avatar($core_students_id);

// Get course level
$course_level = pull_field("core_course_level", "db343", "WHERE id=$core_students_level_of_entry");

// get percentage of application completed and display
list($required_fields, $required_filled) = filled_percentage_on_new_forms($core_students_id);
$filled_percentage = round(100 * $required_filled / $required_fields, 0, PHP_ROUND_HALF_DOWN);
if ($filled_percentage == 'NAN') {
    $filled_percentage = 0;
}

// Fetch application stages
// ----------------------------------------------------------------------------
// Fetch application stages
$sql = "SELECT db1131 FROM dir_appli_stages WHERE id = '$core_students_application_status'";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$application_status = $stmt->fetchColumn();
$application_status = explode('-', $application_status);
$application_status = trim($application_status[1]);
//----------------------------------------------------------------------------
// Fetch application stages END

// Compares stage student is in to argument
function stage_complete($stage_id, $type = 0)
{
    global $core_students_application_status;

    if ($type !== 0) {
        return ($core_students_application_status > $stage_id) ? ' <span class="glyphicon glyphicon-ok"></span>' : '';
    } else {
        return ($core_students_application_status > $stage_id) ? ' completed' : '';
    }
}

//Grab multiple student profiles
$sql_children = "SELECT * FROM core_students WHERE usergroup='$_SESSION[usergroup]' AND rec_id='$_SESSION[uid]' ORDER BY db39 ASC";
$stmt = $dbh->prepare($sql_children);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$list_children = array();
foreach ($results as $row_children) $list_children[] = $row_children;
$child_ids = "";
$i = 0;
foreach ($list_children as $child) {
    if ($i == 0) {
        $child_ids .= "$child[id]";
    } else {
        $child_ids .= ",$child[id]";
    }
    $i++;
}

// Grab private messages
$sql = "SELECT * FROM core_notes WHERE rel_id IN($child_ids) AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') ORDER BY date DESC";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$messages = array();
foreach ($results as $row) $messages[] = $row;

//Grab Blogs
$sql = "SELECT * FROM assist_articles WHERE usergroup='$_SESSION[usergroup]' AND db1697='yes' AND LOCATE('$_SESSION[ulevel]',db1701) AND (rec_archive IS NULL OR rec_archive = '') ORDER BY date DESC";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$blog_assist = array();
foreach ($results as $row_assist) $blog_assist[] = $row_assist;

// Also grab student user id to know who sent the message
$student_uid = $_SESSION['uid'];

//custom_internal_number generator
function custom_internal_number_generator()
{

    $dbh = get_dbh();
    $sql = "SELECT *,CAST(db888 AS SIGNED) as db888 FROM core_students WHERE usergroup='" . $_SESSION['usergroup'] . "' ORDER BY db888 DESC LIMIT 1";
    dev_debug($sql);
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $checks = array();
    foreach ($results as $user_info) {
        $last_number = $user_info['db888'];
    }

    if (!$last_number) {
        $last_number = 1000;
    }

    $internal_ref_number = $last_number + 1;
    dev_debug('internal_ref_number' . $internal_ref_number);
    return $internal_ref_number;
}

//Hide the Next & Previous Buton
if ($appli_submited_check != "1") {
    //$totally_kill_proceed_buttons = 1;
}

//The message hides the message that says "Click cbutton to continue"
$hide_click_this_button_message = 1;

//Custom thank you message. This message appears on the submission page
function custom_thank_message()
{
    global $website_url_applicant;
    echo '<h1 class="main">Thank you.</h1>
<div class="alert alert-success"><i class="fa-lg fa fa-check-square-o" aria-hidden="true"></i> Your submission has been sent successfully.</div>
<a href="' . $website_url_applicant . '/application/Checklist" class="btn btn-primary"><i class="fa fa-arrow-left" aria-hidden="true"></i> Go back to my account</a>
</div>';
}

$button_name = "Signup";
//get the domain info for the subdomain
$school_logo = "logo.png";
$page_title = "$schools_institution_name";
$page_description = "A simple convenient way to apply to us direct.";

//let people auto login
$_SESSION['autologin'] = 'yes';


$page_link = "personal-information";


/////checklist
//only show if this is checklist
$checklist = strtolower(find_slug(1));
if ($checklist == 'checklist') {
//////////tasks


    // mark task as done as soon as the mark as done button is pressed.
    if (isset($_POST['mark_as_done'])) {
        $dbh = get_dbh();
        $task_id = ($_POST['mark_as_done']);

        $sql = "UPDATE core_tasks SET db1746 = 'completed',db1747 = now() WHERE id = '$task_id'";
        $sql = $dbh->prepare($sql);
        $sql->execute();

//get details of task
        $task_title = pull_field("core_tasks", "concat(db1741,' - ',db1743)", "WHERE id = '$task_id' ");
        $uname = pull_field("core_students", "username_id", "WHERE id='$_SESSION[student_id]' ");
        $student_link_url = engine_url . "/direct/proc?pg=4&vw=$uname&ref=$_SESSION[student_id]";

//	send message to owner about the task being marked as done

        $sql = "INSERT INTO core_notes 
		(username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139) 
		VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$_SESSION[student_id]', '14', 'Task Completion Alert - $task_title', '" . $task_id . "', 'yes', '$student_link_url', '0', 'no')";// if users does not want alerts lock using this
        $stmt = $dbh->prepare($sql);
        $stmt->execute();


    }

    $dbh = get_dbh();
    $task_sql = "SELECT 
id,
DATE_FORMAT(date, '%d/%m/%Y @%H%i') AS 'crafted',
DATEDIFF(db1744,NOW()) AS 'time_left',
db1741 AS 'title',
db1743 AS 'description',
db22441 AS 'Attached To',
(SELECT concat(db39,' ',db40) FROM core_students WHERE core_students.id=core_tasks.rel_id) AS 'Relating To',
(SELECT db106 FROM form_users WHERE id=db1745) AS 'assigned_to',
db22444 as party_responsible,
'' AS 'Application',
db1746 AS 'status'
FROM core_tasks
WHERE db22444='applicant' and (rec_archive is null or rec_archive ='') and rel_id = ?
AND db1746 IN ('completed','active','on_hold')
ORDER BY id ASC
";
#echo $task_sql;

    $sth3 = $dbh->prepare($task_sql);
    $sth3->execute(array($_SESSION['student_id']));
    $tasks = array();
    while ($row3 = $sth3->fetch(PDO::FETCH_ASSOC)) $task_rows[] = $row3;

    $task_incomplete_sql_count = "SELECT
count(*)
FROM core_tasks
WHERE db22444='applicant' and db1746 IN ('active','on_hold') and (rec_archive is null or rec_archive ='') and rel_id = ?";
    $sth4 = $dbh->prepare($task_incomplete_sql_count);
    $sth4->execute(array($_SESSION['student_id']));
    $task_incomplete_count = $sth4->fetchColumn();
}
?>
<style>
    .form_wrapper {
        background: #58b74e;
        margin: 0;
        text_align: left;
        font-family: Arial
    }

    .form_style_bkg {
        color: #666;
        padding: 0;
        margin: 10px auto;
        max-width: 850px;
        background-color: #fff;
        -webkit-box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
        -moz-box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
        box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
    }

    .top_logo {
        padding: 0;
        max-height: 150px;
    }

    .form_detail {
        padding: 10px
    }

    .slogan {
        background: #58b74e;
        color: #fff
    }

    .box > .icon {
        text-align: center;
        position: relative;
    }

    .box > .icon > .image {
        position: relative;
        z-index: 2;
        margin: auto;
        width: 88px;
        height: 88px;
        border: 8px solid white;
        line-height: 88px;
        border-radius: 50%;
        background: #fff;
        vertical-align: middle;
        padding: 10px
    }

    .box > .icon:hover > .image {
        background: #f0f0f0;
    }

    .box > .icon > .image > i {
        font-size: 36px !important;
        color: #fff !important;
    }

    .box > .icon:hover > .image > i {
        color: white !important;
    }

    .box > .icon > .info {
        margin-top: -24px;
        background: rgba(0, 0, 0, 0.04);
        border: 1px solid #e0e0e0;
        padding: 15px 0 10px 0;
    }

    .box > .icon:hover > .info {
        background: rgba(0, 0, 0, 0.04);
        border-color: #e0e0e0;
        color: white;
    }

    .box > .icon > .info > h3.title {
        font-family: "Roboto", sans-serif !important;
        font-size: 16px;
        color: #222;
        font-weight: 500;
    }

    .box > .icon > .info > p {
        font-family: "Roboto", sans-serif !important;
        font-size: 13px;
        color: #666;
        line-height: 1.5em;
        margin: 20px;
    }

    .box > .icon:hover > .info > h3.title, .box > .icon:hover > .info > p, .box > .icon:hover > .info > .more > a {
        color: #222;
    }

    .box > .icon > .info > .more a {
        font-family: "Roboto", sans-serif !important;
        font-size: 12px;
        color: #222;
        line-height: 12px;
        text-transform: uppercase;
        text-decoration: none;
    }

    .box > .icon:hover > .info > .more > a {
        color: #fff;
        padding: 6px 8px;
        background-color: #004555
    }

    .box .space {
        height: 30px;
    }
</style>
