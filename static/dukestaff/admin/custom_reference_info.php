<?php
// list pull
// FUNCTION TO GET_CMS
global $core_students_course_of_study_id;
global $core_students_id;


switch ($ref_type) {
    case "Character_Reference":
        $page_id = 6359;
        break;
    case "Professional_Reference":
        $department = pull_field("core_students a,core_courses b", "db25610", "WHERE a.db889=b.id AND a.id='" . $_SESSION["student_id"] . "'");
        //core_courses WHERE db25610=core_departments.id AND (rec_archive is NULL OR rec_archive ='')
        if (in_array($department, [260, 359, 344, 326, 317, 338, 320, 329, 323, 332, 335, 347, 362, 368, 353, 341, 350, 356, 371, 374])) {
            $page_id = 6353;
        } else {
            $page_id = 6356;
        }

        break;

}//end


dev_debug("page_id=" . $page_id);
dev_debug("ref_type=" . $ref_type);
dev_debug("dob=" . $core_students_date_of_birth);
dev_debug("core_students_course_of_study=" . $core_students_course_of_study_id);

list($page_id, $cms_category, $cms_page_name, $cms_heading, $cms_brief, $cms_article, $cms_data, $cms_publish, $cms_page_title, $cms_keywords, $cms_page_description, $cms_included_form, $cms_privacy, $cms_refreshpage_on_add, $nextpage_url, $cms_introductory_instructions, $cms_pull_file, $cms_save_action, $cms_system_form_id, $cms_system_quiz_id) = get_cms('id', $page_id);

dev_debug("page-details = $page_id,$cms_category,$cms_page_name,$cms_heading,$cms_brief,$cms_article,$cms_data,$cms_publish,$cms_page_title,$cms_keywords,$cms_page_description,$cms_included_form,$cms_privacy,$cms_refreshpage_on_add,$nextpage_url,$cms_introductory_instructions,$cms_pull_file,$cms_save_action,$cms_system_form_id,$cms_system_quiz_id)=get_cms('id',$page_id");

if ($show_reference_form == true && 4 != $_GET['pg']) {
// echo admin only instructions
    echo "<h1>$cms_heading</h1>";
    echo $cms_introductory_instructions;
    echo '<b>' . $cms_brief . '</b><br/>';

// do some tag replacements
    $cms_article = str_replace("{{name}}", $ref_fname, $cms_article);
    $cms_article = str_replace("{{surname}}", $ref_surname, $cms_article);

    $cms_article = str_replace("[[name]]", $ref_fname, $cms_article);
    $cms_article = str_replace("[[surname]]", $ref_surname, $cms_article);
    $cms_article = str_replace("[[date_of_birth]]", $core_students_date_of_birth, $cms_article);

    echo "<h2>Reference data for $ref_fname $ref_surname</h2>";

    //only show for sixthform
    if ($_SESSION['course_id_field'] == 9092) {//year 12

        $page_id = 3650;
        // Expected grades
        //select rel_id,group_concat(db426) from dir_student_public_results where usergroup=96 group by rel_id
        $subject_choices = pull_field("dir_student_public_results", "group_concat(db426)", "WHERE rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]'");

        //$subject_selection = pull_field("dir_custom","CONCAT_WS(',',db6767,db6768,db6769,db6770,db6771,db6772,db6773,db6774,db6775,db6776)","where rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]'");

        // echo $subject_choices."----";
        //echo "=====";
        //echo $subject_selection;
        $selection_subjects = explode(",", $subject_choices);
        //$subject_selection = explode(",",$subject_selection);

        $selection_subjects = str_replace("_", " ", $selection_subjects);
        //$subject_selection = str_replace("_"," ",$subject_selection);

        //selection_subjects
        $selection_subjects_box = "These are the subjects the applicant has declared as currently studying. Please use this list in association with the question titled 'Predicted Grades' below.";
        $selection_subjects_box .= '<table class="table table-bordered" style="background:white">';
        $selection_subjects_box .= "
        <tr>";
        $ii = 1;
        foreach ($selection_subjects as $selection_subjects_boxes) {
            $selection_subjects_box .= "<th>Subject $ii</th>";
            $ii++;//increment
        }
        $selection_subjects_box .= "</tr><tr>";
        foreach ($selection_subjects as $selection_subjects_boxes) {
            $selection_subjects_box .= "<td>$selection_subjects_boxes</td>";
        }
        $selection_subjects_box .= '</tr></table>';

        //$subject_selection
        // $subject_selection_box = '<table class="table table-bordered"><tr>';
        // foreach($subject_selection as $subject_selection_boxes){
        //     $subject_selection_box.="<td>$subject_selection_boxes</td>";
        // }
        //$subject_selection_box.= '</tr></table>';

        //only show for sixform
        $cms_article = str_replace("[[subject_choices]]", $selection_subjects_box, $cms_article);
        $cms_article = str_replace("[[subject_selection]]", $subject_selection_box, $cms_article);
    } else {
        $cms_article = str_replace("[[subject_choices]]", '', $cms_article);
        $cms_article = str_replace("[[subject_selection]]", '', $cms_article);
    }

    echo $cms_article;
}
