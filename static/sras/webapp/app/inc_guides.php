<style type="text/css">
    h2 {
        margin-bottom: 10px;
    }
    h2 a:hover, h2 a:focus{
        color: #ff6600;
        text-decoration: none;
    }
</style>
<div style="margin-top:40px"></div>

<?php
//itinerary
if ( $slug2 == "detail" &&  $slug3!== "" ) {

//get the files
$result2 = get_guides('username',$slug3);
$row = mysqli_fetch_assoc($result2);
?>

<div class="col-md-12">
    
<a href="<?=$website_url?>/app/reviews/<?=$object_id?>"><span class="glyphicon glyphicon-arrow-left" aria-hidden="true"></span></a>
    <h2><b> <?=$row['title']?> </b></h2>
    
    <div class="col-md-8">
<h4><?=$row['summary']?></h4>
    </div>

<div id="guides">
<?=$row['detail']?>    
</div>




<?php
}else{
?>


<div class="col-md-8">
      <h2 class="text-center"><b> Guides </b></h2>
<ul class="list-group">

    <?php
        $result3 = get_guides('cat',1);
            while ( $row3 = mysqli_fetch_assoc( $result3 ) ) {
      ?>
    <li class="list-group-item">
        <h3><h3><a href="<?=$website_url?>/app/guides/detail/<?=$row3['uid']?>/"><?=$row3['title']?></a></h3>
        <span><a href="<?=$website_url?>/app/guides/detail/<?=$row3['uid']?>/" style="color:darkorange"><span class="	glyphicon glyphicon-arrow-right pull-right" aria-hidden="true"></span></a></span>
        <p class="hg-article-body"><?=$row3['summary']?></p>
    </li>
    <?php
        }
    ?>
    
    </ul>
    
</div>
    
</div>
<?php
}///ench page self
?>