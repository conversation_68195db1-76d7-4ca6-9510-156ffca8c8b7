body {
  padding-top: 50px;
  font-family: 'Source Sans Pro', sans-serif;
}


.footer {
  padding-top: 40px;
  padding-bottom: 40px;
  margin-top: 40px;
  border-top: 1px solid #eee;
}

/* Main marketing message and sign up button */
.home-banner {
height: 100%;
-webkit-background-size: cover;
-moz-background-size: cover;
-o-background-size: cover;
background-size: cover;
}
.jumbo_bkg{
display: table;
color:white;
width: 100%;
height: auto;
padding: 80px 0 50px 0;
text-align: center;
color: rgb(255, 255, 255);
background: rgb(12, 129, 160);
background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJod…BoZWlnaHQ9IjEwMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
background: -moz-radial-gradient(center, ellipse cover, rgb(12,129,160) 0%, rgb(0,89,112) 100%);
background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%,rgb(12,129,160)), color-stop(100%,rgb(0,89,112)));
background: -webkit-radial-gradient(center, ellipse cover, rgb(12, 129, 160) 0%,rgb(0, 89, 112) 100%);
background: -o-radial-gradient(center, ellipse cover, rgb(12,129,160) 0%,rgb(0,89,112) 100%);
background: -ms-radial-gradient(center, ellipse cover, rgb(12,129,160) 0%,rgb(0,89,112) 100%);
background: radial-gradient(ellipse at center, rgb(19, 191, 56) 0%,rgb(38, 112, 0) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#389D0C', endColorstr='#097715',GradientType=1 );
}
.jumbotron {
  text-align: center;
  background-color:transparent;
}
.jumbotron .btn {
  padding: 14px 24px;
  font-size: 21px;
}

.jumbotron h1, .jumbotron .h1 {
    font-size: 51px;
    margin-top: 0px;
    line-height: 62px;
	color:#fff;
}
.supplier_link{ padding-top:150px;}

@media (max-width: 768px) {
	.jumbotron h1, .jumbotron .h1 {
    font-size: 35px;
    margin-top: 0px;
    line-height: 35px;
	color:#fff;
	}
	.supplier_link{ padding-top:30px;}
	.jumbo_bkg{
	display: table;
	color:white;
	width: 100%;
	height: auto;
	padding: 10px 0 0px 0;
	}
}

.navbar{background:rgb(123, 165, 34); padding-top:10px}
.nav a{ color:#333 !important;}

.expand{ padding:40px 0}

.jumbotron .btn {
padding: 14px 24px;
font-size: 21px;
border: 0;
background: #df3d00; /* Old browsers */
background: -moz-radial-gradient(center, ellipse cover,  #df3d00 0%, #e66b34 100%); /* FF3.6+ */
background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%,#df3d00), color-stop(100%,#e66b34)); /* Chrome,Safari4+ */
background: -webkit-radial-gradient(center, ellipse cover,  #df3d00 0%,#e66b34 100%); /* Chrome10+,Safari5.1+ */
background: -o-radial-gradient(center, ellipse cover,  #df3d00 0%,#e66b34 100%); /* Opera 12+ */
background: -ms-radial-gradient(center, ellipse cover,  #df3d00 0%,#e66b34 100%); /* IE10+ */
background: radial-gradient(ellipse at center,  #df3d00 0%,#e66b34 100%); /* W3C */
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#df3d00', endColorstr='#e66b34',GradientType=1 ); /* IE6-9 fallback on horizontal gradient */
}
.jumbotron a:hover{background: #df3d00;}

.jumbo_bkg_thin{
display: table;
color:white;
width: 100%;
height: auto;
padding:40px 0 20px 0;
text-align: center;
color: rgb(255, 255, 255);
background: rgb(12, 129, 160);
background: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/Pgo8c3ZnIHhtbG5zPSJod…BoZWlnaHQ9IjEwMSIgZmlsbD0idXJsKCNncmFkLXVjZ2ctZ2VuZXJhdGVkKSIgLz4KPC9zdmc+);
background: -moz-radial-gradient(center, ellipse cover, rgb(12,129,160) 0%, rgb(0,89,112) 100%);
background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%,rgb(12,129,160)), color-stop(100%,rgb(0,89,112)));
background: -webkit-radial-gradient(center, ellipse cover, rgb(12, 129, 160) 0%,rgb(0, 89, 112) 100%);
background: -o-radial-gradient(center, ellipse cover, rgb(12,129,160) 0%,rgb(0,89,112) 100%);
background: -ms-radial-gradient(center, ellipse cover, rgb(12,129,160) 0%,rgb(0,89,112) 100%);
background: radial-gradient(ellipse at center, rgb(19, 191, 56) 0%,rgb(38, 112, 0) 100%);
filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#389D0C', endColorstr='#097715',GradientType=1 );
}

.jumbotron_bkg {
    background: url(../../images/home-icons.png) center 200px no-repeat;
}
.jumbotron_bkg_thin{
    background: url(../../images/internal_banner.jpg) repeat; color:#fff; padding:20px; margin-bottom:10px
}
.jumbotron_bkg_thin h2{ font-size:14px}

.jumbotron_bkg_thin .row h2{ font-size:24px; font-weight:bold}
.jumbotron_bkg_thin .row .fa{ margin:10px 0 10px}
.jumbotron_bkg_thin .bordered{ border-top:1px dashed #A6E29A; padding:10px 0}


.jumbotron_bkg a{color:#A6E29A;}
.jumbotron_bkg a:hover{color:#FFF; background:transparent}
.jumbotron span {
color:#A6E29A;
}
.navbar {
    padding-bottom: 15px;
    border-bottom: 0;
    -webkit-transition: background .5s ease-in-out,padding .5s ease-in-out;
    -moz-transition: background .5s ease-in-out,padding .5s ease-in-out;
    transition: background .5s ease-in-out,padding .5s ease-in-out;
    background: #fff;
-webkit-box-shadow: 0px 6px 17px -2px rgba(163,163,163,0.47);
-moz-box-shadow: 0px 6px 17px -2px rgba(163,163,163,0.47);
box-shadow: 0px 6px 17px -2px rgba(163,163,163,0.47);
}
.navbar-nav a{ font-color:#333}
.navbar-header {
    float:none;
}

.top-nav-collapse {
    padding: 0;
    background-color: #fff;
    border-bottom: 1px solid #d3d3d3 !important;
    background-image: none;
}

.navbar-form {
    padding: 10px 15px;
}

.input-group {
    position: relative;
    display: table;
    border-collapse: separate;
}
.input-group-btn {
    position: relative;
    font-size: 0;
    white-space: nowrap;
}
.input-group-addon, .input-group-btn {
    width: 1%;
    white-space: nowrap;
    vertical-align: middle;
}
.input-group-addon, .input-group-btn, .input-group .form-control {
    display: table-cell;
}
.search-btn {
    background-color: #e43523;
    border: 0px;
    color: white;
    padding: 24px 26px;
    font-size: 20px;
    text-transform: uppercase;
	font-family: 'Arvo', serif;
    max-height: 77px;
}

.main-search {
    padding: 23px 40px 19px 42px;
    border: none;
    width: 100%;
    font-size: 24px;
	font-family: 'Arvo', serif;
	
    color: #4e4e4e;
  
}

.title{ font-size:34px; padding:20px;}

@media screen and (max-width: 768px){
	 .search-form{
		  width: 88%;
	 }
	 .main-search{
		  font-size: 23px;
		  background-image: none;
		  padding: 20px 0 16px 25px;
	 }
	.search-btn {
		background-color: #e43523;
		border: 0px;
		color: white;
		padding: 29px 20px;
		font-size: 10px;
		text-transform: uppercase;
		font-family: 'Arvo', serif;
		max-height: 77px;
	}
	.main-search-wrap{ margin:0 auto}
}
}
@media screen and (max-width: 480px){
	.intro-body{
	 background: none;
}
	 .main-search{
		  font-size: 23px;
		  background-image: none;
		  padding: 20px 0 16px 25px;
	 }
	.search-btn {
		background-color: #e43523;
		border: 0px;
		color: white;
		padding: 29px 20px;
		font-size: 10px;
		text-transform: uppercase;
		font-family: 'Arvo', serif;
		max-height: 77px;
	}
}
.testimonials-section {
    width: 100%;
    padding: 50px 0;
    color: #fff;
    background-color: #fff;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    background-size: cover;
    -o-background-size: cover;
}
.testimonials-section {
    padding: 90px 0px;
}	
.testimonial-wrap{ background-color:#097715; color:#000; padding:20px}

.col-lg-7 p{ font-size:16px}

	
		
/*SUGGESTION BOX*/

	.suggestionsBox2 {
		position: relative;
		left: 0px;
		margin: 0px 0px 0px 0px;
		width: 350px;
		background-color: #097715;
		-moz-border-radius: 7px;
		-webkit-border-radius: 7px;
		border: 2px solid #389D0C;	
		color: #fff;
		list-style:none;
		text-align:left;
	}
	
	.suggestionList2 {
		margin: 0px;
		padding: 0px;
	}
	
	.suggestionList2 li {
		
		margin: 0px 0px 3px 0px;
		padding: 3px;
		cursor: pointer;
	}
	
	.suggestionList2 li:hover {
		background-color: #659CD8;
	}
	
@media screen and (max-width: 480px){
	
	.suggestionsBox2 {
		position: relative;
		left: 0px;
		margin: 0px 0px 0px 0px;
		width: 190px;
		background-color: #097715;
		-moz-border-radius: 7px;
		-webkit-border-radius: 7px;
		border: 2px solid #389D0C;	
		color: #fff;
		list-style:none;
		text-align:left;
	}
	
	.suggestionList2 {
		margin: 0px;
		padding: 0px;
	}
	
	.suggestionList2 li {
		
		margin: 0px 0px 3px 0px;
		padding: 3px;
		cursor: pointer;
	}
	
	.suggestionList2 li:hover {
		background-color: #659CD8;
	}
}


hr{ border-bottom:1px solid #ccc}
.mintext{color:#ff0000}
