<div style="margin-top:40px"></div>

<?php
    require_once __DIR__."/../lib/itinerary_model.php";
    $model = new Itinerary_model();

//itinerary
if ( $slug2 == "detail" &&  $slug3!== "" && $slug4!== "" ) {

//get the itinerary
//$result2 = get_itineraries('username',$slug3);
$row = $model->get_versions( $slug3, $slug4 );
//$row = mysqli_fetch_assoc($result2);
?>

<div class="col-md-12">
    
<a href="<?=$website_url?>/app/itinerary/"><span class="glyphicon glyphicon-arrow-left" aria-hidden="true"></span></a>
    <h2><b> <?=$row['title']?> </b></h2>
    
    <div class="col-md-8">
<h4><?php echo "{$row['trip_start']} - {$row['trip_end']}"?></h4>
    </div>

    <?php if( isset($_GET['print_activities']) && $_GET['print_activities']==="1" ):
            echo '<pre>'; // ?print_activities=1
            print_r($row['activities']);
            echo '</pre>';
        endif;?>

<div style="background-color: lightgrey; padding:20px; text-align: center">DAY 1: JULY 2021</div>
    
    <div class="col-md-8">
    <h3>Day 1: St Petersburg</h3>
    <small>June 17</small>
<p>The National Museum "Kyiv Art Gallery" was opened on November 12, 1922 and was originally called the Kyiv Art Gallery. Arising in a difficult time of turmoil and famine after the revolution of 1917 and the Civil War, the museum in its development has survived all stages of formation, perestroika, poverty and losses that Ukraine went through during its existence as part of the Soviet Union.</p>
    </div>

<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">Itinerary</h6>
                    <div id="content">
                        <ul class="timeline">
                            <li class="event" data-date="12:30 - 1:00pm">
                                <h3>Registration</h3>
                                <p>Get here on time, it's first come first serve. Be late, get turned away.</p>
                            </li>
                            <li class="event" data-date="2:30 - 4:00pm">
                                <h3>Opening Ceremony</h3>
                                <p>Get ready for an exciting event, this will kick off in amazing fashion with MOP &amp; Busta Rhymes as an opening show.</p>
                            </li>
                            <li class="event" data-date="5:00 - 8:00pm">
                                <h3>Main Event</h3>
                                <p>This is where it all goes down. You will compete head to head with your friends and rivals. Get ready!</p>
                            </li>
                            <li class="event" data-date="8:30 - 9:30pm">
                                <h3>Closing Ceremony</h3>
                                <p>See how is the victor and who are the losers. The big stage is where the winners bask in their own glory.</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<?php
}else{
?>


<div class="col-md-8">
      <h2 class="text-center"><b> Itineraries </b></h2>
<ul class="list-group">

    <?php
	    $proposals = $model->get_versions();
        //$result3 = get_itineraries('in','41,44');
            foreach($proposals as $k=>$row3) {
      ?>
<a href="<?=$website_url?>/app/itinerary/detail/<?=$row3['id']?>/<?=$row3['rel_id']?>">
    <li class="list-group-item" style="margin-bottom: 10px;">
        <h3><?=$row3['title']?></h3>
        <span class="	glyphicon glyphicon-arrow-right pull-right" aria-hidden="true"></span>
        <p><?php echo "{$row3['trip_start']} - {$row3['trip_end']}"?></p>
            
    </li> </a>
    <?php
        }
    ?>
    
    </ul>
    
</div>
    
</div>
<?php
}///ench page self
?>