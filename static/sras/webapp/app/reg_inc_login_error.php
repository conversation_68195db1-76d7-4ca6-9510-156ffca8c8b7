<h2>Login</h2>
<?php 
if(find_slug(1)=="loginerror1"){
	echo "<div align=\"left\" class=\"alert alert-warning\"><h3>Sorry!</h3>Your email address has not been validated!<br/> Please check your inbox and validate your email address </div>"; 
}else{
	echo "<div align=\"left\" class=\"alert alert-danger\"> <h3>Invalid Login!</h3> Please check your details</div>"; 
}

?>
<form action="" method="post" role="form" name="form1" id="form1">

  <div class="form-group">
  <label for="email">Your Email</label>
    <input name="email" type="text" id="email" class="form-control" placeholder="Email Address" />
  </div>

  <div class="form-group">
  	<label for="password">Password:</label>
    <input name="pwd" type="password" id="pwd" class="form-control" placeholder="Password" />
  </div>
 
  <div class="form-group">
    <input name="Submit" type="submit" value="Login" class="btn btn-primary btn-lg btn-block btn-frinter"/>
    <input name="run" type="hidden" id="run" value="1" />
    <input name="request_url" type="hidden" value="<?php echo $_SERVER["HTTP_REFERER"]; ?>" />
  </div>

</form>
<?php
// hide these cause it's release
if(find_slug(2)!=="release"){
?>
<a href="forgot">Forgotten Password</a> or 
<a href="register">Register an account</a>
<?php
}
?>
  </p>


