<?php
include("../lib/lib_site.inc.php");
include("functions.php");
if (isset($_POST['u_name_id'])) {

    $fav = sanitise($_POST['u_name_id']);///Status

    $duplicate_check = pull_field("app_favorites", "count(id)", "WHERE rec_id='$_SESSION[uid]' AND rel_id='$fav' AND rec_archive is null ");
    $dbh = get_dbh();

    if ($duplicate_check == 0) {
        /***************** INSERT FUNCTION *****************/
        $sql = "INSERT INTO app_favorites 
		(rec_id, username_id, usergroup, rel_id) 
		VALUES 
		($_SESSION[uid],'" . $activ_code . "', '$usergroup', $fav)";
        dev_debug($sql);
        $stmt = $dbh->prepare($sql);
        $stmt->execute();

        $result[] = "Like Added";
    } else {
        /***************** DELETE FUNCTION *****************/
        $sql = "DELETE FROM app_favorites WHERE rec_id='$_SESSION[uid]' AND rel_id= '$fav' AND rec_archive is null";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        $result[] = "Like Removed";
    }
}

$entry_json = json_encode($result);
echo $entry_json;
exit();


