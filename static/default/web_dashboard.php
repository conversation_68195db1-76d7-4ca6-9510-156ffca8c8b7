<div class="row">
    <div class="col-xs-12 col-md-12">
        <div class="splash">

            <div class="row">

                <div class="col-md-3 profile_image text-center">
                    <img src="<?= $avatar ?>" class="img-circle" style="width:100%">
                    <button class="label label-default" data-toggle="modal" data-target="#photo_update">Change Profile
                        Photo
                    </button>
                </div>

                <div class="table-responsive col-md-5">
                    <h3>Welcome Back, <?= $core_students_first_name ?></h3>

                    <table class="table">
                        <tr>
                            <th>Level</th>
                            <td><?= $course_level ?></td>
                        </tr>
                        <tr>
                            <th>Course</th>
                            <td><?= $core_students_course_of_study ?></td>
                        </tr>
                        <tr>
                            <th>Cohort</th>
                            <td><?= $core_students_cohort ?></td>
                        </tr>
                        <tr>
                            <th>Current Status</th>
                            <td>
                                <?= ($appli_submited_check < 1 ? 'Application Not Yet Submitted' : 'Application Submitted'); ?>
                            </td>
                        </tr>
                        <tr>
                            <th>&nbsp;</th>
                            <td>&nbsp;</td>
                        </tr>
                    </table>
                </div>

                <div class="col-md-4">
                    <div class="alert <?php echo($filled_percentage == "100" ? 'alert-success' : 'alert-info') ?>"
                         align="center">
                        <h3>Your Application is now <b><?= $filled_percentage ?>%</b> complete.</h3>
                        <!--START PROGRESS BAR-->
                        <div class="progress">
                            <div class="progress-bar <?php echo($filled_percentage == "100" ? 'progress-bar-success' : 'progress-bar-info') ?>"
                                 role="progressbar" aria-valuenow="<?= $filled_percentage ?>" aria-valuemin="0"
                                 aria-valuemax="100" style="width: <?= $filled_percentage ?>%">
                                <span class="sr-only"><?= $filled_percentage ?>% Complete</span>
                            </div>
                        </div>
                        <!--END PROGRESS BAR-->
                        <hr/>
                        <?php
                        if ($filled_percentage == "100") {
                            ?>

                            <?php
                            // check if the user has submitted
                            if ($appli_submited_check < 1) {
                                ?>
                                <br/> or <br/>
                                <a href="<?php echo website_url_applicant; ?>/submit_application"
                                   class="btn btn-success">Submit Your Application</a>
                            <?php } else { ?>
                                <a href="<?php echo website_url_applicant; ?>/media-upload" class="btn btn-warning">Upload
                                    Documents</a><br/><br/>
                            <?php } ?>

                            <a href="<?php echo website_url_applicant; ?>/personal-information" class="btn btn-success">View
                                Your Application</a>

                            <?php
                        } else {
                            ?>
                            <a href="<?php echo website_url_applicant; ?>/personal-information" class="btn btn-info">Edit
                                Your Application</a>
                            <?php
                        }
                        ?>

                        <?php
                        $check_offer_status = pull_field("dir_offers", "count(*)", "WHERE rel_id='$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]' AND db16984='yes'");
                        // check if the user has submitted
                        if ($check_offer_status > 0) {
                            ?>
                            <br/> or <br/>
                            <a href="<?php echo website_url_applicant; ?>/offer" class="btn btn-danger">View Your
                                Offer</a>

                        <?php } ?>
                    </div>
                </div>

            </div>

        </div>
    </div>
</div>


<div class="row">
    <div class="col-xs-12 col-lg-6">
        <h2>Communication Inbox</h2>
        <p>Send and receive communication from our administration team here</p>

        <!--CHAT WIZARD-->
        <div class="box widget-chat">

            <div class="widget-actions">
                <form class="form-inline" action="<?= website_url ?>/static/inc/inc_dir_messages_process.php"
                      method="POST">
                    <button class="btn btn-primary">
                        Send
                    </button>
                    <div>
                        <textarea name="new_message" rows="1" id="textarea-chat-example"
                                  style="overflow: hidden; word-wrap: break-word; resize: vertical; height: 100px;"></textarea>
                    </div>
                </form>
            </div>
            <div class="clearfix"></div>

            <!--START CHAT STREAM-->
            <div class="chat_stream">
                <?php
                echo "<div style = 'display:none'>";
                print_r($messages);
                echo "</div>";
                foreach ($messages as $msg) {

                    // current logged in person?
                    if ($msg['rec_id'] === $student_uid) { ?>
                        <div class="chat-message">
                            <img src="<?= $avatar ?>" alt="no image" width="150" border="0"/>
                            <div>
                                <b><?php echo $core_students_first_name; ?></b> says:
                                <span class="pull-right"><?php echo format_date("d M y", $msg["date"]); ?></span>
                                <div><?php echo text_to_html($msg["db76"]); ?></div>
                            </div>
                        </div>
                    <?php } else { ?>
                        <div class="chat-message right">
                            <img src="<?= get_avatar($msg['rec_id']); ?>"/>
                            <div>
                                <b><?php echo pull_field("form_users", "db106", "WHERE id='$msg[rec_id]'"); ?></b> says:
                                <span class="pull-right"><?php echo format_date("d M y", $msg["date"]); ?></span>
                                <div><?php echo text_to_html($msg["db76"]); ?></div>
                            </div>
                        </div>
                    <?php }
                }//end while ?>
            </div>
            <!--END CHAT STREAM-->

        </div>
    </div><!--/span-->
    <?php $class_resources = "col-xs-12 col-lg-6";
    include('static/inc/inc_shared_resources.php'); ?>

    <div class="col-xs-12 col-lg-6">
        <h2>News and Updates</h2>
        <p>Various information to help you through your application</p>

        <ul class="list-inline">
            <?php
            $i = 0;
            foreach ($blog_assist as $msg) {
                ?>
                <li class="col-lg-12">
                    <h4><?php echo $msg["db1695"]; ?></h4>
                    <!--<img src="<?php //echo  $front_web_url_file_loc?>/media/<?php //echo $msg["db1699"]; ?>" class="thumbnail pull-left" alt="nes_image" />-->
                    <p><?php echo $msg["db1698"]; ?></p>
                    <a href="<?php echo website_url_applicant . '/blog/' . $msg["id"]; ?>" class="label label-info">Read
                        More</a>
                    <hr/>
                </li>
            <?php } // end of loop ?>
        </ul>
    </div>
</div>
