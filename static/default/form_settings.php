<?php
$button_name = "Signup";
//get the domain info for the subdomain
$school_logo = "$schools_institution_logo";
$page_title = "$schools_institution_name";
$page_description = "$site_slogan";
//$page_key_title="International Admissions Portal"; // also used in main header

// add a default custom 2 if one deoes not exist
$default_custom2 = pull_field("dir_custom2", "rel_id", "WHERE rel_id='$_SESSION[student_id]'");
if ($default_custom2 == "") {
    default_setup_fresh("dir_custom2", $_SESSION['student_id'], $_SESSION['uid']);
}

?>
<style>
    .form_wrapper {
        background: #58b74e;
        margin: 0;
        text_align: left;
        font-family: Arial
    }

    .form_style_bkg {
        color: #666;
        padding: 0;
        margin: 10px auto;
        max-width: 850px;
        background-color: #fff;
        -webkit-box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
        -moz-box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
        box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
    }

    .bkg {
        background: #fff;
        padding: 20px 10px;
    }

    .top_logo {
        padding: 5px 10px 5px 5px;
        max-height: 200px;
        float: left
    }

    .form_detail {
        padding: 10px
    }

    .slogan {
        background: #58b74e;
        color: #fff
    }

    #terms-and-conditions {
        overflow: scroll;
        height: 500px;
        padding: 20px;
    }
</style>