<?php
// Only runs for the current customer
//this script will run after an offer is processed on the system
if ($accept == 'yes') {
    //update checklist checkbox
    $checkbox_to_select = ($_POST['offer_type'] == 'conditional' ? 'db16120' : 'db16121');

    $dbh = get_dbh();
    $sql2 = "UPDATE chk_z_25_default_checklist SET $checkbox_to_select='yes', db16119 = 'on' WHERE rel_id='$_SESSION[student_id]'";
    $stmt = $dbh->prepare($sql2);
    $stmt->execute();

    // Move along
    $update_stage = 1;// this tells the function below to run
    track_application_stages('db16119', $_SESSION['student_id']);

}

//reject
if ($accept == 'no') {

    //update checklist checkbox
    $checkbox_to_select = ($_POST['offer_type'] == 'unconditional' ? 'db16120' : 'db16121');

    $dbh = get_dbh();
    $sql2 = "UPDATE chk_z_25_default_checklist SET $checkbox_to_select='no' WHERE rel_id='$_SESSION[student_id]'";
    $stmt = $dbh->prepare($sql2);
    $stmt->execute();

}