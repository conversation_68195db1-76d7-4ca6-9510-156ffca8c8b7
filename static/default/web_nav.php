<?php
if ($_SESSION['loggedin']) { // only show to logged in users

    echo '<div class="btn btn-info btn-lg btn-block"><a href="' . website_url_applicant . '/logout">Log out >></a></div><br/>';
}
?>

<?php
if ($_SESSION['loggedin']) { // only show to logged in users

error_log('tracker');

$pages_id = get_page_list_based_on_rules($_SESSION['student_id']);

//only show on the homepage
if ($cms_page_name == "Checklist") {


    $dbh = get_dbh();

    // Get the stages for student's route
    $sql = 'SELECT system_table.form_id, system_table.db_field_name, system_table.name
		FROM system_table
		INNER JOIN core_students ON core_students.db2280 = system_table.pg_id
		WHERE core_students.id = ? AND system_table.type="stage"
		AND locked IN(2,0)
		ORDER BY system_table.form_order';
    $sth = $dbh->prepare($sql);
    $sth->execute(array($_SESSION['student_id']));
    $stages = array();
    while ($row = $sth->fetch(PDO::FETCH_ASSOC)) $stages[] = $row;

    // pull the statuses of stages for this particular student
    $checklist_table_name = 'chk_' . pull_field(
            'system_pages INNER JOIN core_students ON core_students.db2280 = system_pages.page_id',
            'page_name',
            "WHERE core_students.id = '$_SESSION[student_id]'"
        );
    error_log('checklist table is called ' . $checklist_table_name);


    $sql = 'SELECT ';
    foreach ($stages as $stage) {
        $sql .= $stage['db_field_name'] . ', ';
    }
    $sql = substr($sql, 0, -2);
    $sql .= " FROM $checklist_table_name WHERE rel_id = ? LIMIT 1";
    error_log('query to get stage statuses: ' . $sql);
    $sth = $dbh->prepare($sql);
    $sth->execute(array($_SESSION['student_id']));
    $stage_status = $sth->fetch(PDO::FETCH_ASSOC);

    ?>
    <div class="row">
        <div class="col-xs-12">
            <ul class="list-group list-group_home">
                <li class="list-group-item">
                    <h4 class="list-group-item-heading">Application Tracker</h4>
                    <p class="list-group-item-text">A quick summary of how your application is proceeding</p>
                </li>

                <li class="list-group-item">
                    <div class="circle-text">
                        <span>0</span>
                    </div>
                    Application Completion
                    <?php echo($filled_percentage == '100' ? '<img src="' . engine_url . '/images/icon_tick.png" width="16" height="16" alt="tick" />' : '') ?>
                </li>
                <?php
                $i = 1;
                foreach ($stages as $stage) {
                    $exp = explode('-', $stage['name']);
                    $sts = $stage_status[$stage['db_field_name']];
                    $sts_image = ($sts === 'on') ? '<img src="' . engine_url . '/images/icon_tick.png" width="16" height="16" alt="tick" />' : '';
                    ?>
                    <li class="list-group-item<?= stage_complete($i) ?>">
                        <div class="circle-text">
                            <span><?= $i ?></span>
                        </div><?= $exp[1] ?> <?= $sts_image ?>
                    </li>
                    <?php
                    $i++;
                } ?>

            </ul>
        </div>
    </div>
    <?php
    //end of checklist
} else {
?>
<div class="row">
    <div class="col-xs-12">
        <p><b>Your Application</b></p>
        <ul class="list-group">
            <?php
            if ($pages_id != "") {
                get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq','ucas','supplementary_forms') AND id IN($pages_id) ORDER BY FIELD(id,$pages_id)", "yes", "list-group-item", "show_auto_number");
            } else {
                get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");
            }
            //}else{
            //do nothing
            //}

            //}
            if ($_SESSION['student_id'] == 5242) {
                //  echo "web nav pages id = $pages_id";
            }

            //get the right pages for this section
            $pages_id = get_page_list_based_on_rules($_SESSION['student_id']);

            if ($pages_id != "") {
                get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq','ucas','product_page') AND id IN($pages_id) ORDER BY FIELD(id,$pages_id)", "yes", "list-group-item", "show_auto_number");
            } else {
                get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item", "show_auto_number");
            }

            ?>
        </ul>
    </div>
    <?php } ?>


    <?php
    } // end of check if checklist
    if (pull_field('form_schools', 'db30', "WHERE id={$_SESSION['usergroup']}") != '12') {

        ?>

        <div class="row col-xs-12">
            <p><b>Quicklinks</b></p>
            <ul class="list-group">
                <?php
                get_cms_nav("application", "public", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");

                if (!$_SESSION['loggedin']) { // only show to logged in users
                    ?>

                    <li class="list-group-item"><a
                                href="<?php echo website_url_applicant; ?>/parent_register" <?php if ($current_id == 'register') {
                            $sty = 'class="nav_selected"';
                        } ?>>Start a new application >></a></li>
                    <li class="list-group-item"><a href="<?php echo website_url_applicant; ?>/login">Continue an
                            application you have already started >></a></li>
                    <li class="list-group-item"><a href="<?php echo website_url_applicant; ?>/login">Log in to view the
                            status of a completed application >></a></li> <?php
                }

                ?>
            </ul>
        </div>


        <div class="row">
            <div class="col-xs-12">
                <div class="alert alert-warning">
                    <h3>FAQ & Support</h3>
                    <hr/>
                    <p>
                    <h4>Can I track my application after submitting?</h4>
                    Yes, just log in and use Application Tracker in the Quicklinks menu.
                    </p><br/>
                    <a href="<?php echo website_url_applicant; ?>/applicant-faqs" class="label label-info">See more
                        > </a>
                </div>
            </div>
        </div>
    <?php } ?>

