<?php

//get any duplicates	  
$dbh = get_dbh();
$sql = "SELECT 
id as emails 
FROM `lead_profiles`
where usergroup = " . $_SESSION['usergroup'] . "
AND (rec_archive IS NULL OR rec_archive = '')
AND db1058 = '$profiles_email_address' 
AND id!=$candidate_id";
dev_debug($sql);
$sth = $dbh->prepare($sql);
$sth->execute();
$number_of_rows = $sth->fetchall();

$i = 0;
foreach ($number_of_rows as $row) {
    $id_num .= $row['emails'] . ",";
    $i++;
}

$query_lead_emails = substr($id_num, 0, -1);//get rid of last part

////////////////get the data
//Query it
$query_lead = "
SELECT 
id AS Manage,
DATE_FORMAT(date, '%d/%m/%Y @%H%i') AS 'Date Created',
db1058 AS 'Email',
db1043 AS Surname,
db1041 AS 'First name',
(SELECT db1733 FROM lead_stages WHERE id = db1056) AS 'Status' 
FROM `lead_profiles`
WHERE id IN($query_lead_emails)";
//echo $query_lead;

$table_name = "lead_profiles";
$delete_permited = 'yes';// allow deleting
?>
<style>
    .popit .modal-dialog {
        width: 100%;
        max-width: 800px;
        padding: 0;
    }

    .popit .modal-content {
        height: 100%;
        max-width: 800px;
        border-radius: 0;;
        overflow: auto;
    }
</style>
<table class="table table-condensed ">
    <tr>
        <th>Previous Enquiries</th>
        <td><a href="#" style="float:none" data-toggle="modal" data-target="#duplicates-modal"><i
                        class="fa fa-users"></i> View (<?= $i ?>)</a></td>
    </tr>
    <tr>
        <th>Email</th>
        <td><?= $profiles_email_address; ?></td>
    </tr>
    <tr>
        <th>Telephone</th>
        <td><?= $profiles_telephone_number; ?></td>
    </tr>
    <tr class="full_enquiry">
        <th>Planned Year</th>
        <td><?= $profiles_proposed_year_of_study; ?></td>
    </tr>

    <tr class="full_enquiry">
        <th>Interested In</th>
        <td><?= $profiles_subject_of_interest; ?></td>
    </tr>
    <tr class="full_enquiry">
        <th>Planned Term</th>
        <td><?= $profiles_extra_2; ?></td>
    </tr>
    <tr class="full_enquiry">
        <th>Interested City</th>
        <td><?= $profiles_destination_city; ?></td>
    </tr>


    <tr>
        <td align="center" colspan="2">
            <a class="btn btn-default btn-sm btn-block" href="#" id="more">View More</a>
            <div class="full_enquiry"></div>
        </td>
    </tr>

</table>


<!-- Incomplete data view -->
<div class="modal fade popit" id="duplicates-modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" style="font-size: 18px;">Matching Enquiries by Email Address</h4>
            </div>

            <div class="modal-body">
                <p>This view shows you all other enquiries made by the same email address</p>
                <div style="padding:10px 20px">
                    <?php show_report_view($query_lead, 97, 500, 0); ?>
                </div>
            </div>
            <div class="modal-footer">

            </div>

        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
