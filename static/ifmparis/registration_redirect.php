<?php
//this script will take a url from the website and send the aplicant to the correct course & route
include_once "../../engine/admin/inc/lib.inc.php";// call functions
if ($_GET['lang']) {
    $_SESSION['lang'] = $_GET['lang'];
}

if ($_GET['muid']) {

    // get the course data
    $sql_course_data = "
	SELECT *
		FROM core_courses
		WHERE db231 = ?
		AND usergroup = '$_SESSION[usergroup]'
	";

    // execute and get results
    dev_debug($sql_course_data);

    $dbh = get_dbh();
    $sql = $dbh->prepare($sql_course_data);
    $sql->execute(array($_GET['muid']));
    $pdo_course_data_rows = $sql->fetchAll(PDO::FETCH_OBJ);
    $pdo_course_data = $pdo_course_data_rows[0];

    // get the url of where they are coming from
    $from_url = $_SERVER["HTTP_REFERER"];

    //set session values to pupulate the hidden fields on the form
    //$_SESSION['prog_course_selected'] = "<h2>".translate("Application for",$_SESSION['lang']).":<br/><b>". translate($pdo_course_data->db232,$_SESSION['lang']).".</b></h2>";
    $_SESSION['prog_course_selected'] = "Application for: $pdo_course_data->db232"; // done for translation purposes
    //Not the correct course? <a href='$from_url'>go back</a> and select a different course"; // Instructions telling them what they have selected
    $_SESSION['prog_level'] = $pdo_course_data->db341; //- course level
    $_SESSION['prog_course'] = $pdo_course_data->id;  //- course

    // used to set a manual route
    $_SESSION['set_route'] = pull_field("dir_route_rules", "db1764", "WHERE id=" . $pdo_course_data->db22005);  //- route
    $_SESSION['override_route'] = "yes"; // this will override the route query on the applicant registration page
    //$_SESSION['prog_intake'] = $pdo_course_data->;  //- intake


    $link = "https://$_SESSION[subdomain].heiapply.com/application/register";

    dev_debug($_SESSION, "print_r");
    dev_debug($pdo_course_data, "print_r");

    //if debug_remove the header
    if ($_GET['debug_mode'] !== 'yes') {
        header('Location: ' . $link);
        exit;
    }

} else {
    die("An error has occured. Please contact technical <NAME_EMAIL>. Error code: muid missing");
}
