<?php
// if a parent show this
if ($_SESSION['ulevel'] == '7') {
    include(front_header_file_location . "/web_dashboard_parent.php");
} else {
    ?>
    <div class="row">
        <div class="col-xs-12 col-md-12">
            <div class="splash" style="margin-top: 30;">

                <div class="row">

                    <div class="col-sm-2 col-xs-12" align="center">
                        <img src="<?= $avatar ?>" class="img-circle" style="width:100%">
                        <button class="btn btn-default btn-block update" data-toggle="modal"
                                data-target="#photo_update"><i class="fa fa-cog"
                                                               aria-hidden="true"></i> <?= translate("Update", $_SESSION['lang']) ?>
                        </button>
                    </div>

                    <div class="col-sm-6 col-xs-12">
                        <h3><?= translate("Welcome", $_SESSION['lang']) ?>, <?= $my_user_first_name ?></h3>

                        <table class="table">
                            <tr>
                                <th><?= translate("Course Level", $_SESSION['lang']) ?></th>
                                <td><?= $course_level ?></td>
                            </tr>
                            <tr>
                                <th><?= translate("Program", $_SESSION['lang']) ?></th>
                                <td><?= $core_students_course_of_study ?></td>
                            </tr>

                            <tr>
                                <th><?= translate("Intake", $_SESSION['lang']) ?></th>
                                <td><?= $core_students_cohort_intake ?></td>
                            </tr>

                            <!--<tr>
                    <th>Length Of Study</th>
                    <td><?= str_replace("_", " ", $core_students_length_of_study) ?></td>
                  </tr>-->

                            <tr id="<?= $appli_submited_check ?>">
                                <th><?= translate("Application Status", $_SESSION['lang']) ?></th>
                                <td>
                                    <?php
                                    if ($appli_submited_check < 1) {
                                        ?>
                                        <?= translate("Not Yet Submitted", $_SESSION['lang']) ?>
                                        <?php
                                    } else {
                                        ?>
                                        <?= translate("Application Submitted", $_SESSION['lang']) ?>
                                        <?php
                                    }
                                    ?>
                                </td>
                            </tr>
                            <tr>
                                <th>&nbsp;</th>
                                <td>&nbsp;</td>
                            </tr>
                        </table>
                    </div>

                    <div class="col-sm-4 col-xs-12">
                        <div class="alert <?php echo($filled_percentage == "100" ? 'alert-default' : 'alert-default') ?> status"
                             align="center">

                            <?php
                            if ($filled_percentage == "100") {
                                ?>

                                <?php
                                if ($appli_submited_check > 0) {
                                    ?>

                                    <h5><?= translate("Your Application is now", $_SESSION['lang']) ?> <b>
                                            <span>100%</span>
                                        </b> <?= translate("complete.", $_SESSION['lang']) ?></h5>
                                    <div class="progress">
                                        <div class="progress-bar <?php echo($filled_percentage == "100" ? 'progress-bar-success' : 'progress-bar-custom') ?>"
                                             role="progressbar" aria-valuenow="<?= $filled_percentage ?>"
                                             aria-valuemin="0" aria-valuemax="100"
                                             style="width: <?= $filled_percentage ?>%">
                                            <span class="sr-only"><?= $filled_percentage ?><?= translate("% Complete", $_SESSION['lang']) ?></span>
                                        </div>
                                    </div>
                                    <hr/>

                                    <a href="<?php echo website_url_applicant; ?>/<?= $page_link ?>"
                                       class="btn btn-lg btn-custom btn-block"><i class="fa fa-lg fa-check-square-o"
                                                                                  aria-hidden="true"></i> <?= translate("View Completed Application", $_SESSION['lang']) ?>
                                    </a>
                                    <?php
                                } else {
                                    ?>
                                    <h5><?= translate("Your Application is now", $_SESSION['lang']) ?> <b>
                                            <span><?= $filled_percentage ?>%</span>
                                        </b> <?= translate("complete", $_SESSION['lang']) ?>.
                                    </h5>
                                    <!--START PROGRESS BAR-->
                                    <div class="progress">
                                        <div class="progress-bar <?php echo($filled_percentage == "100" ? 'progress-bar-custom' : 'progress-bar-custom') ?>"
                                             role="progressbar" aria-valuenow="<?= $filled_percentage ?>"
                                             aria-valuemin="0" aria-valuemax="100"
                                             style="width: <?= $filled_percentage ?>%">
                                            <span class="sr-only"><?= $filled_percentage ?>% <?= translate("Complete", $_SESSION['lang']) ?></span>
                                        </div>
                                    </div>
                                    <!--END PROGRESS BAR-->
                                    <hr/>

                                    <a href="<?php echo website_url_applicant; ?>/submit_application"
                                       class="btn btn-success btn-block"><?= translate("Submit Your Application", $_SESSION['lang']) ?></a>
                                    <?php
                                }
                                ?>

                                <?php
                            } else {
                                ?>
                                <h5> <?= translate("Your Online Form is now", $_SESSION['lang']) ?><b>
                                        <span><?= $filled_percentage ?>%</span>
                                    </b><?= translate("complete", $_SESSION['lang']) ?></h5>
                                <!--START PROGRESS BAR-->
                                <div class="progress">
                                    <div class="progress-bar <?php echo($filled_percentage == "100" ? 'progress-bar-success' : 'progress-bar-custom') ?>"
                                         role="progressbar" aria-valuenow="<?= $filled_percentage ?>" aria-valuemin="0"
                                         aria-valuemax="100" style="width: <?= $filled_percentage ?>%">
                                        <span class="sr-only"><?= $filled_percentage ?><?= translate("% Complete", $_SESSION['lang']) ?></span>
                                    </div>
                                </div>
                                <!--END PROGRESS BAR-->
                                <hr/>
                                <a href="<?php echo website_url_applicant; ?>/<?= $page_link ?>"
                                   class="btn btn-custom btn-block"><?= translate("Complete Application", $_SESSION['lang']) ?></a>
                                <?php
                            }
                            ?>
                        </div>
                    </div>

                </div>

            </div>
        </div>
    </div>


    <div class="row">
        <?php
        //check if task have been sent
        //end
        if (count($task_rows) > 0) {
            ?>
            <div class="col-xs-12 col-lg-12">
                <h3><?= translate("Tasks", $_SESSION['lang']) ?></h3>
                <p><?= translate("Below you can see a list of all the tasks currently assigned to you. To help us processes your applications we need you to complete all these tasks.", $_SESSION['lang']) ?> </p>

                <?php
                //echo generate_table($sql3, array($profile_id),'');
                include("/var/www/vhosts/heiapply.com/httpdocs/engine/tools/tasks/core_view.php");
                include("/var/www/vhosts/heiapply.com/httpdocs/engine/tools/tasks/core_footer.php");
                ?>

            </div>
            <?php
        }// task check end
        ?>
        <div class="clearfix"></div>
    </div>

    <div class="row">
        <div class="col-xs-12 col-lg-6">
            <h4><?= translate("Communication Mailbox", $_SESSION['lang']) ?></h4>
            <p><?= translate("Communicate with the administration team directly.", $_SESSION['lang']) ?></p>

            <!--CHAT WIZARD-->
            <div class="box widget-chat">

                <div class="widget-actions">
                    <form class="form-inline" action="<?= website_url ?>/static/inc/inc_dir_messages_process.php"
                          method="POST">
                        <button class="btn btn-custom">
                            <?= translate("Send", $_SESSION['lang']) ?>
                        </button>
                        <div>
                            <textarea name="new_message" rows="1" id="textarea-chat-example"
                                      style="overflow: hidden; word-wrap: break-word; resize: vertical; height: 100px;"></textarea>
                        </div>
                    </form>
                </div>
                <div class="clearfix"></div>


                <!--START CHAT STREAM-->
                <div class="chat_stream">
                    <?php foreach ($messages as $msg) {
                        // current logged in person?
                        if ($msg['rec_id'] === $student_uid) { ?>
                            <div class="chat-message">
                                <img src="<?= $avatar ?>" alt="no image" width="150" border="0"/>
                                <div>
                                    <b><?php echo $core_students_first_name; ?></b> <?= translate("says", $_SESSION['lang']) ?>
                                    :
                                    <span class="pull-right"><?php echo format_date("d M y h:i:s", $msg["date"]); ?></span>
                                    <div><?php echo text_to_html($msg["db76"]); ?></div>
                                </div>
                            </div>
                        <?php } else { ?>
                            <div class="chat-message right">
                                <img src="<?= get_avatar($msg['rec_id']); ?>"/>
                                <div>
                                    <b><?php echo pull_field("form_users", "db106", "WHERE id='$msg[rec_id]'"); ?></b> <?= translate("says", $_SESSION['lang']) ?>
                                    :
                                    <span class="pull-right"><?php echo format_date("d M y h:i:s", $msg["date"]); ?></span>
                                    <div><?php echo text_to_html($msg["db76"]); ?></div>
                                </div>
                            </div>
                        <?php }
                    }//end while ?>
                </div>
                <!--END CHAT STREAM-->

            </div>
        </div><!--/span-->

        <?php
        //check if letters have been sent
        //end
        if (count($letters) > 0) {
            ?>
            <div class="col-xs-12 col-lg-6">
                <h3><?= translate("Letters", $_SESSION['lang']) ?>:</h3>
                <p><?= translate("You have mail", $_SESSION['lang']) ?>:</p>

                <div class="table-responsive">
                    <table class="table">

                        <thead class="hidden">
                        <tr>
                            <th><?= translate("Subject", $_SESSION['lang']) ?>:</th>
                            <th><?= translate("Letter", $_SESSION['lang']) ?>:</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        foreach ($letters as $letter) { ?>
                            <tr>
                                <td><i class="fa fa-envelope"
                                       aria-hidden="true"></i> <?php echo format_date('d/m/Y', $letter['date']) ?></td>
                                <td><a href="<?php echo $letter['db20300'] ?>"
                                       title='<?= translate("Click To Download Letter", $_SESSION['lang']) ?>:'><?php echo $letter['db20301'] ?></a>
                                </td>
                            </tr>

                        <?php }
                        ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php
        }// letters check end
        ?>
        <?php $class_resources = "col-xs-12 col-lg-6 shared_resources";
        include('static/inc/inc_shared_resources.php'); ?>


        <div class="col-xs-12 col-lg-6">
            <h4><?= translate("News and Updates", $_SESSION['lang']) ?></h4>
            <p><?= translate("Various information to help you through your application", $_SESSION['lang']) ?>:</p>

            <ul class="list-inline">
                <?php
                $i = 0;
                foreach ($blog_assist as $msg) {
                    ?>
                    <li class="col-lg-12" style="text-align: justify;">
                        <h4><?php echo $msg["db1695"]; ?></h4>

                        <?php
                        //show images
                        if ($msg["db1699"]) { ?>
                            <img src="<?= $front_web_url_file_loc ?>/media/<?php echo $msg["../43airschool - Copy/db1699"]; ?>"
                                 class="thumbnail pull-left" alt="nes_image"/>
                        <?php } // end image show  ?>

                        <p><?php echo $msg["db1698"]; ?></p>
                        <a href="<?php echo website_url_applicant . '/blog/' . $msg["id"]; ?>"
                           class="label label-default"><?= translate("Read More", $_SESSION['lang']) ?>:</a>
                        <hr/>
                    </li>
                <?php } // end of loop ?>
            </ul>
        </div>
    </div> <!-- .row -->

    <!--
<div class="row">
    <div class="col-xs-12">
    <h3>Please read this important information before you begin your application</h3>
        <div style="width:650px; height:400px; overflow:auto; border:4px solid #f0f0f0; padding:5px; background-color:#f4f4f4; margin-bottom:10px">
          <?php
    // FUNCTION TO GET_CMS
    //list($page_id,$cms_category,$cms_page_name,$cms_heading,$cms_brief,$cms_article)=get_cms('page_name',"Tier4_Information");
    //echo $cms_article;//pull_field("form_cms","db650","WHERE db647='Tier4_Information'");
    ?>
        </div>
    </div>
</div>
-->
<?php }