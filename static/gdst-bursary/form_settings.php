<?php
//this function wil automatically inject a custom student number into the system at the point at which a student is registered.
//custom_internal_number generator
function custom_internal_number_generator()
{

    $dbh = get_dbh();
    $sql = "SELECT *,CAST(db888 AS SIGNED) as db888 FROM core_students WHERE usergroup='" . $_SESSION['usergroup'] . "' ORDER BY db888 DESC LIMIT 1";
    dev_debug($sql);
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $checks = array();
    foreach ($results as $user_info) {
        $last_number = $user_info['db888'];
    }

    if (!$last_number) {
        $last_number = 1000;
    }

    $internal_ref_number = $last_number + 1;
    dev_debug('internal_ref_number' . $internal_ref_number);
    return $internal_ref_number;
}

$dbh = get_dbh();
// Get info about student
list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake) = get_core_students($_SESSION['student_id']);

// what's the curse level?
// this mess is because course is not the id but the name...
/*
$sql = "SELECT core_course_level.db343
    FROM core_students
    INNER JOIN core_courses ON core_courses.id=core_students.db889
    INNER JOIN core_course_level ON core_course_level.id = core_courses.db341
    WHERE core_students.id = '$core_students_id'";
$res = mysqli_query($dbcon,$sql);
list($course_level) = mysqli_fetch_row($res);
*/

$avatar = get_avatar($core_students_rec_id);
$course_level = pull_field("core_course_level", "db343", "WHERE id=$core_students_level_of_entry");

// GET PERCENTAGE AND DISPLAY
list($required_fields, $required_filled) = filled_percentage_on_new_forms($core_students_id);

$filled_percentage = round(100 * $required_filled / $required_fields, 0, PHP_ROUND_HALF_DOWN);

// error_log("Filled: $filled_percentage%");

// error_log("Total required: $required_fields");
// error_log("Filled in: $required_filled");

// Fetch application stages
$sql = "SELECT db1131 FROM dir_appli_stages WHERE id = '$core_students_application_status'";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$application_status = $stmt->fetchColumn();
$application_status = explode('-', $application_status);
$application_status = trim($application_status[1]);

// Compares stage student is in to argument
function stage_complete($stage_id, $type = 0)
{
    global $core_students_application_status;

    if ($type !== 0) {
        return ($core_students_application_status > $stage_id) ? ' <span class="glyphicon glyphicon-ok"></span>' : '';
    } else {
        return ($core_students_application_status > $stage_id) ? ' completed' : '';
    }
}

// add new comment
if ($_POST["new_message"]) {
    $message = sanitise($_POST['new_message']);

    $random_id = random();
    $sql = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db76, db77, db80, db139) VALUES ('" . $random_id . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . floating_info("ref") . "', '$message', '" . session_info('uid') . "', '" . session_info('uid') . "', 'no')";
    $stmt = $dbh->prepare($sql);
    $stmt->execute();

    //add tracker
    track_use("Sent a private message", "$random_id");
}

//Grab children
##$pre_select = $_SESSION['chld_view'];
$sql_children = "SELECT *,(SELECT db232 FROM core_courses WHERE id=db889) AS course  FROM core_students WHERE usergroup='$_SESSION[usergroup]' AND (rec_id='$_SESSION[uid]') AND (core_students.rec_archive IS NULL OR core_students.rec_archive ='' ) ORDER BY id ASC";
$stmt = $dbh->prepare($sql_children);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$list_children = array();

foreach ($results as $row_children) $list_children[] = $row_children;
$child_ids = "";
$i = 0;
foreach ($list_children as $child) {
    if ($i == 0) {
        $child_ids .= "$child[id]";
    } else {
        $child_ids .= ",$child[id]";
    }
    $i++;
}

// Grab messages
$sql = "SELECT * FROM core_notes WHERE rel_id IN($child_ids) AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') ORDER BY date DESC";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$messages = array();
foreach ($results as $row) $messages[] = $row;
//Grab Blogs
$sql = "SELECT * FROM assist_articles WHERE usergroup='$_SESSION[usergroup]' AND LOCATE('$_SESSION[ulevel]',db1701) ORDER BY date DESC";
//echo $sql;
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
//$blog_assist = array();

foreach ($results as $row_assist) $blog_assist[] = $row_assist;
//print_r($blog_assist);

// Also grab student user id to know who sent the message
$student_uid = $_SESSION['uid'];

$button_name = "Signup";
//get the domain info for the subdomain
$school_logo = "logo.png";
$page_title = "$schools_institution_name";
$page_description = "A simple convenient way to apply to us direct.";
//$page_key_title="International Admissions Portal"; // also used in main header

$custom_enquiry_success_message = "Thank you for your interest. <br/><br/>Our Admission Manager will be in contact with you shortly. In the meantime, please do not hesitate to contact us on +44000000000 if you have any questions. You can also continue to browse our website or see what activities our pupils are participating in on our <a href=\"https://wcbs-demo.heiapply.com/application/\" style=\"text-decoration:underline\">Website</a>";
