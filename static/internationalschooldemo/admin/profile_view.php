<div class="col-sm-12 col-md-4 col-lg-4">
    <img src="<?= $avatar ?>" class="img-circle" alt="no image" width="100" border="0" id="tour_profile_photo"
         onerror="this.src='/engine/images/blank_user_icon_fallback.png'"/>
</div>
<div class="col-sm-12 col-md-8 col-lg-4 name">
    <h2 style="color:#F60"><?php echo $core_students_has_applied; ?></h2>
    <div class="info_detail">
        <?php
        $last_login_date = last_logged_in($core_students_rec_id, "D jS \of M Y h:i:s A");
        if ($last_login_date) {
            ?>
            <b>Last Login:</b>
            <?php echo $last_login_date; ?>
        <?php } else {
            echo "Hasn't logged in yet";
        } ?>
    </div>
</div>
<?php $parent = "";
?>

<div class="col-sm-12 col-md-12 col-lg-12">
    <hr>
    <div class="title">Student #</div>
    <div class="info_detail"><?php echo $unique_id; ?></div>
    <div class="title">Email</div>
    <div class="info_detail"><?php echo $core_students_email_address; ?></div>
    <div class="title">Entry Year:</div>
    <div class="info_detail"><?php echo $core_students_cohort; ?></div>
    <div class="title">Class:</div>
    <div class="info_detail"><?php echo $core_students_course_of_study; ?></div>
    <div class="title">Nationality</div>
    <div class="info_detail"><?php echo $core_students_country_of_origin; ?></div>
    <div class="title">Date of Birth</div>
    <div class="info_detail"><?php echo format_date("m-d-Y", $core_students_date_of_birth); ?></div>
    <?php

    if ($short_course_level !== 'yes') { ?>
        <!--ACTION BUTTONS-->
        <div class="clearfix"></div><br/>
        <div class="btn-group btn-group-sml">
            <a href="<?= engine_url ?>/includes/inc_view_data.php?vw=<?= $_GET['vw']; ?>&ref=<?= $_GET['ref']; ?>&width=950&height=600&jqmRefresh=false"
               class="thickbox btn btn-warning btn-sm" title="View Application"><i class="fa fa-file-text-o"></i> View
                Application Form Data</a>
        </div>
        <!--ACTION BUTTONS END-->
        <?php
    }

