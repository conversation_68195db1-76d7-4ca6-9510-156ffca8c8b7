<?php
$dbh = get_dbh();

include_once(base_path . "/admin/config.php");

$pages_id = get_page_list_based_on_rules($_SESSION['student_id']);

// Get info about student
list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake, $core_students_deleted, $core_student_application_ucas, $core_student_ucas_pass, $core_students_course_of_study_id, $core_students_course_audio) = get_core_students($_SESSION['student_id']);
dev_debug(json_encode($_SESSION));


$sth = $dbh->prepare("SELECT * FROM core_students WHERE id='{$core_students_id}'");
$sth->execute();
$studentsModel = new Students;
$campaign_id = $studentsModel->get_applicant_navigate_page($sth->fetch(PDO::FETCH_ASSOC), true);
if (in_array($campaign_id, [476,676])) {
    $_SESSION['show_edu'] = 'educational_details';
} else {
    $_SESSION['show_edu'] = 'higher_education';
}
$_SESSION['studentCourseApplied'] = $core_students_course_of_study_id;
// Get avatar
$avatar = get_student_avatar($core_students_id);

// Get course level
$course_level = pull_field("core_course_level", "db343", "WHERE id=$core_students_level_of_entry");

// get percentage of application completed and display
list($required_fields, $required_filled) = filled_percentage_on_new_forms($core_students_id);
$filled_percentage = round(100 * $required_filled / $required_fields, 0, PHP_ROUND_HALF_DOWN);

// Fetch application stages
// ----------------------------------------------------------------------------
// Fetch application stages
$sql = "SELECT db1131 FROM dir_appli_stages WHERE id = '$core_students_application_status'";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$application_status = $stmt->fetchColumn();
$application_status = explode('-', $application_status);
$application_status = trim($application_status[1]);
//----------------------------------------------------------------------------
// Fetch application stages END

// Compares stage student is in to argument
function stage_complete($stage_id, $type = 0)
{
    global $core_students_application_status;

    if ($type !== 0) {
        return ($core_students_application_status > $stage_id) ? ' <span class="glyphicon glyphicon-ok"></span>' : '';
    } else {
        return ($core_students_application_status > $stage_id) ? ' completed' : '';
    }
}

//Grab multiple student profiles
$sql_children = "SELECT * FROM core_students WHERE usergroup='$_SESSION[usergroup]' AND rec_id='$_SESSION[uid]' ORDER BY db39 ASC";
$stmt = $dbh->prepare($sql_children);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$list_children = array();
foreach ($results as $row_children) $list_children[] = $row_children;
$child_ids = "";
$i = 0;
foreach ($list_children as $child) {
    if ($i == 0) {
        $child_ids .= "$child[id]";
    } else {
        $child_ids .= ",$child[id]";
    }
    $i++;
}

// Grab private messages
$sql = "SELECT * FROM core_notes WHERE rel_id IN($child_ids) AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL OR rec_archive = '') ORDER BY date DESC";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$messages = array();
foreach ($results as $row) $messages[] = $row;

//Grab Blogs
$sql = "SELECT * FROM assist_articles WHERE usergroup='$_SESSION[usergroup]' AND db1697='yes' AND LOCATE('$_SESSION[ulevel]',db1701) AND (rec_archive IS NULL OR rec_archive = '') ORDER BY date DESC";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$blog_assist = array();
foreach ($results as $row_assist) $blog_assist[] = $row_assist;

// Also grab student user id to know who sent the message
$student_uid = $_SESSION['uid'];

//custom_internal_number generator
function custom_internal_number_generator()
{

    $dbh = get_dbh();
    $sql = "SELECT *,CAST(db888 AS SIGNED) as db888 FROM core_students WHERE usergroup='" . $_SESSION['usergroup'] . "' ORDER BY db888 DESC LIMIT 1";
    dev_debug($sql);
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $checks = array();
    foreach ($results as $user_info) {
        $last_number = $user_info['db888'];
    }

    if (!$last_number) {
        $last_number = 1000;
    }

    $internal_ref_number = $last_number + 1;
    dev_debug('internal_ref_number' . $internal_ref_number);
    return $internal_ref_number;
}

//Hide the Next & Previous Buton
if ($appli_submited_check != "1") {
    //$totally_kill_proceed_buttons = 1;
}

//The message hides the message that says "Click cbutton to continue"
$hide_click_this_button_message = 1;

//Custom thank you message. This message appears on the submission page
function custom_thank_message()
{
    global $website_url_applicant;
    echo '
<div class="alert alert-success"><i class="fa-lg fa fa-check-square-o" aria-hidden="true"></i> Your submission has been sent successfully.</div>
<a href="' . $website_url_applicant . '/application/Checklist" class="btn btn-primary"><i class="fa fa-arrow-left" aria-hidden="true"></i> Go back to my account</a>
</div>';
}

$button_name = "Signup";
//get the domain info for the subdomain
$school_logo = "logo.png";
$page_title = "$schools_institution_name";
$page_description = "A simple convenient way to apply to us direct.";

//let people auto login
$_SESSION['autologin'] = 'no';

//$page_key_title="International Admissions Portal"; // also used in main header

$default_parents = pull_field("core_parents", "count(*)", "WHERE rel_id='$_SESSION[student_id]'");

if ($default_parents == 0) {
    default_setup_fresh("core_parents", $_SESSION['student_id'], $_SESSION['uid']);
}

dev_debug("Course of Study **$core_students_course_of_study_id**");
//the link to send users based on their route
//get the correct landing page to send them to

//if no route then check if this is for programme
$page_to_navigate_to = pull_field("core_recruitment_campaign,form_cms", "db647", "WHERE db28379=form_cms.id AND core_recruitment_campaign.usergroup = $_SESSION[usergroup] AND db28374=$core_students_level_of_entry");

if (!$page_to_navigate_to) {
    //check if route is set
    $page_to_navigate_to = pull_field("core_recruitment_campaign,form_cms", "db647", "WHERE db28379=form_cms.id AND core_recruitment_campaign.usergroup = $_SESSION[usergroup] AND db28378=$core_student_application_route");
}

//if no route then check if this is for programme
if (!$page_to_navigate_to) {
    $page_to_navigate_to = pull_field("core_recruitment_campaign,form_cms", "db647", "WHERE db28379=form_cms.id AND core_recruitment_campaign.usergroup = $_SESSION[usergroup] AND db28374=$core_students_course_of_study");
}
if ($page_to_navigate_to !== '') {
    $page_link = "$page_to_navigate_to";
} else {
    $page_link = "personal_information";
}
$checks = [];
$checks[] = pull_field('dir_custom2', 'db10145', "WHERE rel_id = {$_SESSION['student_id']}");
$checks[] = pull_field('dir_custom2', 'db10147', "WHERE rel_id = {$_SESSION['student_id']}");
$checks[] = pull_field('dir_custom2', 'db10149', "WHERE rel_id = {$_SESSION['student_id']}");
$checks[] = pull_field('dir_custom2', 'db10406', "WHERE rel_id = {$_SESSION['student_id']}");
$checks[] = pull_field('dir_custom2', 'db10413', "WHERE rel_id = {$_SESSION['student_id']}");
$checks[] = pull_field('dir_custom2', 'db10153', "WHERE rel_id = {$_SESSION['student_id']}");
$checks[] = pull_field('dir_custom2', 'db10285', "WHERE rel_id = {$_SESSION['student_id']}");

///if one of the above is yes send email below
$template_ids = [];
if (in_array('yes', $checks)) {
    $selected_template_id1 = '9908';
    $subject1 = pull_field('coms_template', 'db1086', "WHERE id='{$selected_template_id1}'");
    $email1_check = pull_field('form_email_log', 'id', "where rel_id = {$_SESSION['student_id']} and (db1149= '{$subject1}' or db1158= {$selected_template_id1}) and db1153='{$_SESSION['user']}'");
    //if email has not been sent before send it
    if (empty($email1_check)) {
        $template_ids[] = $selected_template_id1;
    }
}


$require_interview = pull_field('dir_custom2', 'db10161', "WHERE rel_id = {$_SESSION['student_id']}");
if ($require_interview == 'yes') {
    $selected_template_id2 = '9902';
    $subject2 = pull_field('coms_template', 'db1086', "WHERE id='{$selected_template_id2}'");

    $email2_check = pull_field('form_email_log', 'id', "where rel_id = {$_SESSION['student_id']} and (db1149= '{$subject2}' or db1158= {$selected_template_id2}) and db1153='{$_SESSION['user']}'");
    //if email has not been sent before send it
    if (empty($email2_check)) {
        $template_ids[] = $selected_template_id2;
    }
}

$criminal_records_reg = pull_field('dir_registration8', 'db35320', "WHERE rel_id = {$_SESSION['student_id']}");
if ($criminal_records_reg == 'yes') {
    $selected_template_id3 = '9899';
    $subject3 = pull_field('coms_template', 'db1086', "WHERE id='{$selected_template_id3}'");
    $email3_check = pull_field('form_email_log', 'id', "where rel_id = {$_SESSION['student_id']} and (db1149= '{$subject3}' or db1158= {$selected_template_id3}) and db1153='{$_SESSION['user']}'");
    //if email has not been sent before send it
    if (empty($email3_check)) {
        $template_ids[] = $selected_template_id3;
    }
}


load_helper('email');
require_once($_SERVER['DOCUMENT_ROOT'] . "/admin/app/models/enquiries.php");

include_once(base_path . "/admin/config.php");
if (!empty($template_ids)) {

    foreach ($template_ids as $template_id) {
        //send out the email template to the student
        $qry = "SELECT db1085 as template, db1086 as subject FROM coms_template WHERE id='{$template_id}'";
        $sth8 = $dbh->prepare($qry);
        $sth8->execute();
        $template = $sth8->fetch(PDO::FETCH_ASSOC);
        $email_message = $template['template'];
        $subject = $template['subject'];
        $enquiries_model = new enquirieModel();
        $use_twig = pull_field("lead_preferences", "db62990", "WHERE usergroup=" . $_SESSION['usergroup']);

        if ("on" == $use_twig) {
            $template_args = array(
                'email_html' => $email_message,
                "student_id" => $core_students_id,
                "including_defaults" => true
            );
            $email_message = process_email_variables($template_args);
        } else {
            $email_message = $enquiries_model->email_template_replace_values_from_db($email_message, $core_students_id, "applicant");
        }

        $email = new Email_helper(true);
        $email->setUserLevel(Email_helper::APPLICANT);
        $email->to($_SESSION['user'])->subject($subject)->plain($email_message)->html($email_message)->replyTo('')->template($template_id);
        $email->category('personal-direct-msg')->setRelId($_SESSION['student_id'])->send();

    }
}

/////checklist
//only show if this is checklist
$checklist = strtolower(find_slug(1));
if ($checklist == 'checklist') {
    //////////tasks


    // mark task as done as soon as the mark as done button is pressed.
    if (isset($_POST['mark_as_done'])) {
        $task_id = ($_POST['mark_as_done']);

        $sql = "UPDATE core_tasks SET db1746 = 'completed',db1747 = now() WHERE id = '$task_id'";
        $sql = $dbh->prepare($sql);
        $sql->execute();

        //get details of task
        $task_title = pull_field("core_tasks", "concat(db1741,' - ',db1743)", "WHERE id = '$task_id' ");
        $uname = pull_field("core_students", "username_id", "WHERE id='$_SESSION[student_id]' ");
        $student_link_url = engine_url . "/direct/proc?pg=4&vw=$uname&ref=$_SESSION[student_id]";

        //	send message to owner about the task being marked as done

        $sql = "INSERT INTO core_notes 
		(username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139) 
		VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$_SESSION[student_id]', '14', 'Task Completion Alert - $task_title', '" . $task_id . "', 'yes', '$student_link_url', '0', 'no')";// if users does not want alerts lock using this
        $stmt = $dbh->prepare($sql);
        $stmt->execute();


    }

    $dbh = get_dbh();
    $task_sql = "SELECT 
	id,
	DATE_FORMAT(date, '%d/%m/%Y @%H%i') AS 'crafted',
	DATEDIFF(db1744,NOW()) AS 'time_left',
	db1741 AS 'title',
	db1743 AS 'description',
	db22441 AS 'Attached To',
	(SELECT concat(db39,' ',db40) FROM core_students WHERE core_students.id=core_tasks.rel_id) AS 'Relating To',
	(SELECT db106 FROM form_users WHERE id=db1745) AS 'assigned_to',
	db22444 as party_responsible,
	'' AS 'Application',
	db1746 AS 'status',
	db52933 as task_hide_options
	FROM core_tasks
	WHERE db22444='applicant' and (rec_archive is null or rec_archive ='') and rel_id = ?
	AND db1746 IN ('completed','active','on_hold')
	ORDER BY id ASC
	";
#echo $task_sql;

    $sth3 = $dbh->prepare($task_sql);
    $sth3->execute(array($_SESSION['student_id']));
    $tasks = array();
    while ($row3 = $sth3->fetch(PDO::FETCH_ASSOC)) $task_rows[] = $row3;

    $task_incomplete_sql_count = "SELECT
count(*)
FROM core_tasks
WHERE db22444='applicant' and db1746 IN ('active','on_hold') and (rec_archive is null or rec_archive ='') and rel_id = ?";
    $sth4 = $dbh->prepare($task_incomplete_sql_count);
    $sth4->execute(array($_SESSION['student_id']));
    $task_incomplete_count = $sth4->fetchColumn();
}


if (!function_exists('after_submit')) {
    function after_submit()
    {
        global $front_website_url;
        global $campaign_id;
        $email_already_sent = (int)pull_field("form_email_log", "count(id)", "WHERE rel_id = '$_SESSION[student_id]' AND usergroup='$_SESSION[usergroup]' AND db1149='Application with Declarations'");
        if ($email_already_sent == 0 && in_array($campaign_id, [479, 476, 676]) && included_declarations()) {
            $uname = pull_field("core_students", "username_id", "WHERE id='$_SESSION[student_id]'");
            $student_link_url = engine_url . "/direct/proc?pg=4&vw=$uname&ref=$_SESSION[student_id]";
            list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake, $core_students_deleted, $core_student_application_ucas, $core_student_ucas_pass, $core_students_course_of_study_id, $core_students_course_audio) = get_core_students($_SESSION['student_id']);

            //get template
            list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template(9473);
            $host_url = $front_website_url . '/application';
            $message_html = str_replace('{{student_portal}}', '<a href="$host_url">student portal</a>', $message_html);
            $message_plain = str_replace('{{student_portal}}', '<a href="$host_url">student portal</a>', $message_plain);


            //search replace
            $message_plain = email_template_replace_values("{{name}}", $_SESSION['name'], $coms_template_plain_text_version);
            $message_html = email_template_replace_values("{{name}}", $_SESSION['name'], $coms_template_html_version);
            $message_plain = email_template_replace_values("{{student_first_name}}", $core_students_first_name, $message_plain);
            $message_html = email_template_replace_values("{{student_first_name}}", $core_students_first_name, $message_html);
            $message_plain = email_template_replace_values("{{first_name}}", $core_students_first_name, $message_plain);
            $message_html = email_template_replace_values("{{first_name}}", $core_students_first_name, $message_html);
            $message_plain = email_template_replace_values("{{student_surname}}", $core_students_surname, $message_plain);
            $message_html = email_template_replace_values("{{student_surname}}", $core_students_surname, $message_html);
            $message_plain = email_template_replace_values("{{student_course}}", $core_students_course_of_study, $message_plain);
            $message_html = email_template_replace_values("{{student_course}}", $core_students_course_of_study, $message_html);
            $message_plain = email_template_replace_values("{{student_link_url}}", "<a href='$student_link_url'>student profile</a>", $message_plain);
            $message_html = email_template_replace_values("{{student_link_url}}", "<a href='$student_link_url'>student profile</a>", $message_html);
            $message_plain = email_template_replace_values("{{course}}", $core_students_course_of_study, $message_plain);
            $message_html = email_template_replace_values("{{course}}", $core_students_course_of_study, $message_html);
            $message_plain = email_template_replace_values("{{cohort}}", $_SESSION['school_cycle'], $message_plain);
            $message_html = email_template_replace_values("{{cohort}}", $_SESSION['school_cycle'], $message_html);
            $message_plain = email_template_replace_values("{{middle_name}}", $core_students_middle_name ? $core_students_middle_name : "", $message_plain);
            $message_html = email_template_replace_values("{{middle_name}}", $core_students_middle_name ? $core_students_middle_name : "", $message_html);

            // $message_html = text_to_html($message_plain);
            $emailTo = "<EMAIL>";// student email
            $emailFrom = $coms_template_email_address_to_send_from;//pull_field("form_schools","db1118","WHERE id='$_SESSION[usergroup]'");//school email

            log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, "Application with Declarations", $_SESSION['student_id'], '0');
        }
    }
}
if (!function_exists('included_declarations')) {
    function included_declarations()
    {
        $query = "SELECT concat_ws('|||',db10151,db10155,db10304,db10310,db10315,db10318,db10319,db10320,db776,db781,db838) as declarations FROM dir_registration1 , dir_custom2 WHERE rel_id='{$_SESSION['student_id']}' AND dir_registration1.rel_id=dir_custom2.rel_id AND dir_registration1.usergroup='{$_SESSION['usergroup']}' LIMIT 1";
        $sth = dbh()
            ->prepare($query);
        $declarations = $sth->fetchColumn();
        $responses = explode("|||", $declarations);
        return in_array('yes', $responses);
    }
}

if ($core_students_course_of_study_id == 9626){
	//get  the data
	$dataSql = " SELECT db31799,db35367,db17740,db17741 FROM dir_registration1 where rel_id={$_SESSION['student_id']}";
	dev_debug($dataSql);
	$stm = $dbh->prepare($dataSql);
	$stm->execute();
	$scholarshipEligibility = $stm->fetch(PDO::FETCH_ASSOC);
	
	if ($scholarshipEligibility['db31799'] == 'no' || $scholarshipEligibility['db35367'] == 'no' ||$scholarshipEligibility['db17740'] == 'no' ||$scholarshipEligibility['db17741'] == 'no' ) {
		$allPages = explode(',',$pages_id);
		
		array_splice($allPages, 3);
		
		$allPages[] = 9908;
		
		$pages_id = implode(',',$allPages);
	}
}
?>
<style>
    .form_wrapper {
        background: #58b74e;
        margin: 0;
        text_align: left;
        font-family: Arial
    }

    .form_style_bkg {
        color: #666;
        padding: 0;
        margin: 10px auto;
        max-width: 850px;
        background-color: #fff;
        -webkit-box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
        -moz-box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
        box-shadow: -1px -1px 9px rgba(50, 50, 50, 0.78);
    }

    .top_logo {
        padding: 0;
        max-height: 150px;
    }

    .form_detail {
        padding: 10px
    }

    .slogan {
        background: #58b74e;
        color: #fff
    }

    .box > .icon {
        text-align: center;
        position: relative;
    }

    .box > .icon > .image {
        position: relative;
        z-index: 2;
        margin: auto;
        width: 88px;
        height: 88px;
        border: 8px solid white;
        line-height: 88px;
        border-radius: 50%;
        background: #fff;
        vertical-align: middle;
        padding: 10px
    }

    .box > .icon:hover > .image {
        background: #f0f0f0;
    }

    .box > .icon > .image > i {
        font-size: 36px !important;
        color: #fff !important;
    }

    .box > .icon:hover > .image > i {
        color: white !important;
    }

    .box > .icon > .info {
        margin-top: -24px;
        background: rgba(0, 0, 0, 0.04);
        border: 1px solid #e0e0e0;
        padding: 15px 0 10px 0;
    }

    .box > .icon:hover > .info {
        background: rgba(0, 0, 0, 0.04);
        border-color: #e0e0e0;
        color: white;
    }

    .box > .icon > .info > h3.title {
        font-family: "Roboto", sans-serif !important;
        font-size: 16px;
        color: #222;
        font-weight: 500;
    }

    .box > .icon > .info > p {
        font-family: "Roboto", sans-serif !important;
        font-size: 13px;
        color: #666;
        line-height: 1.5em;
        margin: 20px;
    }

    .box > .icon:hover > .info > h3.title, .box > .icon:hover > .info > p, .box > .icon:hover > .info > .more > a {
        color: #222;
    }

    .box > .icon > .info > .more a {
        font-family: "Roboto", sans-serif !important;
        font-size: 12px;
        color: #222;
        line-height: 12px;
        text-transform: uppercase;
        text-decoration: none;
    }

    .box > .icon:hover > .info > .more > a {
        color: #fff;
        padding: 6px 8px;
        background-color: #004555
    }

    .box .space {
        height: 30px;
    }
</style>
