
   
    
</div> <!-- .row -->

</div> <!-- #main -->
</div> <!-- .row surrounding main -->
	
</div> <!-- #everything -->

<div class="footer-wrap">
    <div class="container">
        <div class="row">
            <div id="footer col-xs-12">
                <ul class="footer-links">
                    <li>Copyright © <?=date('Y')?> - Powered by <a href="http://heiapply.com/" target="_blank" title="Paperless Admissions Software" alt="Paperless Admissions Software">HEIapply</a></li>
                </ul>
            </div><!-- #footer -->
        </div>
    </div>
</div>
<!--Tales Format-->
<script type="text/javascript" src="https://cdn.datatables.net/r/dt/dt-1.10.9,cr-1.2.0,fh-3.0.0,sc-1.3.0/datatables.min.js"></script>

<script type="text/javascript" src="<?php echo engine_url; ?>/tools/datatables/plug-ins/bootstrap/dataTables.bootstrap.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/select/1.2.0/js/dataTables.select.min.js"></script>
<script type="text/javascript" src="https://cdn.datatables.net/rowreorder/1.1.2/js/dataTables.rowReorder.min.js"></script>
<style>
    .dataTables_filter {
        float: right;
    }
    select.input-sm {
        width: 33%;
    }
    input.input-sm {
        width: 75%;
    }
    ul.pagination {
        margin-top: 10px;
        margin-bottom: 30px;
        float:right;
    }
</style>
<script>
    $(function() {
        $(document).ready(function() {

            // DataTable
            var settings_table = $('.settings_data_table').DataTable(
                {
                    colReorder: true,
                    stateSave: true


                }
            );

            // Apply the search
            //settings_table.columns().every( function () {
            //    var that = this;

            //   $( 'input', this.footer() ).on( 'keyup change', function () {
            //        that
            //            .search( this.value )
            //            .draw();
            //    } );
            //} );

            // Setup - add a text input to each footer cell
            //$('.settings_data_table tfoot th').each( function () {
            //    var title = $('#data_table tfoot th').eq( $(this).index() ).text();
            //    $(this).html( '<input type="text" class="form-control input-sm" title="start typing to activate filters" placeholder="filter '+title+'" />' );
            //} );
        } );
    });

</script>

<script type="text/javascript">
    let db17741 = $('[name="db17741"]');
    let db17740 = $('[name="db17740"]');
    let db35367 = $('[name="db35367"]');
    let db31799 = $('[name="db31799"]');
    
    if (db17741.length>0 || db17740.length>0  || db35367.length>0  || db31799.length>0 ) {
        console.log("exec 1")
        function nextpagemanipulate(){
            
            if( $('[name="db17741"]:checked').val()==='no' ||$('[name="db17740"]:checked').val()==='no' ||$('[name="db35367"]:checked').val()==='no' ||$('[name="db31799"]:checked').val()==='no'  ){
                $('[name="next_page_url"]').val("<?php echo 'https://'.$_SERVER['SERVER_NAME'].'/'.$path_url_tag.'/can-not-continue' ?>")
            }else{
                $('[name="next_page_url"]').val("<?php echo 'https://'.$_SERVER['SERVER_NAME'].'/'.$path_url_tag.'/school_education' ?>")
            }

        }

        db17741.change(function(){
            nextpagemanipulate();
        })

        db17740.change(function(){
            nextpagemanipulate();
        })

        db35367.change(function(){
            nextpagemanipulate();
        })

        db31799.change(function(){
            nextpagemanipulate();
        })

        nextpagemanipulate();
    }else{
        console.log("exec 2")
    }




</script>


<!-- <div style="float:left; margin-top:-150px; padding:20px"><img src="<?php echo $front_web_url_file_loc; ?>/images/logo.png"></div> -->

</body>
</html>
