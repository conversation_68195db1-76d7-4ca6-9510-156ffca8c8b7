/*CONTROLLER CONTAINER*/
.controller_container{ padding:15px}

.form-horizontal{ width:98%; font-size:14px}


/*CHECK BOXES*/
.form-horizontal .checkbox{ margin-right:5px; float:left; min-height:0}
.checkstage{ border-bottom:2px solid #ccc}

/*bootstrap*/
.checkbox{ 
height: 15px;
float: left;
width: 20px;
margin-right: 10px;
}
.radios{
height: 15px;
width: 20px;
margin-right: 10px; 
}
.checklist li{ margin-bottom:5px; list-style-type: none; }
.checklist_horizontal li{ margin-bottom:5px; list-style-type: none; display: inline-block; margin-right: 10px; width:auto;}
.checklist_horizontal .checkbox{ margin-right: 0px;}
h1,h2,h3{ margin-bottom: 0px;}
h1{ font-size:28px}
h2{ font-size:24px; margin-top:2px}
h3{ font-size:18px}
p{ font-size:14px}

.postcode_field{
    width: 50%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.428571429;
    color: #555;
    vertical-align: middle;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 6px 0px 0px 6px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    -webkit-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
}
.btn-postcode{
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: normal;
    line-height: 1.428571429;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 0px 6px 6px 0px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
    color: #fff;
    background-color: #f0ad4e;
    border-color: #eea236;  
}
.postcode_field_dropdown{
    margin-top: 10px;
    display:block;
    width: 100%;
    height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.428571429;
    color: #555;
    vertical-align: middle;
    background-color: #d8d1c8;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    -webkit-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
}

/* --- popup message start---*/
#text {margin:50px auto; width:500px;}
.hotspot {color:#fff; padding-bottom:10px; cursor:pointer; text-align:center;}

#tt {position:absolute; display:block; background:url(../images/pop_img/tt_left.gif) top left no-repeat; margin-bottom:100px; margin-top:-10px;}
#tttop {display:block; height:5px; margin-left:5px; background:url(../images/pop_img/tt_top.gif) top right no-repeat; overflow:hidden}
#ttcont {display:block; padding:2px 12px 3px 7px; margin-left:5px; background:#FFFF99; color:#000; border:1px #FFCC99 solid; -moz-border-radius:5px 5px 5px 0; -webkit-border-radius: 5px 5px 0 0;}
#ttbot {display:block; height:5px; margin-left:5px; background:url(../images/pop_img/tt_bottom.gif) top right no-repeat; overflow:hidden}
/* --- popup message end---*/


h1, .h1 {
    font-size: 46px;
}

h2, .h2 {
    font-size: 39px;
}

h3, .h3 {
    font-size: 32px;
}

.splash h3 {
    margin-bottom: 40px;
    margin-top: 10px;
}

.sidebar h3 {
    margin-bottom: 17px;
}

h4, .h4 {
    font-size: 25px;
}

h5, .h5 {
    font-size: 20px;
}
