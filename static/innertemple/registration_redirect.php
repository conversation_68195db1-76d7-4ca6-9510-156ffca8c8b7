<?php
//this script will take a url from the website and send the applicant to the correct course & route
include_once "../../engine/admin/inc/lib.inc.php";// call functions

if ($_GET['clid']) {

    // get the course level data
    $sql_course_level_data = "
	SELECT *
	FROM core_course_level
	WHERE id = '$_GET[clid]'
	AND usergroup = '$_SESSION[usergroup]'
	";

    // execute and get results
    dev_debug($sql_course_level_data);

    $dbh = get_dbh();
    $sql = $dbh->prepare($sql_course_level_data);
    $sql->execute();
    $pdo_course_level_data_rows = $sql->fetchAll(PDO::FETCH_OBJ);
    $pdo_course_level_data = $pdo_course_level_data_rows[0];

    // get the url of where they are coming from
    $from_url = $_SERVER["HTTP_REFERER"];

    //set session values to populate the hidden fields on the form
    $_SESSION['prog_course_selected'] = "<a href='$from_url' class=\"pull-right\">< back to previous page</a><h2>Application for:<br/><b>" . $pdo_course_level_data->db343 . "</b></h2>
	<i>Not the correct option? <a href='$from_url'><b>go back</b></a> and select a different option</i>
	<h2><b>Apply Now</b></h2><p>Applying is quick and easy, simply complete the form below and start preparing your application</p>"; // Instructions telling them what they have selected
    $_SESSION['prog_level'] = $pdo_course_level_data->id; //- course level
//	$_SESSION['prog_course'] = $pdo_course_data->id;  //- course

}// end course level check

else if ($_GET['muid']) {

    // get the course data
    $sql_course_data = "
	SELECT *
	FROM core_courses
	WHERE db231 = '$_GET[muid]'
	AND usergroup = '$_SESSION[usergroup]'
	";

    // execute and get results
    dev_debug($sql_course_data);

    $dbh = get_dbh();
    $sql = $dbh->prepare($sql_course_data);
    $sql->execute();
    $pdo_course_data_rows = $sql->fetchAll(PDO::FETCH_OBJ);
    $pdo_course_data = $pdo_course_data_rows[0];

    // get the url of where they are coming from
    $from_url = $_SERVER["HTTP_REFERER"];

    //set session values to pupulate the hidden fields on the form
    $_SESSION['prog_course_selected'] = "<a href='$from_url' class=\"pull-right\">< back to previous page</a><h2>Application for:<br/><b>" . $pdo_course_data->db232 . ".</b></h2>
	" . $pdo_course_data->db234 . "<i>Not the correct course? 
	<a href='$from_url'><b>go back</b></a> and select a different course</i><h2><b>Apply Now</b></h2><p>Applying is quick and easy, simply complete the form below and start preparing your application</p>"; // Instructions telling them what they have selected
    $_SESSION['prog_level'] = $pdo_course_data->db341; //- course level
    $_SESSION['prog_course'] = $pdo_course_data->id;  //- course

} else {

    $_SESSION['prog_level'] = '';
    $_SESSION['prog_course_selected'] = '';
    $_SESSION['prog_course'] = '';

}// end course check

// used to set a manual route
//$_SESSION['set_route'] = pull_field("dir_route_rules","db1764","WHERE id=".$pdo_course_data->db22005);  //- route
//$_SESSION['override_route'] = "yes"; // this will override the route query on the applicant registration page
//$_SESSION['prog_intake'] = $pdo_course_data->;  //- intake

$link = "https://$_SESSION[subdomain].heiapply.com/application/register";

dev_debug($_SESSION, "print_r");
dev_debug($pdo_course_data, "print_r");

//if debug_remove the header
if ($_GET['debug_mode'] !== 'yes') {
    header('Location: ' . $link);
    exit;
}
	
