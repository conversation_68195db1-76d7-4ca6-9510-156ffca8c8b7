<?php

$dbh = get_dbh();
if (!empty($from_required_file_rules_function)) {
    $cs_course_of_study_id = pull_field('core_students', 'db889', "WHERE id=" . $student_id . " AND usergroup=" . $_SESSION['usergroup']);
    $cs_student_id = $student_id;
} else {
    $cs_course_of_study_id = $core_students_course_of_study_id;
    $cs_student_id = $_SESSION['student_id'];
}
dev_debug('cs_course_of_study_id :' . $cs_course_of_study_id);
if ('13700' == $cs_course_of_study_id) {
    $edu_history = pull_field('dir_student_public_results', 'group_concat(db39703)', "WHERE rel_id = '$cs_student_id' AND usergroup='$_SESSION[usergroup]'");
    $provided_history = explode(',', $edu_history);
    //echo "provided_history <pre>".print_r($provided_history,1)."</pre>";
    if (count($provided_history) == 0) {
        //$add_education_when_course_is_student_or_graduate = true;
    } elseif (!in_array('Law_Degree', $provided_history)) {
        //$add_education_when_course_is_student_or_graduate_both = true;
        if (in_array('Other_Degree', $provided_history) && in_array('Conversion_Course', $provided_history)) {
            //$add_education_when_course_is_student_or_graduate_both = false;
            //$add_education_when_course_is_student_or_graduate_both_uploads = true;

            // if (in_array('Proof of Conversion Course',$app_uploaded_files)&&in_array('Proof of Non-Law Undergraduate Degree',$app_uploaded_files)) {
            //     $add_education_when_course_is_student_or_graduate_both_uploads = false;
            // }

            $camp_reqs = ['Proof of Conversion Course', 'Proof of Non-Law Undergraduate Degree'];

            $campsreqs_sql = "SELECT id,db743  FROM form_file_category WHERE db744='live'  and usergroup = " . session_info('usergroup') . " and db743  in ('" . implode("','", $camp_reqs) . "') ORDER BY db743";
            dev_debug($campsreqs_sql);
            $sql14 = $dbh->prepare($campsreqs_sql);

            $sql14->execute();
            $camps_cus_files = $sql14->fetchall();

            //echo "camps_cus_files <pre>".print_r($camps_cus_files,1)."</pre>";
            //echo "uploadedfiles <pre>".print_r($uploadedfiles,1)."</pre>";

            if (!empty($camps_cus_files)) {
                foreach ($camps_cus_files as $campskey => $campsvalue) {
                    if (!empty($required_files_list_text)) {
                        // comming from application.php
                        if (!in_array($campsvalue['id'], $uploadedfiles)) {
                            if (strpos($required_files_list_text, $campsvalue['db743']) === false) {
                                $required_files_list_text .= "<li>" . $campsvalue['db743'] . "</li>";
                            }

                        }


                    }


                    if (!empty($from_required_file_rules_function)) {
                        //from required file rules function
                        $all_required_files .= "," . $campsvalue['id'];
                        dev_debug('all_required_files cfile ' . $all_required_files);
                    }
                }
            }

        }
    }
}


