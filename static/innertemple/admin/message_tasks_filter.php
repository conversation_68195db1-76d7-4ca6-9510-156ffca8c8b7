<?php
include_once(base_path . "/admin/config.php");


$curr_coh = $_GET['cohort'] ?? $_SESSION['school_cycle'];
$school_cycle_sql = " AND db1680='" . $curr_coh . "'";

$dbh = get_dbh();
$query = "SELECT id,db1681 FROM dir_cohorts WHERE id!='' AND usergroup=" . $_SESSION['usergroup'] . " AND (client_archive is null OR client_archive = '') $school_cycle_sql  AND (rec_archive is null OR rec_archive ='') order by db1681";
dev_debug($query);
$sth = $dbh->prepare($query);
$sth->execute();

$results_list = array();
foreach ($row_result = $sth->fetchAll() as $row) {
    $results_list[] = $row;
}

$apps = new Students();

$coh = $apps->cohorts_list(['school_id' => $_SESSION['usergroup']]);

?>
<link rel="stylesheet" href="/admin/assets/css/select2.min.css">
<script src="/admin/assets/js/select2.min.js"></script>


<select class="btn-default btn select_search" style="width: 17%;" name="cohort" id="cohort_field">
    <option value="all">Filter by cohort</option>
    <?php if (!empty($coh[0])) { ?>
        <option <?php echo($_GET['cohort'] == 'all_cohorts' ? 'selected' : "") ?> value="all_cohorts">
            All Cohorts
        </option>
        <?php foreach ($coh as $key => $value) { ?>
            <option value="<?php echo $value['title'] ?>" <?php if (!empty($_GET['cohort']) && ($value['title'] == $_GET['cohort']) || empty($_GET['cohort']) && ($value['title'] == $_SESSION['school_cycle'])) { ?> selected <?php } ?>><?php echo $value['title'] ?></option>
            <?php
        }
    } ?>
</select>
<script type="text/javascript">
    $(document).on('change', '#cohort_field', function () {
        // $('#custom_filter_form').submit();
        console.log(this)
        //$('.messages_form').submit();

        var new_url = replaceUrlParam('cohort', $('#cohort_field').val())
        //console.log(new_url)
        window.location.href = new_url
    })

</script>


<select class="btn-default btn select_search" style="width: 30%;" name="intake" id="intake_field">
    <option value="all">Filter by term</option>
    <?php if (isset($results_list[0])) {
        foreach ($results_list as $key => $value) { ?>
            <option value="<?php echo $value['id'] ?>" <?php if ($value['id'] == $_GET['intake']) { ?> selected <?php } ?>><?php echo $value['db1681'] ?></option>
            <?php
        }
    } ?>
</select>
<script type="text/javascript">
    $(document).on('change', '#intake_field', function () {
        // $('#custom_filter_form').submit();
        console.log(this)
        //$('.messages_form').submit();

        var new_url = replaceUrlParam('intake', $('#intake_field').val())
        //console.log(new_url)
        window.location.href = new_url
    })

    $(document).ready(function () {
        $('.select_search').select2()
    })

</script>
