<?php
// list pull
// FUNCTION TO GET_CMS
//Bar Scholarship /Pegasus Scholarship
switch ($ref_course ?? null) {
    case 9626 :
        // If the applicant is on the GDL course (ID 10556) then send the referee email ID 3974 and direct them to the reference page ID 5195
    case 10556:
        $page_id = 5195;
        break;
    case 9788:
        // If the applicant is on the Pupilage course (ID 10577) then send the referee email ID 3977 and direct them to the reference page ID 5198
    case 10577:
        // If the applicant is on the Internship course (ID 10580) then send the referee email ID 3980 and direct them to the reference page ID 5198
    case 10580:
        $page_id = 5198;
        break;
    default:
        $page_id = 4907;
}

dev_debug("page_id=" . $page_id);
dev_debug("ref_type=" . $ref_type);
dev_debug("core_students_course_of_study=" . $ref_course);

list($page_id, $cms_category, $cms_page_name, $cms_heading, $cms_brief, $cms_article, $cms_data, $cms_publish, $cms_page_title, $cms_keywords, $cms_page_description, $cms_included_form, $cms_privacy, $cms_refreshpage_on_add, $nextpage_url, $cms_introductory_instructions, $cms_pull_file, $cms_save_action, $cms_system_form_id, $cms_system_quiz_id) = get_cms('id', $page_id);

dev_debug("page-details = $page_id,$cms_category,$cms_page_name,$cms_heading,$cms_brief,$cms_article,$cms_data,$cms_publish,$cms_page_title,$cms_keywords,$cms_page_description,$cms_included_form,$cms_privacy,$cms_refreshpage_on_add,$nextpage_url,$cms_introductory_instructions,$cms_pull_file,$cms_save_action,$cms_system_form_id,$cms_system_quiz_id)=get_cms('id',$page_id");

if ($page_id) {
    if (4 != $_GET['pg']) {
        // echo admin only instructions
        echo "<h1>$cms_heading</h1>";
        echo $cms_introductory_instructions;
        echo '<b>' . $cms_brief . '</b><br/>';
        // do some tag replacements
        $cms_article = str_replace("[[name]]", $ref_fname, $cms_article);
        $cms_article = str_replace("[[surname]]", $ref_surname, $cms_article);
        echo $cms_article;
    }
} else {
    die('reference form error occured. Please notify admin');
}
