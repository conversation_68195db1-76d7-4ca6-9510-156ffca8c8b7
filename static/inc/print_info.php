<?php
$phptime_start = microtime(true);

if (isset($_GET['testing_errors']) && $_GET['testing_errors']) {
    echo "HERE";
    error_reporting(E_ALL | E_STRICT);
    ini_set('display_errors', 1);
}

if (isset($_GET['print']) && "true" == $_GET['print']) {
    include_once $_SERVER['DOCUMENT_ROOT'] . "/engine/admin/inc/lib.inc.php";
    include_once $_SERVER['DOCUMENT_ROOT'] . "/admin/config.php";
}


include_once "print_support.php";


/**
 *
 */
class ApplicationPrinter extends Large_tables_printer
{
    private $school_info;

    function print_info($applicant_id = false, $post = [], $print_on_server = false)
    {
        global $school_info;

        $this->school_info = $school_info;
        $external_profile_url = base_path . '/' . front_header_file_location . "/admin/custom_pages_view.php"; // link to external
        // if (file_exists($external_profile_url)) {
        //     echo "<pre>".print_r($external_profile_url,true)."<pre>";
        //     include "$external_profile_url";
        // }

        $students = new Students;
        $form_templates = new FormTemplates;
        $form_cms = new FrontendPages;
        $fields = new Fields;

        if (!empty($_POST['temp_data_holder'])) {
            $temp_infos = json_decode(file_get_contents($_POST['temp_data_holder']), true);
            $_SESSION = $temp_infos['SESSION'];
            $_POST = $temp_infos['POST_DATA'];
        }

        /** ===================================
         * Applicant Details
         * ====================================  */
        if (!$applicant_id) {
            $applicant_id = $_POST['applicant_id'];
            $post = $_POST;
        }
        dev_debug("HERE 1");
        if (isset($_GET['dumppost']) || isset($_GET['bulk_print_debug'])) {
            echo "<pre>" . print_r($applicant_id, 1) . "<pre>";
            echo "<pre>" . print_r($post, 1) . "<pre>";
            exit();
        }
        //echo $applicant_id;
        $applicants_args = array("id" => $applicant_id, 'school_id' => $school_info['id'], 'show_form_answers' => true, 'no_test_check' => true, 'no_rec_archieve_check' => true);
        //get columns
        $cms_pages = [];
        foreach ($post['sections'] as $section) {
            $cms_pages[] = $section;
        }
        $final_pages = implode(',', $cms_pages);

        //echo $final_pages;

        //get columns from system form fields
        dev_debug("HERE 2");
        $page_columns = $students->get_print_columns($final_pages);
        $final_columns = array_column($page_columns, 'db_name');

        //append other columns
        $applicant_info_columns = [];
        foreach ($post['applicant_info'] as $key => $value) {
            $key = str_replace("'", '', $key);
            $applicant_info_columns[] = $key;
        }
        $general_info_columns = [];
        foreach ($post['general_info'] as $key => $value) {
            $key = str_replace("'", '', $key);
            $general_info_columns[] = $key;
        }
        $other_columns = array_unique(array_merge($applicant_info_columns, $general_info_columns));
        $final_columns = array_merge($final_columns, $other_columns);
        $final_columns = array_filter($final_columns, 'strlen');
        $applicants_args['columns'] = $final_columns;
        $applicants_args['show_rejected'] = 'yes';
        $applicants_args['show_withdrawn'] = 'yes';
        $applicants_args['run_query'] = 'yes';
        dev_debug("HERE 3");
        //$applicants_args['dump_query']='yes';
        $entry_info = $students->get($applicants_args);

        // echo '<pre>';
        // print_r($entry_info);
        // exit();

        /** ===================================
         * Gather info required on the layout
         * ====================================    */
        if ($post['print_application']) {

            //ckeck the general information
            if ($post['include_general_info'] == "on") {

                if ($post['all_general_info'] == "all") {
                    $general_info_to_include = "all";
                } else {
                    $gi = array();
                    foreach ($post['general_info'] as $key => $value) {
                        $gi[] = str_replace("'", "", $key);
                    }
                    $general_info_to_include = $gi;
                }
            }

            if ($post['include_applicant_profile_info'] == "on") {
                $ga = array();
                foreach ($post['applicant_info'] as $key => $value) {
                    $ga[] = str_replace("'", "", $key);
                }
                $applicant_info_to_include = $ga;

            }

            //check the sections include
            if (count($post['sections']) > 0) {
                $sections_id = $post['sections'];
            }

            //check if include title is needed
            if ($post['include_print_title'] == 1) {
                $form_title = $post['form_title'];
            }
        }
        dev_debug("HERE 4");

        /** ===================================
         * Get the pages to view
         * ====================================    */
        $pages_args = array('student_id' => $applicant_id);
        //$pages_to_show = $students->get_student_pages($pages_args);
        // if (isset($_GET['print'])&&"true"==$_GET['print']) {
        //       $pages_to_show        = get_page_list_based_on_rules($applicant_id,true,false,true);
        // }else{
        //      //called form students model 	
        //      $pages_to_show        = $students->get_page_list_based_on_rules($applicant_id,true,false,true);	
        // }
        $adminOnlyForms = true;
        if ($_SESSION['ulevel'] == '4') {
            $adminOnlyForms = false;
        }

        $pages_to_show = get_page_list_based_on_rules_print($applicant_id, true, false, $adminOnlyForms);
        $external_profile_url = $_SERVER['DOCUMENT_ROOT'] . '/' . "static/" . $_SESSION['subdomain'] . "/admin/custom_pages_view.php"; // lexternal
        if (file_exists($external_profile_url)) {
            //get the right pages for this section
            include_once "$external_profile_url";
            $core_students_course_of_study_id = pull_field('core_students', 'db889', 'where id=' . $applicant_id);
            if ($core_students_course_of_study_id == 9299) {
                $pages_to_show = custom_page_ids($core_students_course_of_study_id, 9299, $pages_to_show, $applicant_id); //media upload and submit page
            } elseif ($core_students_course_of_study_id == 9470) {
                $pages_to_show = custom_page_ids($core_students_course_of_study_id, 9470, $pages_to_show, $applicant_id); //media upload and submit page
            } elseif ($core_students_course_of_study_id == 9464) {
                $pages_to_show = custom_page_ids($core_students_course_of_study_id, 9464, $pages_to_show, $applicant_id); //media upload and submit page
            } else {
                $pages_to_show = $pages_to_show;
            }
        }
        $dbh = get_dbh();
        $sql = "SELECT *,(SELECT db232 FROM core_courses WHERE id=db889) AS course
		FROM core_students
		WHERE usergroup='$_SESSION[usergroup]'
		  AND (id='$applicant_id')
		  AND (core_students.rec_archive IS NULL OR core_students.rec_archive ='' )
		ORDER BY id ASC
		";
        $app = $dbh->prepare($sql);
        $app->execute();
        $applicant_info = $app->fetch(PDO::FETCH_ASSOC);
        $core_student_current_stage = pull_field(
            'dir_stage_tracker',
            'group_concat(CONCAT("\'", db1142, "\'"))',
            "WHERE rel_id ='{$applicant_id}' AND db1142 LIKE 'db%' AND usergroup='$_SESSION[usergroup]'"
        );

        $campaign_id = $students->get_applicant_navigate_page($applicant_info, true);
        dev_debug("applicant_info :" . json_encode($applicant_info));
        dev_debug("campaign_id:" . $campaign_id);
        $multiple_stages_pages = get_next_applicant_stage_pages($core_student_current_stage, $campaign_id);
        dev_debug("multiple_stages_pages:" . $multiple_stages_pages);
        if (!empty($multiple_stages_pages)) {
            $pages_to_show = $pages_to_show . ',' . $multiple_stages_pages;
        }
        dev_debug("HERE 5");

        $pages_args = array("id_in" => $pages_to_show, 'has_form' => 1, "school_id" => $_SESSION['usergroup']);
        if (4 == $_SESSION['ulevel']) {
            $pages_args['form_ids_not_in'] = pull_field('lead_preferences', 'db155585', 'where usergroup=' . $_SESSION['usergroup']);
            $pages_args['publish'] = true;
        }

        if (!empty($_SESSION['custom_ulevel'])) {
            $pages_args['form_ids_not_in'] = pull_field('lead_preferences', 'db129971', 'where usergroup=' . $_SESSION['usergroup']);
        }
        $pages_args['order_by_field'] = $pages_to_show;
        // $_GET['debug_mode']="yes";
        // $_GET['dumphtml']="yes";
        // $_GET['print']="yes";
        $pages = $form_cms->get($pages_args);
        //$_GET['debug_mode']="no";

        /** ===================================
         * Check if student is using new forms
         * ====================================    */
        $new_form_ids_found = false;
        foreach ($pages as $cms_page) {
            if ($cms_page['new_form']) {
                $new_form_ids_found = true;
            }
        }

        if (!$entry_info['id']) {
            $data = array(
                'meta_title' => 'Not found',
                'view_file' => 'error_pages/404',
            );
            //$this->view($this->layout,$data);
            exit();
        }

        /** ===================================
         * Details
         * ====================================  */
        $data = array( 
            'meta_title' => 'Print',
            'view_file' => 'applicants/print',
            'applicant' => $entry_info,
            'form_title' => $post['form_title'],
            'general_info_to_include' => $general_info_to_include,
            'sections_id' => $sections_id,
            'applicant_info_to_include' => $applicant_info_to_include,

            'new_form_ids_found' => $new_form_ids_found,
            'applicant_id' => $applicant_id,
            'school_info' => $school_info,

            'print_on_server' => $print_on_server,
            'print_on_seperate_pages' => $post['print_on_seperate_pages'],
            'post' => $post,
            'pages' => $pages,
            'campaign_id' => get_application_campaign($applicant_id)
        );

        //echo "<pre>".print_r($data,1)."</pre>";exit();
        if (isset($_GET['print']) && "true" == $_GET['print']) {
            ob_start(); 
            include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/layout/print_header.php';
            include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/' . $data['view_file'] . ".php";
            include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/layout/clean_footer.php';
            $html = ob_get_clean();


            ini_set("pcre.backtrack_limit", "5000000");
            try {

                if (class_exists('\mPDF')) {
                    $mpdf = new \mPDF();
                } else {
                    $mpdf = new \Mpdf\Mpdf();
                }

                if (in_array('print_activity_timeline', $post)) {
                    $mpdf->AddPage('L');
                }
                //$mpdf->use_kwt = true;
                $mpdf->keep_table_proportions = TRUE;
                //$mpdf->ignore_table_percents = true;
                dev_debug("HERE 5");

                //$mpdf->shrink_tables_to_fit=0;
                if ('on' == pull_field('lead_preferences', 'db254765', "WHERE usergroup=" . $_SESSION['usergroup'])) {
                    $doc = new DOMDocument();
                    $doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
                    $html = $this->checkLargeTables($doc);

                }
                dev_debug("HERE 6");


                if (isset($_GET['dumphtml'])) {
                    echo $html;
                    exit();
                }
                $mpdf->WriteHTML($html);
                header('Content-Type: application/pdf');
                $mpdf->Output('application_' . $applicant_id . '.pdf', 'I');
                exit();

            } catch (\Exception $e) {
                echo $html;
                exit();
            }
        } else {
            //$data['print_on_server']=1;
            include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/layout/print_header.php';
            include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/' . $data['view_file'] . ".php";
            include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/layout/clean_footer.php';
        }
    }
}

if (isset($_GET['print']) && "true" == $_GET['print']) {
    $printer = new ApplicationPrinter();
    $printer->print_info();// code...
}



