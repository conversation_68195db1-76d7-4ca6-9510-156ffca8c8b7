<?php
// ini_set('display_errors', 1);
// ini_set('log_errors', 1);
// echo"<pre>";
// error_reporting(E_ALL);
// echo"</pre>";
require('../../engine/admin/inc/lib.inc.php');
require_once('../../vendor/autoload.php');

use PayPal\Api\Payment;
use PayPal\Api\PaymentExecution;
use PayPal\Rest\ApiContext;
use PayPal\Auth\OAuthTokenCredential;

// chk_login(); //check if user is logged in
// PayPal settings.
///set the environment
if (isset($_GET['live_or_sandbox'])) {
    if ($_GET['live_or_sandbox'] == 'sandbox') {
        $enableSandbox = true;
    } else {
        $enableSandbox = false;
    }
} else {
    $enableSandbox = false;///this means if no parameter is sent we will always have mode set to live
}

$paypalConfig = array(
    // 'client_id' => 'ARy37DtyY8KCVJVq0pbYa_OO4zld9uvLErHdXB74S4rzYe0cgA_kVFsFCbCXuGyhQ6lvLbSI70seL0Yi',
    // 'client_secret' => 'ECk2L0QzjmMwnxdaCJtxB0JFY_QRWI1JACFZiRjxIk1gu7kPQPYY63f9nvPqbvvaY81U5ADjU0oECQ8P' 
    'client_id' => $_GET['clientid'],
    'client_secret' => $_GET['secretid']
);
$apiContext = getApiContext(
    $paypalConfig['client_id'],
    $paypalConfig['client_secret'],
    $enableSandbox
);


function getApiContext($clientId, $clientSecret, $enableSandbox = false)
{
    $apiContext = new ApiContext(
        new OAuthTokenCredential($clientId, $clientSecret)
    );
    $apiContext->setConfig(
        array(
            'mode' => $enableSandbox ? 'sandbox' : 'live',
            //'log.FileName' => '../some-paypal-log-file.log',
            // 'log.LogLevel' => $enableSandbox ? 'DEBUG' : 'INFO'
        )
    );
    return $apiContext;
}

if (empty($_GET['payment_id']) || empty($_GET['payer_id'])) {
    throw new Exception('The response is missing the paymentId and PayerID');
}

$paymentId = $_GET['payment_id'];
$payment = Payment::get($paymentId, $apiContext);
$input_payment = json_decode($payment, true);
$eventJSON = json_encode($input_payment);
// print_r($eventJSON);
try {
    //$db = new mysqli($dbConfig['host'], $dbConfig['username'], $dbConfig['password'], $dbConfig['name']);
    $payment = Payment::get($paymentId, $apiContext);

    $data = array(
        'transaction_id' => $payment->getId(),
        'payment_amount' => $payment->transactions[0]->amount->total,
        'payment_status' => $payment->getState(),
        'invoice_id' => $payment->transactions[0]->invoice_number,
        'payment_desc' => $payment->transactions[0]->description,
    );
    if (addPayment($data, $eventJSON) !== false && $data['payment_status'] === 'approved') {
        // Payment successfully added, redirect to the payment complete page.
        //header('location:payment-successful.html');
        ###first send a confirmation email
        $confirmation_email = $_GET['confirmation_email'];
        //$confirmation_email = '<EMAIL>';
        if ($confirmation_email && is_numeric($confirmation_email)) {
            $confirmation_email = pull_field('form_users', 'db119', "WHERE id = {$confirmation_email} AND usergroup = {$_SESSION['usergroup']}");
        }
        $emailFrom = master_email;
        $fullname = $_SESSION['fullname'];
        $applicant_email = $_SESSION['user'];
        if ("" == $_SESSION['fullname'] || "" == $_SESSION['user']) {
            $s_username = $_GET['uname'];
            $ug_val = $_GET['ug'];
            $fullname = pull_field("core_students", "concat(db39,' ',db40)", "WHERE username_id='$s_username' AND usergroup='$ug_val' and (rec_archive IS NULL or rec_archive='')");
            $applicant_email = pull_field("core_students", "db764", "WHERE username_id='$s_username' AND usergroup='$ug_val' and (rec_archive IS NULL or rec_archive='')");
        }
        $message_plain_msg =
            "An applicant has just completed payment through Application Fees page.
        Applicant Name: $fullname
        Applicant Email: $applicant_email
       
        
        Kind Regards
        HEIapply Applications Platform
        ______________________________________________________
        This is an automated response. <br>
        PLEASE DO NOT REPLY, please login to portal to respond to the applicant.
        ";

        $message_html = text_to_html($message_plain_msg);
        $emailFrom = master_email;
        $disabled_notifications = pull_field("form_notifications_config", "db37245", "WHERE usergroup='" . $_SESSION['usergroup'] . "'");
        if (strpos('.' . $disabled_notifications, 'paypal_success_email') == false) {
            //echo "$emailTo,$message_plain_msg,$message_html,$emailFrom";
            log_email($confirmation_email, "Confirmation of Payment", $message_plain_msg, $message_html, $emailFrom, "New Message Alert");

        }

        if (!isset($_SESSION['user'])) {
            $last_url = "/application/login";
            $message = "log in to your account";
            session_destroy();
        } else {
            $last_url = $_SESSION['last_url'];
            $message = "go back into your account";
        }

        echo '<div align="center" style="margin:40px auto"><h2>Thank you for your purchase.</h2> If you are not automatically redirected in 5 seconds or less, please follow this link to <a href="' . $last_url . '">' . $message . ' </a></div>';

        //auto redirect the user
        echo '<meta http-equiv="refresh" content="2;url=' . $last_url . '">';

        track_use("Paypal Payment Successful");
        #echo "<pre>";
        #print_r($_SESSION);
        #echo "</pre>";
        exit(1);
    } else {
        // Payment failed
        echo '<div align="center" style="margin:40px auto"><h2>Sorry your payment failed</h2>. Please <a href="' . $_SESSION['last_url'] . '">go back and try another payment option </a></div>';
        track_use("Paypal Payment Failed");
    }
} catch (Exception $e) {
    // Failed to retrieve payment from PayPal
}

function addPayment($data, $eventJSON)
{
    global $database;
    $dbh = get_dbh();
    if (is_array($data)) {
        $ug_val = $_GET['ug'];
        $s_username = $_GET['uname'];
        $student_id = pull_field("core_students", "id", "WHERE username_id='$s_username' AND usergroup='$ug_val' and (rec_archive IS NULL or rec_archive='')");
        $uid = pull_field("core_students", "rec_id", "WHERE username_id='$s_username' AND usergroup='$ug_val' and (rec_archive IS NULL or rec_archive='')");
        if (!$student_id) {
            return false;
        }
        if (empty($_SESSION)) {
            session_start();
        }
        $_SESSION['uid'] = $uid;
        $_SESSION['ref'] = $student_id;
        $_SESSION['access'] = $ug_val;
        $_SESSION['pp_track_destroy'] = "destroy_this";
        $_SESSION['usergroup'] = $ug_val;

        $insert_data = implode(",", $data);
        $query = "INSERT INTO sis_pp_dump (username_id, rec_id, usergroup, rel_id,db34758) VALUES (:rnd, :rec_id, :usergroup, :rel_id,:json)";
        $sth = $dbh->prepare($query);
        $sth->execute(array(
            'rnd' => random(),
            'rec_id' => $uid,
            'usergroup' => $ug_val,
            'rel_id' => $student_id,
            'json' => $eventJSON
        ));

        ////now update into the student 
        $username_id = $_GET['product_username'];
        $payment_desc = "Payment - " . $username_id . "";
        $payment_date = date("Y-m-d");


        ////check if applicant details are in sis_student_fees
        $check_sql = "select COUNT(id) as count from sis_student_fees where rel_id = :student_id  AND (rec_archive IS NULL OR rec_archive = '')";
        $check_sth = $dbh->prepare($check_sql);
        $check_sth->execute(array(
            'student_id' => $student_id
        ));
        $check_results = $check_sth->fetchAll(PDO::FETCH_ASSOC);
        $check_res = $check_results[0]['count'];
        // echo "<pre>";
        // print_r($check_results);
        // echo "</pre>";
        // echo "check_results[0][count]= $check_res";  
        // exit();
        if ($check_res == 0) {///if no results in there then insert the record
            $sth1 = $dbh->prepare("INSERT INTO sis_student_fees (username_id, rec_id, usergroup, rel_id, db1492, db1493, db37345, db1495,
            db15459,db34450)VALUES (?,?,?,?,?,?,?,?,?,?)");
            $sth1->execute(array(random(), $uid, $ug_val, $student_id,
                'Paypal', 'Initiate Payment', '', '', $payment_date, 'Initiate Payment'));
        }
        ///end of check

        $update_sql = ("UPDATE sis_student_fees SET db1492='Paypal',db1493='Registration Fees', db37345='" . $data['transaction_id'] . "',db1495=" . $data['payment_amount'] . ",db15459='" . $payment_date . "',db34450='" . $payment_desc . "' WHERE rel_id = '" . $student_id . "'");
        // print_r($update_sql);
        // exit();
        $sth1 = $dbh->prepare($update_sql);
        $sth1->execute();

        $external_custom_offer_hook_url = front_header_file_location . "/admin/inc_custom_payment_hook.php";// link to external admin file
        // Check if the external file exists
        // log_email('<EMAIL>', "MY SQL", "Student:$_SESSION[student_id] File:$external_custom_offer_hook_url", text_to_html("Student:$_SESSION[student_id] File:$external_custom_offer_hook_url"), '<EMAIL>', 'error_log');
        if (file_exists($external_custom_offer_hook_url)) {
            include("$external_custom_offer_hook_url");
            //$response_message.="$accept - ";
        }

        return true;
    }
    return false;
}
