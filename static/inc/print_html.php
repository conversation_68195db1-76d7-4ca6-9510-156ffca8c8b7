<?php

//include_once $_SERVER['DOCUMENT_ROOT'] . "/engine/admin/inc/lib.inc.php";
include_once $_SERVER['DOCUMENT_ROOT'] . "/admin/config.php";
require_once $_SERVER['DOCUMENT_ROOT'] . '/vendor/autoload.php';

if (!class_exists('Db_helper')) load_helper('db');
//use Carbon\Carbon;

//echo "<pre>".print_r($_POST,1)."</pre>";

/**
 *
 */
class HtmlPrinter
{
    function print_info($args = [])
    {
        $fields = new Fields;
        $form_templates = new FormTemplates;
        $dir = media_store . 'reports/';
        if (!empty($_GET['html_ajax'])) {
            $request_body = file_get_contents('php://input');
            //$arry = json_decode($request_body,1);

            $path = $dir . random() . ".html";
            file_put_contents($path, $request_body);

            //echo "<pre>".print_r($arry,1)."</pre>";exit();
            //$_SESSION['html_ajax']=$arry['html_ajax'];
            echo json_encode(['success' => true, 'tempfile_path' => $path]);
            exit();
        }
        if (isset($_GET['print']) && "true" == $_GET['print'] || isset($args['print'])) {
            if (isset($args['full_html'])) {
                $html = $args['full_html'];
            } else {

                ob_start();
                include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/layout/print_header.php';
                echo $args['html'] ?? $_POST['html'];

                if (!empty($_POST['html_from_ajax'])) {
                    $form_args = json_decode(file_get_contents($_POST['html_from_ajax']), 1)['html_ajax'];
                    //echo file_get_contents($_POST['html_from_ajax']);

                    //$template = $form_templates->get($form_args);
                    $form_args['include_js'] = true;
                    $form_args['printing_pdf'] = true;
                    echo '<form action="formsubmit.php" method="post">';
                    $form_templates->form_html_only($form_args);
                    echo '</form>';
                    //echo "<pre>".print_r($form_info,1)	."</pre>";
                }
                include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/layout/clean_footer.php';
                $html = ob_get_clean();
                ob_end_clean();

                $html = str_replace('form-control', '', $html);
                $html = str_replace('type="checkbox"', 'type="checkbox" value="false"', $html);
            }

            if (isset($_GET['dumphtml'])) {
                echo $html;
                exit();
            }

            ini_set("pcre.backtrack_limit", "5000000");
            try {
                if (class_exists('\mPDF')) {
                    $mpdf = new \mPDF();
                } else {
                    $mpdf = new \Mpdf\Mpdf();
                }
                if (!empty($_POST['html_from_ajax'])) {
                    $mpdf->SetDisplayMode('fullpage');
                    $mpdf->useActiveForms = true;
                }

                $mpdf->WriteHTML($html);
                // $mpdf->use_kwt = true;
                //$mpdf->shrink_tables_to_fit = 1;

                $mpdf->shrink_tables_to_fit = 0;
                //$mpdf->keep_table_proportions = true;

                if (!empty($_POST['applicant_file_name'])) {
                    $dbg = new Db_helper();
                    $file_username_id = random();

                    $student_id = !empty($_SESSION['student_id']) ? $_SESSION['student_id'] : (!empty($_POST['application_id']) ? $_SESSION['application_id'] : false);

                    $target_path = media_store . '/' . "applicants/{$student_id}";
                    if (!is_dir($target_path)) {
                        // make directory
                        $old_umask = umask(0);
                        mkdir($target_path, 0755, true);
                        // print_r(mkdir($target_path, 0755,true));

                        umask($old_umask);
                        //echo "$target_path The directory $dirname was successfully created as it did not exist.";
                        //dev_debug("Created folder $target_path");
                    } else {

                    }

                    $wiriingto = "{$target_path}/{$file_username_id}.pdf";

                    $mpdf->Output($wiriingto, 'F');
                    if ($_GET['debug_file_save']) {
                        echo "here2</br>";
                        echo $wiriingto;
                        $files = array_diff(scandir($target_path), array('.', '..'));
                        echo "<pre>" . print_r($files, 1) . "</pre>";
                        echo 'class_exists' . class_exists('Db_helper');
                    }

                    $dbg->insert('form_file', ['rel_id' => $student_id, 'rec_id' => $_SESSION['uid'], 'username_id' => $file_username_id, 'usergroup' => $_SESSION['usergroup'], 'date' => date("Y-m-d H:i:s"), 'db201' => $_SESSION['uid'], 'db204' => "applicants/{$student_id}/{$file_username_id}.pdf", 'db199' => $_POST['applicant_file_name']]);

                    echo json_encode(['success' => true, "message" => "file uploaded"]);


                } else {
                    header('Content-Type: application/pdf');
                    $mpdf->Output('application_reference.pdf', 'I');
                    exit();
                }

            } catch (\Exception $e) {
                echo 'exeption' . "</br>";
                echo $html . "</br>";
                echo $e->getMessage() . "</br>";

                if (isset($_GET['debugging'])) {
                    echo $e->getMessage();
                }

                exit();
            }
        } else {
            //$data['print_on_server']=1;
            include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/layout/print_header.php';
            echo $_POST['html'];
            include $_SERVER['DOCUMENT_ROOT'] . '/admin/app/views/layout/clean_footer.php';
        }
    }

}

if (isset($_GET['print']) && "true" == $_GET['print']) {
    $printer = new HtmlPrinter();
    $printer->print_info();// code...
}

