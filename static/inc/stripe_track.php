<?php
// ini_set('display_errors', 1);
// ini_set('log_errors', 1);
// echo"<pre>";
// error_reporting(E_ALL);
// echo"</pre>";
require('../../engine/admin/inc/lib.inc.php');
//Insert into the DB
$username_id = $_GET['product_username'];
$payment_desc = "Payment - " . $username_id . "";
$payment_date = date("Y-m-d");
$student_id = floating_info("ref");
$payment_towards = $_GET['payment_towards'];
$payment_token = $_GET['payment_token'];
$payment_amount = $_GET['payment_amount'];

$dbh = get_dbh();
$update_sql = ("UPDATE sis_student_fees SET db1492='Stripe',db1493='" . $payment_towards . "', db37345='" . $payment_token . "',db1495=" . $payment_amount . ",db15459='" . $payment_date . "',db34450='" . $payment_desc . "' WHERE rel_id = '" . $student_id . "'");
// print_r($update_sql);
// exit();
$sth1 = $dbh->prepare($update_sql);
$sth1->execute();
echo "Thank you for your purchase";