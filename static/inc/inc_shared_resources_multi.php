<?php

/*--------------------------------
this file manages where we need multiple applications on one page
// FUNCTION TO GET_CORE_SHARED_RESOURCE
---------------------------------*/
$core_students_cohort_intake_id = pull_field("core_students", "db1682", "WHERE id = $core_students_id");
$core_students_course_of_study_id = pull_field("core_students", "db889", "WHERE id = $core_students_id");

//get shared resources
$shared_args = array(
    'student_id' => $core_students_id,
    'intake' => $core_students_cohort_intake_id,
    'programme' => $core_students_course_of_study_id,
    'stage' => $core_students_application_status
);
$shared_resources = get_shared_resources($shared_args);

//get shared public files
$shared_public_resources = get_public_files_multi($core_students_id);
$public = array();
foreach ($shared_public_resources as $key => $resource) {
    $public[$resource['db199']][] = $resource;
}

?>
<div class="<?php echo $class_resources; ?>">
    <style type="text/css">
        ul::-webkit-scrollbar {
            width: 0.3em;
        }

        ul::-webkit-scrollbar-track {
            -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
            border-radius: 4px;
        }

        ul::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.1);
            */ border-radius: 4px;
        }
    </style>

    <?php
    //check if any
    //if(!empty($shared_public_resources)){
    ?>
    <h4><?= translate(terminology("Documents Shared With You", curPageURL(), '', true), $_SESSION['lang']) ?></h4>
    <div class="shared_resources_text">
        <p><?= translate(terminology('The list below shows documents that have been shared with you as part of the application process.', curPageURL(), 'documents shared with you subtext', true), $_SESSION['lang']) ?></p>
    </div>
    <ul class="list-inline" style="<?php echo $style; ?>">
        <?php foreach ($public as $key => $pub) {
            $ids = array_column($pub, 'id');
            ?>
            <li>
                <b><?php echo $key ?> </b>
                <p><?php echo $pub[0]['db1133']; ?></p>

                <a data-file="<?php echo implode(',', $ids) ?>"
                   class="btn btn-default btn-sm resource_file_dw_<?= $core_students_id ?>"
                   data-title="<?php echo $pub_resource['db199']; ?>">
                    <i class="fa fa-download"></i> <?php echo $pub_resource['db199']; ?>
                    <?php echo count($pub) . " file(s)" ?>
                </a>
            </li>
            <hr>
        <?php } ?>

        <?php foreach ($shared_resources as $resource) { ?>
            <li>
                <b><?php echo translate($resource['db34631'], $_SESSION['lang']) ?> </b>
                <p><?php echo $resource['db34632']; ?></p>
                <?php foreach ($resource['files'] as $file) {
                    $actual_file = encode($file['db204']); ?>
                    <a data-file="<?php echo $file['id']; ?>"
                       class="btn btn-default btn-sm resource_file_dw_<?= $core_students_id ?>"
                       data-title="<?php echo $file['db199']; ?>"><i
                                class="fa fa-download"></i> <?php echo $file['db199']; ?></a>
                <?php } ?>
            </li>
            <hr>
        <?php } ?>

    </ul>


    <script>
        $(function () {
            const wait = (ms) => new Promise(resolve => setTimeout(resolve, ms));
            $('.resource_file_dw_<?=$core_students_id?>').on('click', function (e) {
                e.preventDefault();
                let data = $(this).data();
                let files = [];
                if (typeof (data.file) == 'string') {
                    files = data.file.split(',');
                } else {
                    files.push(data.file);
                }
                files.forEach(async (file) => {
                    window.open("<?php echo website_url . '/admin/files/download/'; ?>" + file, '_blank');
                    await wait(1500);
                });
            })
        });
    </script>
</div>
