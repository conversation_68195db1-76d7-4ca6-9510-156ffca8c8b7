<?php
// select a header and a footer based on the subdomain
session_start();

ini_set('display_errors', 1);
ini_set('log_errors', 1);
//ini_set('error_log', dirname(__FILE__) . '/error_log.txt');
error_reporting(E_ALL);

//ini_set('memory_limit', '512M');  //increase size as you need
function filled_percentage_on_new_forms2($students_id)
{

// CHECK FOR SESSION
    include_once("../../engine/admin/inc/lib.inc.php");

    $dbh = get_dbh();
    //Get all the fields required
    $required_fields_sql = "
        SELECT * 
        FROM system_form_fields
            LEFT JOIN system_forms ON system_forms.id = system_form_fields.system_forms_id
            LEFT JOIN system_table ON system_form_fields.system_table_id = system_table.form_id
            LEFT JOIN system_pages ON system_pages.page_id = system_table.pg_id
            LEFT JOIN system_cat ON system_cat.sys_cat_id = system_pages.project
            INNER JOIN form_cms ON form_cms.db26231 = system_forms.id
        WHERE 
            system_table.required IN ('required','yes')
            AND system_form_fields.usergroup='" . $_SESSION['usergroup'] . "'
        ORDER BY system_table.pg_id
        ";

    $system_table_names = array();
    $sth = $dbh->prepare($required_fields_sql);
    $sth->execute();
    //	$results = $sth->fetch(PDO::FETCH_ASSOC);

    while ($field = $sth->fetch(PDO::FETCH_ASSOC)) {

        $i++;

        $table_name = $field['sys_cat_abv'] . "_" . $field['page_name'];

        if (!array_key_exists($table_name, $system_table_names)) {
            $i = 0;
            $system_table_names[$table_name] = array(
                'title' => $table_name,
                'id' => $field['pg_id'],
                'page_name' => $field['db647'],
                'fields' => array()
            );
        }
        $system_table_names[$table_name]['fields'][$i] = $field;
    }


    $required_filled = 0;
    $required_fields = 0;

    foreach ($system_table_names as $table) {
        //get the table data
        $student_data_sql = "SELECT * FROM " . $table['title'] . " WHERE rel_id='" . $student_id . "'  limit 1";

        $sth = $dbh->prepare($student_data_sql);
        $sth->execute();
        $student_data = $sth->fetchAll(PDO::FETCH_ASSOC);

        //check the fields
        foreach ($table['fields'] as $field) {

            //all required
            if ($student_data[0][$field['db_name']]) {
                $required_fields += 1;
            }

            //all missing
            if (!$student_data[0][$field['db_name']]) {
                $required_missing += 1;
            }

        }

    }


    $required_filled = ($required_fields - $required_missing);
    echo "$required_fields,$required_filled";

    return array($required_fields, $required_filled);

}

$student_id = $_GET['stu'];
list($one, $two) = filled_percentage_on_new_forms2($student_id);

echo $one . ' - ' . $two;

print_r(filled_percentage_on_new_forms2($student_id));