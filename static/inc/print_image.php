<?php
ob_start();

require_once 'signature-to-image.php';

// require('../../engine/admin/inc/lib.inc.php');
// include_once"../../engine/admin/inc/site_lib.inc.php";// call functions

include_once("../../admin/config.php");


$students = new Students;
$form_templates = new FormTemplates;
$form_cms = new FrontendPages;
$db_helper = new Db_helper();
$tech = new techModel();

$table_name = $tech->get_table_name($_GET['table_id']);

$json = pull_field($table_name, $_GET['db_field_name'], "where rel_id =" . $_GET['application_id'] . " AND usergroup =" . $_SESSION['usergroup']); // From Signature Pad
$img = sigJsonToImage($json);
//$rawImageBytes = ob_get_clean();


if (!empty($_GET['img_tag'])) {
    ob_get_clean();
    ob_start();
    imagepng($img);
    // imagedestroy($img);
    $rawImageBytes = ob_get_clean();
    echo "<img src='data:image/png;base64," . base64_encode($rawImageBytes) . "' />";

} else {
    ob_get_clean();
    header('Content-Type: image/png');
    imagepng($img);
    imagedestroy($img);

}




