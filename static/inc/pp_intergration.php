<?php
require('../../engine/admin/inc/lib.inc.php');
chk_login(); //check if user is logged in
// ini_set('display_errors', 1);
// ini_set('log_errors', 1);
// echo"<pre>";
// error_reporting(E_ALL);
// echo"</pre>";
$dbh = get_dbh();
$sql = "select * from sis_products where usergroup = :usergroup";
$sth = $dbh->prepare($sql);
$sth->execute(array(
    'usergroup' => $_SESSION['usergroup']
));
$button_results = $sth->fetchAll(PDO::FETCH_ASSOC);
$result = $button_results[0];
$currency_id = $result["db34754"];
$description = $result["db34836"];
$amount = $result["db34756"];
$currency_abv = pull_field("system_currency", "abv", "WHERE id='$currency_id'");


?>
<div id="paypal-button"></div>
<input type="hidden" id="button_currency" value="<?php echo $currency_abv ?>">
<input type="hidden" id="button_desc" value="<?php echo $description ?>">
<input type="hidden" id="button_amount" value="<?php echo $amount ?>">
<script src="https://www.paypalobjects.com/api/checkout.js"></script>
<script>
    // var amount = document.getElementById("button_amount").value;
    // var currency = document.getElementById("button_currency").value;
    // var desc =  document.getElementById("#button_desc").value;

    paypal.Button.render({
        // Configure environment
        env: 'sandbox',
        client: {
            sandbox: 'ARy37DtyY8KCVJVq0pbYa_OO4zld9uvLErHdXB74S4rzYe0cgA_kVFsFCbCXuGyhQ6lvLbSI70seL0Yi',
            production: 'demo_production_client_id'
        },
        // Customize button (optional)
        locale: 'en_US',
        style: {
            size: 'small',
            color: 'gold',
            shape: 'pill',
        },
        // Set up a payment
        // Set up a payment
        payment: function (data, actions) {
            return actions.payment.create({
                payment: {
                    transactions: [
                        {
                            amount: {
                                total: '<?php echo $amount; ?>',
                                currency: '<?php echo $currency_abv; ?>'
                            },
                            description: '<?php echo $description; ?>'
                        }
                    ]
                }
            });
        },
        // Execute the payment
        onAuthorize: function (data, actions) {
            return actions.payment.execute().then(function () {
                // Show a confirmation message to the buyer
                //console.log(data);
                payment_id = data.paymentID;
                payer_id = data.payerID;
                order_id = data.orderID;
                payment_token = data.paymentToken;
                //console.log('redirecting');
                window.location = "pp_track.php?payment_id=" + payment_id + "&payer_id=" + payer_id + "&order_id=" + order_id + "&payment_token=" + payment_token;
                //window.alert('Thank you for your purchase!');

            });
        }
    }, '#paypal-button');

</script>