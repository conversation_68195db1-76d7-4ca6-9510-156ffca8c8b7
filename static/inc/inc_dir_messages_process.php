<?php
require('../../engine/admin/inc/lib.inc.php');
chk_login(); //check if user is logged in

/////////////////////////////////////////////////////////////////////////////////////
list($schools_institution_name, $schools_subdomain, $schools_ucas_code, $schools_website_address, $schools_main_contact, $schools_top_person_in_charge, $schools_billing_contact, $schools_gender, $schools_institution_type, $schools_recruitment_cycle_start_date, $schools_recruitment_cycle_end_date, $schools_semester__start_date, $schools_institution_logo, $schools_institution_sets, $schools_result_entry_method, $schools_page_refresh_after_pop_box, $schools_activate_modules, $schools_sub_modules, $schools_tag, $schools_archive_record, $form_schools_id, $form_schools_rec_id, $form_schools_usergroup, $form_schools_rel_id, $form_schools_authorised_email_domain, $form_schools_ukprn, $form_schools_main_general_email_address, $form_schools_main_international_email_address, $form_schools_email_footer_text, $form_schools_email_footer_html, $form_schools_email_sent_to_applicant_on_successful_signup, $force_switchoff) = get_schools($_SESSION['usergroup']);

if ($_POST['new_message']) {

    // category of message
    $category = isset($_POST['category']) ?: '6';

    $dbh = get_dbh();
    $message = addslashes(sanitise($_POST['new_message']));

    $random_id = random();
    if ($_SESSION["userlevel"] == 7 || $_SESSION["userlevel"] == 5 || $_SESSION['multi_application'] == 'yes') {
        $rel_id = $_POST['child_id'];
        if (!$rel_id || $rel_id == '' || $rel_id == 'messages') {
            $rel_id = floating_info('ref');
        }
        $sql = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db139) VALUES ('" . $random_id . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$rel_id', '$category', '$message', '" . session_info('uid') . "', '" . session_info('uid') . "', 'no')";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
    } else {
        $sql = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db139) VALUES ('" . $random_id . "', '" . session_info("uid") . "', '" . session_info("access") . "', '" . floating_info("ref") . "', '$category', '$message', '" . session_info('uid') . "', '" . session_info('uid') . "', 'no')";
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
    }
    $last_insert_id = $dbh->lastInsertId();


    //send admin emails telling them abaout new message
    //send email to admin
    $message_plain_msg =
        "You have a new message alert from an applicant
	Applicant Name:$_SESSION[fullname]
	Applicant Email:$_SESSION[user]
	Message:$message.
	
	Kind Regards
	HEIapply Applications Platform
	______________________________________________________
	This is an automated response. <br>
	PLEASE DO NOT REPLY, please login to portal to respond to the applicant.
	";

    $message_html = text_to_html($message_plain_msg);
    $emailFrom = pull_field("form_schools", "db1117", "WHERE id='$_SESSION[usergroup]'");
    if (!isset($emailFrom)) {
        $emailFrom = master_email;
    }
    $emailTo = pull_field("form_schools", "db1118", "WHERE id='$_SESSION[usergroup]'");//Main
    $disabled_notifications = pull_field("form_notifications_config", "db37245", "WHERE usergroup='" . session_info("access") . "'");

    //check if they want to get alerts when someone submits
    if ($schools_page_refresh_after_pop_box == "yes" && strpos('.' . $disabled_notifications, 'communication_inbox_message') == false) {
        //echo "$emailTo,$message_plain_msg,$message_html,$emailFrom";

        //add intake to email subject sent to admin for wmg
        if ($_SESSION['usergroup'] == '72') {
            $student_id = ($_SESSION['student_id'] > 0 ? $_SESSION['student_id'] : $_SESSION['application_id']);
            list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake) = get_core_students($student_id);
            $subject = "New Message From Applicant - " . $core_students_cohort_intake;
            log_email($emailTo, terminology($subject, $_SESSION['url'], 'Applicant portal message from applicant.', true), $message_plain_msg, $message_html, $emailFrom, "New Message Alert");

        } else {

            $subject = terminology("New Message From Applicant", $_SESSION['url'], 'Applicant portal message from applicant.', true);
            log_email($emailTo, $subject, $message_plain_msg, $message_html, $emailFrom, "New Message Alert");
        }

    }

    //log event to activity tracker
    log_notification($last_insert_id, '', '', 'core_notes');

    //add tracker
    track_use("Sent a private message " . floating_info("ref"), "$random_id");

    header("Location: $_SESSION[last_url]");
}
