<?php


/**
 *
 */
class Large_tables_printer
{

    function checkLargeTables(&$doc)
    {
        //new code to split table large cells
        $tables = $doc->getElementsByTagName('table');
        foreach ($doc->getElementsByTagName('table') as $table) {
            //for ($x = 0; $x < count($tables); $x++){
            //$table=$tables[$x];
            // iterate over each row in the table
            $trs = $table->getElementsByTagName('tr');
            $cloneArr = [];
            foreach ($trs as $tr) {
                $cloned = 0;
                foreach ($tr->getElementsByTagName('td') as $td) { // get the columns in this row
                    if (strlen($td->textContent) > 2000) {

                        $stringhtml = $doc->saveHTML($table);
                        // $f = $doc->createDocumentFragment(); 
                        // $f->appendHTML($stringhtml); 
                        $stringhtml = $this->replace_tablewithdivs($stringhtml);
                        $doc2 = DOMDocument::loadHTML($stringhtml);
                        $f = $doc2->getElementsByTagName('div')->item(0);

                        $table->parentNode->replaceChild($doc->importNode($f, true), $table);

                        return $this->checkLargeTables($doc);

                        //echo (string) $table."<br>";

                    }
                }
            }
        }
        return @$doc->saveHTML();

    }


    function replace_tablewithdivs($html)
    {
        $tags = ['table', 'tbody', 'tr', 'td'];
        foreach ($tags as $key => $value) {
            $html = str_replace("<{$value}", "<div", $html);
            $html = str_replace("{$value}>", "div>", $html);
        }
        //echo $html;exit();
        return $html;
    }


}

if (!function_exists('get_cms')) {
    // get cms page and content
    function get_cms($by = 'id', $value = 1, $core_students_level_of_entry = '')
    {

        // if admin show everything not just group
        $usergroups = usergroups_management();

        //if language then show it
        if (isset($_SESSION['lang']) && $_SESSION['lang'] !== '' && $_SESSION['lang'] !== 'en') {
            $lang_check = "AND db47583=(SELECT id FROM form_languages WHERE db21281 ='$_SESSION[lang]' LIMIT 1) ";
        }

        //error_log('usergroup_managemant: '.$usergroups);
        //error_log('core_students_level_of_entry: '.$core_students_level_of_entry);

        $dbh = get_dbh();

        $user_exepmt_ul = array(21, 11); // theses are schools that use the usergroup condition to decide what to show to different users. These UGs will undergo changes soon

        if ($core_students_level_of_entry && in_array($_SESSION[usergroup], $user_exepmt_ul)) {
            if ($by == 'id') {
                $sql = "SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' $lang_check AND $usergroups LIMIT 1";
            } else {
                $sql = "SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' $lang_check AND $usergroups LIMIT 1";
            }
        } else {
            if ($by == 'id') {
                $sql = "SELECT *, db863 as 'next_page' FROM form_cms WHERE id='$value' $lang_check AND $usergroups ORDER BY id DESC LIMIT 1";
            } else {
                $sql = "SELECT *, db863 as 'next_page' FROM form_cms WHERE db647='$value' $lang_check  AND $usergroups ORDER BY id DESC LIMIT 1";
            }
        }
        //echo $sql;
        dev_debug("form_cms - check - $sql");
        $stmt = $dbh->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $num = count($results);

        if ($num == 0) {

            if ($core_students_level_of_entry) {
                if ($by == 'id') {
                    $sql2 = "SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' $lang_check AND cms.usergroup='1' LIMIT 1";
                } else {
                    $sql2 = "SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' $lang_check AND cms.usergroup='1' LIMIT 1";
                }
            } else {

                // error_log('core_students_level_of_entry1: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL)  AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' AND $usergroups LIMIT 1");
                // error_log('core_students_level_of_entry2: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' AND.$usergroups LIMIT 1");
                //error_log('core_students_level_of_entry3: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' AND cms.usergroup='1' LIMIT 1");
                //error_log('core_students_level_of_entry4: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' AND.cms usergroup='1' LIMIT 1");
                if ($by == 'id') {
                    $sql2 = "SELECT *, db863 as 'next_page' FROM form_cms WHERE id='$value' $lang_check  AND usergroup='1' LIMIT 1";
                } else {
                    $sql2 = "SELECT *, db863 as 'next_page' FROM form_cms WHERE db647='$value' $lang_check AND usergroup='1' LIMIT 1";
                }
            }
            //echo $sql;
            $stmt = $dbh->prepare($sql2);
            $stmt->execute();
            $results2 = $stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($results2 as $row) {

                $id = $row['id'];
                $cms_category = $row['db656'];
                $cms_page_name = $row['db647'];
                $cms_heading = $row['db648'];
                $cms_brief = $row['db649'];
                $cms_article = $row['db650'];
                $cms_data = $row['db651'];
                $cms_publish = $row['db652'];
                $cms_page_title = $row['db653'];
                $cms_keywords = $row['db654'];
                $cms_page_description = $row['db738'];
                $cms_included_form = $row['db655'];
                $cms_privacy = $row['db739'];
                $cms_refreshpage_on_add = $row['db823'];
                $nextpage_url = $row['next_page'];
                $cms_introductory_instructions = $row['db1198'];
                $cms_pull_file = $row['db1440'];
                $cms_save_action = $row['db9853'];
                $cms_system_form_id = $row['db26231'];
            }
        } else {
            //error_log('core_students_level_of_entry1: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' AND $usergroups LIMIT 1");
            // error_log('core_students_level_of_entry2: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND $usergroups AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' AND.$usergroups LIMIT 1");
            // error_log('core_students_level_of_entry3: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND (FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.id != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.id='$value' AND cms.usergroup='1' LIMIT 1");
            //error_log('core_students_level_of_entry4: '."SELECT *, (SELECT id   FROM form_cms cms1 WHERE (cms1.rec_archive IS NULL OR cms1.rec_archive = '') AND cms1.usergroup='1' AND )FIND_IN_SET($core_students_level_of_entry, cms1.db738) = 0 OR FIND_IN_SET($core_students_level_of_entry, cms1.db738) IS NULL) AND cms1.db748 >= cms.db748  and cms1.db647 != '$value' ORDER BY cms1.db748 LIMIT 1) as 'next_page' FROM form_cms cms WHERE cms.db647='$value' AND.cms usergroup='1' LIMIT 1");


            foreach ($results as $row) {

                $id = $row['id'];
                $cms_category = $row['db656'];
                $cms_page_name = $row['db647'];
                $cms_heading = $row['db648'];
                $cms_brief = $row['db649'];
                $cms_article = $row['db650'];
                $cms_data = $row['db651'];
                $cms_publish = $row['db652'];
                $cms_page_title = $row['db653'];
                $cms_keywords = $row['db654'];
                $cms_page_description = $row['db738'];
                $cms_included_form = $row['db655'];
                $cms_privacy = $row['db739'];
                $cms_refreshpage_on_add = $row['db823'];
                $nextpage_url = $row['next_page'];
                //        $cms_introductory_instructions = $row['db1198'];
                $cms_pull_file = $row['db1440'];
                $cms_save_action = $row['db9853'];
                $cms_system_form_id = $row['db26231'];
                $cms_system_quiz_id = $row['db26741'];
            }
        }

        // check for sessions in field
        $cms_article = session_floating($cms_article);
        $cms_brief = session_floating($cms_brief);
        $cms_heading = session_floating($cms_heading);
        $cms_page_description = session_floating($cms_page_description);
        $cms_introductory_instructions = session_floating($cms_introductory_instructions);

        //check for get queries in field
        $cms_brief = get_floating($cms_brief);
        $cms_article = get_floating($cms_article);
        $cms_heading = get_floating($cms_heading);
        $cms_page_description = get_floating($cms_page_description);

        // look for subdomain and correct it to current session
        $cms_article = str_replace("subdomain", "$_SESSION[subdomain]", $cms_article);

        //LOOK FOR AND CONVERT SHORT CODES 2018-11-28 TAWANDA ONYIMO
        include_once(base_path . "/admin/config.php");
        $online = new OnlineLearning;
        $cms_article = $online->proccess_short_codes($cms_article);
        //$cms_article .= website_url.'app/libs/shortcodes/index.php';
        // add some eye candy tot he instructions
        $non_active_admin = array(2, 3, 9);
        if (in_array($_SESSION['ulevel'], $non_active_admin) && $cms_introductory_instructions !== '') {
            $cms_introductory_instructions = '<div class="info" id="instructions" style="width:60%">' . $cms_introductory_instructions . '</div>';
        } else {
            $cms_introductory_instructions = '';
        }

        return array($id, $cms_category, $cms_page_name, $cms_heading, $cms_brief, $cms_article, $cms_data, $cms_publish, $cms_page_title, $cms_keywords, $cms_page_description, $cms_included_form, $cms_privacy, $cms_refreshpage_on_add, $nextpage_url, $cms_introductory_instructions, $cms_pull_file, $cms_save_action, $cms_system_form_id, $cms_system_quiz_id);
    }
}


////////////////////////////////////////////////////////////////////////////////////////////////
// THIS FUNCTION PULLS ALL THE PAGES THAT RELATE TO THE PAGE RULES
////////////////////////////////////////////////////////////////////////////////////////////////
function get_page_list_based_on_rules_print($student_id_val = '', $supplementary_required = false, $return_ruleid = false, $admin_only_forms = false)
{

    //if non use candidate id
    if ($student_id_val == '') {
        $student_id_val = $_SESSION['student_id'];
    }

    //START check if student id exists
    if ($student_id_val) {
        //get rules for usergroup
        $dbh = get_dbh();
        $page_rule_query = "SELECT db_field_name AS 'name', pr.id AS 'rule_id', db19262 AS 'condition' FROM dir_page_rules pr INNER JOIN system_table ON form_id=db19266
        WHERE (pr.rec_archive IS NULL or pr.rec_archive='')
        AND pr.usergroup = $_SESSION[usergroup] ORDER BY db19264 ASC";
        $sql = $dbh->prepare($page_rule_query);
        dev_debug($page_rule_query);
        $sql->execute();
        $page_rules = $sql->fetchAll(PDO::FETCH_OBJ);
        //loop through rules for usergroup
        $pages_id = "";

        foreach ($page_rules as $page_rule) {
            //check if page has already been found
            //if($_SESSION['student_id'] == 20218){
            // echo " condition = $page_rule->condition";
            //}
            $column = "db19263";
            if ($supplementary_required) {
                $column = "concat(db19263,',',IFNULL(db33769,''))";
            }
            if ($admin_only_forms) {
                $column = "concat(" . $column . ",',',IFNULL(db166655,''))";
            }
            if ($pages_id == "") {
                if ($page_rule->condition == "default") {
                    $query_3 = "SELECT $column
                    FROM dir_page_rules pr
                    WHERE  pr.id = $page_rule->rule_id
                            AND (pr.rec_archive IS NULL or pr.rec_archive='')
                    ORDER BY db19264 ASC";
                    dev_debug($query_3);
                    $sql = $dbh->prepare($query_3);
                    $sql->execute();
                    $result = $sql->fetchColumn();
                    if ($result) {
                        $ids = array_filter(explode(",", $result));
                        $pages_id = implode(',', $ids);
                        $ret_rule_id = $page_rule->rule_id;
                    }
                } else {
                    //pull the actual field value
                    $page_rule_value = pull_field("core_students", "$page_rule->name", "WHERE id = $student_id_val");

                    //test the rule
                    $sql_val = "SELECT $column
                    FROM dir_page_rules pr
                            INNER JOIN core_students cs ON db19262 = '$page_rule_value'
                    WHERE $_SESSION[usergroup]
                            AND cs.id=$student_id_val
                            AND pr.id = $page_rule->rule_id
                            AND (pr.rec_archive IS NULL or pr.rec_archive='')
                    ORDER BY db19264 ASC";
                    dev_debug($sql_val);
                    $sql = $dbh->prepare("$sql_val");
                    $sql->execute();
                    $result = $sql->fetchColumn();
                    if ($result) {
                        $ids = array_filter(explode(",", $result));
                        $pages_id = implode(',', $ids);
                        $ret_rule_id = $page_rule->rule_id;
                    }


                    //$pages_id = pull_field("dir_page_rules,core_students","db19263","WHERE db19262=$page_rules_set AND dir_page_rules.usergroup=$_SESSION[usergroup] AND core_students.id=$_SESSION[student_id] AND (dir_page_rules.rec_archive IS NULL or dir_page_rules.rec_archive='')");


                }
            } else {
                //do nothing
            }
        }
        //if($_SESSION['student_id'] == 20790){
        //echo "web nav pages id = $pages_id // $sql_val";
        //}

        //if($_SESSION['uid']==38866){
        //echo "web nav pages id = $pages_id //sql_val = $sql_val";
        //}
        if ($return_ruleid) {
            return $ret_rule_id;
        } else {
            return $pages_id;
        }

    } //END check if student id exists


} //end function
