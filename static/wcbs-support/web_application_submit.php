<?php

/////////////
//if($_SESSION['student_id'] == 9621){

if ($appli_submited_check < 1) {

    // Check if international and show payment page
    $payment_made = pull_field("sis_student_fees", "sum(db1495)", "WHERE rel_id = '$_SESSION[student_id]'  AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL or rec_archive='') ");

    //check if exemnpt from payment
    $payment_exempt = pull_field("core_students", "db25618", "WHERE id = '$_SESSION[student_id]'  AND usergroup='$_SESSION[usergroup]' AND (rec_archive IS NULL or rec_archive='') ");

    //check if payment is sufficent and check if this user is suppose to pay
    if ($payment_exempt !== 'yes') {
        if ($payment_made < 50) {
            $error_check_done = 2;
        }
    }

}

//echo $_SESSION['student_id'];
//echo  "sasasa=".$core_students_cohort_intake;
//}
/////////////////

//error check
if ($error_check_done > 0) {
    echo $error_msg;

////////////////////////////////////////start pay
    //if($_SESSION['student_id'] == 9621){


    if ($error_check_done == 2) {
        echo '
				<hr/><h3>Application Fees</h3>
				<p>In order to submit your application, you need to pay your application fees of &euro;50 GBP.</p>
				<div>
				<p class="ng-binding">Using the payment button below, you can make a one-off payment using your debit or credit card. You just need to enter your details and your card details.</p>
				<p class="ng-binding">To make life easy, we accept Maestro (Switch), Solo, Visa Delta, Visa Electron, Visa and MasterCard.</p>
				
				<h2>Pay Now</h2>
				</div>';

        #require_once(base_path.'engine/modules/stripe_checkout.php');
        require_once(base_path . 'engine/tools/stripe/application_form_charge/form.php');

        $error_check_done = 1;
    }

    //}
////////////////////////////////////////end pay


} else {
    ?>


    <?php

    if ($_POST['process'] == 1) {

        if ($_POST['agree'] !== "1") {

            $error_msg = '<div class="alert alert-danger">
		<strong>Sorry!</strong>
		<p>Please confirm (by ticking the box) that you have read and understood the important information above!</p>
		</div>';

        } else {

            $uname = $unique_id;//username_id
            $uname_id = $_SESSION['student_id'];//id
            $student_link_url = engine_url . "/direct/proc?pg=4&vw=$uname&ref=$uname_id";

            $dbh = get_dbh();

            // Auto-mark submission notifications as read
            if ($schools_recruitment_cycle_end_date == 'yes') {
                $add_alert_msg1 = 'auto';
            } else {
                $add_alert_msg1 = 'no';
            }

            $sql = "INSERT INTO core_notes 
		(username_id, rec_id, usergroup, rel_id, db73, db76, db77, db80, db79, db91, db139) 
		VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$uname_id', '8', 'Dear Admissions, please find my application and documents', '" . session_info("uid") . "', 'yes', '$student_link_url', '3', '$add_alert_msg1')";// if users does not want alerts lock using this
            $stmt = $dbh->prepare($sql);
            $stmt->execute();
            log_notification($dbh->lastInsertId(), '', '', 'core_notes');

            // update student profile
            // add tracker in system and update status
            $update_stage = 1;// this tells the function below to run
            update_application_stage(12, $uname_id);

            //check if they want to get alerts when someone submits
            if ($schools_page_refresh_after_pop_box == 'yes') {

//send email to admin
                $message_plain =
                    "A new application alert
Applicant Email:$_SESSION[user]
URL to application: $student_link_url

Kind Regards
HEIapply Applications Platform
______________________________________________________
This is an automated response. PLEASE DO NOT REPLY.
";

                $message_html = text_to_html($message_plain);
                $emailFrom = master_email;
                $emailTo = pull_field("form_schools", "db1118", "WHERE id='$_SESSION[usergroup]'");//Main

                track_use("$emailTo, $message_plain, $message_html, $emailFrom,");

                log_email($emailTo, "New Online Application", $message_plain, $message_html, $emailFrom, "New Application Alert");
            }

            //check if they have personalised templates based on some other calculation
            if (!$select_email_template) {
                $select_email_template = $form_schools_email_sent_to_applicant_on_successful_signup;
            }
            if ($_SESSION['usergroup'] == 3) {
                include('automated_email_hooks.php');
            }

//check if the school has set the welcome email template. if so then send it	
//send email to user
            if (is_numeric($select_email_template)) {

                //get template
                list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($select_email_template);
                $host_url = $front_website_url . '/application';
                $message_html = str_replace('{{student_portal}}', '<a href="$host_url">student portal</a>', $message_html);
                $message_plain = str_replace('{{student_portal}}', '<a href="$host_url">student portal</a>', $message_plain);


                //search replace
                $message_plain = email_template_replace_values("{{name}}", $_SESSION['name'], $coms_template_plain_text_version);
                $message_html = email_template_replace_values("{{name}}", $_SESSION['name'], $coms_template_html_version);
                $message_plain = email_template_replace_values("{{student_first_name}}", $core_students_first_name, $message_plain);
                $message_html = email_template_replace_values("{{student_first_name}}", $core_students_first_name, $message_html);
                $message_plain = email_template_replace_values("{{first_name}}", $core_students_first_name, $message_plain);
                $message_html = email_template_replace_values("{{first_name}}", $core_students_first_name, $message_html);
                $message_plain = email_template_replace_values("{{student_surname}}", $core_students_surname, $message_plain);
                $message_html = email_template_replace_values("{{student_surname}}", $core_students_surname, $message_html);
                $message_plain = email_template_replace_values("{{student_course}}", $core_students_course_of_study, $message_plain);
                $message_html = email_template_replace_values("{{student_course}}", $core_students_course_of_study, $message_html);
                $message_plain = email_template_replace_values("{{student_portal}}", "<a href='$host_url'>student portal</a>", $message_plain);
                $message_html = email_template_replace_values("{{student_portal}}", "<a href='$host_url'>student portal</a>", $message_html);
                $message_plain = email_template_replace_values("{{course}}", $core_students_course_of_study, $message_plain);
                $message_html = email_template_replace_values("{{course}}", $core_students_course_of_study, $message_html);
                $message_plain = email_template_replace_values("{{form_links}}", $concatenated_msg, $message_plain);
                $message_html = email_template_replace_values("{{form_links}}", $concatenated_msg, $message_html);
                $message_plain = email_template_replace_values("{{cohort}}", $_SESSION['school_cycle'], $message_plain);
                $message_html = email_template_replace_values("{{cohort}}", $_SESSION['school_cycle'], $message_html);

//	$message_plain = html2text($message_html);


                //$message_plain = email_template_replace_values("{{student_portal}}",$_SERVER['HTTP_HOST'].'/application/login',$message_plain);
                //$message_html = email_template_replace_values("{{student_portal}}",$_SERVER['HTTP_HOST'].'/application/login',$message_html);

                // $message_html = text_to_html($message_plain);
                $emailTo = $_SESSION['user'];// student email
                $emailFrom = $coms_template_email_address_to_send_from;//pull_field("form_schools","db1118","WHERE id='$_SESSION[usergroup]'");//school email

                log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, "New Application Welcome Email", $_SESSION['student_id'], '0');

            }//end send email check


            if (function_exists('custom_thank_message')) {
                custom_thank_message();
            } else {
                echo "<strong><br/><br/>Thank you.<br/>
Your submission has been sent successfully.<br/><br/>
<p><a href=\"$website_url_applicant/application/Checklist\" class=\"btn btn-info\">Go back to my account >> </a></p>
		</strong>";
            }


            // stop the form below from showing after they submit
            $stop_form_below_from_showing = 1;

        } // agree check end

        //if this is a custom request go an get the custom script
        // located in form_settings.php
        if (function_exists('after_submit')) {
            after_submit();
        }
    }

    // deadline for resi has passed, kill submit button
    $checkDate = explode("|", pull_field("core_courses", "concat(db709,'|',db710)", "WHERE id='$core_students_course_of_study_id' AND usergroup='$_SESSION[usergroup]' "));

    //check if any dates have been set
    if ($checkDate[1] && $checkDate[0] && $checkDate[1] !== '' && $checkDate[0] !== '') {

        //check if they have already submitted
        if ($appli_submited_check < 1 && (count($checkDate) > 1 && $checkDate[1] !== '0000-00-00' && $checkDate[1] !== '' && $checkDate[1] !== null) && ($checkDate[0] != null && $checkDate[0] != '')) { //check if date exist end
            $times = $checkDate[1] . ' ' . $checkDate[0];

            $date_closed = format_date('d/m/Y', "$checkDate[1]");

            $current_day = date('Y-m-d');
            $current_hour = date('Hi');

            $deadline_date = strtotime($checkDate[1]);
            $date_of_submit = strtotime($current_day);

            //check if todays date is the same as the deadline date
            if ($date_of_submit == $deadline_date) {

                // if current hour has passed if so rule out
                if ($current_hour > $checkDate[0]) {

                    $stop_form_below_from_showing = 1;//stop the terms and conditions showing
                    echo '<div class="alert alert-danger"><h2>Sorry, the application window has now closed</h2><p>Thank you for your the time you have spent on this application. Unfortunately the application window closed on ' . $date_closed . '.<br/><br/>Thank you once again for your application. Applications Team. </p></div>';

                }
            } //if date of submission has simply passed then show sorry message
            elseif ($date_of_submit > $deadline_date) {

                $stop_form_below_from_showing = 1;//stop the terms and conditions showing
                echo '<div class="alert alert-danger"><h2>Sorry, the application window has now closed</h2><p>Thank you for your the time you have spent on this application. Unfortunately the application window closed on ' . $date_closed . '.<br/><br/>Thank you once again for your application. Applications Team. </p></div>';

            }

        }//check if date exist end

    }
    ?>

    <?php
//missing uloads
//moved to lib file
    ?>

    <?php


// Check if the form has just been submitted	
//check if they have submitted already
    if ($stop_form_below_from_showing == '') {
        if ($appli_submited_check < 1) {
            ?>

            <h3>Please read this important information before you submit your application</h3>
            <div id="terms-and-conditions">
                <?php
                list($page_id, $cms_category, $cms_page_name, $cms_heading, $cms_brief, $cms_article) = get_cms('page_name', "confirm_Information");

                //add content filters based on hide show
                if (function_exists('session_based_show_hide')) {
                    $cms_article = session_based_show_hide("$cms_article");
                }

                echo $cms_article;

                ?>
            </div>
            <br/>
            <?php echo $error_msg; ?>
        <form action="<?= pull_field('lead_preferences', 'db202301', "WHERE usergroup='{$_SESSION['usergroup']}'") == 'yes' ? website_url_applicant . '/thank_you' : '' ?>"
              method="post">
            <p>
                Tick here
                <input name="agree" type="checkbox" id="agree" value="1"/>
                to agree to the terms and conditions above.<br/>
            </p>
            <?php
// check if 100%... if not then don't show
            if ($okay_for_form2 == "on") {
                $kill_other = 1;
                ?>
                <input type="Submit" name="Submit" id="button" class="btn btn-success btn-lg" value="Submit Application"
                       onclick="return appliSubmit()">
                <input name="process" type="hidden" value="2"/>
                <?php
            }// end check
            ?>

            <?php
// check if 100%... if not then don't show
            if ($kill_other !== 1) {

                //if($filled_percentage=="100" || $check_required>=$min_required){
                //echo "$check_required>=$min_required";
                if ($filled_percentage == "100") {
                    $show_button = 1;
                }

                //check if $min_required is set. if so then deal with it
                if ($min_required > 0) {
                    if ($check_required >= $min_required) {
                        $show_button = 1;
                    }
                }

                if (isset($check_required) && $check_required >= $min_required) {
                    $show_button = 1;
                }

                //check for missing files
                if (missing_uploads_checks($core_students_id) == "yes") {
                    $show_button = 2;
                }


                if ($show_button == 1) {
                    ?>
                    <input type="Submit" name="Submit" id="button" class="btn btn-success btn-lg"
                           value="Submit Application" onclick="return appliSubmit()">
                    <input name="process" type="hidden" value="1"/>
                    <?php
                } elseif ($show_button == 2) {
                    ?>

                    <p class="text-danger">Sorry you <b>CANNOT</b> submit as you have not uploaded all the required
                        files.<br/><br/>
                        <?php
                        missing_uploads($core_students_id);
                        ?>
                    </p>
                    <input type="button" name="Submit" id="button" class="btn btn-danger btn-lg"
                           value="Submit Application" onclick="return appliSubmit()" disabled>
                    <?php
                } else {
                    ?>
                    <p class="text-danger">Sorry you <b>CANNOT</b> submit as you have not yet filled in all the required
                        fields..<br/><br/>
                    <div class="alert alert-danger">
                        <?php
                        echo "<h4>Incomplete questions</h4><p>Please go back into the form to complete them</p>";

                        $usergroup_functionality = pull_field("form_schools", "db1261", "WHERE id='" . $_SESSION['usergroup'] . "'");
                        $usergroup_functionality = explode(",", $usergroup_functionality);

                        if (in_array('custom_system_forms', $usergroup_functionality)) {
                            get_missing_data_for_new_forms($core_students_id);
                            #echo '1';
                        } else {
                            incomplete_questions($core_students_id, find_slug(2));
                            #echo '2';
                        }


                        ?>
                    </div>
                    </p>
                    <input type="button" name="Submit" id="button" class="btn btn-danger btn-lg"
                           value="Submit Application" onclick="return appliSubmit()" disabled>
                    <?php

                }
                //}// end 100% check
                ?>
                </form>
            <?php } else {
                echo " <br/><br/>You have already submitted.<br/><br/>For the tracker, please click <strong><a href=\"checklist\">this link</a> </strong> this link to track its progress.";

            }// check if submitted
        } else {
            echo " <br/><br/>You have already submitted.<br/><br/>For the tracker, please click <strong><a href=\"checklist\">this link</a> </strong> this link to track its progress.";
        } // check if form submitted
    }
//echo "//".$kill_other.'\\'.$filled_percentage.'-'.$check_required.'-'.$min_required;
    ?>
    <?php
}// end error check
