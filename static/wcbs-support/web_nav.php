<?php

if ($_SESSION['loggedin']) { // only show to logged in users

    echo '<div class="btn btn-custom btn-lg btn-block"><a href="' . website_url_applicant . '/logout">' . translate("Log out", $_SESSION['lang']) . ' </a></div><br/>';


//only show on the homepage
    if ($cms_page_name == "Checklist" || find_slug(2) == "blog") {
        ?>
        <div class="row">

            <?php
            //eliminate from blog
            if (find_slug(2) !== "blog") {
                ?>
                <div class="col-xs-12">
                    <div class="alert alert-warning">
                        <h3><?= translate("FAQ & Support", $_SESSION['lang']) ?></h3>
                        <hr/>
                        <p>
                        <h4><?= translate("Can I track my application after submitting?", $_SESSION['lang']) ?></h4>
                        <?= translate("Yes, just log in and use Application Tracker in the Quicklinks menu", $_SESSION['lang']) ?>
                        .
                        </p><br/>
                        <a href="<?php echo website_url_applicant; ?>/applicant-faqs"
                           class="label label-info"><?= translate("See more", $_SESSION['lang']) ?> > </a>
                    </div>
                </div>
                <?php
                //eliminate from blog end
            }
            ?>
        </div>


        <div class="col-xs-12 col-lg-12">
            <h3><?= translate("News and Updates", $_SESSION['lang']) ?></h3>
            <p><?= translate("Various information to help you through your application", $_SESSION['lang']) ?></p>

            <ul class="list-inline">
                <?php
                $i = 0;
                foreach ($blog_assist as $msg) {
                    ?>
                    <li class="col-lg-12">
                        <h4><?php echo $msg["db1695"]; ?></h4>
                        <h5><?php echo $msg["db1698"]; ?></h5>
                        <!--                    <img src="--><?php //=$front_web_url_file_loc?><!--/media/-->
                        <?php //echo $msg["db1699"]; ?><!--" class="thumbnail pull-left" alt="nes_image" />-->
                        <a href="<?php echo website_url_applicant . '/blog/' . $msg["id"]; ?>"
                           class="label label-info"><?= translate("Read More", $_SESSION['lang']) ?></a>
                        <hr/>
                    </li>
                <?php } // end of loop
                echo($msg == '' ? '<small>- ' . translate("Sorry no message to show", $_SESSION['lang']) . '</small>' : '');
                ?>
            </ul>
        </div>
        <?php
    }

    if ($cms_page_name !== "Checklist" && find_slug(2) !== "blog") {

        if ($appli_submited_check > 1) {


            $dbh = get_dbh();

            // Get the stages for student's route
            $sql = 'SELECT system_table.form_id, system_table.db_field_name, system_table.name
		FROM system_table
		INNER JOIN core_students ON core_students.db2280 = system_table.pg_id
		WHERE core_students.id = ? AND system_table.type="stage"
		AND locked IN(2,0)
		ORDER BY system_table.form_order';
            $sth = $dbh->prepare($sql);
            $sth->execute(array($_SESSION['student_id']));
            $stages = array();
            while ($row = $sth->fetch(PDO::FETCH_ASSOC)) $stages[] = $row;

            // pull the statuses of stages for this particular student
            $checklist_table_name = 'chk_' . pull_field(
                    'system_pages INNER JOIN core_students ON core_students.db2280 = system_pages.page_id',
                    'page_name', "WHERE core_students.id = '$_SESSION[student_id]'");


            $sql = 'SELECT ';
            foreach ($stages as $stage) {
                $sql .= $stage['db_field_name'] . ', ';
            }
            $sql = substr($sql, 0, -2);
            $sql .= " FROM $checklist_table_name WHERE rel_id = ? LIMIT 1";
            $sth = $dbh->prepare($sql);
            $sth->execute(array($_SESSION['student_id']));
            $stage_status = $sth->fetch(PDO::FETCH_ASSOC);

            ?>
            <div class="row">
                <div class="col-xs-12">
                    <ul class="list-group list-group_home">
                        <li class="list-group-item">
                            <h4 class="list-group-item-heading"><?= translate("Application Tracker", $_SESSION['lang']) ?></h4>
                            <p class="list-group-item-text"><?= translate("A quick summary of how your application is proceeding", $_SESSION['lang']) ?></p>
                        </li>

                        <li class="list-group-item">
                            <div class="circle-text">
                                <span>0</span>
                            </div><?= translate("Application Completion", $_SESSION['lang']) ?>
                            <?php echo($filled_percentage == '100' ? '<img src="' . engine_url . '/images/icon_tick.png" width="16" height="16" alt="tick" />' : '') ?>
                        </li>
                        <?php
                        $i = 1;
                        foreach ($stages as $stage) {
                            $exp = explode('-', $stage['name']);
                            $sts = $stage_status[$stage['db_field_name']];
                            $sts_image = ($sts === 'on') ? '<img src="' . engine_url . '/images/icon_tick.png" width="16" height="16" alt="tick" />' : '';
                            ?>
                            <li class="list-group-item<?= stage_complete($i) ?>">
                                <div class="circle-text">
                                    <span><?= $i ?></span>
                                </div><?= $exp[1] ?> <?= $sts_image ?></li>
                            <?php
                            $i++;
                        } ?>

                    </ul>
                </div>
            </div>
            <?php
            //end of checklist
        } else {
            //get the student information
            $dbh = get_dbh();
            $sql = 'SELECT *
			FROM core_students
			WHERE core_students.id = ?';
            $sth = $dbh->prepare($sql);
            $sth->execute(array($_SESSION['student_id']));
            while ($row = $sth->fetch(PDO::FETCH_ASSOC)) $choosen_student_info = $row;

            //print_r($choosen_student_info);
            ?>
            <div class="row">
                <div class="col-xs-12">
                    <ul class="list-group">
                        <li class="list-group-item"><h4>Your Application</h4></li>
                        <?php
                        //get the right pages for this section
                        $pages_id = get_page_list_based_on_rules($_SESSION['student_id']);

                        if ($pages_id != "") {
                            get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq','ucas') AND id IN($pages_id) ORDER BY FIELD(id,$pages_id)", "yes", "list-group-item");
                        } else {
                            get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");
                        }
                        ?>
                    </ul>
                </div>
            </div>
        <?php } ?>

        <?php
    }// end of check if checklist

}//end checklist check

if (!$_SESSION['loggedin']) { // only show to logged in users
    ?>
    <div class="row col-xs-12">
        <p><b><?= translate("Quicklinks", $_SESSION['lang']) ?></b></p>
        <ul class="list-group">
            <?php
            get_cms_nav("application", "public", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");
            ?>
            <li class="list-group-item"><a
                        href="<?php echo website_url_applicant; ?>/parent_register" <?php if ($current_id == 'register') {
                    $sty = 'class="nav_selected"';
                } ?>><?= translate("Start a new application", $_SESSION['lang']) ?> >></a></li>
            <li class="list-group-item"><a
                        href="<?php echo website_url_applicant; ?>/login"><?= translate("Continue  an application you have already started", $_SESSION['lang']) ?>
                    >></a></li>
            <li class="list-group-item"><a
                        href="<?php echo website_url_applicant; ?>/login"><?= translate("Log in to view the status of a completed application", $_SESSION['lang']) ?>
                    >></a></li>
        </ul>
    </div>
    <?php
}

