<!-- <PERSON><PERSON><PERSON> BUTTONS TO USE IN YOUR IMPLEMENTATION-->
<!-- Your custom buttons need to launch the #booking_widget modal and should have the data-id attribute containing the ID of the short course --

<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#booking_widget" data-id="SCHEDULED_COURSE_ID">Book Now</button>
<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#booking_widget" data-id="1156">Book Now 2</button> 

 END OF SAMPLE BUTTONS -->
<?php
require_once('booking_model.php');
$booking_session = new Booking_model();
$attendee_form_id = 725;
?>
<script src="/admin/assets/js/vuejs/vue-2.6.11.min.js"></script>
<style type="text/css">
    .demobook {
        margin-top: 30px;
        margin-bottom: 45px;
    }

    .demobook .tab-content {
        padding-bottom: 15px;
    }

    .demobook .active {
        background-color: transparent;
    }

    .demobook ul.list-group {
        background: transparent;
        border-radius: 5px;
    }

    .demobook ul.list-group li.list-group-item {
        background: transparent;
        padding: 7px 15px;
    }

    .demobook ul.list-group.attendees li.list-group-item:first-child {
        border-radius: 5px 5px 0 0;
    }

    .demobook ul.list-group.attendees li.list-group-item:last-child {
        border-radius: 0 0 5px 5px;
    }

    .demobook .table {
        margin: 0;
        border: 0;
    }

    .demobook .table td, .demobook .table th, .demobook .table tr {
        border: none;
    }

    .demobook .checkbox {
        width: 100%;
        cursor: pointer;
        padding-left: 0;
        float: none;
    }

    .demobook .checkbox input {
        position: absolute;
        right: 15px;
    }

    .demobook .btn {
        display: inline-block;
        padding: 6px 12px;
        margin-bottom: 0;
        font-size: 14px;
        font-weight: normal;
        line-height: 1.42857143;
        text-align: center;
        white-space: nowrap;
        vertical-align: middle;
        -ms-touch-action: manipulation;
        touch-action: manipulation;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        background-image: none;
        border: 1px solid transparent;
        border-radius: 4px;
    }

    .demobook .btn:focus,
    .demobook .btn:active:focus,
    .demobook .btn.active:focus,
    .demobook .btn.focus,
    .demobook .btn:active.focus,
    .demobook .btn.active.focus {
        outline: thin dotted;
        outline: 5px auto -webkit-focus-ring-color;
        outline-offset: -2px;
    }

    .demobook .btn:hover,
    .demobook .btn:focus,
    .demobook .btn.focus {
        color: #333;
        text-decoration: none;
    }

    .demobook .btn:active,
    .demobook .btn.active {
        background-image: none;
        outline: 0;
        -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
        box-shadow: inset 0 3px 5px rgba(0, 0, 0, .125);
    }

    .demobook .btn-sm,
    .demobook .btn-group-sm > .btn {
        padding: 5px 10px;
        font-size: 12px;
        line-height: 1.5;
        border-radius: 3px;
    }

    .demobook .btn-xs,
    .demobook .btn-group-xs > .btn {
        padding: 1px 5px;
        font-size: 12px;
        line-height: 1.5;
        border-radius: 3px;
    }

    .demobook .form-control {
        display: block;
        width: 100%;
        height: 34px;
        padding: 6px 12px;
        font-size: 14px;
        line-height: 1.42857143;
        color: #555;
        background-color: #fff;
        background-image: none;
        border: 1px solid #ccc;
        -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, .075);
        -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
        -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
        transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
    }

    .demobook .label {
        display: inline;
        padding: .2em .6em .3em;
        font-size: 75%;
        font-weight: bold;
        line-height: 1;
        color: #fff;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: .25em;
    }

    .demobook .label-default {
        background-color: #777;
    }

    .demobook .label-default[href]:hover,
    .demobook .label-default[href]:focus {
        background-color: #5e5e5e;
    }

    .demobook hr {
        border: 0;
        border-top: 1px solid #bbb;
        margin: 10px 0 20px 0;
    }

    .demobook label {
        font-weight: 400;
    }

    .demobook textarea.form-control {
        height: auto;
    }

    @media (max-width: 991px) {
        .modal-xl {
            width: 100%;
        }
    }

    @media (min-width: 992px) {
        .modal-xl {
            width: 900px;
        }
    }

    @media (min-width: 1200px) {
        .modal-xl {
            width: 1170px;
        }
    }
</style>

<!-- MODAL CONTAINING THE BOOKING WIDGET -->

<div class="modal fade" id="booking_widget" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" v-cloak>
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title">{{booking_session.course.title}} - Short course Booking</h4>
            </div>
            <div class="modal-body">

                <div class="row demobook">
                    <div class="col-sm-12">
                        <div class="panel panel-default">
                            <div class="panel-body">
                                <div class="tab-content">
                                    <div role="tabpanel" class="tab-pane fade in active" id="demobook1">
                                        <h4>{{booking_session.course.title}}</h4>
                                        <button class="btn btn-info pull-right" type="button"
                                                v-on:click="start_booking(booking_session.course.id)">Book Now
                                        </button>
                                        <button class="hidden" id="start_booking" type="button" data-toggle="tab"
                                                data-target="#demobook2">Book Now
                                        </button>
                                    </div>
                                    <div role="tabpanel" class="tab-pane fade" id="demobook2">
                                        <legend>Bookings</legend>
                                        <div class="col-sm-9">
                                            <ul class="list-group attendees">
                                                <!-- <li class="list-group-item">Attendee One <span class="close">&times;</span></li> -->
                                                <li class="list-group-item" v-for="att in booking_session.attendees">
                                                    {{get_name(att)}}
                                                    <span class="close" v-on:click="remove_item(att.id,'attendees')">&times;</span>
                                                    <span class="pull-right" style="padding-right: 10px;"> {{booking_session.course.price|currencyFormat}}</span>
                                                </li>
                                                <li class="list-group-item" v-for="p in booking_session.products">
                                                    {{p.name}}
                                                    <span class="close" v-on:click="remove_item(p.id,'products')">&times;</span>
                                                    <span class="pull-right" style="padding-right: 10px;"> {{p.price | currencyFormat}}</span>
                                                    <span class="pull-right" style="padding-right: 10px;">{{p.quantity}} x </span>
                                                </li>
                                            </ul>
                                            <ul class="list-group" style="margin-top: 15px;">
                                                <li class="list-group-item">
                                                    <table class="table">
                                                        <tr>
                                                            <th>Total</th>
                                                            <th class="text-right total"><small><i>{{booking_session.course.abv}}</i></small>
                                                                <span>{{order_total|currencyFormat}}</span>
                                                            </th>
                                                        </tr>
                                                    </table>
                                                </li>
                                            </ul>
                                        </div>
                                        <div class="col-sm-3">
                                            <button class="btn btn-default" type="button" data-toggle="tab"
                                                    data-target="#demobook2b" v-on:click="show_add_attendee()">Add
                                                Attendee
                                                <span class="fa fa-user-plus"></span>
                                            </button>
                                        </div>
                                        <div class="col-sm-3">
                                            <button class="btn btn-default" type="button" data-toggle="tab"
                                                    data-target="#demobook3b">Add Product&nbsp;
                                                <span class="fa fa-cart-plus"></span>&nbsp;&nbsp;
                                            </button>
                                        </div>

                                        <br clear="all">
                                        <button class="btn btn-default" type="button" data-toggle="tab"
                                                data-target="#demobook1">Back
                                        </button>
                                        <button class="btn btn-info pull-right" type="button"
                                                v-on:click="prepare_cart_summary()">Next
                                        </button>
                                        <button class="hidden" id="prepare_cart_summary" type="button" data-toggle="tab"
                                                data-target="#demobook4a">Next
                                        </button>
                                    </div>
                                    <div class="tab-pane fade" role="tabpanel" id="demobook2b">
                                        <legend>Add Attendee</legend>
                                        <div class="col-sm-12">
                                            <ul class="list-group hidden">
                                                <li class="list-group-item">Add Attendee</li>
                                                <li class="list-group-item">
                                                    <div class="form-group">
                                                        <label class="control-label">Full Name</label>
                                                        <input id="demobook_user" type="text" name="name"
                                                               class="form-control input-sm">
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="control-label">Email Address</label>
                                                        <input id="demobook_email" type="email" name="email"
                                                               class="form-control input-sm">
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="control-label">Phone Number</label>
                                                        <input id="demobook_phone" type="phone" name="phone"
                                                               class="form-control input-sm">
                                                    </div>
                                                </li>
                                                <li class="list-group-item text-right">
                                                    <button class="btn btn-info btn-xs" onclick="add_attendee()">Add
                                                    </button>
                                                </li>
                                                <li class="list-group-item">

                                                </li>
                                            </ul>
                                            <form id="forms_view" class="form form-horizontal"
                                                  v-on:submit="add_attendee()"></form>

                                        </div>
                                        <button class="btn btn-default" type="button" data-toggle="tab"
                                                data-target="#demobook2" id="demobook_done">Back
                                        </button>
                                    </div>
                                    <div class="tab-pane fade" role="tabpanel" id="demobook3b">
                                        <legend>Add Product</legend>
                                        <div class="col-sm-12">
                                            <ul class="list-group">
                                                <li class="list-group-item">
                                                    <div class="form-group">
                                                        <label class="control-label">Search</label>
                                                        <input type="search" v-model="product_search"
                                                               class="form-control input-xs"
                                                               placeholder="search by product name">
                                                    </div>
                                                </li>
                                                <li class="list-group-item">
                                                    <table class="table table-condensed table-striped table-hover">
                                                        <tr>
                                                            <th>Product Name</th>
                                                            <th>Price <small>(<i>{{booking_session.course.abv}}</i>
                                                                    )</small></th>
                                                            <th>Quantity</th>
                                                            <th></th>
                                                        </tr>
                                                        <tr v-for="p in get_products">
                                                            <td class="title">{{p.name}}</td>
                                                            <td class="price">{{p.price|currencyFormat}}</td>
                                                            <td>
                                                                <input type="number" v-model="p.quantity"
                                                                       class="input-xs" placeholder="1">
                                                            </td>
                                                            <td>
                                                                <button v-if="p.selected" class="btn btn-xs btn-info"
                                                                        v-on:click="add_product(p)">
                                                                    <span class="fa fa-check-square"></span>
                                                                    Update
                                                                </button>
                                                                <button v-else="" class="btn btn-xs btn-info"
                                                                        v-on:click="add_product(p)">
                                                                    <span class="fa fa-cart-plus"></span>
                                                                    Add
                                                                </button>

                                                            </td>
                                                        </tr>
                                                    </table>
                                                </li>
                                                <!-- <li class="list-group-item text-right">
                                                    <button class="btn btn-info btn-xs" onclick="add_product()">Add</button>
                                                </li> -->
                                            </ul>
                                        </div>
                                        <br clear="all">
                                        <div class="col-sm-12">
                                            <button class="btn btn-default" type="button" data-toggle="tab"
                                                    data-target="#demobook2" id="demobook_done2">Back
                                            </button>
                                            <button class="btn btn-primary pull-right" type="button" data-toggle="tab"
                                                    data-target="#demobook4a">Continue
                                            </button>
                                        </div>
                                    </div>
                                    <div role="tabpanel" class="tab-pane fade" id="demobook4a">
                                        <legend>Cart Summary</legend>
                                        <div class="col-sm-12">
                                            <table class="table table-condensed">
                                                <tr>
                                                    <th colspan="2">Item</th>
                                                    <th>Qty</th>
                                                    <th>Unit Cost</th>
                                                    <th>Sub Total</th>
                                                    <th>Total <small>(<i>{{booking_session.course.abv}}</i> )</small>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <td colspan="5">
                                                        <b>{{booking_session.course.title}}</b>
                                                    </td>
                                                    <td rowspan="5" style="vertical-align: bottom;">
                                                        <b> {{ order_total|currencyFormat }}</b>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>&nbsp;</td>
                                                    <td>Attendees</td>
                                                    <td class="num_attendees">{{booking_session.attendees.length}}</td>
                                                    <td class="cost_per_attendee">
                                                        {{booking_session.course.price|currencyFormat}}
                                                    </td>
                                                    <td class="total_attendee_cost">{{ (booking_session.course.price *
                                                        booking_session.attendees.length) | currencyFormat }}
                                                    </td>
                                                </tr>
                                                <tr v-for="(product,index) in booking_session.products">
                                                    <td v-if="index==0" v-bind:rowspan="booking_session.products.length"
                                                        style="vertical-align: middle;">Products
                                                    </td>
                                                    <td>{{product.name}}</td>
                                                    <td>{{product.quantity}}</td>
                                                    <td> {{product.price|currencyFormat}}</td>
                                                    <td> {{(product.price * product.quantity)|currencyFormat}}</td>
                                                </tr>
                                            </table>
                                        </div>
                                        <br clear="all">
                                        <div class="col-sm-12">
                                            <button class="btn btn-primary" type="button" data-toggle="tab"
                                                    data-target="#demobook2">Back
                                            </button>
                                            <button class="btn btn-primary pull-right" type="button" data-toggle="tab"
                                                    data-target="#payment_tab">Confirm and Continue
                                            </button>
                                        </div>
                                    </div>
                                    <div role="tabpanel" class="tab-pane fade" id="payment_tab">
                                        <legend>Make Payment</legend>
                                        <div class="col-sm-6 col-sm-offset-3">
                                            <ul class="list-group">
                                                <li class="list-group-item">
                                                    <label class="checkbox"
                                                           v-on:click="showlogin('account','target5', 'target7')">Account
                                                        <input type="radio" name="payment_method">
                                                    </label>
                                                </li>
                                                <li class="list-group-item">
                                                    <label class="checkbox"
                                                           v-on:click="showlogin('pay all now','target7', 'target5')">Pay
                                                        All Now
                                                        <input type="radio" name="payment_method">
                                                    </label>
                                                </li>
                                                <li class="list-group-item">
                                                    <label class="control-label">Notes</label>
                                                    <textarea class="form-control"
                                                              v-model="booking_session.notes"></textarea>
                                                </li>
                                            </ul>
                                        </div>
                                        <br clear="all">
                                        <button class="btn btn-default" type="button" data-toggle="tab"
                                                data-target="#demobook4a">Back
                                        </button>
                                        <button class="hidden" data-toggle="tab" data-target="#login_tab" type="button"
                                                id="showlogin">Next
                                        </button>
                                    </div>
                                    <div role="tabpanel" class="tab-pane fade" id="invoice_tab">
                                        <legend>Invoice Details</legend>
                                        <div v-if="logged_in" class="alert alert-info">You are logged in as
                                            {{logged_in}}
                                        </div>
                                        <div v-for="error in invoice_errors" class="alert alert-error">{{error}}</div>
                                        <div class="col-lg-12">
                                            <div class="form-group">
                                                <label class="control-label">Purchase Order Number</label>
                                                <input type="text"
                                                       v-model="booking_session.invoice_account.order_number"
                                                       class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <h6 style="margin: 0;">Delivery / Work Address</h6>
                                            <hr style="margin: 10px 0 20px 0">
                                            <div class="form-group">
                                                <label>Delivery Name</label>
                                                <input type="text"
                                                       v-model="booking_session.invoice_account.delivery.delivery_name"
                                                       class="form-control">
                                            </div>
                                            <div class="form-group">
                                                <label>Address</label>
                                                <textarea class="form-control"
                                                          v-model="booking_session.invoice_account.delivery.delivery_address"
                                                          rows="2"></textarea>
                                            </div>
                                            <div class="form-group">
                                                <label>Contact Name</label>
                                                <input type="text"
                                                       v-model="booking_session.invoice_account.delivery.contact_name"
                                                       class="form-control">
                                            </div>
                                            <div class="form-group">
                                                <label>Contact Number</label>
                                                <input type="phone"
                                                       v-model="booking_session.invoice_account.delivery.contact_number"
                                                       class="form-control">
                                            </div>
                                            <div class="form-group">
                                                <label>Contact Email</label>
                                                <input type="email"
                                                       v-model="booking_session.invoice_account.delivery.contact_email"
                                                       class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <h6 style="margin: 0;">
                                                Invoice Address
                                                <small class="pull-right">
                                                    <label class="checkbox" style="display: inline;">
                                                        <input type="checkbox"
                                                               v-model="booking_session.invoice_account.same_address"
                                                               style="opacity: 1;position: relative;left: 0;">
                                                        <span style="position: relative;top: 4px;">Use Delivery Address</span>
                                                    </label>
                                                </small>
                                            </h6>
                                            <hr style="margin: 10px 0 20px 0">
                                            <div class="form-group">
                                                <label>Organisation</label>
                                                <input type="text"
                                                       v-model="booking_session.invoice_account.billing.organisation"
                                                       class="form-control">
                                            </div>
                                            <div class="form-group">
                                                <label>Address</label>
                                                <textarea class="form-control"
                                                          v-model="booking_session.invoice_account.billing.billing_address"
                                                          rows="2"></textarea>
                                            </div>
                                            <div class="form-group">
                                                <label>Contact Name</label>
                                                <input type="text"
                                                       v-model="booking_session.invoice_account.billing.contact_name"
                                                       class="form-control">
                                            </div>
                                            <div class="form-group">
                                                <label>Contact Number</label>
                                                <input type="phone"
                                                       v-model="booking_session.invoice_account.billing.contact_number"
                                                       class="form-control">
                                            </div>
                                            <div class="form-group">
                                                <label>Contact Email</label>
                                                <input type="email"
                                                       v-model="booking_session.invoice_account.billing.contact_email"
                                                       class="form-control">
                                            </div>
                                        </div>

                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Training Start Date</label>
                                                <input type="date"
                                                       v-model="booking_session.invoice_account.training_start"
                                                       class="form-control">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>Deliver Resources By</label>
                                                <input type="date"
                                                       v-model="booking_session.invoice_account.deliver_date"
                                                       class="form-control">
                                            </div>
                                        </div>
                                        <!-- <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>I already have the required resource pack and/or trainers manual</label>
                                                <div class="radio">
                                                    <label><input type="radio" name="optionsRadios" value="yes"> Yes</label>
                                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    <label><input type="radio" name="optionsRadios" value="no"> No</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label>I have ordered the required resource pack and/or trainers manual</label>
                                                <div class="radio">
                                                    <label><input type="radio" name="optionsRadios2" value="yes"> Yes</label>
                                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                    <label><input type="radio" name="optionsRadios2" value="no"> No</label>
                                                </div>
                                            </div>
                                        </div> -->

                                        <br clear="all">
                                        <div class="col-sm-12">
                                            <button class="btn btn-default" type="button" data-toggle="tab"
                                                    data-target="#payment_tab">Back
                                            </button>
                                            <button class="btn btn-info pull-right" type="button"
                                                    v-on:click="place_order()">Place Order
                                            </button>
                                            <button class="hidden" type="button" id="booking_complete_tab"
                                                    data-toggle="tab" data-target="#booking_complete">Done
                                            </button>
                                        </div>
                                    </div>
                                    <div role="tabpanel" class="tab-pane fade" id="login_tab">
                                        <legend>Checkout Options</legend>
                                        <div v-if="logged_in">
                                            <div class="alert alert-info">You are already logged in as {{logged_in}}
                                            </div>
                                            <br clear="all">
                                            <button type="button" class="btn btn-primary pull-right"
                                                    v-on:click="login_skip()">Continue
                                            </button>
                                            <button type="button" class="hidden" id="invoice_tab_btn" data-toggle="tab"
                                                    data-target="#invoice_tab">Login
                                            </button>
                                            <button type="button" class="hidden" id="demobook7_btn" data-toggle="tab"
                                                    data-target="#demobook7">Login
                                            </button>
                                        </div>
                                        <div v-else>
                                            <div class="col-sm-6">
                                                <h6 style="margin: 0;">Login to existing account</h6>
                                                <hr>
                                                <div class="form-group">
                                                    <label>Email Address</label>
                                                    <input type="email" v-model="login.email" name="email"
                                                           class="form-control">
                                                </div>
                                                <div class="form-group">
                                                    <label>Password</label>
                                                    <input type="password" v-model="login.pwd" name="pwd"
                                                           class="form-control">
                                                </div>
                                                <div v-bind:class="'alert alert-error '+login_error">
                                                    {{login_error_msg}}
                                                </div>
                                                <div class="form-group">
                                                    <button type="button" class="btn btn-primary pull-right"
                                                            v-on:click="login_ajax()">Login
                                                    </button>
                                                    <button type="button" class="hidden" id="invoice_tab_btn"
                                                            data-toggle="tab" data-target="#invoice_tab">Login
                                                    </button>
                                                    <button type="button" class="hidden" id="demobook7_btn"
                                                            data-toggle="tab" data-target="#demobook7">Login
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <h6 style="margin: 0;">Continue as guest</h6>
                                                <hr>
                                                <div v-for="msg in register_errors" class="alert alert-error">{{msg}}
                                                </div>
                                                <div class="form-group">
                                                    <label>Full Name</label>
                                                    <input type="text" v-model="register.name" name="register_name"
                                                           class="form-control">
                                                </div>
                                                <div class="form-group">
                                                    <label>Email Address</label>
                                                    <input type="email" v-model="register.email" name="register_email"
                                                           class="form-control">
                                                </div>
                                                <div class="form-group">
                                                    <label>
                                                        <span class="required"></span>
                                                        Create a password</label>
                                                    <input v-model="register.pwd" type="password" class="form-control"
                                                           placeholder="Enter your Password">
                                                </div>
                                                <div class="form-group">
                                                    <label>
                                                        <span class="required"></span>
                                                        Confirm the password</label>
                                                    <input v-model="register.pwd2" type="password" class="form-control"
                                                           placeholder="Repeat Password">
                                                </div>

                                                <div class="form-group">
                                                    <label for="usercode">
                                                        <span class="required"></span>
                                                        Please authenticate your entry using the code below</label>
                                                    <div class="input-group">
														<span class="input-group-addon" style="padding: 0;">
															<img class="orange_text"
                                                                 src="<?= website_url ?>/reg_inc_pngimg.php"
                                                                 align="middle"/>
														</span>
                                                        <input v-model="register.usercode" type="number"
                                                               data-validation-error-msg="You must enter a authentication code"
                                                               class="form-control"
                                                               placeholder="Enter authentication code here">
                                                    </div>
                                                </div>

                                                <div class="form-group">
                                                    <button type="button" class="btn btn-primary pull-right"
                                                            v-on:click="register_ajax()">Continue as guest
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <br clear="all">
                                        <div class="col-sm-12">
                                            <button class="btn btn-default" type="button" data-toggle="tab"
                                                    data-target="#payment_tab">Back
                                            </button>
                                        </div>
                                    </div>
                                    <div role="tabpanel" class="tab-pane fade" id="demobook7">
                                        <legend>Make Payment - Stripe</legend>
                                        <div class="col-sm-12">
                                            <div class="alert alert-info">
                                                <h2>Stripe Payment Window</h2>
                                            </div>
                                        </div>
                                        <br clear="all">
                                        <div class="col-sm-12">
                                            <button class="btn btn-default" type="button" data-toggle="tab"
                                                    data-target="#payment_tab">Back
                                            </button>
                                        </div>
                                    </div>
                                    <div role="tabpanel" class="tab-pane fade" id="booking_complete">
                                        <legend>Booking Successful</legend>
                                        <div class="col-sm-12">
                                            <div class="alert alert-info">
                                                <h2>You have successfully booked onto
                                                    {{booking_session.course.title}}</h2>
                                            </div>
                                        </div>
                                        <br clear="all">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- END OF BOOKING WIDGET MODAL -->

<form style="display: none;">
    <input type="hidden" name="temp_user_id" id="temp_user_id"
           value="<?php echo $booking_session->get_temp_user_id(); ?>">
</form>
<script type="text/javascript">
    let attendees = [];
    let course_id = false;
    let booking_session = {attendees: [], course: {}, products: []};
    let methods = {
        start_booking: function (cid) {
            let vm = this;
            let bs = vm.booking_session;
            bs.products = [];
            bs.attendees = [];
            vm.product_list = [];
            bs.course_id = cid;
            $("#start_booking").tab('show');
            let href = window.location.origin + '/booking_model.php';
            let user = $('#temp_user_id').val();
            let data = {ajax_call: 1, session_id: user, action: 'start_booking', course_id: cid};
            $.ajax({
                type: "POST",
                url: href,
                data: data,
                dataType: 'json',
                success: function (response) {
                    // console.log(response);
                    if (response['attendees']) {
                        bs.attendees = JSON.parse(response.attendees);
                    }
                    if (response['products']) {
                        bs.products = JSON.parse(response.products);
                    }
                    bs.course = response.course;
                    vm.product_list = response.products_list;

                    if (response.user_id) {
                        bs.user_id = response.user_id;
                        vm.login_error = 'hidden';
                        vm.logged_in = response.fullname;
                    }
                },
                error: function (e1, e2) {
                    console.log(e1);
                }
            });
        },
        get_name: function (item) {
            let name = '';
            if (item.db15054) name = item.db15054 + ' ';
            if (item.db15055) name += item.db15055;
            return name;
        },
        add_attendee: function () {
            let vm = this.booking_session;
            console.log(vm.course);
            let sid = $('#temp_user_id').val();
            let f = $('#forms_view').serializeArray();
            $('#demobook2b form').trigger("reset");
            f.push({name: 'ajax_call', value: 1});
            f.push({name: 'session_id', value: sid});
            f.push({name: 'course_id', value: vm.course_id});
            f.push({name: 'action', value: 'update_order'});
            f.push({name: 'section', value: 'attendees'});
            // console.log(f);
            let href = window.location.origin + '/booking_model.php';
            $.ajax({
                type: "POST",
                url: href,
                data: f,
                dataType: 'json',
                success: function (response) {
                    // console.log(response);
                    if (Array.isArray(response)) {
                        vm.attendees = response;
                        console.log(vm.course);
                        let total = 0;
                        $("#demobook_done").tab('show');
                    }
                },
                error: function (e1, e2) {
                    console.log(e1);
                }
            });

        },
        add_product: function (product) {
            product.selected = true;
            if (!product.quantity) product.quantity = 1;
            let bs = this.booking_session;
            let products = this.booking_session.products;
            let p = products.find(entry => entry.id == product.id);
            if (p) {
                // console.log(product.name+' already added');
                p = product;
            }

            let sid = $('#temp_user_id').val();
            let f = [];
            f.push({name: 'id', value: product.id});
            f.push({name: 'name', value: product.name});
            f.push({name: 'price', value: product.price});
            f.push({name: 'quantity', value: product.quantity});
            f.push({name: 'ajax_call', value: 1});
            f.push({name: 'session_id', value: sid});
            f.push({name: 'course_id', value: bs.course_id});
            f.push({name: 'action', value: 'update_order'});
            f.push({name: 'section', value: 'products'});
            this.update_product(f);
        },
        update_product: function (f) {
            let href = window.location.origin + '/booking_model.php';
            let vm = this;
            $.ajax({
                type: "POST",
                url: href,
                data: f,
                dataType: 'json',
                success: function (response) {
                    // console.log(response);
                    if (Array.isArray(response)) {
                        vm.booking_session.products = response;
                        // console.log('products updated');
                    }
                },
                error: function (e1, e2) {
                    console.log(e1);
                }
            });
        },
        remove_item: function (id, section) {
            let sid = $('#temp_user_id').val();
            let href = window.location.origin + '/booking_model.php';
            let bs = this.booking_session;
            let d = {
                ajax_call: 1,
                session_id: sid,
                course_id: bs.course_id,
                action: 'remove_item',
                section: section,
                id: id
            };
            $.ajax({
                type: "POST",
                url: href,
                data: d,
                dataType: 'json',
                success: function (response) {
                    // console.log(response);
                    if (Array.isArray(response)) {
                        bs[section] = response;
                    }
                },
                error: function (e1, e2) {
                    console.log(e1);
                }
            });
        },
        show_add_attendee: function () {
            let form = $('#forms_view_div form').html();
            $('#forms_view').html(form);
        },
        prepare_cart_summary: function () {
            $('#prepare_cart_summary').tab('show');
        },
        showlogin: function (method, target, target2) {
            this.booking_session.payment_method = method;
            $('.' + target2).addClass('hidden');
            $('.' + target).removeClass('hidden');
            $('#showlogin').tab('show');
        },
        login_ajax: function () {
            let vm = this;
            if (vm.login.email && vm.login.pwd) {
                let href = window.location.origin + '/booking_model.php';
                $.ajax({
                    type: "POST",
                    url: href,
                    data: vm.login,
                    dataType: 'json',
                    success: function (response) {
                        console.log(response);
                        if (response.success && response.user_id) {
                            vm.booking_session.user_id = response.user_id;
                            vm.login_error = 'hidden';
                            vm.logged_in = response.fullname;
                            console.log(vm.booking_session.payment_method);
                            if (vm.booking_session.payment_method == 'account') {
                                $('#invoice_tab_btn').tab('show');
                            } else {
                                $('#demobook7_btn').tab('show');
                            }
                        } else {
                            vm.login_error_msg = response.error;
                            vm.login_error = '';
                        }
                    },
                    error: function (e1, e2) {
                        console.log(e1);
                    }
                });
            }
        },
        login_skip: function () {
            let vm = this;
            if (vm.booking_session.user_id) {
                if (vm.booking_session.payment_method == 'account') {
                    $('#invoice_tab_btn').tab('show');
                } else {
                    $('#demobook7_btn').tab('show');
                }
            } else {
                vm.logged_in = false;
            }
        },
        register_ajax: function () {
            let vm = this;
            if (vm.register.email && vm.register.pwd) {
                let href = window.location.origin + '/booking_model.php';
                $.ajax({
                    type: "POST",
                    url: href,
                    data: vm.register,
                    dataType: 'json',
                    success: function (response) {
                        console.log(response);
                        if (response.success && response.user_id) {
                            vm.booking_session.user_id = response.user_id;
                            vm.login_error = 'hidden';
                            vm.logged_in = response.fullname;
                            // console.log(vm.booking_session.payment_method);
                            if (vm.booking_session.payment_method == 'account') {
                                $('#invoice_tab_btn').tab('show');
                            } else {
                                $('#demobook7_btn').tab('show');
                            }
                        } else {
                            vm.register_errors = response.messages;
                            // Vue.set( vm, 'register_errors', response.messages );
                            // vm.login_error = '';
                        }
                    },
                    error: function (e1, e2) {
                        console.log(e1);
                    }
                });
            }
        },
        validate_invoice: function () {
            let invoice = this.booking_session.invoice_account;
            let e = false;
            let success = true;
            this.invoice_errors = [];
            if (!invoice.order_number) {
                this.invoice_errors.push('Purchase Order Number is required');
                success = false;
            }

            for (a in invoice.delivery) {
                if (!invoice.delivery[a]) {
                    e = 'Please complete all delivery details';
                    success = false;
                }
            }
            if (e) this.invoice_errors.push(e);
            if (!invoice.same_address) {
                e = false;
                for (a in invoice.billing) {
                    if (!invoice.billing[a]) {
                        e = 'Please complete all invoice address details';
                        success = false;
                    }
                }
                if (e) this.invoice_errors.push(e);
            }
            return success;
        },
        place_order: function () {
            if (this.validate_invoice()) {
                let invoice = this.booking_session;
                let errors = this.invoice_errors;
                invoice.ajax_call = 1;
                invoice.action = 'place_order';
                invoice.session_id = getCookie("sc_booking");
                let href = window.location.origin + '/booking_model.php';
                $.ajax({
                    type: "POST",
                    url: href,
                    data: invoice,
                    dataType: 'json',
                    success: function (response) {
                        console.log(response);
                        if (response.success) {
                            $('#booking_complete_tab').tab('show');
                        } else if (response.messages) {
                            response.messages.forEach(function (msg) {
                                errors.push(msg);
                            });
                            // errors.push(response.message);
                        }
                    },
                    error: function (e1, e2) {
                        console.log(e1);
                        errors.push(e1);
                    }
                });
            }
        },
        searchProducts: function (asset, title, category, location) {
            let filtered = true;
            if (title && asset.title && !asset.title.toUpperCase().includes(title.toUpperCase())) return false;
            if (location) {
                if (!('parameters' in asset)) return false;
                if (!asset.parameters.location) return false;
                if (location != asset.parameters.location) return false;
            }
            if (category) filtered = asset.category == category;
            return filtered;
        },
    };
    let data = {
        booking_session: {
            attendees: [],
            course: {price: 0, title: '', currency: '', abv: ''},
            products: [],
            payment_method: '',
            notes: '',
            user_id: '',
            invoice_account: {
                order_number: '',
                same_address: false,
                delivery: {
                    delivery_name: '',
                    delivery_address: '',
                    contact_name: '',
                    contact_number: '',
                    contact_email: ''
                },
                billing: {
                    organisation: '',
                    billing_address: '',
                    contact_name: '',
                    contact_number: '',
                    contact_email: ''
                },
                training_start: '', deliver_date: '',
            }
        },
        login: {email: '', pwd: '', ajax_call: 1, action: 'login'},
        register: {name: '', email: '', ajax_call: 1, action: 'register'},
        login_error: 'hidden',
        login_error_msg: '',
        invoice_errors: [],
        logged_in: false,
        product_list: [],
        product_search: '',
        register_errors: []
    };
    let filters = {
        currencyFormat: function (number) {
            if (number) return parseFloat(number).toFixed(2);
            return '0.00';
        },
    };
    var app = new Vue({
        el: '#booking_widget',
        data: data,
        computed: {
            order_total: function () {
                let p = this.booking_session.products;
                let t = this.booking_session.attendees.length * this.booking_session.course.price * 1;
                for (let i = 0; i < p.length; i++) {
                    t += (p[i].price * 1 * p[i].quantity);
                }
                return t.toFixed(2);
            },
            get_products: function () {
                let title = this.product_search;
                if (title)
                    return this.product_list.filter(asset => (asset.name && asset.name.toUpperCase().includes(title.toUpperCase())));
                return this.product_list;
            },
        },
        methods: methods,
        filters: filters,
    });

    function getCookie(cname) {
        let name = cname + "=";
        let decodedCookie = decodeURIComponent(document.cookie);
        let ca = decodedCookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') {
                c = c.substring(1);
            }
            if (c.indexOf(name) == 0) {
                return c.substring(name.length, c.length);
            }
        }
        return "";
    }

    function setCookie(cname, cvalue, exdays) {
        let d = new Date();
        d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
        let expires = "expires=" + d.toUTCString();
        document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
    }

    function checkCookie() {
        let id = getCookie("sc_booking");
        if (id != "") {
            $('#temp_user_id').val(id);
        } else {
            let d = new Date();
            id = d.getTime();
            setCookie("sc_booking", id, 30);
            $('#temp_user_id').val(id);
            console.log('Snap,! It\'s empty: ' + id);
        }
    }

    $(document).ready(function () {
        checkCookie();
        $('#demobook2b form').submit(function (e) {
            e.preventDefault();
        });

        let href = window.location.origin + '/forms.php?pick_page=261&remove_assets=yes';
        $.get(href, function (data) {
            $("#forms_view_div").html(data);
        });

        $('#booking_widget').on('show.bs.modal', function (event) {
            let button = $(event.relatedTarget) // Button that triggered the modal
            let id = button.data('id') // Extract info from data-* attributes
            app.start_booking(id); // initiate booking widget
        })
    });
</script>

<div id="forms_view_div" style="display: none;">
    <?php
    // $cms_system_form_id = $attendee_form_id;
    // include("forms_view.php");
    // $cms_system_form_id = null;
    ?>
</div>
