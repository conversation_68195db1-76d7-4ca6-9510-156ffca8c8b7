<?php
if (!$_SESSION) {
    session_start();
}
if (isset($_SESSION['uid'])) {

    // ini_set('display_errors', 1);
    // ini_set('display_startup_errors', 1);
    // error_reporting(E_ALL);
    include("../../../engine/admin/inc/lib.inc.php");
    include("fix_zero_files.php");
    $front_web_url_file_loc = $front_website_url . '/' . front_header_file_location;

    $dbh = get_dbh();

    $student_id = $_GET['student_id'];

    ///now get the information
    list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake, $core_students_deleted, $core_student_application_ucas, $core_student_ucas_pass, $core_students_course_of_study_id, $core_students_course_audio) = get_core_students($student_id);

    /***now get other supporting documents(digital images)***/
    $documentsquery = "SELECT * FROM form_file_large_format WHERE (`db26064` = 'digital_image' OR `db26064` = 'image') AND db26071 != '' AND db31576!=0 and rel_id =" . $student_id . "  and usergroup = " . $_SESSION['usergroup'] . " ORDER BY db37353 ASC";

    dev_debug($documentsquery);
    $stmt = $dbh->prepare($documentsquery);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $documents_list = array();

    foreach ($results as $drow) {
        $documents_list[] = array(
            'no' => $drow['id'],
            'title' => $drow['db26060'],
            'material' => $drow['db26062'],
            'dimension' => $drow['db26762'],
            'link' => link_linkify($drow['db129755']),
            'description' => link_linkify($drow['db26063']),
            'upload_number' => $drow["db37353"],
            'medium' => $drow["db26064"],
            'year' => $drow["db26061"],
            'file_size' => $drow["db31576"],
        );
    }

    /***now get other supporting documents(video/audio)***/
    $movingquery = "SELECT * FROM form_file_large_format WHERE `db26064` = 'moving_image' and rel_id =" . $student_id . " AND db26071 != '' AND db31576!=0 and usergroup = " . $_SESSION['usergroup'] . " ORDER BY db37353 ASC";
    dev_debug($movingquery);
    $stmt = $dbh->prepare($movingquery);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $moving_list = array();

    foreach ($results as $mrow) {

        $moving_list[] = array(
            'no' => $mrow['id'],
            'title' => $mrow['db26060'],
            'medium' => $mrow['db26064'],
            'excerpt' => $mrow['db26066'],
            'original' => $mrow['db26065'],
            'link' => link_linkify($drow['db129755']),
            'description' => link_linkify($mrow['db26063']),
            'sound' => $mrow['db26068'],
            'color' => $mrow['db26069'],
            'type' => $mrow['db26067'],
            'upload_number' => $mrow["db37353"],
            'medium' => $mrow["db26064"],
            'year' => $mrow["db26061"],
            'file_size' => $mrow["db31576"],
            'registration' => $mrow['db26070']
        );
    }
} else {
    echo "Sorry you do not have access to this page.";
    exit();
}
?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en-US">
<head>
    <title>Support Documentation
        - <?php echo $core_students_first_name . " " . $core_students_surname . "  [" . $unique_id . "]"; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="robots" content="index,follow"/>
    <script type='text/javascript' src='<?php echo $front_web_url_file_loc; ?>/js/jquery-1.10.2.min.js'></script>
    <link rel="shortcut icon" href="<?php echo $front_web_url_file_loc; ?>/resources/img/favicon.ico"
          type="image/vnd.microsoft.icon"/>
    <link rel="icon" href="<?php echo $front_web_url_file_loc; ?>/resources/img/favicon.ico"
          type="image/vnd.microsoft.icon"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="stylesheet" href="<?php echo $front_web_url_file_loc; ?>/css/fontend.css" type="text/css" media="screen">
    <!-- Bootstrap core CSS -->
    <link href="<?= $front_web_url_file_loc ?>/resources/css/bootstrap.min.css" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,400,700,600,300"
          rel="stylesheet" type="text/css">
    <link href="<?= $front_web_url_file_loc ?>/resources/css/custom.css" rel="stylesheet">

    <!--REQUIRED IN HEADER FOR EACH CUSTOM HEADER START-->
    <script type="text/javascript" src="<?php echo $front_web_url_file_loc; ?>/js/sorttable.js"></script>
    <link rel="stylesheet" href="<?php echo $front_web_url_file_loc; ?>/css/sortable.css" type="text/css"
          media="screen">
    <link rel="stylesheet" href="<?php echo $front_website_url; ?>/css/style.css" type="text/css" media="screen">
    <script src="<?php echo $front_web_url_file_loc; ?>/js/datedropdown.js" type="text/javascript"
            charset="utf-8"></script>
    <script src="<?php echo $front_web_url_file_loc; ?>/js/gotonextpage_check.js" type="text/javascript"
            charset="utf-8"></script>
    <script src="<?php echo $front_web_url_file_loc; ?>/js/submit_appli_check.js" type="text/javascript"
            charset="utf-8"></script>
    <script src="/js/autologout_v1.js" type="text/javascript" charset="utf-8"></script>
    <!--REQUIRED IN HEADER FOR EACH CUSTOM HEADER END-->


</head>

<body>
<div class="container">
    <div class="row">
        <div class="col-xs-12 col-md-8 pull-left">
            <h1>Support Documentation</h1>
        </div>

        <div class="col-xs-12 col-md-4 pull-right" style="margin-top:20px;">
            <button type="button" class="btn btn-primary" onclick="printInformation()">Print Information</button>
        </div>
    </div>
    <br>
    <div class="row">
        <table class="table table-bordered keytable">
            <tr>
                <td width="30%" class="tdheadings">
                    <?php echo $unique_id; ?>
                </td>
                <td width="70%" align="right" class="tdheadings">
                    <?php echo $core_students_first_name . " " . $core_students_surname; ?>
                </td>
            </tr>
        </table>
    </div>

    <div class="row">
        <h1>Digital Images</h1>
        <table class="table" style="width:90% !important;">
            <?php $y = 1;
            foreach ($documents_list as $doc) { ?>
                <tr>
                    <td class="tdheadings">
                        Image<?php echo $y; ?> Thumbnail
                    </td>
                    <td>
                        [blank]
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Upload Number
                    </td>
                    <td>
                        <?php echo $doc['upload_number']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Medium
                    </td>
                    <td>
                        <?php echo $doc['medium']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Title
                    </td>
                    <td>
                        <?php echo $doc['title']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Year
                    </td>
                    <td>
                        <?php echo $doc['year']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Material
                    </td>
                    <td>
                        <?php echo $doc['material']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        File Size
                    </td>
                    <td>
                        <?php echo $doc['file_size'] . " KB"; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Dimension
                    </td>
                    <td>
                        <?php echo $doc['dimension']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        External Link
                    </td>
                    <td>
                        <?php echo $doc['link']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Description
                    </td>
                    <td>
                        <?php echo $doc['description']; ?>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td></td>
                </tr>
                <?php $y++;
            } ?>
        </table>
        <br>
        <h1>Video/Audio Documents</h1>
        <table class="table" style="width:90% !important;">
            <?php $z = 1;
            foreach ($moving_list as $mov) { ?>
                <tr>
                    <td class="tdheadings">
                        Upload Number
                    </td>
                    <td>
                        <?php echo $mov['upload_number']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Medium
                    </td>
                    <td>
                        <?php echo $mov['medium']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Title
                    </td>
                    <td>
                        <?php echo $mov['title']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Year
                    </td>
                    <td>
                        <?php echo $mov['year']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Sound
                    </td>
                    <td>
                        <?php echo $mov['sound']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Color
                    </td>
                    <td>
                        <?php echo $mov['color']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Registration
                    </td>
                    <td>
                        <?php echo $mov['registration']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Excerpt Length
                    </td>
                    <td>
                        <?php echo $mov['excerpt']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Original Length
                    </td>
                    <td>
                        <?php echo $mov['original']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        External Link
                    </td>
                    <td>
                        <?php echo $mov['link']; ?>
                    </td>
                </tr>
                <tr>
                    <td class="tdheadings">
                        Description
                    </td>
                    <td>
                        <?php echo $mov['description']; ?>
                    </td>
                </tr>
                <tr>
                    <td></td>
                    <td></td>
                </tr>
                <?php $z++;
            } ?>
        </table>
    </div>
</div>
</body>
</html>
<style type="text/css">
    .tdheadings {
        font-weight: bold;
    }

    .keytable {
        width: 90% !important;
    }
</style>
<script>
    function printInformation() {
        window.print();
    }
</script>