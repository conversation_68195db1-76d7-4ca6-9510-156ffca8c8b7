/* ==========================================================================
   =00 Custom CSS
   ========================================================================== */
/*
   Details:   Custom CSS
   Origional Authors: <AUTHORS>
   (!)Note:   Use 'Find' with '=**' to jump to numbered sections
              e.g. type '=01' to jump to 'Bootstrap overrides' section.

   01 . . . . Bootstrap overrides
   02 . . . . Global
   03 . . . . Top bar
   04 . . . . 3x homepage boxes
   05 . . . . Sidebar panels
   06 . . . . Forms
   07 . . . . Links
   08 . . . . Buttons
   09 . . . . Typography
   10 . . . . Lists e.g. 'Quicklinks' sidebar menu
   11 . . . . Footer
   12 . . . . Application checklist page
   13 . . . . Media query - tablet viewports
   14 . . . . Media query - mobile viewports
   15 . . . . Media query - desktop (small) viewports

/* ==========================================================================
   =01 Bootstrap overrides
   ========================================================================== */

/* Remove border-radius globally */
.img-rounded,
.img-thumbnail,
code,
kbd,
pre,
.form-control,
.input-sm,
.form-group-sm .form-control,
.input-lg,
.form-group-lg .form-control,
.btn,
.btn-lg,
.btn-group-lg > .btn,
.btn-sm,
.btn-group-sm > .btn,
.btn-xs,
.btn-group-xs > .btn,
.dropdown-menu,
.btn-group-vertical > .btn:first-child:not(:last-child),
.btn-group-vertical > .btn:last-child:not(:first-child),
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn,
.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn,
.input-group-addon,
.input-group-addon.input-sm,
.input-group-addon.input-lg,
.nav-tabs > li > a,
.nav-tabs.nav-justified > li > a,
.nav-pills > li > a,
.nav-tabs-justified > li > a,
.navbar-toggle,
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu,
.breadcrumb,
.pagination,
.container .jumbotron,
.container-fluid .jumbotron,
.thumbnail,
.alert,
.progress,
.panel,
.panel-group .panel,
.well,
.well-lg,
.well-sm,
.modal-content,
.tooltip-inner,
.popover {
    -webkit-border-radius: 0;
        -moz-border-radius: 0;
            border-radius: 0;
}

/*
 * Increase base font-size and line-height globally
 * 'Typography' section (increase base font-size globally):
 * - @font-size-base: 16px; (up from 14px)
 * - @line-height-base: 1.63; (up from 1.428571429)
 */
body {
	font-family: 'Open Sans',sans-serif;
    font-size: 16px;
    line-height: 1.63;
}

.img-thumbnail {
    line-height: 1.63;
}

hr {
    margin-top: 26px;
    margin-bottom: 26px;
}

h1,
.h1,
h2,
.h2,
h3,
.h3 {
    margin-top: 26px;
    margin-bottom: 13px;
}

h4,
.h4,
h5,
.h5,
h6,
.h6 {
    margin-top: 13px;
    margin-bottom: 13px;
}

h1,
.h1 {
    font-size: 41px;
}

h2,
.h2 {
    font-size: 34px;
}

h3,
.h3 {
    font-size: 28px;
}

h4,
.h4 {
    font-size: 20px;
}

h5,
.h5 {
    font-size: 16px;
}

h6,
.h6 {
    font-size: 14px;
}

p {
    margin: 0 0 13px;
}

.lead {
    margin-bottom: 26px;
    font-size: 18px;
}

small,
.small {
    font-size: 87%;
}
.terms-legal  a:link, a:visited{ text-decoration:underline; color:white}

.page-header {
    padding-bottom: 12px;
    margin: 52px 0 26px;
}

ul,
ol {
    margin-bottom: 13px;
}

dl {
    margin-bottom: 26px;
}

dt,
dd {
    line-height: 1.63;
}

blockquote {
    padding: 13px 26px;
    margin: 0 0 26px;
    font-size: 20px;
}

blockquote footer,
blockquote small,
blockquote .small {
    line-height: 1.63;
}

address {
    margin-bottom: 26px;
    line-height: 1.63;
}

pre {
    padding: 12.5px;
    margin: 0 0 13px;
    font-size: 15px;
    line-height: 1.63;
}

.table {
    margin-bottom: 26px;
}
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
    line-height: 1.63;
}

legend {
    margin-bottom: 26px;
    font-size: 24px;
    }

output {
    font-size: 16px;
    line-height: 1.63;
}

.form-control {
    height: 40px;
    font-size: 16px;
    line-height: 1.63;
}

.baseline {
    border-bottom: 1px dashed #ccc;
    padding: 5px 0;
}

@media screen and (-webkit-min-device-pixel-ratio: 0) {

    input[type="date"].form-control,
    input[type="time"].form-control,
    input[type="datetime-local"].form-control,
    input[type="month"].form-control {
        line-height: 40px;
    }
    input[type="date"].input-sm,
    input[type="time"].input-sm,
    input[type="datetime-local"].input-sm,
    input[type="month"].input-sm,
    .input-group-sm input[type="date"],
    .input-group-sm input[type="time"],
    .input-group-sm input[type="datetime-local"],
    .input-group-sm input[type="month"] {
        line-height: 33px;
    }
    input[type="date"].input-lg,
    input[type="time"].input-lg,
    input[type="datetime-local"].input-lg,
    input[type="month"].input-lg,
    .input-group-lg input[type="date"],
    .input-group-lg input[type="time"],
    .input-group-lg input[type="datetime-local"],
    .input-group-lg input[type="month"] {
        line-height: 49px;
    }

}

/* ==========================================================================
   =02 Global
   ========================================================================== */

body {
    /*
    font-size: 16px;
    */
    line-height: 1.63;
    background: #fff;
}

#wrapper {
    background: none;
}

.portal {
    background: #ffffff;
}

.panel {
    margin-bottom: 30px;
}

.splash,
.sidebar {
    margin-top: 30px;
}

hr {
    border-top: 1px solid #bbb;
}

ul {
    margin-top: 30px;
    margin-bottom: 30px;
}

/* ==========================================================================
   =03 Top bar
   ========================================================================== */

   #topbar {
    background-color: #fff;
    display: block;
    height: 100px;
    padding: 20px 0 0 0;
    position: relative;
    z-index: 1001;
}

#topbar a{ font-size:12px; color:#000;}
#topbar .backbutton{padding: 5px; text-align: center;}
#topbar h2{ font-size:2em; font-weight:bold; padding:10px; margin:0; font-family: 'Open Sans',sans-serif;}
#topbar h3{ font-size:1.4px; padding:10px 10px 0 10px; color:#fff} 
#topbar span{ margin-right:10px} 

.topbar_base {
    background: #f2f2f2;
}

.topbar_base .breadcrumb{
    padding-left: 0px;
    margin: 0;
    font-family: 'Gill Sans W01 Medium','Open Sans';
	}


.head-top {
    background-color: #828282;
}

.topbar-wrap {
    background-color: #fff;
    display: block;
    position: relative;
    -webkit-box-shadow: 0 3px 5px rgba(0,0,0,0.25);
       -moz-box-shadow: 0 3px 5px rgba(0,0,0,0.25);
            box-shadow: 0 3px 5px rgba(0,0,0,0.25);
    z-index: 1000;
}

.topbar .logo img.esms-logo-left {
    width: 191px;
    height: auto;
    float: left;
}
.topbar .logo img.esms-logo-right {
    width: 129px;
    height: auto;
    float: left;
}
/* ==========================================================================
   =04 3x homepage boxes
   ========================================================================== */

   .box > .icon > .info > .more a {
    font-family: 'Open Sans',sans-serif; !important;
    font-size: 16px;
    color: #fff;
    line-height: 1.63;
    text-transform: none;
    text-decoration: none;
}

.box > .icon > .info > h3.title {
    font-family: 'Gill Sans W01 Medium', 'Open Sans',sans-serif; !important;
    font-size: 32px;
    color: #333;
    font-weight: normal;
    margin-top: 10px;
}

.box > .icon > .info > p {
    font-family: 'Open Sans',sans-serif; !important;
    font-size: 16px;
    color: #333;
    line-height: 1.64em;
    margin: 20px 0;
}

.box > .icon > .info {
    margin-top: 0px;
    background: none;
    border: none;
    padding: 15px 0 10px 0;
}

.box > .icon > .info:hover a {
    background-color: #828282 !important;
    border-color: #b2b2b2 !important;
}

.box > .icon > .info:hover h3.title {
    color: #333;
}

.box > .icon > .info:hover a:hover {
    background-color: #565656 !important;
    border-color: #b2b2b2 !important;
}

.box > .icon:hover > .info {
    background: none;
    border-color: transparent;
    color: #fff;
}

.box > .icon > .info i {
    color: #1c3f94;
}

.box {
    display: block;
    background-color: #fdfdfd;
    border: 1px solid #ccc;
    margin: 0 0 15px 0;
    padding: 0 20px;
    -webkit-border-radius: 0px;
       -moz-border-radius: 0px;
            border-radius: 0px;
    -webkit-transition: all 0.2s ease-out 0s;
       -moz-transition: all 0.2s ease-out 0s;
         -o-transition: all 0.2s ease-out 0s;
            transition: all 0.2s ease-out 0s;
    -webkit-backface-visibility: hidden;
       -moz-backface-visibility: hidden;
        -ms-backface-visibility: hidden;
            backface-visibility: hidden;
}

/*
.box:hover,
.box:active,
.box:focus {
    background: #fff;
    border-color: #aaa;
    -webkit-box-shadow: 0px 0px 20px 0px rgba(0,0,0,0.2);
       -moz-box-shadow: 0px 0px 20px 0px rgba(0,0,0,0.2);
            box-shadow: 0px 0px 20px 0px rgba(0,0,0,0.2);
}
*/

/* ==========================================================================
   =05 Sidebar panels
   ========================================================================== */

.panel {
    background: url(../img/sidebar_border.png) top left no-repeat;
    padding-top: 5px;
    border-radius: 0px;
}

.panel-body {
    background: #fbfbfb;
    padding: 15px;
}

.panel-body h5 {
    margin-top: 0;
}

.panel-default>.panel-heading {
    color: #333;
    background: #fbfbfb;
    border-color: #ddd;
}

.panel-heading {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    padding: 10px 15px 10px;
    border-bottom: 1px solid transparent;
}

.panel-heading h4 {
    padding-top: 0;
    margin-top: 5px;
    margin-bottom: 8px;
}

ul.faq {
    list-style-type: none;
    margin: 0 0 12px 0;
    padding: 0;
}

ul.faq li {
    margin: 0 0 18px 0;
    line-height: 1.3;
}

ul.faq li a.question {
    font-family: 'Gill Sans W01 Medium',"Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 26px;
    line-height: 1.1;
    color: #1c3f94;
}

ul.faq li a.question:hover,
ul.faq li a.question:active,
ul.faq li a.question:focus {
    color: #255079;
}

ul.faq li span {
    display: block;
    margin-top: 5px;
}

/* ==========================================================================
   =06 Forms
   ========================================================================== */

input.radios {
    margin-top: 12px !important;
}

input.radios:not(:first-child) {
    margin-left: 25px;
}

form {
    margin-bottom: 50px;
}

input#opc_input {
    font-size: 16px;
    padding-left: 17px;
    color: #444;
    background: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 0px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    box-shadow: inset 0 1px 1px rgba(0,0,0,0.075);
    -webkit-transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    transition: border-color ease-in-out .15s,box-shadow ease-in-out .15s;
    width: 100%;
}

button#opc_button {
    border-radius: none;
    color: #fff;
    -webkit-transition: all .25s linear;
       -moz-transition: all .25s linear;
        -ms-transition: all .25s linear;
         -o-transition: all .25s linear;
    transition: all .25s linear;
    white-space: normal;
    font-size: 16px;
    line-height: 1.63;
    display: block;
    margin-bottom: 0;
    margin-top: 8px;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 16px;
    border-radius: 0;
    -webkit-user-select: none;
       -moz-user-select: none;
        -ms-user-select: none;
    user-select: none;
}

.slide_list {
    width: 100% !important;
}

.form-horizontal .control-label,
.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
    padding-top: 7px !important;
    margin-top: 0;
    margin-bottom: 0;
}

.required:after {
    content: "* ";
    color: #333 !important;
    font-size: 16px !important;
}

.alert-warning {
    background-color: #fcf8e3;
    border-color: #faebcc;
    color: #8a6d3b !important;
}

.alert-warning i {
    color: #8a6d3b;
}

.hotspot {
    margin-right: 0 !important;
}

.hotspot,
.required i {
    color: #1c3f94 !important;
}

/* ==========================================================================
   =07 Links
   ========================================================================== */

a,
a img {
    -webkit-backface-visibility: hidden;
        -moz-backface-visibility: hidden;
            backface-visibility: hidden;
    -webkit-transition: all 0.25s linear;
        -moz-transition: all 0.25s linear;
        -o-transition: all 0.25s linear;
        -ms-transition: all 0.25s linear;
            transition: all 0.25s linear;
    opacity: 1.0;
}

a:hover img,
a:active img,
a:focus img {
    opacity: 0.8;
}

a, a h1, a h2, a h3, a h4, a h5, a h6, .panel a {
    color: #1c3f94;
    -webkit-transition: all .25s linear;
    -moz-transition: all .25s linear;
    -o-transition: all .25s linear;
    -ms-transition: all .25s linear;
    transition: all .25s linear;
}

a:link,
a:visited {
    color: #363636;
}

a:hover,
a:active,
a:focus {
    color: #255079;
}

/* ==========================================================================
   =08 Buttons
   ========================================================================== */

.btn-success,
#topbar .btn-success {
    background-color: #4CAF50;
    border-color: #4CAF50;
    color: #fff !important;
}

.btn-success:hover,
.btn-success:active,
.btn-success:focus,
#topbar .btn-success:hover,
#topbar .btn-success:active,
#topbar .btn-success:focus {
    background-color: #4CAF50;
    border-color: #4CAF50;
    color: #fff !important;
}

.btn-custom {
    background-color: #333333;
    color: #fff !important;
}

.btn-info {
    color: #fff !important;
    background-color: #333333;
}

.btn-info:hover,
.btn-info:active,
.btn-info:focus {
    background-color: #333333;
    border-color: #333333;
    color: #fff !important;
}

.btn-custom:hover,
.btn-custom:active,
.btn-custom:focus {
    background-color: #333333;
    border-color: #333333;
    color: #fff !important;
}

.btn-success,
.btn-primary,
.btn-info,
.btn-danger {
    color: #fff !important;
}

.btn-info:hover,
.btn-info:active,
.btn-info:focus {
    color: #fff;
}

.btn-custom a {
    color: #fff;
}

.btn-custom a:hover {
    color: #fff;
    text-decoration: none;
}

.btn-primary {
    background-color: #1c3f94;
    border-color: #b2b2b2;
    color: #fff !important;
}

.btn-primary:hover,
.btn-primary:active,
.btn-primary:focus {
    background-color: #2b5c8c;
    border-color: #255079;
}

.btn-default,
#topbar .btn-default {
    background-color: #4CAF50;
    border-color: #4CAF50;
    color: #fff !important;
}

.btn-default:hover,
.btn-default:active,
.btn-default:focus,
#topbar .btn-default:hover,
#topbar .btn-default:active,
#topbar .btn-default:focus {
    background-color: #4CAF50;
    border-color: #4CAF50;
    color: #fff !important;
}

.btn,
#topbar .btn {
    color: #fff;
    -webkit-transition: all .25s linear;
        -moz-transition: all .25s linear;
        -ms-transition: all .25s linear;
        -o-transition: all .25s linear;
    transition: all .25s linear;
    white-space: normal;
    line-height: 1.63;
    display: inline-block;
    margin-top: 8px;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    margin-bottom: 5px;
    height: 44px;
    border-radius: 4px;
    padding: 9px 15px;
    -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
    user-select: none;
}

.sidebar .btn {
    margin-top: 0;
}

button.update {
    margin-bottom: 25px;
}

/* ==========================================================================
   =09 Typography
   ========================================================================== */

   /* Load fonts */
   @font-face {
    font-family: 'pt_sansregular';
    src: url('../fonts/PTS55F-webfont.eot');
    src: url('../fonts/PTS55F-webfont.eot?#iefix') format('embedded-opentype'),
    url('../fonts/PTS55F-webfont.woff') format('woff'),
    url('../fonts/PTS55F-webfont.ttf') format('truetype'),
    url('../fonts/PTS55F-webfont.svg#pt_sansregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'pt_sansbold';
    src: url('../fonts/PTS75F-webfont.eot');
    src: url('../fonts/PTS75F-webfont.eot?#iefix') format('embedded-opentype'),
    url('../fonts/PTS75F-webfont.woff') format('woff'),
    url('../fonts/PTS75F-webfont.ttf') format('truetype'),
    url('../fonts/PTS75F-webfont.svg#pt_sansbold') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'pt_sansitalic';
    src: url('../fonts/PTS56F-webfont.eot');
    src: url('../fonts/PTS56F-webfont.eot?#iefix') format('embedded-opentype'),
    url('../fonts/PTS56F-webfont.woff') format('woff'),
    url('../fonts/PTS56F-webfont.ttf') format('truetype'),
    url('../fonts/PTS56F-webfont.svg#pt_sansitalic') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'pt_sansbold_italic';
    src: url('../fonts/PTS76F-webfont.eot');
    src: url('../fonts/PTS76F-webfont.eot?#iefix') format('embedded-opentype'),
    url('../fonts/PTS76F-webfont.woff') format('woff'),
    url('../fonts/PTS76F-webfont.ttf') format('truetype'),
    url('../fonts/PTS76F-webfont.svg#pt_sansbold_italic') format('svg');
    font-weight: normal;
    font-style: normal;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Gill Sans W01 Medium', 'Open Sans',sans-serif;;
}

p+h1, p+h2, p+h3, p+h4, p+h5, p+h6,
ul+h1, ul+h2, ul+h3, ul+h4, ul+h5, ul+h6 {
    margin-top: 40px;
}
h1{ margin-top: 0; }
span.main,h1.main {
    display: block;
    background: #828282;
    color: #e6f6ce;
    margin-top: 0;
    padding: 13px 20px 22px 20px;
    position: relative;
    font-family: 'Gill Sans W01 Medium','Open Sans',sans-serif;;
    font-weight: normal;
    margin-bottom: 20px;
}

.alert h3 {
    margin-top: 5px;
}

p {
    font-size: 16px;
    line-height: 1.63;
}
.lead {
    clear: both;
    border-left: 8px solid #828282;
    font-family: 'Open Sans',sans-serif;
    font-size: 31px;
    line-height: 1.4;
    margin-top: 40px;
    margin-bottom: 40px;
    padding-left: 20px;
}

h1, h2, h3, h4, h5, h6 {
    margin-bottom: 20px;
    color: #444;
}

h1, .h1 {
    font-size: 40px;
}

h2, .h2 {
    font-size: 33px;
}

h3, .h3 {
    font-size: 26px;
}

.splash h3 {
    margin-bottom: 40px;
    margin-top: 10px;
}

.sidebar h3 {
    margin-bottom: 17px;
}

h4, .h4 {
    font-size: 25px;
}

h5, .h5 {
    font-size: 20px;
}

.status h5 {
    /*
    font-size: 20px;
    */
    line-height: 1.23;
    margin-top: 5px;
    margin-bottom: 10px;
    text-align: center;
}

/* 'Complete' */
.status b {
    font-family: 'Open Sans',sans-serif;
    font-weight: normal;
    display: block;
    margin-top: 15px;
    margin-bottom: 20px;
}

/* Percentage */
.status b span {
    font-size: 65px;
    line-height: 1.0;
    font-family: 'Open Sans',sans-serif;
    display: block;
}

/* Circle styles */
 .circle {
    position: relative;
    display: block;
    margin: 2em 0;
    background-color: transparent;
    color: #222;
    text-align: center;
}

.circle:after {
    display: block;
    padding-bottom: 100%;
    width: 100%;
    height: 0;
    border-radius: 50%;
    background-color: #828282;
    content: "";
}

.circle__inner {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.circle__wrapper {
    display: table;
    width: 100%;
    height: 100%;
}

.circle__content {
    display: table-cell;
    color: #fff;
    /*
    padding: 1em;
    */
    vertical-align: middle;
    font-size: 60px;
    line-height: 1.0;
    font-family: 'Open Sans',sans-serif;
}

.circle__content span {
    font-family: 'Open Sans',sans-serif;
}

h6, .h6 {
    font-size: 18px;
}

figure {
    margin-bottom: 25px;
}

figure.pull-left {
    margin-right: 15px;
}

figure.pull-right {
    margin-left: 15px;
}

figure figcaption {
    font-family: 'Open Sans',sans-serif;
}

blockquote,
.quote {
    background: url(../img/quotes.png) 0 13px no-repeat;
    padding-left: 110px;
    border-left: none;
    min-height: 90px;
    font-family: 'Open Sans',sans-serif;
    font-size: 31px;
    margin-top: 50px;
    margin-bottom: 50px;
}

blockquote em,
.quote em {
    display: block;
    margin-top: 10px;
    text-align: right;
    font-size: 16px;
}

blockquote strong,
.quote strong {
    font-family: 'Open Sans',sans-serif;
}

/* ==========================================================================
   =10 Lists e.g. 'Quicklinks' sidebar menu
   ========================================================================== */

   ul {
    padding-left: 15px;
    margin-left: 0;
    list-style-type: square;
}

ul li {
    margin: 0 0 10px 0;
}

ul.list-group {
    border: 0;
    margin: 0;
    box-shadow: none;
    background: #fbfbfb;
    padding-top: 5px;
    background: url(../img/sidebar_border.png) top left no-repeat;
    border-bottom: 1px solid #ccc;
    font-family: 'Gill Sans W01 Medium';
}

ul.list-group li {
    background: #fbfbfb;
    border-bottom: none;
    margin-bottom: 0;
    list-style: none;
    list-style-image: none;
    position: relative;
    display: block;
}

ul.list-group li h4 {
    margin-bottom: 0;
    margin-top: 0;
    padding: 15px;
}

ul.list-group li h4.list-group-item-heading {
    margin-bottom: 0;
    margin-top: -5px;
    padding: 0 15px;
}


ul.list-group li a {
    color: #333333;
	text-transform: capitalize;
    display: block;
    padding: 15px;
    position: relative;
    -webkit-transition: all .15s linear;
       -moz-transition: all .15s linear;
        -ms-transition: all .15s linear;
         -o-transition: all .15s linear;
            transition: all .15s linear;
}

ul.list-group li a:hover,
ul.list-group li a:active,
ul.list-group li a:focus {
    text-decoration: none;
    background-color: #333333;
    background-image: none;
    color: #fff;
}

ul.list-group li.active {
    background-color: #333333;
    /* border-color: #ccc; */
    border-color: #333333;
}

ul.list-group li.active a {
    color: #fff;
    font-weight: normal;
    background-color: #333333;
    background-image: none;
}

.list-group-item {
    padding: 0;
}

.list-group-item h4,
.list-group-item p {
    padding: 15px 15px 15px 15px;
}

.list-group-item h4 {
    padding-bottom: 0px;
    padding-top: 0;
}

.circle-text {
    width: 2.3em;
    float: left;
    clear: both;
	margin: -15px 10px 0px 15px
}

.circle-text span {
    float: left;
    width: 100%;
    padding-top: 37%;
    margin-left: 1px;
    text-align: center;
    color: #333;
    font-weight: bold;
}

.list-group_home {
    line-height: 1.63;
    font-size: 16px;
}

.list-group_home .list-group-item {
    padding-top: 20px;
    padding-bottom: 15px;
}

.list-group-item p {
    padding-bottom: 0px;
}

.list-group-item-text {
    margin-bottom: 0;
    line-height: 1.63;
}

.list-group-item:first-child {
    border-top-right-radius: 0px;
    border-top-left-radius: 0px;
}

.list-group-item:last-child {
    margin-bottom: 0;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: 0px;
}

.alert,
.alert-default {
    color: rgb(51, 51, 51);
    font-size: 16px;
    line-height: 1.63;
	background:#f2f2f2;
}

.alert-info {
    color: #1c3f94;
}

.box .widget-actions {
    margin: 0 -20px 0 -20px;
    padding: 10px 20px 20px 20px;
}

.box .widget-actions form {
    margin-top: 10px;
    padding-top: 0;
}

.box .widget-actions .btn {
    margin-top: 0px;
}

.widget-chat form>div {
    margin: -40px 100px 0 0;
}

/* ==========================================================================
   =11 Footer
   ========================================================================== */

.footer-wrap {
    background: #333333;
    border-top: 5px solid #1a1a1a;
    padding-top: 25px;
    margin-top: 25px;
	color:white;
}

.footer-links {
    background: transparent;
    font-size: 12px;
    color: #555555;
    margin: 0;
    padding: 0;
	text-align:center;
}

ul.footer-links > li {
    display: inline;
    border-right: none;
    padding: 0 15px;
}

.footer-links a {
  color: #555555;
}

.footer-links a:hover,
.footer-links a:active,
.footer-links a:focus {
    color: #fff;
}

.accreditations {background: #1a1a1a; padding: 20px; margin: 20px 0;}

.accreditations .img-responsive {
    margin: 0 auto;
}

/* ==========================================================================
   =12 Application checklist page
   ========================================================================== */

.progress-bar-custom {
    position: relative;
    background-color: #1c3f94;
	/* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#1c3f94+0,2a7270+49,00b2b0+100 */
	background: #1c3f94; /* Old browsers */
	background: -moz-linear-gradient(top, #ec532a 0%, #e21737 49%, #e21737 100%); /* FF3.6-15 */
	background: -webkit-linear-gradient(top, #ec532a 0%,#e21737 49%,#e21737 100%); /* Chrome10-25,Safari5.1-6 */
	background: linear-gradient(to bottom, #ec532a 0%,#e21737 49%,#e21737 100%); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#1c3f94', endColorstr='#1c3f94',GradientType=0 ); /* IE6-9 */
}

.progress {
    background-color: #fff;
    border-radius: 0px;
    height: 30px;
    -webkit-box-shadow: inset 0 1px 1px rgba(0,0,0,0.1);
    box-shadow: inset 0 1px 1px rgba(0,0,0,0.3);
}

.progress-bar_REMOVED:after {
    content: "";
    position: absolute;
    top: 0; left: 0; bottom: -20px; right: -20px;
    background-image: linear-gradient(
      -45deg,
      rgba(255, 255, 255, .2) 25%,
      transparent 25%,
      transparent 50%,
      rgba(255, 255, 255, .2) 50%,
      rgba(255, 255, 255, .2) 75%,
      transparent 75%,
      transparent
    );
    z-index: 1;
    background-size: 50px 50px;
    animation: move 2s linear infinite;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    border-top-left-radius: 20px;
    border-bottom-left-radius: 20px;
    overflow: hidden;
}

.circle-text:after {
    background: #c6c8cc;
}

.circle-text.completed:after {
    background: #828282;
}

.completed i {
    margin-top: 4px;
}

.label,
.label:link,
.label:visited {
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0;
    font-size: 10px;
    line-height: 1.63;
    display: inline-block;
    margin-bottom: 0;
    margin-top: 0px;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 0px 5px;
    background-color: #ff0000;
    border-color: #b2b2b2;
}

.label:hover,
.label:active,
.label:focus,
.label-default {
    background-color: #828282;
    border-color: #b2b2b2;
}

.label-default[href]:hover,
.label-default[href]:focus {
    background-color: #fcb813;
    border-color: #436711;
}

.alert {
    border-radius: 0px;
}

.splash {
    background: #fbfbfb;
    border: 1px solid #ddd;
    border-bottom: 7px solid #e6e6e6;
    padding: 15px 15px 0px 30px;
    margin-left: 0;
    margin-right: 0;
	
}

.list-group-item-heading {
    margin-top: -5px;
}

/* ==========================================================================
   =13 Media query - tablet viewports
   ========================================================================== */

@media only screen and (min-width: 768px) {

    .splash {
        margin-top: 0px;
        margin-bottom: 0;
    }

    .sidebar {
        margin-bottom: 0px;
    }

    .sidebar .row {
        margin-top: 30px;
    }

    .portal {
        margin-top: 30px;
    }

    .panel {
        margin-bottom: 0;
    }

}

/* ==========================================================================
   =14 Media query - mobile viewports
   ========================================================================== */

@media (max-width: 767px) {

    .box > .icon > .info > h3.title {
        font-size: 28px;
    }

    .lead {
        font-size: 24px;
    }

    .table-responsive {
        margin-bottom: 19.5px;
    }

    .splash {
        margin-top: 0px;
        margin-bottom: 0;
    }

    .sidebar {
        margin-bottom: 15px;
    }

    .sidebar .row {
        margin-top: 15px;
    }

    .portal {
        margin-top: 0px;
    }

    .panel {
        margin-bottom: 0;
    }

    p.lead {
        border-left: 5px solid #828282;
        font-size: 24px;
        margin-top: 25px;
        margin-bottom: 25px;
        padding-left: 14px;
    }

    span.main,h1.main {
        display: block;
        padding: 7px 15px 12px 15px;
        font-size: 26px;
    }

    h1 {
        font-size: 34px;
    }

    h2 {
        font-size: 27px;
    }

    h3 {
        font-size: 27px;
    }

    h4 {
        font-size: 22px;
    }

    h5 {
        font-size: 19px;
    }

    h6 {
        font-size: 17px;
    }

    h3 {
        font-size: 26px;
    }

    figure {
        float: none;
        margin-left: auto;
        margin-right: auto;
        text-align: center;
        display: block;
    }

    blockquote,
    .quote {
        background: url(../img/quotes_small.png) top left no-repeat;
        font-size: 23px;
        padding-left: 0px;
        padding-top: 50px;
    }

small,
.small {
	text-align:center;
}

}

/* ==========================================================================
   =15 Media query - desktop (small) viewports
   ========================================================================== */

@media only screen and (min-width: 992px) {

    .sidebar {
        margin-top: 0px;
    }

    .sidebar .row {
        margin-top: 30px;
    }

    .portal {
        margin-top: 10px;
    }

    .splash {
        margin-top: 30px;
    }

    input#opc_input {
        width: auto;
    }

    button#opc_button {
        display: inline-block;
        margin-top: 4px;
        margin-left: 3px;
    }

}


#go_to_prev,#continue_to_next{border-radius: none;
    color: #fff;
    -webkit-transition: all .25s linear;
    -moz-transition: all .25s linear;
    -ms-transition: all .25s linear;
    -o-transition: all .25s linear;
    transition: all .25s linear;
    white-space: normal;
    font-size: 16px;
    line-height: 1.63;
    display: inline-block;
    margin-bottom: 0;
    margin-top: 8px;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 6px 12px;
    font-size: 16px;
    border-radius: 0;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
background-color: #828282;
    border-color: #b2b2b2;}


#go_to_prev:hover,#continue_to_next:hover{background-color: #527d16; border-color: #436711;}

#main_body{
    font-family: "Open Sans" !important;
}

.li_push_left{
    margin-left: -14px;
}

.tick_done {
    color: #ffffff !important;
    padding-right: 10px;
}

.table-responsive {
    font-family: "Open Sans";
}
