<?php
//pullin the file that holds all the queries 
include("form_settings.php");
?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" dir="ltr" lang="en-US">
<head>
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta name="robots" content="index,follow"/>
    <script type='text/javascript' src='<?php echo $front_web_url_file_loc; ?>/js/jquery-1.10.2.min.js'></script>
    <link rel="shortcut icon" href="<?php echo $front_web_url_file_loc; ?>/resources/img/favicon.ico"
          type="image/vnd.microsoft.icon"/>
    <link rel="icon" href="<?php echo $front_web_url_file_loc; ?>/resources/img/favicon.ico"
          type="image/vnd.microsoft.icon"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <link rel="stylesheet" href="<?php echo $front_web_url_file_loc; ?>/css/fontend.css" type="text/css" media="screen">
    <!-- Bootstrap core CSS -->
    <link href="<?= $front_web_url_file_loc ?>/resources/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://netdna.bootstrapcdn.com/font-awesome/4.0.3/css/font-awesome.min.css" rel="stylesheet">

    <!-- Custom styles for this template -->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300italic,400italic,600italic,700italic,400,700,600,300"
          rel="stylesheet" type="text/css">
    <link href="https://fast.fonts.net/cssapi/2b8a9979-38a0-4375-8c3f-4bfc73f3bacf.css" rel="stylesheet"
          type="text/css">
    <link href="<?= $front_web_url_file_loc ?>/resources/css/test_only_gill_sans_font_face.css" rel="stylesheet">
    <link href="<?= $front_web_url_file_loc ?>/resources/css/custom.css" rel="stylesheet">
    <link href="<?= $front_web_url_file_loc ?>/resources/css/custom_new.css" rel="stylesheet">

    <!--REQUIRED IN HEADER FOR EACH CUSTOM HEADER START-->
    <script type="text/javascript" src="<?php echo $front_web_url_file_loc; ?>/js/sorttable.js"></script>
    <link rel="stylesheet" href="<?php echo $front_web_url_file_loc; ?>/css/sortable.css" type="text/css"
          media="screen">
    <link rel="stylesheet" href="<?php echo $front_website_url; ?>/css/style.css" type="text/css" media="screen">
    <script src="<?php echo $front_web_url_file_loc; ?>/js/datedropdown.js" type="text/javascript"
            charset="utf-8"></script>
    <script src="<?php echo $front_web_url_file_loc; ?>/js/gotonextpage_check.js" type="text/javascript"
            charset="utf-8"></script>
    <script src="<?php echo $front_web_url_file_loc; ?>/js/submit_appli_check.js" type="text/javascript"
            charset="utf-8"></script>
    <script src="/js/autologout_v1.js" type="text/javascript" charset="utf-8"></script>
    <!--REQUIRED IN HEADER FOR EACH CUSTOM HEADER END-->

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=UA-24817220-2"></script>

    <script>

        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());


        gtag('config', 'UA-24817220-2');

    </script>

</head>

<body>
<style type="text/css">
    .required_field {
        padding: 5px;
        background-color: #f5f5f5;
        margin: 5px;
        font-size: 11px;
        font-family: 'Open Sans', sans-serif;
    }

    .required_field .hidable {
        display: none;
    }

    .orange_text {
        filter: grayscale(100%);
    }

    .warning {
        width: 100% !important;
        color: #4a4a4a;
        background-color: #f5f5f5;
    }

    .alert-info {
        background-image: -webkit-linear-gradient(top, #d9edf7 0, #b9def0 100%);
        background-image: -o-linear-gradient(top, #d9edf7 0, #b9def0 100%);
        background-image: linear-gradient(to bottom, #f5f5f5 0, #f5f5f5 100%);
        background-repeat: repeat-x;
        filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffd9edf7', endColorstr='#ffb9def0', GradientType=0);
        border-color: #f5f5f5;
    }

    .alert {
        text-shadow: 0 1px 0 rgba(255, 255, 255, 0.2);
        -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 1px 2px rgba(0, 0, 0, 0.05);
        box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.25), 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .alert-info {
        background-color: #f5f5f5;
        border-color: #bce8f1;
        color: #333;
    }

    .esms-logo-left {
        width: 191px;
        height: 53px;
    }

    @media (max-width: 768px) {
        .esms-logo-left {
            width: 120px;
            height: auto;
            margin: 0;
            padding-left: 0;
        }

        .esms-logo-right {
            display: none;
        }

        .header_items {
            position: relative;
        }

        #topbar {
            text-align: center;
            background: transparent;
            height: 70px;
        }
    }

</style>

<div id="everything" class="container-fluid">

    <div class="topbar-wrap">

        <!--the container below to be replaced with an include of slimmed header file -->
        <div id="everything" class="container header_items">
            <a href="#" class="mobile_menu_trigger">
          <span class="menu_icon" aria-hidden="true">
            <span></span>
            <span></span>
            <span></span>
          </span>
                Menu
            </a>
            <div id="topbar">

                <a href="<?= website_url_applicant ?>" title="Home" class="logo-sec">
                    <img class="esms-logo-left" src="<?php echo $front_web_url_file_loc; ?>/resources/img/logo.png"
                         alt="Home">
                    <img class="esms-logo-right"
                         src="<?php echo $front_web_url_file_loc; ?>/resources/img/<EMAIL>" width="129"
                         height="53">
                </a>


            </div><!-- .row -->
        </div>

        <div class="topbar_base">
            <div class="container">
                <div class="col-md-12 breadcrumb">
                    <a href="https://<?= $schools_website_address ?>">ESMS</a> &gt; <a
                            href="<?= website_url; ?>/application/">Admissions</a> &gt; <a
                            href="<?= website_url; ?>/application/Checklist">Your Profile</a> &gt;
                </div>
            </div>
        </div>

    </div> <!-- .topwrap -->

    <div id="everything" class="container" id="main_body">

        <div class="row">
            <div id="wrapper" class="col-xs-12">

                <!-- for custom nav, portal etc. -->
 

 
