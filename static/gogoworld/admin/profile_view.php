<?php
$core_students_level_of_entry=pull_field("core_course_level","db343","WHERE id=$core_students_level_of_entry");

$route_name = pull_field("dir_route_rules","db2281","WHERE db1764=".$core_student_application_route);  //- route name
?>
        
        <div class="col-sm-12 col-md-4 col-lg-4">
			<img src="<?=$avatar?>" class="img-circle" alt="no image" width="100" border="0"  id="tour_profile_photo" onerror="this.src='/engine/images/male_icon.jpg'"/>
        </div>
        <div class="col-sm-12 col-md-8 col-lg-4 name">
          <div class="info_detail">
                    <h2 style="color:#F60">Route: <span class="label label-info"><?php echo $route_name; ?></span></h2>
            <?php
              $last_login_date = last_logged_in($core_students_rec_id,"D jS \of M Y h:i:s A");  
              if($last_login_date){
            ?>
            <b>Last Login:</b>
            <?php echo $last_login_date; ?>
            <?php }else{ echo "Hasn't logged in yet"; } ?>
            </div>
        </div>
        
        <div class="col-sm-12 col-md-12 col-lg-12">
        <hr>
          <div class="print-inline-div">
            <div class="title">Email</div>  
            <div class="info_detail"><?php echo $core_students_email_address; ?></div>
          </div>
          <div class="print-inline-div">
          <div class="title">Telephone:</div>
          <div class="info_detail"><?php echo pull_field("dir_custom","db6763","WHERE rel_id=".$_GET['ref']); ?></div>
          </div>
          <div class="print-inline-div">
            <div class="title">Entry Year:</div>
            <div class="info_detail"><?php echo $core_students_cohort_intake; ?></div> 
          </div>
          <!-- <div class="print-inline-div">
            <div class="title"> City/School</div>
            <div class="info_detail"><?php echo $core_students_level_of_entry.' / '.$core_students_course_of_study; ?></div> 
          </div> -->
          <div class="print-inline-div">
            <div class="title"> Length</div>
            <div class="info_detail"><?php echo str_replace("_"," ",$core_students_length_of_study); ?></div> 
          </div>
          <div class="print-inline-div">
            <div class="title">Nationality</div>
          <div class="info_detail"><?php echo $core_students_country_of_origin; ?></div>
          </div>
          <div class="clearfix"></div>
          <div class="print-inline-div">
            <div class="title">Date of Birth</div>
            <div class="info_detail"><?php if($core_students_date_of_birth!="0000-00-00"&&$core_students_date_of_birth!=""){ echo format_date("d/m/Y",$core_students_date_of_birth);}  ?></div> 
          </div>
           <div class="clearfix"></div>
           <div class="print-inline-div">
            <div class="title">Market</div>
          <div class="info_detail"><?php echo pull_field('core_students',"db510","WHERE id =$core_students_id")?></div>
          </div>
         <?php
          $assigned_ids=pull_field('core_assignations','group_concat(db69836)',"WHERE rel_id=".$_GET['ref']."  AND db69884='2' and ((core_assignations.db69743 is null or core_assignations.db69743 ='') AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = ''))"); 
          if (""!=$assigned_ids) {
               //$assigned_users_sch_coodinator=pull_field('form_users',"GROUP_CONCAT(CONCAT(db106, ' ', db111,' '))","WHERE form_users.id in (".$assigned_ids.") and db31907=11"); 
                $assigned_users_std_coodinator=pull_field('form_users',"GROUP_CONCAT(CONCAT(db106, ' ', db111,' '))","WHERE form_users.id in (".$assigned_ids.") ");
                 //$assigned_users_admins=pull_field('form_users',"GROUP_CONCAT(CONCAT(db106, ' ', db111,' '))","WHERE form_users.id in (".$assigned_ids.") and db31907 not in (11,19)"); 
           }
           if (""!=str_replace(" ", "", $assigned_users_std_coodinator) ) {
          ?>
          <div class="clearfix"></div>
          <div class="print-inline-div">
            <div class="title">Student Coordinator:</div>
          <div class="info_detail"><?php echo $assigned_users_std_coodinator ; ?></div>
          </div>
          <?php
           }
           $assigned_users_sch_coodinator=pull_field('save fr',"db592","WHERE id =$core_students_course_of_study_id");
            if (""!=str_replace(" ", "",$assigned_users_sch_coodinator)) {
           ?>
           <div class="clearfix"></div>
             <div class="print-inline-div">
              <div class="title">School Coordinator:</div>
            <div class="info_detail"><?php echo pull_field('form_users',"CONCAT(db106, ' ', db111,' ')","WHERE form_users.id in (".$assigned_users_sch_coodinator.") AND
              usergroup='$_SESSION[usergroup]' "); ?></div>
          </div>

           <?php 
           } 
          if (""!=str_replace(" ", "",$assigned_users_admins)) {
           ?>
           <!--  <div class="clearfix"></div>
             <div class="print-inline-div">
              <div class="title">Assigned Admins:</div>
            <div class="info_detail"><?php echo $assigned_users_admins; ?></div>
          </div> -->
          <?php 
           }  
            ?>

           
           	  
            <?php
			if($_SESSION['ulevel']!=='8'){
			?>
      <div class="clearfix"></div>
      <div class="btn-group btn-group-sml">  
       <?php #if($_SESSION['usergroup']!="49"){ ?>   
        <a href="javascript::void();" class="show_conversation btn btn-warning btn-sm" title="communication inbox"id="<?=$_GET['ref'];?>">
          <i class="fa fa-envelope"></i> Communication inbox
        </a> 
        <?php #} ?> 
        <?php
       # if($_SESSION['user']=="<EMAIL>"){
        ?>  
        <br><br><a href="<?=engine_url?>/includes/inc_view_file_check_new.php?vw=<?=$_GET['vw'];?>&ref=<?=$_GET['ref'];?>&rt=<?=$core_student_application_route?>&width=950&height=600&jqmRefresh=false&files=all" class="thickbox btn btn-warning btn-sm" title="Verify Documents" target="_blank">
          <i class="fa fa-file-text-o"></i> Verify Documents <?php if($_SESSION['usergroup']!="49"){ ?> New <?php } ?>
        </a>
      <?php #} ?>
        </div>
            
            <?php
			}
			?>
            
          <?php

	         if ($short_course_level !=='yes') {?>
           <!--ACTION BUTTONS-->
           <div class="clearfix"></div><br/>
           <div class="btn-group btn-group-sml">     
            <a href="<?=engine_url?>/includes/inc_view_data.php?vw=<?=$_GET['vw'];?>&ref=<?=$_GET['ref'];?>&width=950&height=600&jqmRefresh=false" 
              class="thickbox btn btn-warning btn-sm" title="View Application" target="_blank">
              <i class="fa fa-file-text-o"></i> View Application Form Data
            </a>
            </div>
           <!--ACTION BUTTONS END-->
            <?php 
			}
			?>

