<?php

// if(!$_POST['new_message']){
// 		$category='';
// 	}
// 	echo "<pre>".print_r($_POST,true)."</pre>";exit();

//$_GET['debug_mode']="yes";
$suppmail_templte = "";

if ($_POST['form_id'] == 3059) {
    $suppmail_templte = 15377;
}

$assigned_ids = explode(',', pull_field('core_assignations', 'group_concat(db69836)', "WHERE rel_id=" . $_SESSION['student_id'] . "  AND db69884='2' and ((core_assignations.db69743 is null or core_assignations.db69743 ='') AND (core_assignations.rec_archive IS NULL OR core_assignations.rec_archive = ''))"));

if (!empty($suppmail_templte)) {
    list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($suppmail_templte);


    $emailTo = pull_field("lead_preferences", "db258245", "WHERE usergroup={$_SESSION['usergroup']}");

    $type = pull_field('system_forms', "name", "WHERE id = '" . $_POST['form_id'] . "' ");

    $subject = email_template_replace_values("{{cohort_id}}", $core_students_cohort_intake, $coms_template_subject_line);
    $message_plain = email_template_replace_values("{{name}}", $_SESSION['name'], $coms_template_plain_text_version);
    $message_html = email_template_replace_values("{{name}}", $_SESSION['name'], $coms_template_html_version);
    $message_plain = email_template_replace_values("{{reviewer}}", $assigned_users_std_coodinator, $message_plain);
    $message_html = email_template_replace_values("{{reviewer}}", $assigned_users_std_coodinator, $message_html);
    $message_plain = email_template_replace_values("{{link_url}}", "$front_website_url/engine/direct/proc?pg=4&vw=$_SESSION[student_id]&ref=$_SESSION[student_id]", $message_plain);
    $message_html = email_template_replace_values("{{link_url}}", "$front_website_url/engine/direct/proc?pg=4&vw=$_SESSION[student_id]&ref=$_SESSION[student_id]", $message_html);
    $message_plain = email_template_replace_values("{{type}}", $type, $message_plain);
    $message_html = email_template_replace_values("{{type}}", $type, $message_html);


    log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, "Supplementary form submitted", $_SESSION['student_id']);

}

if (!empty($_GET['debug_mode'])) {
    echo "halt";
    exit();
}

