<?php


//$student_id =  session_info("uid");

$route = pull_field("core_students", "db2280", "WHERE id = '$student_id'");


if ($_SESSION['usergroup'] == 49) {

    switch ($route) {
        case 404://summer


            $dbh = get_dbh();
            $sql = $dbh->prepare("UPDATE z_49_default_checklist SET db29077 = 'on' WHERE rel_id = '$student_id';");
            $sql->execute();
            $sql = $dbh->prepare("UPDATE z_49_default_checklist SET db41791 = 'yes' WHERE rel_id = '$student_id';");
            $sql->execute();


            break;

    }
}

