<?php
$languages = [
    'en' => 1,   // English
    'fr' => 2,   // French
    'ar' => 3,   // Arabic
    'pt' => 4,   // Portugese
    'ur' => 5,   // Urdu
    'bg' => 7,   // Bulgarian
    'zh' => 8,   // Chinese
    'de' => 9,   // German
    'es' => 10,  // Spanish
    'sw' => 11,  // Swedish
    'it' => 12,  // Italian
    'cy' => 13,  // Welsh
    'pl' => 14,  // Polish
    'ru' => 15   // Russian
];
define('LANGUAGES', $languages);
session_start();
global $lang;

// Read batch request and split into enquiries
//$dataJSON = file_get_contents('php://input');//this is repetition,from api controller, since its an included file
//$data = json_decode($dataJSON, true);//this is repetition,from api controller, since its an included file
//echo "<pre>"."dataCustomHook";
//echo json_encode($data);
//echo "</pre>";
//exit();
//$data = $data_for_hook;
$dbh = get_dbh();

$sql = "SELECT db48841 FROM lead_preferences WHERE usergroup = ?";
$st = $dbh->prepare($sql);
$st->execute([$_SESSION['usergroup']]);
$list = FALSE;
if ($st->rowCount() == 1) {
    $row = $st->fetchObject();
    $list = $row->db48841;
}
$countries = $list ? explode(',', $list) : [];
$unsupported_countries = array_map(function ($country) {
    return ucfirst(trim($country));
}, $countries);
//$unsupported_countries = array("Zimbabwean","Cameroonian","Bangladeshi","Nigerian", "Nigerien","Burmese","Laotian","Iraqi","Burundian","Somali","Congolese","Sudanese","Liberian", "Eritrean","Venezuelan","Nepalese","Ghanaian","Iranian", "Lebanese", "Libyan", "Mongolian","Ugandan","Uzbekistani","Yemenite","Sri Lankan","Pakistan","Indian", "Vietnamese","Rwandan","Ethiopian","Chadian","Afghan","Kazakhstani","Motswana","Botswana","Bhutanese","Egyptian","Indonesian","Filipino");

if (!empty($data)) {///this input variable is defined in the api controller

    $interest_null = null;
    $status = 0;
    $chosen_school_id = 0;
    // $usergroup_id =  $_SESSION['usergroup'];
    $dbh = get_dbh();

    if (in_array($data['db1054'], $unsupported_countries)) {
        ///send an email to the enquirer
        ///### first get template for GGW
        $dbh = get_dbh();
        $tempsql = "select id from coms_template where db1083 = :subject and usergroup =:usergroup and db47591=1";
        $tempsth = $dbh->prepare($tempsql);
        $tempsth->execute(array(
            'subject' => 'Blacklist Email',
            'usergroup' => $_SESSION['usergroup']
        ));
        $temp_results = $tempsth->fetch(PDO::FETCH_ASSOC);
        $template_id = $temp_results['id'];

        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_id);
        // //now send and log the email

        $message_html = $coms_template_html_version;
        $message_plain = $coms_template_plain_text_version;
        $emailTo = $data['db1058'];
        $emailFrom = $coms_template_email_address_to_send_from;
        $reference = '0';
        //$_SESSION['usergroup'] = 49;
        log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, "New Message Alert", $reference);
        $unsupported_response = array(
            'enquirer_name' => $data['db1041'].' '.$data['db1043'],
            'enquirer_email' => $data['db1058'],
            'enquirer_nationality' => $data['db1054'],
            'part_message_sent' => 'Unfortunately, due to the strict procedures of Japanese immigration, we are unable to assist students from certain countries not listed in the MOFA list.'
        );
        echo json_encode($unsupported_response);
        exit;
    } else {
        $status = 1;
    }
    $form_language = $data['db31762'];
    #select db from trans where lan=$form_language
    #$lang = new Translations(LANGUAGES['en'], LANGUAGES['de']);
    if($form_language == 'German') {
        $lang = new Translations(LANGUAGES['en'], LANGUAGES['de']);
    }else if($form_language == 'French') {
        $lang = new Translations(LANGUAGES['en'], LANGUAGES['fr']);
    }else if($form_language == 'Spanish') {
        $lang = new Translations(LANGUAGES['en'], LANGUAGES['es']);
    }else if($form_language == 'Italian') {
        $lang = new Translations(LANGUAGES['en'], LANGUAGES['it']);
    }else if($form_language == 'Swedish') {
        $lang = new Translations(LANGUAGES['en'], LANGUAGES['sw']);
    }else if($form_language == 'Portuguese') {
        $lang = new Translations(LANGUAGES['en'], LANGUAGES['pt']);
    }

    $fname = $data['db1041'];
    $middle_name = $data['db1042'];
    $surname = $data['db1043'];
    $email = $data['db1058'];
    $age = $data['db30531'];
    //$telephone = $data['db1046'];
    //$dob = $data['db1047'];
    if(!empty($data['db1048'])){
        if($form_language == 'English') {
            $gender = $data['db1048'];
        }else{
            $gender = $lang->translate($data['db1048']);
        }
    }else{
        $gender = "";
    }

    $nationality = $data['db1054'];
    if(!empty($data['db30525'])){
        if($form_language == 'English') {
            $interest_raw = $data['db30525'];
        }else{
            $interest_raw = $lang->translate($data['db30525']);
        }
    }else{
        $interest_raw = "";
    }

    //$study_japanese = $data['Adventure Japan'];
    if ($interest_raw == '3 months or LESS') {
        $interest = '3_months_or_LESS';
    } elseif ($interest_raw == '6 months or MORE') {
        $interest = '6_months_or_MORE';
    } elseif ($interest_raw == 'Study Trips (2-4 week study/activity trip)') {
        $interest = 'Study_Trips_(2-4_week_study/activity_trip)';
    } else {
        $interest = 'Other';
    }
    ///get id of hear about
    if(!empty($data['db1049'])){
        if($form_language == 'English') {
            $hear_of_us_words = $data['db1049'];
        }else{
            $hear_of_us_words = $lang->translate($data['db1049']);
        }
    }else{
        $hear_of_us_words = "";
    }
    if(!empty($hear_of_us_words)){
        $hear_of_us_words_escaped = $hear_of_us_words;
        $hear_id = pull_field('core_hear_about_us', "id", "where usergroup=49 AND db16997='$hear_of_us_words_escaped'");
    }else{
        $hear_id = "";
    }
    if ($hear_id == '') {
        $hear_id = pull_field('core_hear_about_us', "id", "where usergroup=49 AND db16997='Other'");
    }
    $hear_of_us = $hear_id;


    if(!empty($data['db30530'])){
        if($form_language == 'English') {
            $hear_of_us_2nd_layer = $data['db30530'];
        }else{
            $hear_of_us_2nd_layer = $lang->translate($data['db30530']);
        }
    }else{
        $hear_of_us_2nd_layer = "";
    }

    if(!empty($data['db31770'])){
        if($form_language == 'English') {
            $hear_of_us_3rd_layer = $data['db31770'];
        }else{
            $hear_of_us_3rd_layer = $lang->translate($data['db31770']);
        }
    }else{
        $hear_of_us_3rd_layer = "";
    }


    $hear_of_us_other_info = $data['db30529'];
    $raw_message = $data['db1055'];
    // $message = addslashes($raw_message);
    $message = $raw_message;
    $attending_year = pull_field('form_schools', "db36", "where id=49");
    $raw_city_interest = addslashes($data['db31772']);
    if(!empty($raw_city_interest)){
        if($form_language == 'English') {
            $city_interest = $raw_city_interest;
        }else{
            $city_interest = $lang->translate($raw_city_interest);
        }

        if ($city_interest == 'Undecided' OR $city_interest == 'Ihaven/\'t made my choice yet') {
            $city_id = 0;
        } else {
            $city_id = pull_field('core_course_level', "id", "where usergroup=49 AND db343='$city_interest'");
        }

    }else{
        $city_interest = "";
        $city_id = 0;
    }


//    if ($study_japanese == 'Online') {
//        $school_interest = $study_japanese;
//    } else {
//        if ($raw_city_interest == 'Tokyo') {
//            $school_interest = $data['Tokyo schools'];///which field is this?
//        } elseif ($raw_city_interest == 'Outside of Tokyo') {
//            $school_interest = $data['Outside Tokyo schools'];///which field is this?
//        }
//    }

    ///add and escape for school to cater for I haven't made my choice yet choice.
    //$school_interest = $school_interest;///not being sent to lead_profiles anymore

    if(!empty($data['db30526'])){
        if($form_language == 'English') {
            $term_interest = $data['db30526'];
        }else{
            $term_interest = $lang->translate($data['db30526']);
        }
    }else{
        $term_interest = "";
    }

    if(!empty($data['db30528'])){
        if($form_language == 'English') {
            $graduate_school = $data['db30528'];
        }else{
            $graduate_school = $lang->translate($data['db30528']);
        }
    }else{
        $graduate_school = "";
    }

    $what_language = $data['db31762'];
    $market_field = $data['db30532'];
    //$source_url = 'Zapier CRM App';///db32506
    $source_url = $data['db32506'];
    ///get school id
    //if there is no defined school interest then school id is empty
    if($form_language == 'English') {
        $school_interest = $data['db1052'];
    }else{
        $school_interest = $lang->translate($data['db1052']);
    }
    if (!isset($school_interest) || $school_interest == '') {
        $chosen_school_id = '';
    } else if ($school_interest == 'Online') {
        $school_criteria = "Online Courses";///check this info on ticket #18668
        $chosen_school_id = pull_field('core_courses ', "id", "WHERE db235='public' and (rec_archive is NULL or rec_archive = '') and usergroup =49 AND db232 = '$school_criteria'");
    } else {
        $chosen_school_id = pull_field('core_courses ', "id", "WHERE db235='public' and (rec_archive is NULL or rec_archive = '') and usergroup =49 AND db232 like '%$school_interest%'");
    }

    if ($chosen_school_id == '' || !isset($chosen_school_id)) {
        $chosen_school_id = 0;
    }
    ///get proposed intakes
    $where_criteria = "$term_interest " . $attending_year;
    $proposed_intake_id = pull_field('dir_cohorts ', "id", "WHERE usergroup=49 AND db1680='$attending_year' AND db18719 ='$chosen_school_id' AND db1681 LIKE '%$term_interest%'");
    if ($proposed_intake_id == '' || !isset($proposed_intake_id)) {
        $proposed_intake_id = 0;
    }

    $engsql = "INSERT INTO lead_profiles (date,username_id, rec_id, usergroup, rel_id, db1041,db1042,db1043,db1058,db1048,db1054,db1050,db1051,db30526,db31772,db1049,db1055,db30528,db1056,db31762,db32506,db30525,db1053,db1052,db31774,db30531,db30530,db31770,db30529,db30532) VALUES ('" . date('Y-m-d H:i:s') . "','" . random() . "', 1,   49, 1, '" . $fname . "', '" . $middle_name . "', '" . $surname . "', '" . $email . "', '" . $gender . "','" . $nationality . "', '" . $attending_year . "', '" . $interest_null . "', '" . $term_interest . "', '" . $city_interest . "', '" . $hear_of_us . "', '" . $message . "', '" . $graduate_school . "', '" . $status . "', '" . $what_language . "', '" . $source_url . "', '" . $interest . "', '" . $city_id . "', '" . $chosen_school_id . "', '" . $proposed_intake_id . "', '" . $age . "','" . $hear_of_us_2nd_layer . "','" . $hear_of_us_3rd_layer . "','" . $hear_of_us_other_info . "','" . $market_field . "')";

    $dbh->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $portsql = "INSERT INTO lead_profiles (date,username_id, rec_id, usergroup, rel_id, db1041,db1042,db1043,db1058,db1048,db1054,db1050,db1051,db30526,db31772,db1049,db1055,db30528,db1056,db31762,db32506,db30525,db1053,db1052,db31774,db30531,db30530,db31770,db30529,db30532) VALUES ('" . date('Y-m-d H:i:s') . "','" . random() . "', 1,   49, 1, :fname, :middle_name, :surname, :email, :gender,:nationality, :attending_year, :interest_null, :term_interest, :city_interest, :hear_of_us, :msg, :graduate_school, :the_status, :what_language, :source_url, :interest, :city_id, :chosen_school_id, :proposed_intake_id, :age,:hear_of_us_2nd_layer,:hear_of_us_3rd_layer,:hear_of_us_other_info,:market_field)";
    //replace mysqli_query with pdo
    $data_to_insert = array();
    try {
        $portsth = $dbh->prepare($portsql);
        $data_to_insert = array(
            'fname' => $fname,
            'middle_name' => $middle_name,
            'surname' => $surname,
            'email' => $email,
            'gender' => $gender,
            'nationality' => $nationality,
            'attending_year' => $attending_year,
            'interest_null' => $interest_null,
            'term_interest' => $term_interest,
            'city_interest' => $city_interest,//db31772
            'hear_of_us' => $hear_of_us,
            'msg' => $message,
            'graduate_school' => $graduate_school,
            'the_status' => $status,
            'what_language' => $what_language,
            'source_url' => $source_url,
            'interest' => $interest,
            'city_id' => $city_id,
            'chosen_school_id' => $chosen_school_id,
            'proposed_intake_id' => $proposed_intake_id,
            'age' => $age,
            'hear_of_us_2nd_layer' => $hear_of_us_2nd_layer,
            'hear_of_us_3rd_layer' => $hear_of_us_3rd_layer,
            'hear_of_us_other_info' => $hear_of_us_other_info,
            'market_field' => $market_field
        );

        $portsth->execute($data_to_insert);
        update_stage_tracker($dbh->lastInsertId(), $status);
        //echo "Testing SQL";
        echo json_encode($data_to_insert);
    } catch (PDOException $e) {
        handle_sql_errors($portsql, $e->getMessage());
    }

}else {
    echo json_encode($data);
    echo "Cant determine the input from user";
}

function update_stage_tracker($id, $status)
{
    $dbh = get_dbh();
    $sth_create_stage_tracker = $dbh->prepare("INSERT INTO lead_stage_tracker (username_id, rec_id, usergroup, rel_id, db27514) VALUES (?,?,?,?,?)");
    $sth_create_stage_tracker->execute(array(random(), session_info("uid"), 49, $id, $status));
    $update_date = date('Y-m-d H:i:s');
    $sql = "UPDATE lead_profiles SET  db62993='$update_date' WHERE id = '$id' AND usergroup = '$_SESSION[usergroup]'";
    $sql1 = $dbh->prepare($sql);
    $sql1->execute();

}

function process_study_trip_enquiry($data, $unsupported_countries)
{


    $markets = array(
        'en' => 'International',
        'it' => 'Italian',
        'es' => 'Spanish',
        'sv' => 'Swedish'
    );

    $field_titles = array( //field title mappings made using ticket 39374
        'first_name' => $data['1'],
        'email_address' => $data['2'],
        'message' => $data['5'],
        'family_name' => $data['6'],
        'age' => $data['8'],
        'study_trip_destination' => $data['17'],   //better suited to 'In which country would you like to study' instead of 'how long will the applicant be studying for'
        'nationality' => $data['18'],
        'hear_about_us' => $data['19'],
        'site_language' => $data['20'],  //market_field
        'gender' => $data['22'],
        'source_url' => $data['source_url']
    );

    //check if nationality supported
    $nationality_supported = nationality_supported($field_titles['site_language'], $field_titles['email_address'], $field_titles['nationality'], $unsupported_countries, $_SESSION['usergroup']);
    //check if destination is supported
    $destination_is_supported = destination_supported($field_titles['study_trip_destination'], $_SESSION['usergroup']);

    //if nationality and destination are supported then create a new enquiry record
    if ($nationality_supported && $destination_is_supported) {
        save_enquiry($field_titles, $markets, $_SESSION['usergroup']);
    }
}

function save_enquiry($enquiry, $markets, $usergroup)
{
    $dbh = get_dbh();
    $fname = $enquiry['first_name'];
    $surname = $enquiry['family_name'];
    $email = $enquiry['email_address'];
    $gender = $enquiry['gender'];
    $nationality = $enquiry['nationality'];
    $age = $enquiry['age'];
    ///get id of hear about
    $hear_of_us_words = $enquiry['hear_about_us'];
    $about_us_sql = "SELECT id from core_hear_about_us WHERE db16997 = :hear_of_us AND usergroup= :usergroup AND (rec_archive is NULL or rec_archive='')";
    $about_us_sth = $dbh->prepare($about_us_sql);
    $about_us_sth->execute(array('hear_of_us' => $hear_of_us_words, 'usergroup' => $usergroup));
    $hear_id = $about_us_sth->fetchColumn();
    if (!is_numeric($hear_id)) {
        $hear_id = pull_field('core_hear_about_us', 'id', "WHERE usergroup=$usergroup AND db16997='Other' AND (rec_archive is NULL or rec_archive='')");
    }
    $hear_of_us = $hear_id;
    $message = $enquiry['message'];
    $status = 1;
    $site_language = $enquiry['site_language'];
    $market_field = $markets[$site_language];
    $destination = $enquiry['study_trip_destination'];
    $source_url = $enquiry['source_url'];
    $study_length = 'Study_Trips_(2-4_week_study/activity_trip)';

    $dbh->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $debug_only_sql = "INSERT INTO lead_profiles (username_id, rec_id, usergroup, rel_id, db1041,db1043,db1058,db1048,db1054,db1049,db1055,db1056,db32506,db30531,db30532,db36576,db30525) VALUES ('" . random() . "', 1, '" . $usergroup . "', 1, '" . $fname . "', '" . $surname . "', '" . $email . "', '" . $gender . "','" . $nationality . "', '" . $hear_of_us . "', '" . $message . "', '" . $status . "', '" . $source_url . "', '" . $age . "', '" . $market_field . "', '" . $destination . "','" . $study_length . "')";

    $sql = "INSERT INTO lead_profiles (username_id, rec_id, usergroup, rel_id, db1041,db1043,db1058,db1048,db1054,db1049,db1055,db1056,db32506,db30531,db30532,db36576,db30525) VALUES ('" . random() . "', 1, :usergroup, 1, :fname, :surname, :email, :gender, :nationality, :hear_of_us, :msg, :the_status, :source_url, :age, :market_field, :interested_study_country, :study_length)";

    try {
        $sth = $dbh->prepare($sql);
        $data_to_insert_study = array(
            'usergroup' => $usergroup,
            'fname' => $fname,
            'surname' => $surname,
            'email' => $email,
            'gender' => $gender,
            'nationality' => $nationality,
            'hear_of_us' => $hear_of_us,
            'msg' => $message,
            'the_status' => $status,
            'source_url' => $source_url,
            'age' => $age,
            'market_field' => $market_field,
            'interested_study_country' => $destination,
            'study_length' => $study_length
        );
        $sth->execute($data_to_insert_study);
        update_stage_tracker($dbh->lastInsertId(), $status);
        echo "Testing SQL";
        print_r($debug_only_sql); ///testing to see if this stage will be reached by webhook
    } catch (PDOException $e) {
        handle_sql_errors($sql, $e->getMessage());
    }
    echo "<pre>" . "</pre>";
}

function destination_supported($destination, $usergroup)
{
    $destination_is_supported = false;
    $supported = pull_field('lead_preferences', 'db157922', "WHERE usergroup=$usergroup AND (rec_archive is NULL or rec_archive='')"); //db157922 holds the supported study trip destinations
    $supported = explode(',', $supported);
    if (in_array($destination, $supported)) {
        $destination_is_supported = true;
    }
    return $destination_is_supported;
}


function nationality_supported($submitted_language, $email, $nationality, $unsupported_countries, $usergroup)
{
    $nationality_is_supported = false;
    $dbh = get_dbh();

    //update the list of unsupported countries to the study trips list
    $_SESSION['usergroup'] = 49;
    $sql = "SELECT db233531 FROM lead_preferences WHERE usergroup = ?";
    $st = $dbh->prepare($sql);
    $st->execute([$_SESSION['usergroup']]);
    $list = FALSE;
    if ($st->rowCount() == 1) {
        $row = $st->fetchObject();
        $list = $row->db233531;
    }
    $countries = $list ? explode(',', $list) : [];
    $unsupported_countries = array_map(function ($country) {
        return ucfirst(trim($country));
    }, $countries);

    if (in_array($nationality, $unsupported_countries)) {
        //send an email to the enquirer
        //first get template for GGW
        //get the template in the submitted language
        $submitted_language = $submitted_language == 'en' ? 'eng' : $submitted_language;
        $lang_sql = "SELECT id from form_languages WHERE db21281 = :submitted_language AND (rec_archive is NULL or rec_archive='') AND usergroup=$usergroup limit 1";
        $lang_sth = $dbh->prepare($lang_sql);
        $lang_sth->execute(array('submitted_language' => $submitted_language));
        $language = $lang_sth->fetch(PDO::FETCH_ASSOC);
        //check if session usergroup has a record for the submitted language, if not use dafault usergroup
        if (!(is_array($language) && count($language) === 1)) {
            $lang_sql = "SELECT id from form_languages WHERE db21281 = :submitted_language AND (rec_archive is NULL or rec_archive='') AND usergroup=1 limit 1";
            $lang_sth = $dbh->prepare($lang_sql);
            $lang_sth->execute(array('submitted_language' => $submitted_language));
            $language = $lang_sth->fetch(PDO::FETCH_ASSOC);
        }
        $language_id = $language['id'];
        $template_id = pull_field('coms_template', 'id', "WHERE usergroup=$usergroup AND db1083 ='Blacklist Email' AND db47591 = $language_id AND (rec_archive is NULL or rec_archive='')");
        //if no template exists in the selected language then default to the english template of the default usergroup i.e language id equal to 1
        if (!is_numeric($template_id)) {
            $template_id = pull_field('coms_template', 'id', "WHERE usergroup=$usergroup AND db1083 ='Blacklist Email' AND db47591 = 1 AND (rec_archive is NULL or rec_archive='')");
        }
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_id);
        // //now send and log the email

        $message_html = $coms_template_html_version;
        $message_plain = $coms_template_plain_text_version;
        $emailTo = $email;
        $emailFrom = $coms_template_email_address_to_send_from;
        $reference = '0';
        log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, "New Message Alert", $reference);

        echo "Message HTML=" . $message_html;
        echo "<pre>" . "</pre>";
        print_r($unsupported_countries);
    } else {
        $nationality_is_supported = true;
    }

    return $nationality_is_supported;
}
