<div class="sidebar" style="margin-top: 22px;">
    <?php
    if ($_SESSION['loggedin']) { // only show to logged in users

        echo '<div ><a href="' . website_url_applicant . '/logout" class="btn btn-danger btn-lg btn-block"><i class="fa fa-lg fa-sign-out" aria-hidden="true"></i>' . translate('Log out', $_SESSION['lang']) . '</a></div>';

        error_log('tracker');

        //only show on the homepage
        if ($cms_page_name == "Checklist") {


            $dbh = get_dbh();

            // Get the stages for student's route
            $sql = 'SELECT system_table.form_id, system_table.db_field_name, system_table.name
			FROM system_table
			INNER JOIN core_students ON core_students.db2280 = system_table.pg_id
			WHERE core_students.id = ? AND system_table.type="stage"
			AND locked IN(2,0)
			ORDER BY system_table.form_order';
            $sth = $dbh->prepare($sql);
            $sth->execute(array($_SESSION['student_id']));
            $stages = array();
            while ($row = $sth->fetch(PDO::FETCH_ASSOC)) $stages[] = $row;

            // pull the statuses of stages for this particular student
            $checklist_table_name = 'chk_' . pull_field(
                    'system_pages INNER JOIN core_students ON core_students.db2280 = system_pages.page_id',
                    'page_name',
                    "WHERE core_students.id = '$_SESSION[student_id]'"
                );
            error_log('checklist table is called ' . $checklist_table_name);


            $sql = 'SELECT ';
            foreach ($stages as $stage) {
                $sql .= $stage['db_field_name'] . ', ';
            }
            $sql = substr($sql, 0, -2);
            $sql .= " FROM $checklist_table_name WHERE rel_id = ? LIMIT 1";
            error_log('query to get stage statuses: ' . $sql);
            $sth = $dbh->prepare($sql);
            $sth->execute(array($_SESSION['student_id']));
            $stage_status = $sth->fetch(PDO::FETCH_ASSOC); ?>
            <div class="row">
                <div class="col-xs-12">
                    <ul class="list-group list-group_home">
                        <li class="list-group-item">
                            <h4 class="list-group-item-heading"><?= translate('Application Tracker', $_SESSION['lang']) ?></h4>
                            <p class="list-group-item-text"><?= translate('A quick summary of how your application is proceeding', $_SESSION['lang']) ?></p>
                        </li>

                        <li class="list-group-item">
                            <div class="circle-text <?php echo($filled_percentage == '100' ? 'completed' : '') ?>">
                                <span>1</span>
                            </div><?= translate('Application Completion', $_SESSION['lang']) ?>
                        </li>
                        <?php
                        $i = 1;
                        foreach ($stages as $stage) {

                            $exp = explode('-', $stage['name']);
                            $sts = $stage_status[$stage['db_field_name']];
                            //            $sts_image = ($sts === 'on') ? '<img src="'.engine_url.'/images/icon_tick.png" width="16" height="16" alt="tick" />' : '';

                            if ($sts === 'on') {
                                //$sts_image = '<img src="'.engine_url.'/images/icon_tick.png" width="16" height="16" alt="tick" />';
                                $tick_class = 'tick_class';
                                $number = '<i class="fa fa-lg fa-check" aria-hidden="true"></i>';
                            } else {
                                $sts_image = '';
                                $tick_class = '';
                                $number = $i + 1;
                            }
                            ?>
                            <li class="list-group-item clearfix <?= stage_complete($i) ?><?php echo ' ' . $tick_class; ?>">
                                <div class="circle-text <?php if ($sts === 'on') {
                                    echo "completed";
                                } ?>">
                                    <span><?= $number; ?></span>
                                </div><?= $exp[1] ?> <?= $sts_image ?>
                            </li>

                            <?php
                            $i++;
                        } ?>

                    </ul>
                </div>
            </div>
            <?php
            //end of checklist
        } else {
            ?>
            <div class="row">
                <div class="col-xs-12">
                    <ul class="list-group">
                        <li class="list-group-item">
                            <h4><?= translate('Your Application', $_SESSION['lang']); ?></h4>
                        </li>
                        <?php
                        //get the right pages for this section
                        $pages_id = get_page_list_based_on_rules($_SESSION['student_id']);

                        if ($pages_id != "") {
                            get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq','ucas','product_page') AND id IN($pages_id) ORDER BY FIELD(id,$pages_id)", "yes", "list-group-item", "show_auto_number");
                        } else {
                            get_cms_nav("application", "private", "db656 IN ('forms','information','submit_page','upload','faq')", "yes", "list-group-item");
                        }
                        ?>
                    </ul>
                </div>
            </div>
        <?php } ?>

        <?php
        $faq_image_name = 'faq2_' . $_SESSION['lang'];

        $file_path = "portal/media/" . $_SESSION['subdomain'] . "/" . "faq/{$faq_image_name}.png";

        if (file_exists($_SERVER['DOCUMENT_ROOT'] . '/' . $file_path)) {
            $image_path = $file_path;
        } else {
            $image_path = "portal/media/" . $_SESSION['subdomain'] . "/" . "faq/faq2_eng.png";
        }

        $image_url = getRequestProtocol() . "://{$_SERVER['HTTP_HOST']}/{$image_path}";
        ?>


        <div class="row">
            <div class="col-xs-12" style="margin-bottom: 20px;">
                <div class="panel panel-default sidebar_faqs">
                    <div class="panel-heading">
                        <h4><?= translate('Online Courses', $_SESSION['lang']) ?></h4>
                    </div>
                    <div class="panel-body">
                        <img src="<?php echo $image_url ?>" width="100%">

                        <p><?= translate(terminology("Start your Japanese learning journey with one of our online courses", $_SESSION['url'], '', true), $_SESSION['lang']) ?>
                            .</p>
                        <a href="<?php echo website_url_applicant; ?>/online-beginner-course"
                           class="btn btn-custom"><?= translate('See more', $_SESSION['lang']) ?></a>
                    </div>
                </div>
                <style type="text/css">
                    @media (max-width: 768px) {
                        .sidebar_faqs {
                            display: none;
                        }
                    }
                </style>
            </div>
            <div class="col-xs-12">
                <div class="panel panel-default sidebar_faqs">
                    <div class="panel-heading">
                        <h4><?= translate('FAQ & Support', $_SESSION['lang']) ?></h4>
                    </div>
                    <div class="panel-body">
                        <em><i class="fa fa-question-circle"
                               aria-hidden="true"></i> <?= translate('Need help', $_SESSION['lang']) ?>?</em>
                        <p style="text-align: justify;"><?= translate("We have put together answers to some of the most common questions that applicants ask us. Check it out!", $_SESSION['lang']) ?></p>
                        <a href="<?php echo website_url_applicant; ?>/applicant-faqs"
                           class="btn btn-info"><?= translate('See more', $_SESSION['lang']) ?></a>
                    </div>
                </div>
                <style type="text/css">
                    @media (max-width: 768px) {
                        .sidebar_faqs {
                            display: none;
                        }
                    }
                </style>
            </div>
        </div>


        <?php
    } // end of check if checklist

    if (!$_SESSION['loggedin']) { // only show to logged in users
        ?>


        <?php
        // only show if this is the login page
        if ($cms_page_name == "home") {
            ?>
            <h3>Quick Login</h3>
            <div class="col-md-12 col-sm-12">
                <form role="form" id="get-start-validate" method="post" name="form1"
                      action="<?= $front_website_url ?>/application/login">
                    <div class="form-group">
                        <label for="inputUsernameEmail"><?= translate('Email', $_SESSION['lang']) ?></label>
                        <input name="email" type="text" id="email" data-validation="email"
                               data-validation-error-msg="<?= translate('Please enter an email', $_SESSION['lang']) ?>"
                               class="form-control signin-form"
                               placeholder="<?= translate('Enter Your Email Address', $_SESSION['lang']) ?>">
                    </div>

                    <?php
                    if ($_GET['ucas'] == 1) {
                        ?>
                        <div class="form-group">
                            <label for="inputUcas">UCAS Personal ID</label>
                            <input name="ucas_code" type="text" id="ucas_code" data-validation="required"
                                   class="form-control signin-form"
                                   placeholder="<?= translate('Enter your UCAS Personal ID', $_SESSION['lang']) ?>">
                        </div>
                        <?php
                    } //ens ucas check
                    ?>

                    <div class="form-group">
                        <label for="inputPassword">Password</label>
                        <input name="pwd" type="password" id="pwd" data-validation="required"
                               data-validation-error-msg="<?= translate('Wrong Password', $_SESSION['lang']) ?>"
                               class="form-control signin-form form-password"
                               placeholder="<?= translate('Enter your Password', $_SESSION['lang']) ?>">
                    </div>

                    <button type="submit" name="button" id="button" class="btn btn-default orange-button login">
                        <?= translate('Log In', $_SESSION['lang']) ?>
                    </button>
                    <a class="pull-right" href="forgot"><?= translate('Forgot password', $_SESSION['lang']) ?>?</a>
                    <input name="rn" type="hidden" id="rn" value="1"/>
                    <input name="_token" type="hidden" id="token" value="<?= generateCsrfToken() ?>"/>
                </form>
            </div>

            <div class="clearfix"></div>
            <?php
        }
        // oend login check
        ?>
        <h3><?= translate('Quick Links', $_SESSION['lang']) ?></h3>
        <ul class="list-group">
            <?php
            //hide all second stage pages
            get_cms_nav("application", "public", "db656 IN ('landing_page','forms','information','submit_page','upload','faq')", "yes", "list-group-item");
            if ($_SESSION['loggedin'] === 'yes') :
                ?>
                <li class="list-group-item"><a href="<?php echo website_url_applicant; ?>/parent_register"><i
                                class="fa fa-fw fa-lg fa-check-circle-o"
                                aria-hidden="true"></i> <?= translate('Apply on behalf of a child', $_SESSION['lang']) ?>
                    </a></li>
                <li class="list-group-item"><a href="<?php echo website_url_applicant; ?>/parent_register"><i
                                class="fa fa-fw fa-lg fa-check-circle-o"
                                aria-hidden="true"></i> <?= translate('Apply for a spouse/friend', $_SESSION['lang']) ?>
                    </a></li>
                <li class="list-group-item"><a href="<?php echo website_url_applicant; ?>/register"><i
                                class="fa fa-fw fa-lg fa-check-circle-o"
                                aria-hidden="true"></i> <?= translate('Apply for yourself', $_SESSION['lang']) ?></a>
                </li>
            <?php endif; ?>
            <!--    <li class="list-group-item"><a href="--><?php //echo website_url_applicant;
            ?>
            <!--/login?ucas=1"><i class="fa fa-fw fa-lg fa-clock-o" aria-hidden="true"></i>  --><? //=translate('UCAS Supplimentary')
            ?>
            <!--</a></li>-->
            <li class="list-group-item"><a href="<?php echo website_url_applicant; ?>/login"><i
                            class="fa fa-fw fa-lg fa-check-circle-o"
                            aria-hidden="true"></i> <?= translate('Already Have An Account', $_SESSION['lang']) ?>?</a>
            </li>
        </ul>
    <?php } ?>
</div>