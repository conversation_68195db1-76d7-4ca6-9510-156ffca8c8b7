<?php
//check if offer has been accepted before showing letters
//GET USER TYPE
dev_debug("WEB dashboard START");

$user_type = pull_field("form_users", "db112", "WHERE id='$_SESSION[uid]' AND usergroup='$_SESSION[usergroup]'");
?>
<style type="text/css">
    #small_welcome_box {
        background: #fbfbfb;
        border: 1px solid #ddd;
        border-bottom: 7px solid #e6e6e6;
        padding: 15px;
        margin-bottom: 30px;
        margin-top: 30;
    }

    #small_welcome_box .pic {
        display: block;
        background: #e1e1e1;
        -webkit-border-radius: 100px;
        border-radius: 100px;
        height: 100px;
        max-width: 100px;
        margin: 0 auto;
        -webkit-background-size: cover;
        -o-background-size: cover;
        background-size: cover;
        background-position: center;
    }

    #small_welcome_box .info {
        background: transparent;
        margin: 0;
        padding: 26px 0;
        border: none;
    }

    #small_welcome_box .edit_pic_link {
        background: #fff;
        border: solid 1px #e1e1e1;
    }

    #small_welcome_box h3 {
        font-size: 24px;
        margin: 0;
        margin-top: 10px;
    }


    .course_list {
        background: #fbfbfb;
        border: 1px solid #ddd;
        border-bottom: 1px solid #e6e6e6;
        margin-bottom: 10px;
    }

    .course_list .chld_view_form2 {
        text-align: right;
        padding-right: 10px;
    }

    .course_list .course_title {
        padding-top: 10px;
    }

    .course_list .corse_info {
        padding: 30px;
        background: #FFF;
        border-top: 1px solid #e1e1e1;
    }
</style>
<div id="small_welcome_box">
    <div class="row">
        <div class="col-sm-12">
            <div class="info">
                <div class="row">
                    <div class="col-sm-7">
                        <h3>Welcome back, <?= $_SESSION['name'] ?></h3>
                    </div>
                    <div class="col-sm-5 text-right hidden">
                        <a class="btn btn-light edit_pic_link" href="#" data-toggle="modal" data-target="#photo_update"><i
                                    class="fa fa-cog" aria-hidden="true"></i> Update your user details</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="row">

    <div class="col-xs-12 dash_title">
        <a href="#" data-toggle="modal" data-target="#registration" class="btn btn-custom"
           style="float: right; margin-top: -10px; margin-bottom: 10px;">Apply For Another Programme</a>
        <h2>Manage All Applications</h2>
    </div>

</div>

<div class="row btm_base">
    <div class="col-xs-12 col-md-12">
        <div class="splash">
            <?php
            include("resources/views/multi-application.php");
            ?>
        </div><!--splash-->
    </div>
</div><!--row-->


<div class="row">
    <div class="col-xs-12 col-lg-12">
        <h2><?= translate(terminology("Communication Inbox", $_SESSION['url'], '', true), $_SESSION['lang']) ?></h2>
        <p><?= translate(terminology("Send and receive communication from our administration team below. To respond to a message or ask a question, simply select the child you want to communicate in relation to, and type your message.", $_SESSION['url'], '', true), $_SESSION['lang']) ?></p>

        <!--CHAT RELATED-->
        <script src="<?php echo $front_web_url_file_loc; ?>/js/dashboard.js" type="text/javascript"
                charset="utf-8"></script>

        <script type="text/javascript">
            $(document).ready(function () {

                $('.star').on('click', function () {
                    $(this).toggleClass('star-checked');
                });

                $('.ckbox label').on('click', function () {
                    $(this).parents('tr').toggleClass('selected');
                });

                $('.btn-filter').on('click', function () {
                    var $target = $(this).data('target');
                    if ($target != 'all') {
                        $('.table tbody tr').css('display', 'none');
                        $('.table tbody tr[data-status="' + $target + '"]').fadeIn('slow');
                    } else {
                        $('.table tbody tr').css('display', 'none').fadeIn('slow');
                    }
                });

            });
        </script>

        <?php //if($_SESSION['uid']==1787){ ?>
        <!--new chat-->
        <section class="content">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-body">

                        <?= translate("View messages relating to", $_SESSION['lang']) ?><br/>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default btn-filter" id="all"
                                    data-target="all"><?= translate("All", $_SESSION['lang']) ?>
                            </button>
                            <?php
                            // loop through kids
                            foreach ($list_children as $child) {
                                $new_msgs = 0;
                                foreach ($messages as $message) {
                                    if ($message['rel_id'] == $child['id'] && $message['rec_id'] != $student_uid && $message['db139'] == 'no') {
                                        $new_msgs++;
                                    }
                                }
                                ?>
                                <button type="button" class="btn btn-default btn-filter"
                                        id="<?= $child["id"] ?>"
                                        data-target="<?= $child["id"] ?>"><?= strtoupper($child["db39"]) ?>
                                    <?php if ($new_msgs > 0) { ?>
                                        <span class="label label-danger"><?= $new_msgs ?></span>
                                    <?php } ?>
                                </button>
                                <?php
                                // loop through kids end
                            }
                            ?>

                        </div>

                        <div id="coms" class="box widget-chat">
                            <div class="widget-actions">
                                <form class="form-inline"
                                      action="<?= website_url ?>/static/inc/inc_dir_messages_process.php" method="POST">
                                    <div>
                                                    <textarea name="new_message" rows="1" id="textarea-chat-example"
                                                              style="overflow: hidden; word-wrap: break-word; resize: vertical; height: 100px; width: 100%; margin-top:50px">
                                                    </textarea>
                                    </div>
                                    <input type="hidden" name="child_id" id="hdn_child_id" value="">
                                    <button class="btn btn-success">
                                        <?= translate("Send", $_SESSION['lang']) ?>
                                    </button>
                                </form>
                            </div>
                        </div>

                        <div class="clearfix"></div>

                        <div class="table-container">
                            <table class="table table-filter">
                                <thead>
                                <tr>
                                    <th style="width:2%">Read?</th>
                                    <th><?= translate("Message", $_SESSION['Send']) ?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php
                                $count_messages = 0;
                                foreach ($messages as $msg) {
                                    $count_messages++;
                                    $content = text_to_html($msg["db76"]);
                                    $date = format_date("d M y", $msg["date"]);
                                    $child_name = pull_field("core_students", "db39", "WHERE id='$msg[rel_id]'");
                                    if ($msg['rec_id'] === $student_uid) {
                                        $sender = 'You';//
                                        $checked = 'checked="checked"';
                                        //echo "<br> marked as read: ".$msg['db139']."<br>";
                                        if ($msg['db139'] == 'no') {
                                            $checked = '';
                                        }
                                        echo "<tr data-status=\"" . $msg['rel_id'] . "\">
                                                    <td>
                                                        <div class=\"\">
														
                                                            <input type=\"checkbox\" id=\"$msg[id]\" $checked title=\"mark as read\">
                                                            <label for=\"checkbox1\"></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class=\"media\">
                                                            <a href=\"#\" class=\"pull-left\">
                                                                <img src=\"$_SESSION[domain]/engine/images/blank_user_icon_fallback.png\" class=\"media-photo\">
                                                            </a>
                                                            <div class=\"media-body\">
                                                                <span class=\"media-meta pull-right\">$date</span>
                                                                <h4 class=\"title\">
                                                                    $sender ($child_name)
                                                                </h4>
                                                                <p class=\"summary\">$content</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>";
                                    } else {
                                        $sender = pull_field("form_users", "db106", "WHERE id='$msg[rec_id]'");
                                        $checked = 'checked="checked"';
                                        if ($msg['db139'] == 'no') {
                                            $checked = '';
                                        }
                                        echo "<tr data-status=\"" . $msg['rel_id'] . "\">
                                                    <td>
                                                        <div class=\"\">
														
                                                            <input type=\"checkbox\" id=\"$msg[id]\" $checked title=\"mark as read\">
                                                            <label for=\"checkbox1\"></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class=\"media\">
                                                            <a href=\"#\" class=\"pull-right\">
                                                                <img src=\"$_SESSION[domain]/engine/images/blank_user_icon_fallback.png\" class=\"media-photo\">
                                                            </a>
                                                            <div class=\"media-body\">
                                                                <span class=\"media-meta pull-right\">$date</span>
                                                                <h4 class=\"title\">
                                                                    $sender ($child_name)
                                                                </h4>
                                                                <p class=\"summary\">$content</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>";


                                    }
                                }
                                if ($count_messages == 0) {
                                    echo "<tr><td></td><td>" . translate("Sorry, no messages found", $_SESSION['Send']) . "</td></tr>";
                                }
                                ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--new chat-->
        <?php //} // end session check ?>
        <div class="clearfix"></div>

    </div><!--/span-->

</div> <!-- .row -->

<div class="row">

    <div class="col-xs-12 col-lg-6">
        <h3>News and Updates</h3>
        <p>Various information to help you through your application</p>

        <ul class="list-inline">
            <?php
            $i = 0;
            foreach ($blog_assist as $msg) {
                ?>
                <li class="col-lg-12" style="text-align: justify;">
                    <h4><?php echo $msg["db1695"]; ?></h4>

                    <?php
                    //show images
                    if ($msg["db1699"]) { ?>
                        <img src="<?= $front_web_url_file_loc ?>/media/<?php echo $msg["../43airschool - Copy/db1699"]; ?>"
                             class="thumbnail pull-left" alt="nes_image"/>
                    <?php } // end image show  ?>

                    <p><?php echo $msg["db1698"]; ?></p>
                    <a href="<?php echo website_url_applicant . '/blog/' . $msg["id"]; ?>" class="label label-default">Read
                        More</a>
                    <hr/>
                </li>
            <?php } // end of loop ?>
        </ul>
    </div>
</div> <!-- .row -->