<div class="col-md-12 baseline">
    <h3><?= translate("Welcome Back", $_SESSION['lang']) ?>, <?= $_SESSION['fullname'] ?></h3>
</div>

<div class="col-xs-12 col-md-12">
    <div class="splash">

        <div class="row">
            <h3><?= translate("Your Applications", $_SESSION['lang']) ?></h3>
            <?php
            // loop through kids
            foreach ($list_children as $child) {

                list($required_fields_on_dash, $required_filled_on_dash) = filled_percentage($child["id"]);
                $filled_percentage_on_dash = round(100 * $required_filled_on_dash / $required_fields_on_dash, 0, PHP_ROUND_HALF_DOWN);
                ?>

                <div class="col-md-3">
                    <div class="alert <?php echo($filled_percentage_on_dash == "100" ? 'alert-success' : 'alert-default') ?>"
                         align="center">
                        <h4><?= ucfirst($child["db39"] . ' ' . $child["db40"]) ?><br/>
                            <b><?= $filled_percentage_on_dash ?>%</b> <?= translate("complete", $_SESSION['lang']) ?>.
                        </h4>
                        <!--START PROGRESS BAR-->
                        <div class="progress">
                            <div class="progress-bar <?php echo($filled_percentage_on_dash == "100" ? 'progress-bar-success' : 'progress-bar-custom') ?>"
                                 role="progressbar" aria-valuenow="<?= $filled_percentage_on_dash ?>" aria-valuemin="0"
                                 aria-valuemax="100" style="width: <?= $filled_percentage_on_dash ?>%">
                                <span class="sr-only"><?= $filled_percentage_on_dash ?>% <?= translate("complete", $_SESSION['lang']) ?></span>
                            </div>
                        </div>
                        <!--END PROGRESS BAR-->
                        <hr/>
                        <?php
                        if ($filled_percentage_on_dash == "100") {

                            // check if the user has submitted
                            $submitted = pull_field("core_students", "db41", "WHERE id='" . $child['id'] . "'");
                            if ($submitted == 0) {
//
                                ?>
                                <form method="post" name="chld_view_form1"
                                      action="<?php echo website_url_applicant; ?>/submit_application"
                                      style="margin: 0;">
                                    <input type="hidden" name="chld_view" value="<?= $child["id"] ?>">
                                    <button class="btn btn-custom"
                                            type="submit"><?= translate("Submit Application", $_SESSION['lang']) ?></button>
                                </form>or <br/>
                                <form method="post" name="chld_view_form2"
                                      action="<?php echo website_url_applicant; ?>/personal-information"
                                      style="margin: 0;">
                                    <input type="hidden" name="chld_view" value="<?= $child["id"] ?>">
                                    <button class="btn btn-custom"
                                            type="submit"><?= translate("Edit Application", $_SESSION['lang']) ?></button>
                                </form>
                                <?php
                            } else {
                                ?>
                                <form method="post" name="chld_view_form1"
                                      action="<?php echo website_url_applicant; ?>/tracker" style="margin: 0;">
                                    <input type="hidden" name="chld_view" value="<?= $child["id"] ?>">
                                    <button class="btn btn-custom"
                                            type="submit"><?= translate("Track Application", $_SESSION['lang']) ?></button>
                                </form>
                                <?php
                            }//end submit check
                        } else {
                            ?>
                            <form method="post" name="chld_view_form2"
                                  action="<?php echo website_url_applicant; ?>/personal-information" style="margin: 0;">
                                <input type="hidden" name="chld_view" value="<?= $child["id"] ?>">
                                <button class="btn btn-custom"
                                        type="submit"><?= translate("Edit Application", $_SESSION['lang']) ?></button>
                            </form>
                            <?php
                        }
                        ?>
                    </div>
                </div>

                <?php
                $filled_percentage_on_dash = '';//reset
                $submitted = '';
                // loop through kids end
            }
            ?>

            <!--child register button-->
            <div class="col-md-3" data-toggle="modal" data-target="#registration" style="cursor:pointer">
                <div class="alert alert-default" align="center">
                    <span><?= translate("Add Another Child (new or existing at BSK)", $_SESSION['lang']) ?></span>
                    <h4 style="font-size:80px; align-content:center">+</h4>
                </div>
            </div>

        </div>

    </div>
</div>


<div class="row">
    <div class="col-xs-12 col-lg-12">
        <h2><?= translate("Communication Inbox", $_SESSION['lang']) ?></h2>
        <p><?= translate("Send and receive communication from our administration team below. To respond to a message or ask a question, simply select the child you want to communicate in relation to, and type your message.", $_SESSION['lang']) ?></p>

        <!--CHAT RELATED-->
        <script src="<?php echo $front_web_url_file_loc; ?>/js/dashboard.js" type="text/javascript"
                charset="utf-8"></script>

        <script type="text/javascript">
            $(document).ready(function () {

                $('.star').on('click', function () {
                    $(this).toggleClass('star-checked');
                });

                $('.ckbox label').on('click', function () {
                    $(this).parents('tr').toggleClass('selected');
                });

                $('.btn-filter').on('click', function () {
                    var $target = $(this).data('target');
                    if ($target != 'all') {
                        $('.table tbody tr').css('display', 'none');
                        $('.table tbody tr[data-status="' + $target + '"]').fadeIn('slow');
                    } else {
                        $('.table tbody tr').css('display', 'none').fadeIn('slow');
                    }
                });

            });
        </script>

        <?php //if($_SESSION['uid']==1787){ ?>
        <!--new chat-->
        <section class="content">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-body">

                        <?= translate("View messages relating to", $_SESSION['lang']) ?><br/>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default btn-filter" id="all"
                                    data-target="all"><?= translate("All", $_SESSION['lang']) ?>
                            </button>
                            <?php
                            // loop through kids
                            foreach ($list_children as $child) {
                                $new_msgs = 0;
                                foreach ($messages as $message) {
                                    if ($message['rel_id'] == $child['id'] && $message['rec_id'] != $student_uid && $message['db139'] == 'no') {
                                        $new_msgs++;
                                    }
                                }
                                ?>
                                <button type="button" class="btn btn-default btn-filter"
                                        id="<?= $child["id"] ?>"
                                        data-target="<?= $child["id"] ?>"><?= strtoupper($child["db39"]) ?>
                                    <?php if ($new_msgs > 0) { ?>
                                        <span class="label label-danger"><?= $new_msgs ?></span>
                                    <?php } ?>
                                </button>
                                <?php
                                // loop through kids end
                            }
                            ?>

                        </div>

                        <div id="coms" class="box widget-chat">
                            <div class="widget-actions">
                                <form class="form-inline"
                                      action="<?= website_url ?>/static/inc/inc_dir_messages_process.php" method="POST">
                                    <button class="btn btn-primary">
                                        <?= translate("Send", $_SESSION['lang']) ?>
                                    </button>
                                    <div>
                                                    <textarea name="new_message" rows="1" id="textarea-chat-example"
                                                              style="overflow: hidden; word-wrap: break-word; resize: vertical; height: 100px;"></textarea>
                                    </div>
                                    <input type="hidden" name="child_id" id="hdn_child_id" value="">
                                </form>
                            </div>
                        </div>

                        <div class="clearfix"></div>

                        <div class="table-container">
                            <table class="table table-filter">
                                <thead>
                                <tr>
                                    <th style="width:10%"><?= translate("Marked as read", $_SESSION['Send']) ?></th>
                                    <th><?= translate("Message", $_SESSION['Send']) ?></th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php
                                $count_messages = 0;
                                foreach ($messages as $msg) {
                                    $count_messages++;
                                    $content = text_to_html($msg["db76"]);
                                    $date = format_date("d M y", $msg["date"]);
                                    $child_name = pull_field("core_students", "db39", "WHERE id='$msg[rel_id]'");
                                    if ($msg['rec_id'] === $student_uid) {
                                        $sender = 'You';//
                                        $checked = 'checked="checked"';
                                        //echo "<br> marked as read: ".$msg['db139']."<br>";
                                        if ($msg['db139'] == 'no') {
                                            $checked = '';
                                        }
                                        echo "<tr data-status=\"" . $msg['rel_id'] . "\">
                                                    <td>
                                                        <div class=\"\">
                                                            <input type=\"checkbox\" id=\"$msg[id]\" $checked disabled=\"disabled\" title=\"mark as read\">
                                                            <label for=\"checkbox1\"></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class=\"media\">
                                                            <a href=\"#\" class=\"pull-left\">
                                                                <img src=\"$_SESSION[domain]/engine/images/blank_user_icon_fallback.png\" class=\"media-photo\">
                                                            </a>
                                                            <div class=\"media-body\">
                                                                <span class=\"media-meta pull-right\">$date</span>
                                                                <h4 class=\"title\">
                                                                    $sender ($child_name)
                                                                </h4>
                                                                <p class=\"summary\">$content</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>";
                                    } else {
                                        $sender = pull_field("form_users", "db106", "WHERE id='$msg[rec_id]'");
                                        $checked = 'checked="checked"';
                                        if ($msg['db139'] == 'no') {
                                            $checked = '';
                                        }
                                        echo "<tr data-status=\"" . $msg['rel_id'] . "\">
                                                    <td>
                                                        <div class=\"\">
														
                                                            <input type=\"checkbox\" id=\"$msg[id]\" $checked title=\"mark as read\">
                                                            <label for=\"checkbox1\"></label>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class=\"media\">
                                                            <a href=\"#\" class=\"pull-right\">
                                                                <img src=\"$_SESSION[domain]/engine/images/blank_user_icon_fallback.png\" class=\"media-photo\">
                                                            </a>
                                                            <div class=\"media-body\">
                                                                <span class=\"media-meta pull-right\">$date</span>
                                                                <h4 class=\"title\">
                                                                    $sender ($child_name)
                                                                </h4>
                                                                <p class=\"summary\">$content</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>";


                                    }
                                }
                                if ($count_messages == 0) {
                                    echo "<tr><td></td><td>" . translate("Sorry, no messages found", $_SESSION['Send']) . "</td></tr>";
                                }
                                ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!--new chat-->
        <?php //} // end session check ?>
        <div class="clearfix"></div>

    </div><!--/span-->

</div> <!-- .row -->
<div class="row">
    <?php $class_resources = "col-xs-12 col-lg-12";
    include('static/inc/inc_shared_resources.php'); ?>
</div>

<!--
<div class="row">
    <div class="col-xs-12">
    <h3>Please read this important information before you begin your application</h3>
        <div style="width:650px; height:400px; overflow:auto; border:4px solid #f0f0f0; padding:5px; background-color:#f4f4f4; margin-bottom:10px">
          <?php
// FUNCTION TO GET_CMS
//list($page_id,$cms_category,$cms_page_name,$cms_heading,$cms_brief,$cms_article)=get_cms('page_name',"Tier4_Information");
//echo $cms_article;//pull_field("form_cms","db650","WHERE db647='Tier4_Information'");
?>
        </div>
    </div>
</div>
-->


<?php

$un_verified_email = 0;
if ($un_verified_email) { ?>
    <!-- Verify Email Before Procceding  -->
    <div class="modal fade" id="email_verification" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="myModalLabel">Verify Email Address</h4>
                </div>
                <div class="modal-body text-center">
                    <span class="glyphicon glyphicon-envelope" aria-hidden="true"
                          style="font-size: 54px; margin-bottom: 15px;"></span>
                    <div class="clearfix"></div>
                    Please verify you email address by clicking on a link that we sent to your email address. Check your
                    email.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary btn-block" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function () {
            $("#email_verification").modal("show");
        });
    </script>
<?php } ?>
