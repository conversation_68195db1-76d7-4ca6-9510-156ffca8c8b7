<?php
$show_coupon_message = false;
$show_invalid_access_code_message = false;
if (isset($_REQUEST['access_plan_unlock_code']) && $_REQUEST['access_plan_unlock_code'] != '') {
    //check if this is a valid access plan unlock code...
    $plan_args = array('access_code' => $_REQUEST['access_plan_unlock_code'], 'school_id' => $_SESSION['usergroup']);
    $access_plan_levels = pull_field("ols_usrlvl_pln_crs_link", "db37368", "WHERE db37366 = $_SESSION[ulevel] AND usergroup = $_SESSION[usergroup]");

    if ($access_plan_levels) {
        $access_plan_username_id = pull_field("ols_access_plans", "username_id", "WHERE FIND_IN_SET('$_REQUEST[access_plan_unlock_code]',db21881) AND (rec_archive is NULL or rec_archive = '') AND ols_access_plans.db26817 IN ($access_plan_levels)");
    } else {
        $access_plan_username_id = pull_field("ols_access_plans", "username_id", "WHERE FIND_IN_SET('$_REQUEST[access_plan_unlock_code]',db21881) AND (rec_archive is NULL or rec_archive = '')");
    }

    //$access_plan_username_id = pull_field ("ols_access_plans","username_id","WHERE db21881='".$_REQUEST['access_plan_unlock_code']."' AND (rec_archive is NULL or rec_archive = '')");

    if (isset($access_plan_username_id) && $access_plan_username_id != '') {
        //valid access plan - redirect to show that access plan
        header("Location: " . home_url("/buy-course") . "?access_plan=" . $access_plan_username_id . "&accode=" . $_REQUEST['access_plan_unlock_code']);
        die();
    } else {
        //check to see if this is a coupon code
        $coupon_id = pull_field("ols_coupons", "id", "WHERE db21962='" . $_REQUEST['access_plan_unlock_code'] . "' AND (rec_archive is NULL or rec_archive = '')");
        if (isset($coupon_id) && $coupon_id != '') {
            $show_coupon_message = true;
        } else {
            $show_invalid_access_code_message = true;
        }

    }
    //if not debug

}
$website_address = pull_field("form_schools", "db32", "WHERE id={$_SESSION['usergroup']}");
$website_address = (stripos($website_address, "https://") !== false) ? $website_address : "https://" . $website_address;
if (empty($website_address)) {
    $website_address = home_url("/");
}
//skip second stage in sccess plans 
if (isset($_REQUEST['accode'])) {
    $username_id_is_set = 0;
    $access_plan_levels = pull_field("ols_usrlvl_pln_crs_link", "db37368", "WHERE db37366 = $_SESSION[ulevel] AND usergroup = $_SESSION[usergroup]");
    if (isset ($_REQUEST['access_plan']) && $_REQUEST['access_plan'] != '') {
        $plan_args = array(
            'username_id' => $_REQUEST['access_plan'],
            'school_id' => $_SESSION['usergroup'],
        );
        $username_id_is_set = 1;
    } else {
        $plan_args = array(
            'search' => $_POST['search'],
            'school_id' => $_SESSION['usergroup'],
        );
    }
    if ($username_id_is_set == 1) {
        if ($access_plan_levels) {
            $plan_args['access_plan_level'] = $access_plan_levels;
        }
        $plans = array($OL->get_plans($plan_args));
    } else {

        if (isset ($_REQUEST['course']) && $_REQUEST['course'] != '') {

            $plan_args = array(
                'course' => $_REQUEST['course'],
                'school_id' => $_SESSION['usergroup'],
            );
        } else {
            $plan_args = array(
                'dont_show_hidden_plans' => true,
                'school_id' => $_SESSION['usergroup']
            );

        }
        if ($access_plan_levels) {
            $plan_args['access_plan_level'] = $access_plan_levels;
        }
        $plans = $OL->get_plans($plan_args);
    }
    foreach ($plans as $plan) {
        $courses = $plan['courses'];
        if (isset($plan['access_plan_language']) && $plan['access_plan_language'] != '') {
            $ap_language = '&lang=' . pull_field("form_languages", "db21281", "WHERE id = $plan[access_plan_language]");
        } else {
            $ap_language = '';
        }
        if ($_SESSION['uid']) {
            $location_url = home_url('register_new') . '?plan=' . $plan['username_id'] . $ap_language;
        } else {
            if ($_SESSION['ulevel'] != 19) {
                $location_url = home_url('register_new') . '?plan=' . $plan['username_id'] . $ap_language;
            } else {
                $location_url = home_url('login') . '?redirect=' . home_url('register_new') . '%3Fplan=' . $plan['username_id'] . $ap_language;
            }
        }
        //redirect to third steps
        header("Location: " . $location_url);

    }
}

?>
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8"/>
    <title>Online Learning</title>
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,600,700" rel="stylesheet">
    <script type="text/javascript" src="<?php echo home_url(); ?>assets/js/jquery.min.js"></script>
    <?php if (strpos($_SERVER['REQUEST_URI'], "register_new") !== false) { ?>

        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/6.6.5/sweetalert2.css"/>
        <script type="text/javascript" src="https://js.stripe.com/v2/"></script>
        <script type="text/javascript" src="https://checkout.stripe.com/checkout.js"></script>
    <?php if ($_SESSION['usergroup'] != '47') { ?>
        <script type="text/javascript" src="<?php echo home_url(); ?>assets/js/stripe.js"></script>
    <?php } ?>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/limonte-sweetalert2/6.6.5/sweetalert2.js"></script>
    <?php } ?>
    <!--    <link rel="stylesheet" type="text/css" href="-->
    <?php //echo home_url(); ?><!--assets/css/bootstrap.min.css">-->
    <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet"
          integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">

    <script type="text/javascript" src="<?php echo home_url(); ?>assets/js/vue.global.js"></script>
    <script type="text/javascript" src="<?php echo home_url(); ?>assets/js/flowbite.min.js"></script>
    <script type="text/javascript" src="<?php echo home_url(); ?>assets/js/carousel.min.js"></script>
    <script type="text/javascript" src="<?php echo home_url(); ?>assets/js/vue-select.umd.js"></script>
    <script type="text/javascript" src="<?php echo home_url(); ?>assets/js/vue-sweetalert.min.js"></script>
    <script type="text/javascript" src="<?php echo home_url(); ?>assets/js/bootstrap.min.js"></script>
    <link rel="stylesheet" type="text/css" href="<?php echo home_url(); ?>assets/css/styles.css?v=3">
    <link rel="stylesheet" type="text/css" href="<?php echo home_url(); ?>assets/css/tailwind.css?v=3">
    <link rel="stylesheet" type="text/css" href="<?php echo home_url(); ?>assets/css/carousel.min.css?v=3">
    <link rel="stylesheet" type="text/css" href="<?php echo home_url(); ?>assets/css/sweetalert2.min.css?v=3">
    <link rel="stylesheet" type="text/css" href="<?php echo home_url(); ?>assets/css/vue-select.css?v=3">
    <link rel="stylesheet" type="text/css" href="<?php echo home_url(); ?>assets/css/shortcodes/box-shortcodes.css?v=4">
    <link rel="stylesheet" type="text/css" href="<?php echo home_url(); ?>assets/css/shortcodes/magnific-popup.css?v=4">
    <link rel="stylesheet" type="text/css"
          href="<?php echo home_url(); ?>assets/css/shortcodes/galleries-shortcodes.css?v=4">
    <link rel="stylesheet" type="text/css"
          href="<?php echo home_url(); ?>assets/css/shortcodes/media-shortcodes.css?v=4">
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XL2T3V88EJ"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }

        gtag('js', new Date());

        gtag('config', 'G-XL2T3V88EJ');
    </script>
    <!-- Hotjar Tracking Code for https://inourplace.heiapply.com/online-learning/ -->
    <script>
        (function (h, o, t, j, a, r) {
            h.hj = h.hj || function () {
                (h.hj.q = h.hj.q || []).push(arguments)
            };
            h._hjSettings = {hjid: 4937862, hjsv: 6};
            a = o.getElementsByTagName('head')[0];
            r = o.createElement('script');
            r.async = 1;
            r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
            a.appendChild(r);
        })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
    </script>

    <script type="text/javascript" charset="UTF-8"
            src="https://translate.googleapis.com/translate_static/js/element/main.js"></script>
    <link type="text/css" rel="stylesheet" charset="UTF-8"
          href="https://translate.googleapis.com/translate_static/css/translateelement.css">
    <style>
        .skiptranslate.goog-te-gadget, .skiptranslate.goog-te-gadget > div {
            display: inline-block;
        }

        html.translated-ltr body nav.navbar.navbar-fixed-top {
            padding-top: 39px;
        }
    </style>
</head>
<body class="relative font-quicksand w-screen overflow-x-hidden bg-white">
<?php
// FUNCTION TO GET_FORM_KNOWLEGEBASE
list($notice_on_off,$notice_offline_message) = explode(',',pull_field("form_knowlegebase","concat_ws(',',db1274,db1190)","WHERE id=461"));

if($notice_on_off=='yes'){
echo '<div class="col-md-12" style="background:#e2e2e2; color:#000000; padding:10px; text-align:center">'.$notice_offline_message.'</div><div class="clear"></div>';
}
?>
<nav class="bg-white border-b border-gray-200 dark:bg-gray-900 sticky top-0 z-50 ">
    <div class="w-screen flex flex-wrap items-center justify-between mx-auto py-4 px-16">
        <div class="flex items-center gap-4">
            <a href="https://solihullapproachparenting.com" class="flex items-center  hover:opacity-30">
                <img src="<?php echo '/static/' . $_SESSION['subdomain'] . '/resources/img/img.solihull.svg' ?>"
                     alt="logo"/></a>

            <a href="https://inourplace.co.uk" class="flex  items-center  hover:opacity-30">
                <img src="<?php echo '/static/' . $_SESSION['subdomain'] . '/resources/img/img.inourplace.svg' ?>"
                     alt="logo"/></a>
            <?php if (is_user_logged_in()) { ?>
                <a href="/online-learning/dashboard"
                   class="block py-2 pl-3 pr-4 hover:text-brand-green rounded md:bg-transparent md:p-0 dark:text-white  flex items-center <?php echo find_slug(1) == 'dashboard' ? 'text-active' : 'text-black' ?>"
                   aria-current="page">Dashboard</a>
                <a href="/online-learning/courses"
                   class="self-center whitespace-nowrap hover:text-brand-green dark:text-white  <?php echo find_slug(1) == 'courses' ? 'text-active' : '' ?>">Course
                    library</a>
            <?php } else { ?>
                <a href="/online-learning"
                   class="self-center whitespace-nowrap hover:text-brand-green dark:text-white  <?php echo find_slug(1) == '' ? 'text-active' : '' ?>">Course
                    library</a>
            <?php } ?>
            <a class="self-center whitespace-nowrap text-black hover:text-brand-green dark:text-white  cursor-pointer"
               data-modal-target="checker-modal"
               data-modal-toggle="checker-modal">Free access
                checker</a>
            <?php if (!empty($header_google)): ?>
                <div id="google_translate_header_element" class=""></div>
            <?php endif; ?>
        </div>
        <button data-collapse-toggle="navbar-default" type="button"
                class="inline-flex items-center p-2 w-10 h-10 justify-center text-sm text-gray-500 rounded-lg md:hidden hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 dark:text-gray-400 dark:hover:bg-gray-700 dark:focus:ring-gray-600"
                aria-controls="navbar-default" aria-expanded="false">
            <span class="sr-only">Open main menu</span>
            <svg class="w-5 h-5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 17 14">
                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M1 1h15M1 7h15M1 13h15"/>
            </svg>
        </button>
        <div class="hidden w-full md:block md:w-auto" id="navbar-default">
            <ul class="  flex flex-col p-4 md:p-0 mt-4 border border-gray-100 rounded-lg bg-gray-50 md:flex-row md:space-x-8 md:mt-0 md:border-0 md:bg-white dark:bg-gray-800 md:dark:bg-gray-900 dark:border-gray-700">
                <?php if (is_user_logged_in()) { ?>
                    <li>
                        <div class="flex items-center md:order-2">
                            <button type="button" data-dropdown-toggle="language-dropdown-menu"
                                    class="inline-flex items-center font-medium justify-center px-4 py-2 text-sm text-gray-900 dark:text-white rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 dark:hover:text-white">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                     stroke-width="1.5" stroke="currentColor" aria-hidden="true"
                                     class="w-5 h-5 mr-2 rounded-full">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                          d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                <?= $_SESSION['user'] ?>
                                <svg class="w-2.5 h-2.5 ms-3" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                                     fill="none" viewBox="0 0 10 6">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                                          stroke-width="2" d="m1 1 4 4 4-4"/>
                                </svg>
                            </button>
                            <!-- Dropdown -->
                            <div class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700"
                                 id="language-dropdown-menu">
                                <ul class="py-2 font-medium w-44" role="none">
                                    <li>
                                        <a href="/online-learning/profile"
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-brand-green dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white"
                                           role="menuitem">
                                            Edit Profile
                                        </a>
                                    </li>
                                    <li>
                                        <a data-modal-target="personalisation-modal"
                                           data-modal-toggle="personalisation-modal"
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-brand-green dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white"
                                           role="menuitem">
                                            Manage preferences
                                        </a>
                                    </li>
                                    <li class="hidden">
                                        <a href="#"
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-brand-green dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white"
                                           role="menuitem">
                                            Account Settings
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/online-learning/my-access-plans"
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-brand-green dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white"
                                           role="menuitem">
                                            My Access Plans
                                        </a>
                                    </li>
                                    <li>
                                        <a href="/online-learning/support"
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-brand-green dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white"
                                           aria-current="page">Support</a>
                                    </li>
                                    <li>
                                        <a href="/online-learning/logout"
                                           class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-brand-green dark:text-gray-400 dark:hover:bg-gray-600 dark:hover:text-white"
                                           role="menuitem">
                                            Sign Out
                                        </a>
                                    </li>
                                </ul>
                            </div>
                    </li>
                <?php } else { ?>
                    <li>
                        <a href="/online-learning/register"
                           class="block py-2 pl-3 pr-4  bg-blue-700 rounded md:bg-transparent md:p-0 dark:text-white <?php echo find_slug(1) == 'register' ? 'text-active' : 'text-black' ?>"
                           aria-current="page">Register | Learner</a>
                    </li>
                    <li>
                        <a href="/online-learning/professional_registration"
                           class="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent">Register
                            | Professional</a>
                    </li>
                    <li>
                        <a href="/online-learning/login"
                           class="block py-2 pl-3 pr-4 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent  <?php echo find_slug(1) == 'login' ? 'text-active' : 'text-black' ?>">Sign
                            In</a>
                    </li>
                <?php } ?>
                <li>
                    <a href="https://www.nhs.uk"
                       class="block py-2 pl-3 pr-4 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:border-0 md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent hover:opacity-30">
                        <img src="<?php echo '/static/' . $_SESSION['subdomain'] . '/resources/img/img.NHS.svg' ?>"
                             alt="logo"/>
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>
<?php if (!empty($header_google)): ?>
    <script>
        function googleTranslateElementInit() {
            new google.translate.TranslateElement({
                pageLanguage: 'en-GB',
                layout: google.translate.TranslateElement.InlineLayout.SIMPLE
            }, 'google_translate_header_element');
        }
    </script>
<?php endif; ?>
<script type="text/javascript"
        src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>


<?php if ($path !== 'courses-html'): include("views/eligibility_checker.php"); ?>
    <?php include("views/personalization_modal.php");
endif; ?>

<div id="pushContent"></div>
