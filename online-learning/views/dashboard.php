<?php
/** ===================================
 * Check for user login info
 * ====================================*/
$users = new Users;
$user_args = array('id' => $_SESSION['uid']);
$user_info = $users->get($user_args);

$course_args = array(
    'student_id' => $_SESSION['student_id'],
    'active_courses' => true,
    'raw_html' => false,
    'no_quiz_information' => true,
    'no_unit_description' => true,
    'count_modules' => true,
    'with_categories' => true,
    'no_modules' => true
);

dev_debug("get_courses_v2 4");
$your_courses = $OL->get_courses_v2($course_args);

$access_plan_args = array(
    'student_id' => $_SESSION['student_id'],
    'summary_only' => true
);
$your_access_plans = $OL->get_plans($access_plan_args);

//$students = new Students;
//$avatar = $students.get_student_avatar($_SESSION['student_id']);
//if (!$avatar || $avatar='') {
/*if($user_info['gender']=="Male"){
	$profile_pic = "assets/images/male_icon.jpg";
}else{
	$profile_pic = "assets/images/female_icon.jpg";
}*/
$profile_pic = "assets/images/user_icon.jpg";

if ($_SESSION['ulevel'] == '19') {
    $learner_type = 'Professional';

} else {
    $learner_type = 'Learner';
}
//}
//else {

//}
?>
<style>
    html {
        scroll-behavior: smooth;
    }

    .col-sm-3 .dash_access_code {
        text-align: center;
        padding-bottom: 10px !important;
    }

    .continue_learning {
        padding: 10px 15px;
    }

    .tile-progress .tile-progressbar span {
        background: #fff;
    }

    .tile-progress .tile-progressbar span {
        display: block;
        background: #fff;
        width: 0;
        height: 100%;
        -webkit-transition: all 1.5s cubic-bezier(0.230, 1.000, 0.320, 1.000);
        -moz-transition: all 1.5s cubic-bezier(0.230, 1.000, 0.320, 1.000);
        -o-transition: all 1.5s cubic-bezier(0.230, 1.000, 0.320, 1.000);
        transition: all 1.5s cubic-bezier(0.230, 1.000, 0.320, 1.000);
    }


    /* The colour of the indicators */
    .carousel-indicators li {
        background: #cecece;
    }

    .carousel-indicators .active {
        background: #428bca;
    }

    @media only screen and (max-width: 768px) {
        .col-sm-6 {
            margin-bottom: 15px;
        }
    }

    @media only screen and (max-width: 400px) {
        .carousel {
            margin-bottom: 0;
            padding: 0;
        }
    }
</style>
<div class="w-full min-h-[calc(100vh-7.5rem)] p-4" id="dash-app" v-cloak>
    <div class="bg-white rounded-lg shadow-lg p-4 overflow-hidden mb-8 w-3/4 mx-auto mt-4">
        <div class="flex gap-2">
            <div class="w-5/12 flex gap-2">
                <div>
                    <a href="<?php echo home_url("/edit_profile"); ?>"
                       class="block bg-cover bg-no-repeat bg-center w-24 h-24 rounded-full"
                       style="background-image: url(<?php echo $profile_pic; ?>);"
                    >
                    </a>
                </div>
                <div class="flex flex-col gap-2">
                    <h1 class="font-semibold text-2xl">Welcome
                        Back, <span v-text="userInfo ? userInfo.first_name : learnerType"></span></h1>
                    <p><a href="/online-learning/profile">Edit <span v-text="learnerType"></span> profile</a>
                    </p>
                    <p v-if="school == 42 && learnerType=='Learner' "><a
                                href="<?php echo $website_url_applicant ?? '' ?>/application/Checklist"> Unlock courses
                            for
                            professionals </a></p>
                </div>
            </div>
            <div class="w-3/12">
                <div class="p-4 border-l border-gray-400 pl-5 flex flex-col items-center justify-center gap-2">
                    <a class="block bg-white border-2 border-brand-green text-brand-green p-2.5 w-full hover:bg-brand-green hover:text-white rounded-full font-bold text-center"
                       href="#learn_now">Start Learning Now!</a>
                    <a class="block bg-white border-2 border-brand-orange text-brand-orange p-2.5 w-full hover:bg-brand-orange hover:text-white rounded-full font-bold text-center"
                       href="<?php echo home_url("/courses"); ?>">Buy
                        More Courses</a>
                </div>
            </div>
            <div class="w-4/12">
                <div class="p-4 border-l border-gray-400 pl-5 flex flex-col">
                    <form action='<?php echo home_url("/dashboard") ?>'>
                        <label for="search" class="mb-2 text-sm font-medium text-gray-900">Enter access code</label>
                        <div class="relative mt-4">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                     stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                          d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z"/>
                                </svg>
                            </div>
                            <input type="search" name="access_plan_unlock_code" id="search"
                                   class="block w-full p-4 pl-10 text-sm text-gray-900 border border-gray-300 rounded-full bg-transparent focus:ring-brand-blue focus:border-brand-blue"
                                   required>
                            <button type="submit"
                                    class="text-white absolute right-2.5 bottom-2.5 bg-brand-blue/70 hover:bg-brand-blue/80 focus:ring-4 focus:outline-none focus:ring-brand-blue/40 font-medium rounded-full text-sm px-4 py-2">
                                Verify
                            </button>
                        </div>
                    </form>
                    <p v-if="showCouponMsg.length > 0">This is a coupon code
                    <p>
                    <p v-if="invalidCodeMsg.length > 0 && professionalCode">Please unlock courses for professionals
                        before verifying your access code</p>
                    <p v-if="invalidCodeMsg.length > 0 && !professionalCode">This is an invalid access plan unlock
                        code</p>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center w-full flex" v-if="myCourses && myCourses.length == 0 && !userInfo.last_course_id">
        <div class="w-1/4 flex flex-col gap-4 mx-auto items-center justify-center rounded-lg p-3">
            <h3 class="font-bold tex-3xl">No courses selected yet!</h3>
            <a class="block bg-white border-2 border-brand-green text-brand-green p-2.5 w-full hover:bg-brand-green hover:text-white rounded-full font-bold text-center"
               href="<?php echo home_url("/courses"); ?>">Look for a course </a>
            <div class="clear"></div>
            <img src="assets/images/books.jpg">
        </div>
    </div>


    <div class="w-full flex flex-col gap-10" v-else>
        <div v-if="coursesToContinue && coursesToContinue.length > 0"
             class="w-full flex justify-center items-center mt-4">
            <div class="w-full rounded-lg">
                <div top-bar class="flex justify-between bg-brand-blue text-white rounded-t-lg">
                    <h3 class="font-semibold p-2">Continue Learning <small>(Pick up from where you left)</small>
                    </h3>
                </div>
                <div feature-body class="mt-5 rounded-b-lg h-60">
                    <Carousel :items-to-show="coursesToContinue.length < 3 ? 1: 3.5" snap-align="start"
                              :wrap-around="false">
                        <template #slides>
                            <Slide v-for="course in coursesToContinue" :key="course.id">
                                <div class="carousel__item w-full">
                                    <div class="w-full h-[17rem] rounded-lg shadow-lg flex flex-row  border border-tile-border relative">

                                        <div class="h-full w-3/12 rounded-l-lg bg-brand-orange bg-cover bg-center bg-no-repeat"
                                             :style="{backgroundImage: `url(${course.thumbnail ? course.thumbnail : '<?php echo '/static/' . $_SESSION['subdomain'] . '/resources/img/hero_placeholder.jpg' ?>'})` , backgroundColor:course.color}">

                                        </div>
                                        <div class="h-full w-2" :style="{backgroundColor: course.colour}"></div>
                                        <div class="flex w-8/12 flex-col justify-between flex-grow pb-2">
                                            <div class="flex flex-col gap-1 w-full items-start text-left py-2 px-1">
                                                <h5 category class="uppercase text-sm" :style="{color: course.colour}">
                                                    {{course.category_name}}</h5>
                                                <h3 class="font-bold font-sm text-title-color">{{ course.title }}</h3>
                                                <h6 class="text-xs font-bold"
                                                    v-if="Object.keys(course.transition_course).length == 0">
                                                    {{course.module_count}} Modules</h6>
                                                <p class="font-light text-xs text-gray-400 line-clamp-2"
                                                   v-if="Object.keys(course.transition_course).length == 0"
                                                   v-html="course.excerpt"></p>
                                                <div class="flex flex-col"
                                                     v-if="course.completion_percentage != 100 &&  Object.keys(course.transition_course).length > 0">
                                                    <p class="font-semibold text-xs text-red-400"
                                                       v-if="!course.transition_course.automatic_transition">
                                                        We have a revised version of this course. If you choose to
                                                        switch to the new version you will be starting again but you
                                                        will be getting the latest updates to the course. Once you
                                                        switch you will not be able to view the old version. If you
                                                        choose to resume on the old version you will be able to continue
                                                        on the old course, and come back to this screen to transition
                                                        when it is right for you</p>
                                                    <p class="font-semibold text-xs text-red-400" v-else>
                                                        We have an updated version of this course and will be
                                                        transitioning you to it when you click 'okay'. Please note that
                                                        any progress you have made in the current version will not be
                                                        saved.</p>
                                                    <div class="flex gap-2 justify-between items-center mt-2"
                                                         v-if="Object.keys(course.transition_course).length > 0 && !course.transition_course.automatic_transition">
                                                        <a
                                                                @click.prevent="switch_to_new_course(course)"
                                                                class="flex gap-2 items-center justify-center rounded-full border-2 px-2 cursor-pointer"
                                                                :style="{borderColor : course.colour, color : course.colour}">
                                                            <span>Switch to new version</span>
                                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                 viewBox="0 0 24 24" stroke-width="1.5"
                                                                 stroke="currentColor" class="w-6 h-6">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                      d="M7.5 21 3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5"/>
                                                            </svg>
                                                        </a>
                                                        <a
                                                                :href="course.resume_link"
                                                                class="flex gap-2 items-center justify-center rounded-full border-2 px-2"
                                                                :style="{borderColor : course.colour, color : course.colour}">
                                                            <span>Resume on old version</span>
                                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                 viewBox="0 0 24 24" stroke-width="1.5"
                                                                 stroke="currentColor"
                                                                 class="w-6 h-6">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                      d="M12.75 15l3-3m0 0l-3-3m3 3h-7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                            </svg>
                                                        </a>
                                                    </div>
                                                    <div class="flex gap-2 justify-between items-center mt-2"
                                                         v-if="Object.keys(course.transition_course).length > 0 && course.transition_course.automatic_transition">
                                                        <a
                                                                @click.prevent="switch_to_new_course(course)"
                                                                class="flex gap-2 items-center justify-center rounded-full border-2 px-2  text-brand-green border-brand-green cursor-pointer"
                                                        >
                                                            <span>Okay</span>
                                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                                 viewBox="0 0 24 24" stroke-width="1.5"
                                                                 stroke="currentColor" class="w-6 h-6">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                      d="m4.5 12.75 6 6 9-13.5"/>
                                                            </svg>

                                                        </a></div>
                                                </div>

                                            </div>
                                            <div class="w-full bg-gray-200 dark:bg-gray-700">
                                                <div class="text-xs font-medium text-white text-center p-0.5 leading-none"
                                                     :style="{backgroundColor: course.colour, width: course.completion_percentage + '%'}">
                                                    {{course.completion_percentage}}%
                                                </div>
                                            </div>
                                            <div class="w-full flex justify-center items-center"
                                                 v-if="Object.keys(course.transition_course).length == 0 || course.completion_percentage == 100">
                                                <!-- <a :href="`/online-learning/views/pdf_create_certificate.php?certificate=${course.username_id}`"
                                                   v-if="course.completion_percentage == 100 && course.enable_download_certificate =='yes'"
                                                   target="_blank"
                                                   class="flex gap-2 items-center justify-center rounded-full border-2 px-2"
                                                   :style="{borderColor : course.colour, color : course.colour}">
                                                    <span>Download Certificate</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                         viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                                         class="w-6 h-6">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                              d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z"/>
                                                    </svg>
                                                </a> -->
                                                <a v-else-if="course.resume_link && course.completion_percentage < 100"
                                                   :href="course.resume_link"
                                                   class="flex gap-2 items-center justify-center rounded-full border-2 px-2"
                                                   :style="{borderColor : course.colour, color : course.colour}">
                                                    <span>Resume</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                         viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"
                                                         class="w-6 h-6">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                              d="M12.75 15l3-3m0 0l-3-3m3 3h-7.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                                    </svg>
                                                </a>
                                            </div>
                                            <div class="flex gap-2 justify-between text-xs pt-2">
                                                <div class="flex gap-1 px-2">
                                                    <span class="text-sm block">{{course.language.name}}</span>
                                                    <span v-if="course.language_has_icon"
                                                          class="block w-6 h-6 rounded-full bg-center bg-cover"
                                                          :style="{'background-image': 'url('+ course.language_icon+')'}"></span>
                                                </div>
                                                <div>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                         viewBox="0 0 24 24"
                                                         stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                              d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"/>
                                                    </svg>
                                                </div>
                                                <div v-if="course.has_audio">
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                         viewBox="0 0 24 24"
                                                         stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                              d="M19.114 5.636a9 9 0 010 12.728M16.463 8.288a5.25 5.25 0 010 7.424M6.75 8.25l4.72-4.72a.75.75 0 011.28.53v15.88a.75.75 0 01-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 012.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75z"/>
                                                    </svg>
                                                </div>
                                                <a :href="course.href"
                                                   v-if="Object.keys(course.transition_course).length == 0"
                                                   class="flex justify-end text-brand-green items-center">
                                                    <span class="text-sm">Details</span>
                                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                         viewBox="0 0 24 24"
                                                         stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                              d="M11.25 4.5l7.5 7.5-7.5 7.5m-6-15l7.5 7.5-7.5 7.5"/>
                                                    </svg>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Slide>
                        </template>
                        <template #addons v-if="coursesToContinue.length > 1">
                            <Navigation/>
                        </template>
                    </Carousel>
                </div>
            </div>
        </div>

        <div v-if="coursesToStart.length > 0" class="w-full flex justify-center items-center mt-4">
            <div class="w-full rounded-lg">
                <div top-bar class="flex justify-between bg-brand-blue text-white rounded-t-lg">
                    <h3 class="font-semibold p-2">Start Learning Now <small>(Click start course to start
                            learning)</small>
                    </h3>
                </div>
                <div feature-body class="mt-5 rounded-b-lg h-60">
                    <Carousel :items-to-show="coursesToStart.length < 3 ? 1 : 3.5" snap-align="start"
                              :wrap-around="false">
                        <Slide v-for="course in coursesToStart" :key="course.id">
                            <div class="carousel__item w-full">
                                <div class="w-full h-64 rounded-lg shadow-lg flex flex-row  border border-tile-border relative">
                                    <div class="h-full w-32 rounded-l-lg bg-brand-orange bg-cover bg-center bg-no-repeat"
                                         :style="{backgroundImage: `url(${course.thumbnail ? course.thumbnail : '<?php echo '/static/' . $_SESSION['subdomain'] . '/resources/img/hero_placeholder.jpg' ?>'})` , backgroundColor:course.color}">

                                    </div>
                                    <div class="h-full w-2" :style="{backgroundColor: course.colour}"></div>
                                    <div class="flex flex-col justify-between flex-grow pb-2">
                                        <div class="flex flex-col gap-2 w-full items-start text-left py-2 px-1">
                                            <h5 category class="uppercase text-sm" :style="{color: course.colour}">
                                                {{course.category_name}}</h5>
                                            <h3 class="font-bold font-sm text-title-color">{{ course.title }}</h3>
                                            <h6 class="text-xs font-bold">{{course.module_count}} Modules</h6>
                                            <p class="font-light text-xs text-gray-400 line-clamp-2">
                                                {{course.excerpt}}</p>
                                        </div>
                                        <div class="border-t border-gray-300 flex gap-2 justify-between text-xs pt-2">
                                            <div class="flex gap-1 px-2">
                                                <span class="text-sm block">{{course.language.name}}</span>
                                                <span v-if="course.language_has_icon"
                                                      class="block w-6 h-6 rounded-full bg-center bg-cover"
                                                      :style="{'background-image': 'url('+ course.language_icon+')'}"></span>
                                            </div>
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                     viewBox="0 0 24 24"
                                                     stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                          d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"/>
                                                </svg>
                                            </div>
                                            <div v-if="course.has_audio">
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                     stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                          d="M19.114 5.636a9 9 0 010 12.728M16.463 8.288a5.25 5.25 0 010 7.424M6.75 8.25l4.72-4.72a.75.75 0 011.28.53v15.88a.75.75 0 01-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.01 9.01 0 012.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75z"/>
                                                </svg>
                                            </div>
                                            <a :href="course.href"
                                               class="flex justify-end text-brand-green items-center">
                                                <span class="text-sm">Details</span>
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                                     viewBox="0 0 24 24"
                                                     stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                          d="M11.25 4.5l7.5 7.5-7.5 7.5m-6-15l7.5 7.5-7.5 7.5"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Slide>

                        <template #addons v-if="coursesToStart.length > 1">
                            <Navigation/>
                        </template>
                    </Carousel>
                </div>
            </div>
        </div>
    </div>

    <div class="w-full p-4 flex flex-col">
        <div class="w-full mt-5">
            <h3 class="font-bold text-2xl">Your courses</h3>
        </div>
        <div class="w-full mt-5">

            <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3">
                            Course Title
                        </th>
                        <th scope="col" class="px-6 py-3">
                            Progress
                        </th>
                        <th scope="col" class="px-6 py-3">
                            Action
                        </th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr class="border-b"
                        :class="{'bg-white' : index % 2 == 0 , 'bg-gray-50' : index % 2 !== 0}"
                        v-for="(course,index) in myCourses">
                        <th scope="row"
                            class="px-6 py-2 font-medium text-gray-900 whitespace-nowrap dark:text-white">
                            <a :href="course.href" class="flex flex-row gap-2 font-bold">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                     class="w-6 h-6">
                                    <path d="M11.25 4.533A9.707 9.707 0 006 3a9.735 9.735 0 00-3.25.555.75.75 0 00-.5.707v14.25a.75.75 0 001 .707A8.237 8.237 0 016 18.75c1.995 0 3.823.707 5.25 1.886V4.533zM12.75 20.636A8.214 8.214 0 0118 18.75c.966 0 1.89.166 2.75.47a.75.75 0 001-.708V4.262a.75.75 0 00-.5-.707A9.735 9.735 0 0018 3a9.707 9.707 0 00-5.25 1.533v16.103z"/>
                                </svg>
                                <span>{{course.title}}</span>
                            </a>
                        </th>
                        <td class="px-6 py-2">
                            {{course.completion_percentage}}%
                        </td>
                        <td class="px-6 py-2 flex flex-row gap-2">
                            <a class="block bg-white border-2 border-brand-orange text-brand-orange p-1 w-1/2 hover:bg-brand-orange hover:text-white rounded-full font-bold text-center"
                               :href="course.href">View
                                course</a>
                            <a v-if="course.completion_percentage >= 98 && course.enable_download_certificate=='yes'"
                               class="flex gap-2 justify-center items-center bg-white border-2 border-brand-green text-brand-green p-1 w-1/2 hover:bg-brand-green hover:text-white rounded-full font-bold text-center"
                               :href="`/online-learning/views/pdf_create_certificate.php?certificate=${course.username_id}`"
                               target="_blank"><span>Download
                                    certificate</span>
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                     stroke-width="1.5"
                                     stroke="currentColor" class="w-6 h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                          d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"/>
                                </svg>
                            </a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>

        </div>
    </div>


    <div class="w-full flex flex-col p-4" v-if="myPlans && myPlans.length > 0">
        <div class="w-full mt-5">
            <h3 class="font-bold text-2xl">Your subscriptions</h3>
        </div>

        <div class="w-full mt-5">

            <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
                <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3"><i class="fa fa-book" aria-hidden="true"
                                                             style="color: #fff;"></i>Plan title
                        </th>
                        <th scope="col" class="px-6 py-3">Number of Courses</th>
                        <th scope="col" class="px-6 py-3">&nbsp;</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="(plan, index) in myPlans" class="border-b"
                        :class="{'bg-white' : index % 2 == 0 , 'bg-gray-50' : index % 2 !== 0}">
                        <td class="px-6 py-2">
                            <a :href="`/online-learning/plan-registration?plan=${plan.username_id}`" class="flex gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                                     class="w-6 h-6">
                                    <path fill-rule="evenodd"
                                          d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                                          clip-rule="evenodd"/>
                                </svg>

                                {{plan.title}}
                            </a>
                        </td>
                        <td class="px-6 py-2">{{ plan.courses.length}} courses</td>
                        <td class="px-6 py-2 flex gap-2">
                            <a class="block bg-white border-2 border-brand-green text-brand-green p-1 w-1/2 hover:bg-brand-green hover:text-white rounded-full font-bold text-center"
                               :href="`/online-learning/plan-registration?plan=${plan.username_id}`">View</a>
                            <a class="block bg-white border-2 border-brand-orange text-brand-orange p-1 w-1/2 hover:bg-brand-orange hover:text-white rounded-full font-bold text-center"
                               :href="`/online-learning/plan-registration?plan=${plan.username_id}&cancel=1`">Cancel</a>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<script>
    const {Carousel, Navigation, Slide, Pagination} = VueCarousel;
    const Dashboard = Vue.createApp({
        components: {Carousel, Navigation, Slide, Pagination},
        data: () => ({
            userInfo: <?php echo json_encode($user_info) ?? [] ?>,
            learnerType: '<?php echo $learner_type; ?>',
            school: '<?php echo $_SESSION['usergroup']?>',
            showCouponMsg: '<?php echo $show_coupon_message ?? "" ?>',
            invalidCodeMsg: '<?php echo $show_invalid_access_code_message ?? "" ?>',
            professionalCode: '<?php echo strtoupper(substr($_REQUEST['access_plan_unlock_code'], -3)) == 'PRF' ?>',
            myCourses: <?php echo json_encode($your_courses) ?? [] ?>,
            myPlans: <?php echo json_encode($your_access_plans) ?: [] ?>,
            circumference: 30 * 2 * Math.PI,
            toContinue: false,
            switched: false,
        }),
        computed: {
            coursesToContinue() {
                if (this.toContinue) return false;
                this.toContinue = true;
                const mC = this.myCourses.filter(item => item.started_course === true);
                this.toContinue = false;
                return mC;
            },
            coursesToStart() {
                return this.myCourses.filter(item => item.started_course === false);
            },
        },
        methods: {
            switch_to_new_course(course) {
                Swal.fire({
                    title: "Are you sure?",
                    html: "<span style='color: #d33; font-size: 1.5rem;'>Please note that any progress you have made in the current version will not be saved !</span>",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Continue!",
                    background: "#fff",
                    backdrop: `rgba(0,0,123,0.4) left top no-repeat`
                }).then((result) => {
                    if (result.isConfirmed) {
                        fetch("/admin/ap_eligibility_checker/switch_course", {
                            method: "POST",
                            headers: {
                                "Content-Type": "application/json", // Set the appropriate content type
                            },
                            body: JSON.stringify(course),
                        })
                            .then((response) => {
                                if (!response.ok) {
                                    throw new Error("Network response was not ok");
                                }
                                return response.json();
                            })
                            .then((data) => {
                                if (data.success) {
                                    course.transition_course.switched = true;
                                    window.location.replace('/online-learning/course/' + course.transition_course.id);
                                }
                            })
                            .catch((error) => {
                                console.error("Fetch error:", error);
                            });
                    }
                });


            },
        }
    });
    Dashboard.mount('#dash-app')
</script>