<?php
global $db;
$users = new Users;
$user_args = array('id' => $_SESSION['uid']);
$user_info = $users->get($user_args);
if (!isset($user_info['id'])) {
    exit();
}

if ($_POST['action'] == "update_profile") {
    $profile_info = array(
        'first_name' => $_POST['first_name'],
        'last_name' => $_POST['last_name'],
        'gender' => $_POST['gender'],
        'telephone' => $_POST['telephone'],
        'id' => $_SESSION['uid'],
        'course_audio' => $_POST['course_audio'],
    );
    $users = new Users;
    $users->update($profile_info);

    header("Location: " . home_url("/edit_profile?updated=1"));
}

?>
<div class="w-screen overflow-x-hidden">
    <div class="w-3/4 mx-auto flex gap-4 mt-5">
        <div class="w-9/12">
            <form method="POST" class="bg-white rounded shadow-lg p-8 overflow-hidden mb-8 space-y-4">
                <h1 class="text-2xl font-bold">Edit Profile</h1>

                <?php if ($_GET['updated']) { ?>
                    <div class="alert alert-success" role="alert">Your profile has been updated successfully</div>
                <?php } ?>

                <div class="flex">
                    <div class="w-3/12">
                        <label>First Name</label>
                    </div>
                    <div class="w-9/12">
                        <input class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-2"
                               type="text" name="first_name"
                               value="<?php echo $user_info['first_name']; ?>" autofocus="autofocus">
                    </div>
                </div>

                <hr>

                <div class="flex gap-2">
                    <div class="w-3/12">
                        <label>Last Name</label>
                    </div>
                    <div class="w-9/12">
                        <input class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-2"
                               type="text" name="last_name"
                               value="<?php echo $user_info['last_name']; ?>">
                    </div>
                </div>

                <hr>

                <div class="flex gap-2">
                    <div class="w-3/12">
                        <label>Email</label>
                    </div>
                    <div class="w-9/12">
                        <input class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-2"
                               disabled="" type="text" value="<?php echo $user_info['email']; ?>"/>
                    </div>
                </div>


                <div class="gap-2 hidden">
                    <div class="w-3/12">
                        <label>About</label>
                    </div>
                    <div class="w-9/12">
                        <textarea class="form-control" type="text" name="about"
                                  style="height: 150px;"><?php echo $user_info['about']; ?></textarea>
                    </div>
                </div>

                <!--<hr>
					<?php /*$gender = pull_field("core_students","db44", "where rec_id = $user_info[id]"); */ ?>
					<div class="flex gap-2">
						<div class="w-3/12">
							<label>Gender</label>
						</div>
						<div class="w-9/12">
							<select class="form-control" name="gender" >
								<option <?php /*if($gender){ echo 'selected="selected"'; }*/ ?> value="">Choose...</option>
								<option <?php /*if($gender=="Male"){ echo 'selected="selected"'; }*/ ?> value="Male">Male</option>
								<option <?php /*if($gender=="Female"){ echo 'selected="selected"'; }*/ ?> value="Female">Female</option>
							</select>
						</div>
					</div>-->
                <input type="hidden" name="gender" value="">


                <hr>
                <?php $telephone = pull_field("core_students", "db765", "where rec_id = $user_info[id]"); ?>
                <div class="flex gap-2">
                    <div class="w-3/12">
                        <label>Mobile</label>
                    </div>
                    <div class="w-9/12">
                        <input class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-2"
                               type="text" value="<?php echo $telephone; ?>" name="telephone">
                    </div>
                </div>
                <hr>
                <div class="flex gap-2">
                    <div class="w-3/12">
                        <label>Course Audio</label>
                    </div>
                    <div class="w-9/12">

                        <?php

                        $course_audio = pull_field("core_students", "db25618", "where rec_id = $user_info[id]");
                        if (isset($course_audio) && $course_audio != '') {
                            if ($course_audio == 'on') {
                                $checked_on = "checked=\"checked\"";
                                $checked_off = "";
                            } else {
                                $checked_on = "";
                                $checked_off = "checked=\"checked\"";
                            }
                        } else {
                            $checked_on = "checked=\"checked\"";
                            $checked_off = "";
                        } ?>
                        <ul class="flex gap-4 items-center">
                            <li class="flex gap-2 items-center justify-center"><input value="on" id="id_yes"
                                                                                      name="course_audio"
                                                                                      type="radio" <?php echo $checked_on; ?>><label
                                        for="id_yes"
                                        class="">on</label>
                            </li>
                            <li class="flex gap-2 items-center justify-center"><input value="off" id="id_off"
                                                                                      name="course_audio"
                                                                                      type="radio" <?php echo $checked_off; ?>><label
                                        for="id_off"
                                        class="">off</label>
                            </li>
                        </ul>
                    </div>
                </div>

                <hr>

                <input class="block bg-white border-2 border-brand-green text-brand-green p-1 w-1/4 hover:bg-brand-green hover:text-white rounded-full font-bold text-center"
                       type="submit" value="Save changes">
                <input type="hidden" name="action" value="update_profile">
            </form>



            <form method="POST" class="bg-white rounded shadow-lg p-8 overflow-hidden mb-8 space-y-4">
                <h1 class="font-bold text-2xl">Would you like to receive a friendly reminder email if you have not
                    visited an uncompleted
                    course?</h1>

                <table class="w-full text-sm">
                    <thead>
                    </thead>
                    <tbody>
                    <?php
                    $course_args = array('student_id' => $_SESSION['student_id'], 'active_courses' => true, 'no_quiz_information' => true, 'no_unit_description' => true);
                    $your_courses = $OL->get_courses($course_args);
                    $discourses = pull_field("dir_core_students_meta", "db40613", "WHERE rel_id = $_SESSION[student_id]");
                    $coursesarray = explode(',', $discourses);
                    foreach ($your_courses as $course) {
                        ?>
                        <tr class="border-b flex p-2">
                            <td class="w-3/4"><?php echo $course['title']; ?> </td>
                            <td class="w-1/4">[<span
                                        class="rounded-md px-1 <?php if (!in_array($course['id'], $coursesarray)): ?>bg-green-700 text-white<?php endif ?>"><a
                                            href="" class="remindertogler">yes</a></span>/<span
                                        class="rounded-md px-1 <?php if (in_array($course['id'], $coursesarray)): ?>bg-red-700 text-white<?php endif ?>"><a
                                            href="" class="remindertogler" data-course_id="<?php echo $course['id'] ?>">no</a></span>,
                                default yes]
                            </td>
                        </tr>
                    <?php } ?>
                    </tbody>
                </table>

            </form>


            <form method="POST" class="bg-white rounded shadow-lg p-8 overflow-hidden mb-8 space-y-4">
                <h1 class="text-2xl font-bold">Supplementary questions</h1>

                <table class="w-full text-sm">
                    <thead>
                    <tr class="text-left">
                        <th>Question</th>
                        <th>Response</th>
                        <th>Date</th>
                        <th>Plan</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    // echo '<pre>';
                    // print_r();
                    // exit();
                    $supp_args = array('student_id' => $user_info['student_id']);
                    $supplementary_questions = $OL->get_supplementary_answers($supp_args);
                    foreach ($supplementary_questions as $question) {
                        ?>
                        <tr class="border-b">
                            <td><?php echo $question['question']; ?></td>
                            <td><?php echo $question['answer']; ?></td>
                            <td><?php echo $question['date']; ?></td>
                            <td><?php echo $question['plan']['title']; ?></td>
                        </tr>
                    <?php } ?>
                    </tbody>
                </table>

            </form>

            <form method="POST" class="bg-white rounded shadow-lg p-8 overflow-hidden mb-8 space-y-4">
                <h1 class="font-bold text-2xl">Challenge questions</h1>

                <table class="w-full text-sm">
                    <thead>
                    <tr class="text-left">
                        <th>Question</th>
                        <th>Response</th>
                        <th>Date</th>
                        <th>Plan</th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    // echo '<pre>';
                    // print_r();
                    // exit();
                    $chal_args = array('student_id' => $user_info['student_id']);
                    $challenge_questions = $OL->get_user_challenge_answers($chal_args);
                    foreach ($challenge_questions as $question) {
                        ?>
                        <tr class="border-b">
                            <td><?php echo $question['question']; ?></td>
                            <td><?php echo $question['answer']; ?></td>
                            <td><?php echo $question['date']; ?></td>
                            <td><?php echo $question['plan']['title']; ?></td>
                        </tr>
                    <?php } ?>
                    </tbody>
                </table>

            </form>


            <form method="POST" id="settings_page">


                <table class="table disabled_reminders">
                    <thead>
                    </thead>
                    <tbody>
                    <?php

                    $surveyoptin = pull_field("dir_core_students_meta", "db49864", "WHERE rel_id = $_SESSION[student_id]");
                    //$coursesarray=explode(',', $discourses);
                    ?>
                    <tr>
                        <td>We, the Ourplace team, may very occasionally wish to contact you to ask about your
                            experience of using our online courses. Your details will be treated entirely anonymously
                            and will not be passed onto any third party. We may invite you to take part in research
                            being undertaken by a third party, for example a sponsor who funded access to courses. You
                            will be under no obligation and your details will never be made available to the third party
                            by us. <br> Would you like to be contacted by the Ourplace Team and invited to give
                            feedback? [<span
                                    class="surveyoptinon <?php if ($surveyoptin == "" || $surveyoptin == "yes"): ?>surveyactiveyes<?php endif ?>"><a
                                        href="">yes</a></span>/<span
                                    class="surveyoptinoff <?php if ($surveyoptin == "no"): ?>surveyactiveno<?php endif ?>"><a
                                        href="" data-surveyoptin="no">no</a></span>, default yes]
                        </td>
                    </tr>

                    </tbody>
                </table>

            </form>
        </div>
        <div class="w-3/12">
            <?php include("includes/settings_side_menu.php"); ?>
        </div>
    </div>

</div>
