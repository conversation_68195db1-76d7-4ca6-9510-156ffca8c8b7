<?php
$featured_courses = [];
$courses = [];
//if (!empty($_SESSION['personalization'])) {
$featured_courses_args = array(
    'featured' => 1,
    'school_id' => $_SESSION['usergroup'],
    'raw_html' => false,
    'published_courses' => true,
    'no_quiz_information' => "true",
    'no_unit_description' => true,
    'count_modules' => true,
    'no_modules' => true,
    'with_categories' => true,
);
dev_debug("get_courses_v2 1");

$featured_courses = $OL->get_courses_v2($featured_courses_args);

$course_levels = pull_field("ols_usrlvl_pln_crs_link", "db37367", "WHERE db37366 = '" . $_SESSION['ulevel'] . "' AND usergroup = '" . $_SESSION['usergroup'] . "'");

$course_args = array(
    'search' => str_replace("'", "\'", $_POST['search']),
    'raw_html' => true,
    'school_id' => $_SESSION['usergroup'],
    'published_courses' => true,
    'no_unit_description' => true,
    'no_quiz_information' => "true",
    'count_modules' => true,
    'with_categories' => true,
    'no_modules' => true
);

if (!empty($slugs) && $slugs[3]) {
    $course_args['category'] = $slugs[3];
}
if (!empty($course_levels)) {
    $course_args['course_level'] = $course_levels;
}

$course_args['order'] = "ORDER BY db31066 DESC";
unset($course_args['order']);
$course_args['with_languages'] = true;
$course_args['main_language_courses'] = true;
dev_debug("get_courses_v2 3");
$courses = $OL->get_courses_v2($course_args);
//}
$preferences = $OL->preferences($_SESSION['usergroup']);

$hero = $preferences->db253406 ? '/static/' . $_SESSION['subdomain'] . '/resources/img/banner.jpg' : '/static/' . $_SESSION['subdomain'] . '/resources/img/hero_placeholder.jpg';

$categories = $_SESSION['categories'] = $_SESSION['categories'] ?: $OL->get_category(['usergroup' => $_SESSION['usergroup']]);;
$languages = $_SESSION['languages'] = $_SESSION['languages'] ?: $OL->get_languages(['ug_specific' => true]);

?>
<div class="w-screen overflow-hidden bg-gradient-to-b from-gray-50 to-white" id="course-library" v-cloak>
    <div class="w-full bg-cover bg-center h-72 flex flex-row justify-between px-4 py-2 relative"
         style='background-image: url("<?php echo $hero ?>")'>
        <div class="absolute z-0 bg-gradient-to-b from-transparent via-black/40 to-black/60 h-12 w-full bottom-0 left-0 blur-sm"></div>
        <div class="flex flex-col justify-end z-10">
            <h2 class="font-bold text-6xl text-white">Course library</h2>
            <p class="mt-4 text-lg text-white font-semibold">Browse our collection of free and available to buy online
                courses below.</p>
            <div class="flex gap-4 text-white text-sm mt-4 mb-1 font-semibold">
                <p class="flex gap-2 items-center">
                    <span class="w-5 h-5 rounded-full bg-cover bg-no-repeat bg-center"
                          style="background-image: url('/static/inourplace/resources/img/yes.png')"></span>
                    <span>Lifetime access</span></p>
                <p class="flex gap-2 items-center">
                    <span class="w-5 h-5 rounded-full bg-cover bg-no-repeat bg-center"
                          style="background-image: url('/static/inourplace/resources/img/yes.png')"></span>
                    <span>At your own pace</span>
                </p>
                <p class="flex gap-2 items-center">
                    <span class="w-5 h-5 rounded-full bg-cover bg-no-repeat bg-center"
                          style="background-image: url('/static/inourplace/resources/img/yes.png')"></span>
                    <span>Improve emotional health and wellbeing</span>
                </p>
            </div>
        </div>
        <div class="flex flex-col justify-evenly gap-2 z-10">
            <div class="border-2 border-white p-4 bg-brand-blue text-white rounded-md">
                <h3 class="font-semibold">Free for me?</h3>
                <p class="text-center text-sm my-2">Find out if you are in a prepaid area:</p>

                <div class="flex items-center justify-center mb-1">
                    <button role="button" class="text-center items-center" data-modal-target="checker-modal"
                            data-modal-toggle="checker-modal"><span
                                class="px-4 bg-white text-brand-blue rounded-3xl py-2">Free access checker</span>
                    </button>
                </div>
            </div>
            <div class="border-2 border-white p-4 bg-brand-orange text-white rounded-md">
                <h3 class="font-semibold">Access Code?</h3>
                <p class="text-center text-sm my-2  ">If you have an <span class="font-semibold">Access Code</span>
                    please
                    enter it below:</p>
                <div class="flex border border-white bg-white rounded-3xl">
                    <input type="text" class="border-none rounded-l-3xl w-4/5 bg-transparent text-gray-500"
                           placeholder="Enter code" v-model="code"/>
                    <input type="hidden" v-model="mustLogIn">
                    <button class="rounded-r-3xl bg-brand-orange text-white w-1/5 font-semibold"
                            @click="checkAccessCode">Apply
                    </button>
                </div>
            </div>
        </div>
    </div>
    <form method="POST" class="w-full p-4 bg-gray-100" @submit.prevent="">
        <div class="border border-gray-300 bg-white flex items-center rounded-full">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                 stroke="currentColor" class="w-6 h-6 rounded-l-full ml-2">
                <path stroke-linecap="round" stroke-linejoin="round"
                      d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z"/>
            </svg>
            <input class="flex-grow rounded-r-full h-8 border-transparent focus:border-transparent focus:ring-0"
                   type="text" name="search"
                   v-model="search"
                   placeholder="Search course titles and/or descriptions...">
        </div>
    </form>

    <hr class="h-px my-8 bg-gray-200 border-0 dark:bg-gray-700">

    <div class="w-full flex justify-center items-center mt-4 px-16" v-if="!search">
        <div class="w-full rounded-lg">
            <div top-bar class="flex justify-between bg-brand-blue text-white rounded-t-lg">
                <h3 class="font-semibold p-2">Featured courses</h3>
            </div>
            <div feature-body class="mt-5 rounded-b-lg h-72">
                <Carousel :items-to-show="3.5" snap-align="end" :wrap-around="true"
                          v-show="featuredCourses.length > 0" :key="key">
                    <template v-for="course in featuredCourses" :key="course.id">
                        <Slide :key="course.id">
                            <div class="carousel__item w-full">
                                <div class="w-full h-64 rounded-lg shadow-lg flex flex-row  border border-tile-border">
                                    <div class="h-full w-3/12 rounded-l-lg bg-cover bg-center"
                                         :style="{'background-image': `url(${course.blob})` }">

                                    </div>
                                    <div class="h-full w-2" :style="{backgroundColor: course.colour}"></div>
                                    <div class="flex flex-col w-8/12 flex-grow justify-between">
                                        <div class="flex flex-col gap-2 items-start text-left py-2 px-1">
                                            <h5 category
                                                class="uppercase text-green-600 text-sm">
                                                {{course.category_new ? course.category_new.name : 'n/a'}}</h5>
                                            <h3 class="font-bold font-xs text-title-color text-sm">{{course.title}}</h3>
                                            <h6 class="text-xs font-bold">{{course.module_count}} Modules</h6>
                                            <p class="font-semibold text-xs text-gray-500 line-clamp-2">
                                                {{course.excerpt}}</p>
                                        </div>
                                        <div class="border-t border-gray-300 flex gap-2 justify-between text-xs py-2 px-1">
                                            <div class="flex gap-1 items-center">
                                                <span class="text-sm px-2 font-medium">{{course.language ? course.language.name : 'English'}}</span>

                                                <img v-if="course.language_icon"
                                                     class="w-7 h-7 me-1 rounded-full"
                                                     :src="course.icon"/>
                                            </div>
                                            <div>
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                     stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                          d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"/>
                                                </svg>
                                            </div>
                                            <div>
                                                <svg v-if="course.has_audio"
                                                     xmlns="http://www.w3.org/2000/svg"
                                                     viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                                                    <path d="M13.5 4.06c0-1.336-1.616-2.005-2.56-1.06l-4.5 4.5H4.508c-1.141 0-2.318.664-2.66 1.905A9.76 9.76 0 001.5 12c0 .898.121 1.768.35 2.595.341 1.24 1.518 1.905 2.659 1.905h1.93l4.5 4.5c.945.945 2.561.276 2.561-1.06V4.06zM18.584 5.106a.75.75 0 011.06 0c3.808 3.807 3.808 9.98 0 13.788a.75.75 0 11-1.06-1.06 8.25 8.25 0 000-11.668.75.75 0 010-1.06z"/>
                                                    <path d="M15.932 7.757a.75.75 0 011.061 0 6 6 0 010 8.486.75.75 0 01-1.06-1.061 4.5 4.5 0 000-6.364.75.75 0 010-1.06z"/>
                                                </svg>
                                            </div>
                                            <a :href="course.href"
                                               class="flex justify-end text-brand-green items-center">
                                                <span class="block">Details</span>
                                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                                     stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                                    <path stroke-linecap="round" stroke-linejoin="round"
                                                          d="M11.25 4.5l7.5 7.5-7.5 7.5m-6-15l7.5 7.5-7.5 7.5"/>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </Slide>
                    </template>
                    <template #addons>
                        <navigation/>
                        <pagination/>
                    </template>
                </Carousel>
            </div>
        </div>
    </div>
    <div ref="professional" style="scroll-margin-top:50px;"></div>
    <div class="w-full flex justify-center items-center mt-8 px-16">
        <div class="w-full rounded-lg">
            <div top-bar class="flex justify-between bg-brand-blue text-white rounded-t-lg">
                <h3 class="font-semibold p-2">Full range of courses</h3>
            </div>
            <div category-filter class="w-full p-2 flex items-center border-b">
                <p class="text-sm">Filter by category:</p>
                <div class="flex gap-2 items-center">
                    <a class="block px-2 text-sm cursor-pointer hover:text-brand-blue"
                       :class="{'text-brand-blue font-semibold' : activeCategory.id == undefined}"
                       @click.prevent="activeCategory={}">All</a>
                    <a v-for="category in categories" :key="category.id" @click.prevent="setCategory(category)"
                       class="block text-sm px-2 before:content-['\|'] before:p-2 cursor-pointer hover:text-brand-blue"
                       :class="{'text-brand-blue font-semibold' : activeCategory.id == category.id}"
                       v-show="(category.id !=20 && category.id !=17) || (category.id==20 && level == 19 )">
                        {{category.name}}
                    </a>
                    <div
                            class="text-sm px-2 before:content-['\|'] before:p-2 cursor-pointer hover:text-brand-blue flex items-center gap-2"
                    >
                        <span>Auto-translate</span>
                        <span id="courses_google_translate_element" :class="{'animate-bounce' : pulse}"></span>
                    </div>
                </div>
            </div>
            <div feature-body class="mt-5 rounded-b-lg grid grid-cols-3 gap-4 content-start px-4">
                <template v-for="course in courses" v-if="courses.length">
                    <div class="w-full h-64 rounded-lg shadow-lg col-span-1 flex flex-row  border border-tile-border">
                        <div class="h-full w-3/12 rounded-l-lg bg-cover bg-center"
                             :style="{'background-image': `url(${course.blob})` }">

                        </div>
                        <div class="h-full w-2" :style="{backgroundColor: course.colour}"></div>
                        <div class="flex w-8/12 flex-grow flex-col justify-between">
                            <div class="flex flex-col gap-2 items-start text-left py-2 px-1">
                                <h5 category class="uppercase text-green-600 text-sm">
                                    {{course.category_new ? course.category_new.name : 'n/a'}}</h5>
                                <h3 class="font-bold font-xs text-title-color">
                                    {{course.title}}</h3>
                                <h6 class="text-xs font-bold">{{course.module_count}} Modules</h6>
                                <p class="font-semibold text-xs text-gray-500 line-clamp-2" v-html="course.excerpt"></p>
                            </div>
                            <div class="border-t border-gray-300 flex gap-2 justify-between text-xs py-2 px-1">
                                <div class="flex gap-1 items-center">
                                    <span class="text-sm font-medium">Language</span>
                                    <language-dropdown @scrolldown="setCategory" :id="`full-range-${course.id}`"
                                                       :languages="course.languages"
                                                       :icon="course.icon"></language-dropdown>
                                </div>
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                         stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                              d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"/>
                                    </svg>
                                </div>
                                <div>
                                    <svg v-if="course.has_audio" xmlns="http://www.w3.org/2000/svg"
                                         viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6">
                                        <path d="M13.5 4.06c0-1.336-1.616-2.005-2.56-1.06l-4.5 4.5H4.508c-1.141 0-2.318.664-2.66 1.905A9.76 9.76 0 001.5 12c0 .898.121 1.768.35 2.595.341 1.24 1.518 1.905 2.659 1.905h1.93l4.5 4.5c.945.945 2.561.276 2.561-1.06V4.06zM18.584 5.106a.75.75 0 011.06 0c3.808 3.807 3.808 9.98 0 13.788a.75.75 0 11-1.06-1.06 8.25 8.25 0 000-11.668.75.75 0 010-1.06z"/>
                                        <path d="M15.932 7.757a.75.75 0 011.061 0 6 6 0 010 8.486.75.75 0 01-1.06-1.061 4.5 4.5 0 000-6.364.75.75 0 010-1.06z"/>
                                    </svg>
                                </div>
                                <a :href="course.href" class="flex justify-end text-brand-green items-center">
                                    <span class="block">Details</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                         stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                              d="M11.25 4.5l7.5 7.5-7.5 7.5m-6-15l7.5 7.5-7.5 7.5"/>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </template>
                <div v-else class="col-span-4  h-56 p-4 flex justify-center items-center">
                    <div class="w-1/2 flex flex-col justify-center items-center">
                        <h3 class="font-bold text-2xl text-center">No results found</h3>
                        <div class="flex gap-4 text-gray-400 mt-4">
                            <a @click.prevent="search=''" class="cursor-pointer p-4">Clear Search</a>
                            <a @click.prevent="activeCategory={}" class="border-l cursor-pointer p-4">Clear Filter</a>
                            <a data-modal-target="personalisation-modal"
                               data-modal-toggle="personalisation-modal" class="border-l cursor-pointer p-4">Manage
                                Preferences</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    function googleTranslateElementInitCourses() {
        new google.translate.TranslateElement({
            pageLanguage: 'en-GB',
            layout: google.translate.TranslateElement.InlineLayout.SIMPLE
        }, 'courses_google_translate_element');
    }
</script>
<script type="text/javascript"
        src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInitCourses"></script>
<script>
    const {Carousel, Navigation, Slide, Pagination} = VueCarousel;

    const courses = Vue.createApp({
            components: {
                Carousel, Navigation, Slide, Pagination,
                vSelect: window["vue-select"]
            },
            mounted() {
                this.featured = <?php echo json_encode($featured_courses) ?: [] ?>;
                this.featuredCourses = this.featured;
                this.courses = this.coursesList;
                this.featuredCoursesMethod();
                this.coursesMethod();
            },
            data: () => ({
                featured: [],
                categories: <?php echo json_encode($categories) ?: [] ?>,
                activeCategory: {},
                languages: <?php echo json_encode($languages) ?: [] ?>,
                activeLanguage: {},
                coursesList: <?php echo json_encode($courses) ?: [] ?>,
                search: "",
                code: "<?php echo $_REQUEST['accode'] ?? '' ?>",
                code_error: "",
                ref: null,
                level: '<?= $_SESSION['ulevel'] ?: 0 ?>',
                code_loading: false,
                mustLogIn: "<?php echo ($_SESSION['ulevel'] == 19 & !$_SESSION['uid']) ? 'yes' : 'no' ?>",
                pulse: false,
                courses: [],
                featuredCourses: [],
                key: Date.now()
            }),
            computed: {},
            methods: {
                coursesMethod: async function () {
                    const list = await this.courseListMap();
                    if (this.search.trim().length > 0) {
                        this.courses = list.filter((course) => {
                            return course.title.toLowerCase().indexOf(this.search.toLowerCase()) >= 0
                                || course.description.toLowerCase().indexOf(this.search.toLowerCase()) >= 0
                                || course.excerpt.toLowerCase().indexOf(this.search.toLowerCase()) >= 0;
                        })
                    } else if (this.activeCategory.id !== undefined) {
                        this.courses = list.filter((course) => {
                            return course.category_new !== null && course.category_new.id === this.activeCategory.id;
                        });
                    } else {
                        this.courses = list;
                    }
                },
                courseListMap: async function () {
                    return await Promise.all(
                        this.coursesList.map((course) => {
                            const blob = window.sessionStorage.getItem('course_' + course.id);
                            if (blob == null) {
                                this.fetchImageData(course.thumbnail).then((image) => {
                                    course.blob = image;
                                    window.sessionStorage.setItem('course_' + course.id, course.blob);
                                });
                            } else {
                                course.blob = blob;
                            }
                            const icon = window.sessionStorage.getItem(course.language.code);
                            if (icon == null) {
                                this.fetchImageData(course.language_icon).then((image) => {
                                    course.icon = image;
                                    window.sessionStorage.setItem(course.language.code, course.icon);
                                });
                            } else {
                                course.icon = icon;
                            }
                            return course;
                        })
                    );
                },
                featuredCoursesMethod: async function () {
                    this.featuredCourses = await Promise.all(
                        this.featured.map((course) => {
                            const blob = window.sessionStorage.getItem('course_' + course.id);
                            if (blob == null) {
                                this.fetchImageData(course.thumbnail).then((image) => {
                                    course.blob = image;
                                    window.sessionStorage.setItem('course_' + course.id, course.blob);
                                });
                            } else {
                                course.blob = blob;
                            }
                            const icon = window.sessionStorage.getItem(course.language.code);
                            if (icon == null) {
                                this.fetchImageData(course.language_icon).then((image) => {
                                    course.icon = image;
                                    window.sessionStorage.setItem(course.language.code, course.icon);
                                });
                            } else {
                                course.icon = icon;
                            }
                            return course;
                        })
                    );
                    this.key = Date.now();
                },
                handleGoogleTranslateSelect: (language) => {
                    console.log(language)
                },
                async fetchImageData(url) {
                    try {
                        // Make an HTTP request to fetch binary image data
                        const response = await fetch(url, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'image/*',
                                'Cache-Control': 'public,max-age=31536000'
                            }
                        });
                        const blob = await response.blob();
                        // Assuming the response data is an array of binary image data
                        return await this.blobToBase64(blob);
                    } catch (error) {
                        console.error('Error fetching image data:', error);
                    }
                },
                blobToBase64(blob) {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = () => {
                            resolve(reader.result);
                        };
                        reader.onerror = reject;
                        reader.readAsDataURL(blob);
                    });
                },
                getImageSource(blob) {
                    try {
                        return URL.createObjectURL(blob);
                    } catch (error) {
                        console.error('Error creating object URL:', error);
                        return ''; // Return empty string or handle the error accordingly
                    }
                },
                setCategory(category) {
                    if (category.tag === 'professional') {
                        const el = this.$refs.professional;
                        if (el) {
                            el.scrollIntoView({behavior: "smooth"});
                            this.pulse = true;
                            setTimeout(() => this.pulse = false, 5000);
                        }
                    } else {
                        this.activeCategory = category;
                        this.coursesMethod();
                    }
                },
                setLanguage(language) {
                    this.activeLanguage = language;
                },
                async checkAccessCode() {
                    if (this.code) {
                        this.code_loading = true;
                        try {
                            const response = await fetch(
                                `${window.location.origin}/online-learning/includes/ajax.php`,
                                {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/x-www-form-urlencoded',
                                    },
                                    body: `action=check_access_plan_code&must_log_in=${this.mustLogIn}&code=${this.code}`,
                                }
                            );

                            const info = await response.json();
                            this.handleAccessPlanResponse(info);
                        } catch (error) {
                            console.error('Error:', error);
                            // Handle error
                        }
                    }
                },
                handleAccessPlanResponse(info) {
                    this.code_loading = false;
                    this.code_error = "";
                    if (info.success) {
                        if (info.plan) {
                            window.location.replace(`/online-learning/register_new?plan=${info.plan.username_id}`);
                        } else if (info.coupon) {
                            window.location.replace(`/online-learning/register_new?plan=${info.plan.username_id}`);
                        } else {
                            this.code_error = info.message;
                            Swal.fire({
                                position: "top-end",
                                icon: "warning",
                                title: info.message,
                                showConfirmButton: false,
                                timer: 2500
                            });
                        }
                    } else {
                        this.code_error = info.message;
                        Swal.fire({
                            position: "top-end",
                            icon: "error",
                            title: info.message,
                            showConfirmButton: false,
                            timer: 2500
                        });
                    }
                },
            }
        })
    ;
    courses.component('language-dropdown', {
        props: ['id', 'languages', 'icon'],
        template: `
        <button :id="id" :data-dropdown-toggle="dropdownId" class="flex items-center text-sm pe-2 font-medium text-gray-900 rounded-full shadow-2xl shadow-black hover:text-blue-600 dark:hover:text-blue-500 md:me-0 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:text-white" type="button">
            <span class="sr-only">Open user menu</span>
            <img v-if="icon && icon.length" class="w-7 h-7 me-1 rounded-full" :src="icon" />
            <span v-else class="w-7 h-7 me-1 rounded-full bg-gray-100"></span>
            <svg class="w-2.5 h-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
            </svg>
        </button>

<!-- Dropdown menu -->
<div :id="dropdownId" class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700 dark:divide-gray-600">

    <ul class="py-2 text-sm text-gray-700 dark:text-gray-200" aria-labelledby="dropdownInformationButton">
      <li v-for="language in langs" :key="language.id">
        <a :href="language.href" target="_blank" class="flex gap-2  px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">
            <img :src="language.language_icon" class="w-7 h-7 me-1 rounded-full"/>
            <span>{{language.language.name}}</span>
        </a>
      </li>
      <li>
        <a @click="scrollToElement" class="block px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 dark:hover:text-white">Auto-translate</a>
      </li>
    </ul>
</div>
`,
        computed: {
            dropdownId() {
                return `dropdown-${this.id}`
            }
        },
        mounted() {
            this.languagesMethod();
        },
        data: () => ({
            langs: {}
        }),
        methods: {
            scrollToElement() {
                this.$emit('scrolldown', {id: '17', tag: 'professional'});
            },
            languagesMethod: async function () {
                this.langs = await Promise.all(
                    this.languages.map((lang) => {
                        const blob = window.sessionStorage.getItem(lang.language.code);
                        if (blob == null) {
                            this.fetchImageData(lang.language_icon).then((image) => {
                                lang.icon = image;
                                window.sessionStorage.setItem(lang.language.code, image);
                            });
                        } else {
                            lang.icon = blob;
                        }
                        return lang;
                    })
                );
            },
            async fetchImageData(url) {
                try {
                    // Make an HTTP request to fetch binary image data
                    const response = await fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'image/*',
                            'Cache-Control': 'public,max-age=31536000'
                        }
                    });
                    const blob = await response.blob();
                    // Assuming the response data is an array of binary image data
                    return await this.blobToBase64(blob);
                } catch (error) {
                    console.error('Error fetching image data:', error);
                }
            },
            blobToBase64(blob) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                        resolve(reader.result);
                    };
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            },
        }
    })
    courses.mount('#course-library');
</script>