<form class="form-horizontal" action="" method="post" name="form1" id="form1">

    <div class="w-screen overflow-x-hidden">
        <div class="w-[500px] mx-auto gap-4 my-5">
            <?php
            session_start();

            $dbh = get_dbh();


            //  [3] PROCESS THE REQUEST
            if ($_POST['request'] == 1 && isset($_POST['pass1']) && isset($_POST['pass1'])) {

                $pass1 = sanitise($_POST['pass1']);
                $pass2 = sanitise($_POST['pass2']);
                $email_usr = sanitise($_POST['email_add']);
                $email = pull_field("form_users", "db119", "WHERE username_id = '$email_usr'");
                $usercode = sanitise($_POST['user_code']);
                $unique = sanitise($_POST['unique_code']);

                // Check email
                if (check_email($email) == 0) {
                    $msg .= "ERROR: Incorrect email. Please enter valid email address.<br/>";
                    //$error_msg.='<div class="required_field"><strong>* Error! </strong>- \' '.$msg.' \'</div>';
                }

                //check passwords
                if (strcmp($pass1, $pass2) || empty($pass1)) {
                    $msg .= "Error - Password fields do not match or are empty.<br/>";
                    //$error_msg.='<div class="required_field"><strong>* Error! </strong>- \' '.$msg.' \'</div>';
                    $pass_identical = 1;
                }

                //check passwords length
                if (strlen($pass2) < 8 && $pass_identical !== 1) {
                    $msg .= "ERROR: Password too short. Your password must be at least 8 alphanumeric characters<br/>";
                    //$error_msg.='<div class="required_field"><strong>* Error! </strong>- \' '.$msg.' \'</div>';
                }

                //check spam proof code
                //if (strcmp(md5($usercode),$_SESSION['ckey']))
                //{
                //		 $msg.="ERROR: Invalid authentication code entered. Please enter the correct code as shown in the image<br/>";
                //		 //$error_msg.='<div class="required_field"><strong>* Error! </strong>- \' '.$msg.' \'</div>';
                //}

                // [4] register new password

                //if any error pages exist, then exit otherwise continue
                if ($msg == "") {

                    if ($_GET['local'] && $_GET['local'] == true) {
                        $return_url = "";
                    } else {
                        $return_url = "You can <a href=\"$front_website_url/online-learning/login\">proceed to login</a>";
                    }

                    $md5pass = md5($_POST['pass2']);
                    $sess_usergroup = session_info('usergroup');
                    $sql1 = "UPDATE form_users SET db222='$md5pass' WHERE db119='$email' AND usergroup='$sess_usergroup' and (rec_archive is null or rec_archive= '')";
                    $sql2 = "UPDATE form_password_reset SET rec_archive='1' WHERE db1134='$unique' AND usergroup='$sess_usergroup'";
                    $stmt = $dbh->prepare($sql1);
                    $stmt->execute();
                    $stmt = $dbh->prepare($sql2);
                    $stmt->execute();
                    echo "<h3>Thank you! </h3>Your new password has been set.<br/><br/>" . $return_url;

                    $done = 1;
                    exit();
                }

            }// END REQUEST CHECK

            if (isset($_GET['local']) && $_GET['local'] == true) {
                $db1134 = random();
                $db1135 = pull_field("form_users", "db119", "WHERE  (rec_archive is null or rec_archive= '') and id=" . session_info("uid"));///Requestee email address
                $db1136 = $_SERVER['REMOTE_ADDR'];///ip address

                $host = $_SERVER['HTTP_HOST'];
                $sql = "INSERT INTO form_password_reset (username_id, rec_id, usergroup, rel_id, db1134, db1135, db1136) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("usergroup") . "', '" . floating_info("ref") . "', '$db1134', '$db1135', '$db1136')";
                $stmt = $dbh->prepare($sql);
                $stmt->execute();
                $code = $db1134;
                $usr = $db1135;
            } else {
                $code = $_GET['code'];
                $usr = $_GET['usr'];
            }
            if (empty($usr) && empty($code) && $done !== 1) {
                echo "<div class=\"warning\" style=\"width:600px\">ERROR: Invalid code...<br/> <a href=\"forgot\">Click here to send another one</a></div>";
                exit();
            }

            // [1] santise data
            $email_usr = sanitise($usr);
            $email_chk = pull_field("form_users", "db119", "WHERE username_id = '$email_usr'");

            $sql = "SELECT db1134 FROM form_password_reset WHERE db1135='$email_chk' AND `rec_archive` IS NULL order by id desc LIMIT 1";
            //echo $sql;
            $stmt = $dbh->prepare($sql);
            $stmt->execute();
            $acode = $stmt->fetchColumn();

            //echo "<br/><br/>**$code**$acode**";
            if ($code == $acode) {

// [2] as user to set new password in form	
                ?>
                <!--<form class="form-horizontal" action="" method="post" name="form1" id="form1">-->
                <div class="flex flex-col rounded-2xl shadow-xl space-y-4 p-8">
                    <h3 class="font-bold text-2xl">Please set a new password for your account</h3>
                    <small class="text-gray-400 flex flex-col gap-4">
                        <span>Passwords are case-sensitive and must be at least 8 characters.</span>
                        <span>A good password should contain a mix of capital and lower-case letters, numbers and
                            symbols.</span>
                    </small>
                    <?php
                    if ($msg) {
                        echo '<div class="bg-red-400 rounded-lg p-2 w-full text-white">' . $msg . '</div>';
                    }
                    /*//field 1
                    $form_type="password";
                    $required="yes";
                    $name="Create a password";
                    $db_field_name="pass1";
                    include("engine/inc_controller_fields.php");
                    //field 2
                    $form_type="password";
                    $required="yes";
                    $name="Confirm the password";
                    $db_field_name="pass2";
                    include("engine/inc_controller_fields.php");
                    //auth field
                    $form_type="captcha";
                    $required="yes";
                    $name="Authentication code";
                    $db_field_name="user_code";
                    include("engine/inc_controller_fields.php");*/

                    ?>

                    <div class="flex flex-col gap-2">
                        <label for="pass1" class="font-semibold">Create a password</label>
                        <input type="password" required
                               class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-1.5"
                               name="pass1" id="pass1">
                    </div>

                    <div class="flex flex-col gap-2">
                        <label for="pass2" class="font-semibold">Confirm the password</label>
                        <input type="password" required
                               class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-1.5"
                               name="pass2" id="pass2">
                    </div>

                    <div class="text-center items-center flex justify-center">
                        <input type="submit" name="Submit" value="Set New Password"
                               class="block bg-white border-2 border-brand-blue w-1/2 text-brand-blue p-1 hover:bg-brand-blue hover:text-white rounded-full font-bold text-center"/>
                    </div>
                    <input name="unique_code" type="hidden" value="<?php echo $acode; ?>"/>
                    <input name="request" type="hidden" value="1"/>
                    <input name="email_add" type="hidden" value="<?php echo $usr; ?>"/>
                </div>

                <?php
// [5] else chuck out is activation code eeeor
            } else {

                if ($done !== "1") {
                    $msg = "ERROR: Incorrect activation code...not valid";
                    echo '<div class="warning" style="width:600px">' . $msg . '</div>';
                }
            }
            ?>

        </div>

    </div>


</form>
