<?php
if (!function_exists('getUKPostcodeFirstPart')) {
    function getUKPostcodeFirstPart($postcode)
    {
// validate input parameters
        $postcode = strtoupper($postcode);

// UK mainland / Channel Islands (simplified version, since we do not require to validate it)
        if (preg_match('/^[A-Z]([A-Z]?\d(\d|[A-Z])?|\d[A-Z]?)\s*?\d[A-Z][A-Z]$/i', $postcode))
            return preg_replace('/^([A-Z]([A-Z]?\d(\d|[A-Z])?|\d[A-Z]?))\s*?(\d[A-Z][A-Z])$/i', '$1', $postcode);
// British Forces
        if (preg_match('/^(BFPO)\s*?(\d{1,4})$/i', $postcode))
            return preg_replace('/^(BFPO)\s*?(\d{1,4})$/i', '$1', $postcode);
// overseas territories
        if (preg_match('/^(ASCN|BBND|BIQQ|FIQQ|PCRN|SIQQ|STHL|TDCU|TKCA)\s*?(1ZZ)$/i', $postcode))
            return preg_replace('/^([A-Z]{4})\s*?(1ZZ)$/i', '$1', $postcode);

// well ... even other form of postcode... return it as is
        return $postcode;
    }
}
$error_message_blocked = "You have been blocked from trying to gain access please contact us to unblock you";
$student_already_tried_access = false;
$student_id = $_SESSION['student_id'];
$errors = [];
if (!isset($student_id) || $student_id == '') {
    $plan = $OL->get_plans(array('username_id' => $_GET['plan']));
} else {
    $plan = $OL->get_plans(array('username_id' => $_GET['plan'], 'student_id' => $student_id));

}
$plan_id = $plan['id'];
if (is_user_logged_in()) {
    list($first_name, $last_name) = explode(',', pull_field("form_users", "CONCAT(db106,',',db111)", "WHERE id = $_SESSION[uid]"));
    $profile_info = array();
    $profile_info['first_name'] = $first_name;
    $profile_info['last_name'] = $last_name;


}
dev_debug("Plan ID: $plan_id");
$error_message = $_GET['error_message'];
$error_msg_user = "";
if ($_SESSION['usergroup'] == '47') {
    //check if the file exists
    $external_payments_url = base_path . front_header_file_location . "/admin/pay_config.php";// link to external admin file

    // then check if it has a custom rejection function
    if (file_exists($external_payments_url)) {

        //get config file
        require_once("$external_payments_url");// call custom rejection function
    } else {
        die("sorry payments config file was not found..." . $external_payments_url . "**");
    }
}
/** ===================================
 * Apply Coupon
 * ====================================    */
if ($_POST['action'] == "apply_coupon") {
    global $db;
    $coupon_args = array('plan_id' => $plan['id'], 'code' => $_POST['coupon']);
    $coupon = $OL->validate_coupons($coupon_args);
    //is coupon valid
    if (isset($coupon['id'])) {
        if ($coupon['valid_coupon'] == "Valid") {
            $_SESSION['valid_coupon_id'] = $coupon['id'];
        } else {
            $errors["coupon"] = true;
            $coupon_error = $coupon['invalid_coupon_text'];
        }
    } else {

        // check if there is a coupon code
        $count_access = pull_field("ols_access_plans", "count(*)", "where FIND_IN_SET('$_POST[coupon]',db21881) and usergroup = $_SESSION[usergroup]");
        if ($count_access && $count_access > 0) {
            $coupon_error = "Oops, it looks like you entered an access code! Please go to <a href='/online-learning/plans'>this page</a> to enter your code";
        } else {
            $coupon_error = "$_POST[coupon] is an invalid coupon code";
        }
        $errors["coupon"] = true;
    }

    echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/register_new/?plan=" . $_GET['plan'] . "&coupon_error=" . $coupon_error) . "\">";

    exit();
}

/** ===================================
 * Register user to plan
 * ====================================    */
if ($_POST['action'] == "register_user") {

    dev_debug(" student_already_tried_access0 **$student_already_tried_access** Action **$_POST[action]**");
    global $db;
    $dbh = get_dbh();
    $redirect = false;
    $user_access_plan_supplementary_questions_status = true;
    $user_access_plan_challenge_questions_status = true;
//die($plan['user_challenge_validity']);
    /** ===================================
     * Check if user already exists
     * ====================================    */
    if (!is_user_logged_in()) {
        // Sanitise username input
        $user_email = sanitise($_POST['email']);

        // Sanitise password input
        $pass = sanitise($_POST['password']);
        $pass_confirm = sanitise($_POST['password_confirm']);
        $usercode = sanitise($_POST['usercode']);
        $md5pass = md5($pass);
        $pass_identical = 0;
        //check passwords
        if (strcmp($pass, $pass_confirm) || empty($pass)) {
            $error_msg_user = "ERROR - Password fields do not match or are empty";
            $errors["password"] = true;
            $pass_identical = 1;
        }

        //check passwords length
        if (strlen($pass_confirm) < 8 && $pass_identical !== 1) {
            $errors["password"] = true;
            $error_msg_user = "ERROR: Password too short. Your password must be at least 8 alphanumeric characters";
        }
        //check spam proof code
        if (strcmp(md5(trim($usercode)), $_SESSION['ckey'])) {
            $errors["authentication_code"] = true;
            $error_msg_user = "ERROR: Invalid authentication code entered. Please enter the correct code as shown in the image";
        }
        if ($error_msg_user == "") {
            $user_record = $db->query("SELECT id FROM form_users where db119='" . sanitise($_POST['email']) . "' AND usergroup='$_SESSION[usergroup]'");

            if (count($user_record) > 0) {
                $sql = "SELECT id,db106,db111,db308,db119,db307,usergroup,db112,rel_id FROM form_users WHERE db119 = '$user_email' AND (db307 NOT IN ('yes','deleted') OR db307 IS NULL) AND (rec_archive IS NULL OR rec_archive = '') AND usergroup='$_SESSION[usergroup]' AND (db222 = '$md5pass')";
                $result = $db->query($sql);
                $num = count($result);
                if ($num > 0) {
                    $row = $result[0];

                    // LOAD SESSION WITH USER VARIABLES
                    //$_SESSION['loggedin']= "yes";
                    //$_SESSION['user']= $user_email;
                    $_SESSION['uid'] = $row['id'];
                    $_SESSION['access'] = $row['usergroup'];
                    $_SESSION['ulevel'] = 4;
                    $_SESSION['usergroup'] = $row['usergroup'];
                    $_SESSION['activation'] = '1';
                    $_SESSION['salt'] = md5(random());// generate a random salt for this session
                    $_SESSION['system'] = 'lite';

                    // GET THE ACCESS MODULE AND COLLECT CURRENT USERS SETTINGS

                    // LOAD SESSION MODULE ACCESS VARIABLES
                    $_SESSION['loggedin'] = "yes";
                    $_SESSION['user'] = $user_email;
                    $_SESSION['name'] = $row['db106'];
                    $_SESSION['surname'] = $row['db111'];
                    $_SESSION['fullname'] = $row['db106'] . ' ' . $row['db111'];
                    //$_SESSION['student_id'] = $row['rel_id'];

                    // update last logged in timestamp
                    $date_today = date('Y-m-d H:i:s');
                    $track_page_url = $db->curPageURL();
                    $check_last_login = pull_field("form_last_login", "count(*)", "WHERE rec_id='$_SESSION[uid]'");

                    if ($check_last_login > "0") {
                        $form_last_login_id = pull_field("form_last_login", "id", "WHERE rec_id='$_SESSION[uid]' ORDER BY id DESC LIMIT 1");
                        $fll_info = array('id' => $form_last_login_id, 'rel_id' => $row['rel_id'], 'db1276' => $date_today, 'db1275' => $track_page_url);
                    } else {
                        $fll_info = array('rel_id' => $row['rel_id'], 'db1276' => $date_today, 'db1275' => $track_page_url);
                    }
                    $db->system_table_insert_or_update('form_last_login', $fll_info);
                    // end
                    $student_id = $row['rel_id'];

                    $redirect = true;
                    $student_already_tried_access = true;

                } else {
                    $error_msg_user = "User email already exists but passwords do not match";
                    $errors["email"] = true;

                    ?>

                    <!--
				<div class="text-center not_found_404">
					<i class="fa fa-user-times" aria-hidden="true"></i>
					<h1>User already exists but passwords do not match</h1>
					<p>Please try again</p>
					<br>
					<a class="btn btn-primary btn-lg"
					   href="<?php echo home_url("/login"); ?>?redirect=<?php echo current_url(); ?>">Login to
						account</a>
					<a class="btn btn-primary btn-lg"
					   href="<?php echo home_url("/register_new/?plan=" . $_GET['plan']); ?>">Back</a>
				</div>
				-->
                    <?php //exit();
                }

            } else {
                /** ===================================
                 * Create the user account if none exists
                 * ====================================    */
                $new_student_info = array(
                    'school_id' => $_SESSION['usergroup'],
                    'first_name' => sanitise($_POST['first_name']),
                    'last_name' => sanitise($_POST['last_name']),
                    'email' => sanitise($_POST['email']),
                    'password' => sanitise($_POST['password']),
                    'send_welcome_email' => true,
                );
                $new_student_info = $OL->create_online_student_account($new_student_info);
                $student_id = $new_student_info['student_id'];
                $redirect = true;
            }
        }

    } else {
        if ($plan['user_challenge_validity'] == "Invalid") {
            $errors["challenge_invalid"] = true;
            $student_already_tried_access = true;
        }
    }
    dev_debug(" student_already_tried_access **$student_already_tried_access** Action **$_POST[action]**");

    if ($plan['user_challenge_validity'] == "Blocked") {
        $errors["challenge_invalid"] = true;
        $error_msg_user = $error_message_blocked;
    }

    if ($error_msg_user == "") {
        $redirect = true;
        /** ===================================
         * If no new user is created use the logged in student id
         * ====================================    */
        if (!$student_id) {
            $student_id = $_SESSION['student_id'];
        }


        /** ===================================
         * Update the user info
         * ====================================    */
        $profile_info = array();
        if ($_POST['first_name'] != '') {
            $profile_info['first_name'] = $_POST['first_name'];
        }
        if ($_POST['last_name'] != '') {
            $profile_info['last_name'] = $_POST['last_name'];
        }

        $users = new Users;
        if (!empty($profile_info)) {
            $users->update($profile_info);
        }


        /** ===================================
         * Delete Current Supplementary Answers
         * ====================================    */
        $sup_wehere = array('rel_id' => $student_id, 'db22027' => $plan['id']);
        $db->delete('ols_user_supp_answers', $sup_wehere);

        /** ===================================
         * Add the supplementary Questions
         * ====================================    */
        foreach ($plan['supplementary_questions'] as $question) {
            if ($question['type'] == "checkbox_list" || $question['type'] == "radio_list") {
                $user_supplementary_answer = implode(',', $_POST['sup_' . $question['id']]);
            } else {
                $user_supplementary_answer = $_POST['sup_' . $question['id']];
            }

            $sup_info = array('db22027' => $plan['id'], 'db22025' => $question['id'], 'db22026' => $user_supplementary_answer);
            $sup_info['rel_id'] = $student_id;
            $db->system_table_insert_or_update('ols_user_supp_answers', $sup_info);
            //check to see if some of the answer is mandatory
            $required_field = $question['required'];
            if ($required_field == 'yes' && $_POST['sup_' . $question['id']] == '') {
                $user_access_plan_supplementary_questions_status = $user_access_plan_supplementary_questions_status && false;
            }
        }

        /** ===================================
         * Delete Current Challenge Answers
         * ====================================    */
        $challenge_where = array('rel_id' => $student_id, 'db22028' => $plan['id']);
        $db->delete('ols_user_chal_answers', $challenge_where);

        /** ===================================
         * Add the Challenge Questions
         * ====================================    */
        foreach ($plan['challenge_questions'] as $question) {

            //Verify the challenge Answers (Ignore case and spaces)
            $question_type = $question['type'];
            $challenge_answer = $_POST['challenge_' . $question['id']];


            //strip out and spaces (Postcode)
            $challenge_answer = str_replace(' ', '', $challenge_answer);
            if ($question_type == 'like') {
                $valid = false;
                list($maybe_answer, $maybe_answer_group) = explode('??', pull_field("ols_challenge_answers", "CONCAT(id,'??',db27436)", "WHERE rel_id = '$plan[id]' AND (rec_archive IS NULL OR rec_archive = '') AND db21902 = '$question[id]' AND '$challenge_answer' LIKE CONCAT('%',db62992,'%')"));

                if ($maybe_answer and $maybe_answer != '') {
                    $valid = true;
                    $db26816_valid_answer_id = $maybe_answer;
                    $db56592_valid_answer_group = $maybe_answer_group;
                }
                /*$question_answers_array = explode(',', $question['group_answer']);
                foreach ($question_answers_array as $question_answer) {
                    //strip out spaces
                    $question_answer_nospaces = str_replace(' ', '',$question_answer);
                    //stripos takes care of case
                    if (stripos($challenge_answer, $question_answer_nospaces) === FALSE) { // Yoshi version
                        $valid = false;

                    } else {
                        $valid = true;
                        $db26816_valid_answer_id=pull_field("ols_challenge_answers","id","WHERE db21903 ='$question_answer' AND db21902=$question[id]");
                        break;
                    }
                }*/
            } else {
                $valid = false;
                //check if this is an event access plan type... if it is validate differently
                $access_plan_type = pull_field("ols_access_plans", "db26817", "WHERE id = $plan[id] ");
                if ($access_plan_type != 3) {
                    list($maybe_answer, $maybe_answer_group) = explode('??', pull_field("ols_challenge_answers", "CONCAT(id,'??',db27436)", "WHERE rel_id = '$plan[id]' AND (rec_archive IS NULL OR rec_archive = '') AND db21902 = '$question[id]' AND db62992 = '$challenge_answer'"));
                } else {
                    //$comparison_challenge_answer = preg_replace("/[^A-Za-z]/", "", getUKPostcodeFirstPart($challenge_answer));
                    $comparison_challenge_answer = preg_replace("/(.*?)\d(.*)/", "$1", getUKPostcodeFirstPart($challenge_answer));
                    list($maybe_answer, $maybe_answer_group) = explode('??', pull_field("ols_challenge_answers", "CONCAT(id,'??',db27436)", "WHERE rel_id = '$plan[id]' AND (rec_archive IS NULL OR rec_archive = '') AND db21902 = '$question[id]' AND db62992 = '$comparison_challenge_answer'"));
                }

                if ($maybe_answer and $maybe_answer != '') {
                    $valid = true;
                    $db26816_valid_answer_id = $maybe_answer;
                    $db56592_valid_answer_group = $maybe_answer_group;
                }

                /*$question_answers_array = explode(',', $question['group_answer']);
                foreach ($question_answers_array as $question_answer) {
                    //strip out spaces
                    $question_answer_nospaces = str_replace(' ', '',$question_answer);
                    //exact match
                    if (strcasecmp($challenge_answer, $question_answer_nospaces) != 0) {
                        $valid = false;

                    } else {
                        $valid = true;
                        $db26816_valid_answer_id=pull_field("ols_challenge_answers","id","WHERE db21903 ='$question_answer' AND db21902=$question[id]");
                        break;
                    }
                }*/

            }

            $user_access_plan_challenge_questions_status = ($user_access_plan_challenge_questions_status && $valid);

            if ($valid == true) {
                $answer_status = "Valid";
            } else {
                $answer_status = "Invalid";

            }
            $challenge_info = array('db22032' => $answer_status, 'db22028' => $plan['id'], 'db22029' => $question['id'], 'db22030' => $_POST['challenge_' . $question['id']], 'db26816' => $db26816_valid_answer_id, 'db56592' => $db56592_valid_answer_group);
            $challenge_info['rel_id'] = $student_id;
            $db->system_table_insert_or_update('ols_user_chal_answers', $challenge_info);

        }

        /** ===================================
         * CHeck for the coupon_info
         * ====================================    */
        $coupon_id = $_SESSION['valid_coupon_id'];

        /** ===================================
         * Update the access plan status
         * ====================================    */
        if ($user_access_plan_supplementary_questions_status == true) {
            $supplementary_questions_status = "Valid";
        } else {
            $supplementary_questions_status = "Invalid";
        }

        if ($user_access_plan_challenge_questions_status == true) {
            $challenge_questions_status = "Valid";
        } else {
            $challenge_questions_status = "Invalid";
        }
        if ($student_already_tried_access == true & $challenge_questions_status == "Invalid") {
            //block this student
            $challenge_questions_status = 'Blocked';
            $error_msg_user = $error_message_blocked;

        }
        $user_acccess_plan_alread_exists = pull_field("ols_user_access_plan", "count(*)", "WHERE rel_id=$student_id AND db22019=$plan[id]");
        if ($user_acccess_plan_alread_exists > "0") {
            $action = "update";
            $user_sponsor_validity = pull_field("ols_user_access_plan", "db22058", "WHERE rel_id=$student_id AND db22019=$plan[id]");
            $uap_status = pull_field("ols_user_access_plan", "db22031", "WHERE rel_id=$student_id AND db22019=$plan[id]");
        } else {
            $action = "create";
            $uap_status = '1';
            if ($plan['sponsor_to_verify_access'] == 'yes') {
                $user_sponsor_validity = "Invalid";

            } else {
                $user_sponsor_validity = "Valid";
            }

        }

        $user_access_plan_info = array(
            'student_id' => $student_id,
            'plan_id' => $plan['id'],
            'status' => $uap_status,
            'coupon_id' => $coupon_id,
            'supplementary_question_status' => $supplementary_questions_status,
            'challenge_question_status' => $challenge_questions_status,
            'sponsor_validation_status' => $user_sponsor_validity,
            'action' => $action,
            'challenge_group_id' => pull_field("ols_challenge_answers", "db27436", "where id=$db26816_valid_answer_id")
        );

        $OL->update_or_insert_user_access_plan($user_access_plan_info);


        if ($redirect == true) {
            if (isset($error_message) && $error_message != '') {
                $error_url = "&error_message=" . $error_message;
                echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/register_new/?plan=" . $_GET['plan'] . $error_url) . "\">";
            } else {
                //check if it's free if so proceed
                $plan = $OL->get_plans(array('username_id' => $_GET['plan'], 'student_id' => $student_id));
                $plan_id = $plan['id'];
                $total_amount = $plan['price_including_vat'];
                $discount_amount = 0;
                $total_amount_vat = $plan['price_including_vat'] - $plan['price'];
                $total_amount_excl_vat = $plan['price'];
                $user_access_plan_id = $plan['user_access_plan_id'];
                dev_debug("** $plan[user_challenge_validity] ** $plan[user_challenge_validity] ** $plan[user_supplementary_validity] ** $plan[user_sponsor_validity] ** $plan[price] ** $total_amount **");
                if ($plan['user_challenge_validity'] != "Invalid" && $plan['user_challenge_validity'] != "Blocked" && $plan['user_supplementary_validity'] != "Invalid" && $plan['user_sponsor_validity'] != "Invalid") {
                    $student_username_id = pull_field("core_students", "username_id", "Where id = $student_id");

                    if ($total_amount == '0' || ($plan['price'] == '' || $plan['price'] == '0' || $plan['price'] == '0.00')) {
                        //create an invoice and add to payments here
                        $db14987_currency = '232'; //pull_field("lead_preferences","dbXXXXX","WHERE usergroup = $_SESSION[usergroup]");
                        $db14990_reminder_date = '';
                        $db14991_office_address = pull_field('lead_company_office', 'id', "WHERE (rec_archive is NULL or rec_archive='') AND usergroup=$_SESSION[usergroup]");
                        $db14992_recipient_type = 'Online Learner';
                        $db14993_recipient = $_SESSION['student_id'];
                        $db14995_payment_option = 'Bank Transfer,Cash,Cheque,Pay online';
                        $db14996_number_of_installments_allowed = '0';
                        $db14997_delivery_option = 'Download,Send As Email';
                        $db14998_include_vat = pull_field("ols_access_plans", "db21887", "WHERE id='" . $plan_id . "'");
                        $db14999_discount_amount = $discount_amount;
                        $db15000_comment = '';
                        $db15002_deposit = '';
                        $db15003_deposit_value = '';
                        $db14989_due_date = date('j F Y', strtotime('now'));
                        $db15005_status = 'approved';//one sets admin buttons on in invoice
                        $db15006_display_total = $total_amount_excl_vat;
                        $db342693_vat_rate = pull_field("lead_preferences", "db15030", "WHERE usergroup=$_SESSION[usergroup] and (rec_archive IS NULL OR rec_archive = '')");

                        $sql = "INSERT INTO lead_invoice_settings (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id,db14987,db14988,db14989,db14990,db14991,db14992,db14993,db14995,db14996,db14997,db14998,db14999,db15000,db15002,db15003,db15004,db15005,db15006,db342693)VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$_SESSION[student_id]', CURRENT_TIMESTAMP(), '1', '$db14987_currency', CURRENT_TIMESTAMP(), '$db14989_due_date','$db14990_reminder_date','$db14991_office_address','$db14992_recipient_type','$db14993_recipient','$db14995_payment_option','0','$db14997_delivery_option','$db14998_include_vat','$db14999_discount_amount','$db15000_comment','','','','$db15005_status','$db15006_display_total','$db342693_vat_rate')";
                        $sth = $dbh->prepare($sql);
                        $sth->execute();
                        $invoice_settings_id = $dbh->lastInsertId();

                        //add the invoice item
                        $db15016_title = pull_field("ols_access_plans", "db21876", "WHERE id = '$plan_id'") . ' Access Plan';
                        $db15017_description = '';
                        $db15018_category = 'access plan';
                        $db15019_type = '3';//online Learning
                        $db15020_quantity = "1";
                        $db15021_units = "Access Plan Fee";
                        $db15022_unit_price = pull_field("ols_access_plans", "db21886", "WHERE id='" . $plan_id . "'");
                        $db15024_course = $plan_id;
                        $db15025_start_date = '';
                        $db15026_end_date = '';
                        $sql = "INSERT INTO lead_invoice_items (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db15016,db15017,db15018,db15019,db15020,db15021,db15022,db15024,db15025,db15026,db20215) VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$invoice_settings_id', CURRENT_TIMESTAMP(), '1','$db15016_title','$db15017_description','$db15018_category','$db15019_type','$db15020_quantity','$db15021_units','$db15022_unit_price','$db15024_course','$db15026_start_date','$db15026_end_date','')";

                        $sth = $dbh->prepare($sql);
                        $sth->execute();

                        //add the payment
                        $lead_invoice_settings_username_id = pull_field("lead_invoice_settings", "username_id", "WHERE id = '$invoice_settings_id'");
                        $db1492_method_of_payment = 'online';
                        $db1493_comments = pull_field("ols_access_plans", "db21876", "WHERE id = '$plan_id'") . ' Access Plan';
                        $db1494_payment_towards = $lead_invoice_settings_username_id;
                        if ($total_amount == 0) {
                            $db1495_amount = 0;
                        } else {
                            $db1495_amount = $total_amount / 100;
                        }
                        //$db1501_date_installment_expected = null;
                        $db1503_installment_amount_due = null;
                        //$db15459_payment_date=today;
                        $sql = "INSERT INTO sis_student_fees (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db1492,db1493,db1494,db1495,db1501,db1503,db15459,db34450) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "',  '$_SESSION[student_id]', CURRENT_TIMESTAMP(), '1','$db1492_method_of_payment','$db1493_comments','$db1494_payment_towards','$db1495_amount',CURRENT_TIMESTAMP(),'$db1495_amount', CURRENT_TIMESTAMP(),'Payment')";
                        $sth = $dbh->prepare($sql);
                        $sth->execute();

                        //set the access plans to active
                        $sql = "UPDATE ols_user_access_plan SET db22031 =?, db22059 = ?  WHERE id=?";
                        $sth = $dbh->prepare($sql);
                        $sth->execute(array('2', $lead_invoice_settings_username_id, $user_access_plan_id));

                        //ADD TO THE USER access planTRACKER
                        $sql = "INSERT INTO ols_user_ap_status_track (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db17614) VALUES (?,?,?,?,?,?,?)";

                        $sth = $dbh->prepare($sql);
                        $sth->execute(array(random(), session_info("uid"), session_info("usergroup"), $user_access_plan_id, custom_date_and_time(), session_info("uid"), '2'));
                        $active_access_plan = 1;
                        echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url() . "\">";
                    } else {
                        echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/register_new/?plan=" . $_GET['plan']) . "\">";

                    }
                } else {
                    echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/register_new/?plan=" . $_GET['plan']) . "\">";

                }
            }


            exit();
        }
    } else {
        $error_message = $error_msg_user;
        //$error_url = "&error_message=" . $error_msg_user;
        //echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/register_new/?plan=" . $_GET['plan'] . $error_url) . "\">";

        //exit();
    }
}


if ($_POST['action'] && $_POST['action'] == 'process_payment') {
    require_once(__DIR__ . '/../../admin/app/libs/stripe-php-6.43.1/init.php');

    $plan_id = $_POST['plan_id'];
    $total_amount = $_POST['total_amount'];
    $discount_amount = $_POST['discount_amount'];
    $total_amount_vat = $_POST['total_amount_vat'];
    $total_amount_excl_vat = $_POST['total_amount_excl_vat'];
    $user_access_plan_id = $_POST['uap'];
    $dbh = get_dbh();
    global $db;
    $access_plan_name = pull_field("ols_access_plans", "db21876", "WHERE id=$plan_id");
    $payment_error_message = '';

    if ($total_amount != 0) {

        if ($_SESSION['usergroup'] == '47') {
            $stripe_key = $stripe['secret_key'];
            // Set your secret key: remember to change this to your live secret key in production
            // See your keys here https://dashboard.stripe.com/account/apikeys
            \Stripe\Stripe::setApiKey("$stripe_key");
        } else {
            // Set your secret key: remember to change this to your live secret key in production
            // See your keys here https://dashboard.stripe.com/account/apikeys
            \Stripe\Stripe::setApiKey("********************************");
        }
        // Get the credit card details submitted by the form
        $token = $_POST['stripeToken'];
        if ($token == null || $token === '') {
            $payment_error_message = 'Invalid stripe token. ';

        }
        $stripe_email = $_POST['stripeEmail'];
        if ($stripe_email == null || $stripe_email === '') {
            $payment_error_message .= 'Invalid email from stripe. ';

        }

        // Create the charge on Stripe's servers - this will charge the user's card
        if ($payment_error_message == '') {
            try {
                $description = "Access Plan: " . $access_plan_name . ' AccessPlan/User: ' . $user_access_plan_id . ' Usergroup: ' . $_SESSION['usergroup'];
                $charge = \Stripe\Charge::create(array(
                        "amount" => $total_amount, // amount in cents, again
                        "currency" => "GBP",
                        "source" => $token,
                        "description" => $description,
                        "receipt_email" => $stripe_email)
                );
                //stripe needs it to be in pence
                $total_amount = $total_amount / 100;
            } catch (\Stripe\Error\Card $e) {
                // Since it's a decline, \Stripe\Error\Card will be caught
                $body = $e->getJsonBody();
                $err = $body['error'];
                throw new Exception('This card has been declined: ' . $err['message']);

            } catch (\Stripe\Error\RateLimit $e) {
                // Too many requests made to the API too quickly
                $payment_error_message = 'This card has been declined; Too many requests made to Stripes API too quickly';
            } catch (\Stripe\Error\InvalidRequest $e) {
                // Invalid parameters were supplied to Stripe's API
                $payment_error_message = 'This card has been declined; Invalid parameters were supplied to Stripes API ' . $token . ' ** ' . $total_amount . ' ** ' . $stripe_email;
            } catch (\Stripe\Error\Authentication $e) {
                // Authentication with Stripe's API failed
                // (maybe you changed API keys recently)
                $payment_error_message = 'This card has been declined; Authentication with Stripes API failed';
            } catch (\Stripe\Error\ApiConnection $e) {
                // Network communication with Stripe failed
                $payment_error_message = 'This card has been declined; Network communication with Stripe failed';
            } catch (\Stripe\Error\Base $e) {
                // Display a very generic error to the user, and maybe send
                // yourself an email
                $payment_error_message = 'This card has been declined';
            } catch (Exception $e) {
                // Something else happened, completely unrelated to Stripe
                $payment_error_message = 'This card has been declined';

            }
        }
    }
    if ($payment_error_message == '') {
        //create an invoice and add to payments here
        $db14987_currency = '232'; //pull_field("lead_preferences","dbXXXXX","WHERE usergroup = $_SESSION[usergroup]");
        $db14990_reminder_date = '';
        $db14991_office_address = pull_field('lead_company_office', 'id', "WHERE (rec_archive is NULL or rec_archive='') AND usergroup=$_SESSION[usergroup]");
        $db14992_recipient_type = 'Online Learner';
        $db14993_recipient = $_SESSION['student_id'];
        $db14995_payment_option = 'Bank Transfer,Cash,Cheque,Pay online';
        $db14996_number_of_installments_allowed = '0';
        $db14997_delivery_option = 'Download,Send As Email';
        $db14998_include_vat = pull_field("ols_access_plans", "db21887", "WHERE id='" . $plan_id . "'");
        $db14999_discount_amount = $discount_amount;
        $db15000_comment = '';
        $db15002_deposit = '';
        $db15003_deposit_value = '';
        $db14989_due_date = date('j F Y', strtotime('now'));
        $db15005_status = 'approved';//one sets admin buttons on in invoice
        $db15006_display_total = $total_amount_excl_vat;
        $db342693_vat_rate = pull_field("lead_preferences", "db15030", "WHERE usergroup=$_SESSION[usergroup] and (rec_archive IS NULL OR rec_archive = '')");

        $sql = "INSERT INTO lead_invoice_settings (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id,db14987,db14988,db14989,db14990,db14991,db14992,db14993,db14995,db14996,db14997,db14998,db14999,db15000,db15002,db15003,db15004,db15005,db15006,db342693)VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$_SESSION[student_id]', CURRENT_TIMESTAMP(), '1', '$db14987_currency', CURRENT_TIMESTAMP(), '$db14989_due_date','$db14990_reminder_date','$db14991_office_address','$db14992_recipient_type','$db14993_recipient','$db14995_payment_option','0','$db14997_delivery_option','$db14998_include_vat','$db14999_discount_amount','$db15000_comment','','','','$db15005_status','$db15006_display_total','$db342693_vat_rate')";
        $sth = $dbh->prepare($sql);
        $sth->execute();
        $invoice_settings_id = $dbh->lastInsertId();

        //add the invoice item
        $db15016_title = pull_field("ols_access_plans", "db21876", "WHERE id = '$plan_id'") . ' Access Plan';
        $db15017_description = '';
        $db15018_category = 'access plan';
        $db15019_type = '3';//online Learning
        $db15020_quantity = "1";
        $db15021_units = "Access Plan Fee";
        $db15022_unit_price = pull_field("ols_access_plans", "db21886", "WHERE id='" . $plan_id . "'");
        $db15024_course = $plan_id;
        $db15025_start_date = '';
        $db15026_end_date = '';
        $sql = "INSERT INTO lead_invoice_items (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db15016,db15017,db15018,db15019,db15020,db15021,db15022,db15024,db15025,db15026,db20215) VALUES('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "', '$invoice_settings_id', CURRENT_TIMESTAMP(), '1','$db15016_title','$db15017_description','$db15018_category','$db15019_type','$db15020_quantity','$db15021_units','$db15022_unit_price','$db15024_course','$db15026_start_date','$db15026_end_date','')";

        $sth = $dbh->prepare($sql);
        $sth->execute();

        //add the payment
        $lead_invoice_settings_username_id = pull_field("lead_invoice_settings", "username_id", "WHERE id = '$invoice_settings_id'");
        $db1492_method_of_payment = 'online';
        $db1493_comments = pull_field("ols_access_plans", "db21876", "WHERE id = '$plan_id'") . ' Access Plan';
        $db1494_payment_towards = $lead_invoice_settings_username_id;
        if ($total_amount == 0) {
            $db1495_amount = 0;
        } else {
            $db1495_amount = $total_amount / 100;
        }
        //$db1501_date_installment_expected = null;
        $db1503_installment_amount_due = null;
        //$db15459_payment_date=today;
        $sql = "INSERT INTO sis_student_fees (username_id,rec_id,usergroup,rel_id,rec_lstup,rec_lstup_id,db1492,db1493,db1494,db1495,db1501,db1503,db15459,db34450) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("access") . "',  '$_SESSION[student_id]', CURRENT_TIMESTAMP(), '1','$db1492_method_of_payment','$db1493_comments','$db1494_payment_towards','$db1495_amount',CURRENT_TIMESTAMP(),'$db1495_amount', CURRENT_TIMESTAMP(),'Payment')";
        $sth = $dbh->prepare($sql);
        $sth->execute();

        //set the access plans to active
        $sql = "UPDATE ols_user_access_plan SET db22031 =?, db22059 = ?  WHERE id=?";
        $sth = $dbh->prepare($sql);
        $sth->execute(array('2', $lead_invoice_settings_username_id, $user_access_plan_id));

        //ADD TO THE USER access planTRACKER
        $sql = "INSERT INTO ols_user_ap_status_track (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db17614) VALUES (?,?,?,?,?,?,?)";

        $sth = $dbh->prepare($sql);
        $sth->execute(array(random(), session_info("uid"), session_info("usergroup"), $user_access_plan_id, custom_date_and_time(), session_info("uid"), '2'));
        $active_access_plan = 1;
    }
    if ($payment_error_message == '') {
        echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url() . "\">";
        exit();
    } else {
        if (isset($payment_error_message) && $payment_error_message != '') {
            $error_url = "&payment_error=" . $payment_error_message;
        }

        echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/register_new/?plan=" . $_GET['plan'] . $payment_error_url) . "\">";

        exit();
    }


}

// echo '<pre>';
// print_r($plan);
// echo '</pre>';

if ($_SESSION['valid_coupon_id']) {

    $coupon_args = array('id' => $_SESSION['valid_coupon_id']);
    $coupon = $OL->get_coupons($coupon_args);


    if ($coupon['discount_type'] == "Percentage") {
        $new_total = ($coupon['discount'] / 100) * $plan['price_including_vat'];
        $coupon['total_discount'] = $new_total;
        $coupon['new_total'] = number_format($plan['price_including_vat'] - $new_total, 2);
    } else {
        //exact amount
        $coupon['total_discount'] = $coupon['discount'];
        $coupon['new_total'] = number_format($plan['price_including_vat'] - $coupon['discount'], 2);
    }

}

/** ===================================
 * Remove Coupon
 * ====================================    */
if ($_GET['remove_discount']) {

    unset($_SESSION['valid_coupon_id']);
    echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/register_new/?plan=" . $_GET['plan']) . "\">";
    exit();
}

if ($_GET['error_message'] != $error_message_blocked) {
    $student_id = $_SESSION['student_id'];

    //reload the plan as it may have been saved
    if (!isset($student_id) || $student_id == '') {
        $plan = $OL->get_plans(array('username_id' => $_GET['plan']));
    } else {
        $plan = $OL->get_plans(array('username_id' => $_GET['plan'], 'student_id' => $student_id));

    }

    if ($plan['user_supplementary_validity'] == "Invalid") {
        if (!isset($error_message) || $error_message == '') {
            $error_message = 'Please fill in all required fields, marked with an (*)';
        } else {
            $error_message .= "<br/>" . "Please fill in all required fields, marked with an (*)";
        }
    }
    if ($plan['user_challenge_validity'] == "Invalid") {
        if (!isset($error_message) || $error_message == '') {
            $error_message = '<span class="invalidated_entry"><div class="invalidated_entry_circle">X</div></span> Sorry you are not eligible to use this access code. Please select and purchase the course to gain access';
        } else {
            $error_message .= '<br/><span class="invalidated_entry"><div class="invalidated_entry_circle">X</div></span> Sorry you are not eligible to use this access code. Please select and purchase the course to gain access';
        }
    }

    if ($plan['user_sponsor_validity'] == "Invalid") {
        if (!isset($error_message) || $error_message == '') {
            $error_message = 'We now need to wait for the sponsor to validate';
        } else {
            $error_message .= "<br/>" . "We now need to wait for the sponsor to validate";
        }
    }
}

if ((!isset($plan['user_access_plan_id']) or $plan['user_access_plan_id'] == '')) {
    //create the user access plan so the user can go straight to pay
    $dbh = get_dbh();
    $sql = "INSERT INTO ols_user_access_plan (username_id, rec_id, usergroup, rel_id, rec_lstup, rec_lstup_id, db22019,db22031,db22049,db22056,db22057,db22058) VALUES ('" . random() . "', '" . session_info("uid") . "', '" . session_info("usergroup") . "', '" . session_info("student_id") . "','" . custom_date_and_time() . "', '" . session_info("uid") . "', '" . $plan_id . "', '1', '', '" . $plan['user_supplementary_validity'] . "', '" . $plan['user_challenge_validity'] . "','" . $plan['user_sponsor_validity'] . "')";
    $sth = $dbh->prepare($sql);
    $sth->execute();
    $user_access_plan_id = $dbh->lastInsertId();
    $plan['user_access_plan_id'] = $user_access_plan_id;
    //set valididy to invalid first time in to force 'continue'
    $plan['user_challenge_validity'] = "Invalid";
}


?>

    <div class="w-screen overflow-x-hidden">
        <div class="w-3/4 mx-auto flex gap-2 mt-8">
            <style type="text/css">
                .order_summary {
                    padding: 30px;
                    background: #fff;
                    border-radius: 5px;
                    overflow: hidden;
                    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.1);
                    margin-bottom: 100px;
                }

                .order_summary .plan_title {
                    font-weight: 800;
                }

                .order_summary ul {
                    margin: 0px;
                    padding: 0px;
                    padding-left: 15px;
                    padding-top: 10px;
                    font-size: 12px;
                    line-height: 16px;
                }

                .order_summary h2 {
                    font-size: 18px;
                    font-weight: 800;
                    color: #333;
                    margin-bottom: 20px;
                }

                .order_summary hr {
                    margin: 25px 0;
                    clear: both;
                    padding: 0px;
                    border-top: 1px solid #e1e1e1;
                }

                .total_value {
                    font-weight: 800;
                    min-height: 30px;
                }

                .total_title {
                    min-height: 30px;
                }

                .total_value.main {
                    font-weight: 800;
                    font-size: 24px;
                }
            </style>
            <div class="w-8/12 bg-white">
                <form method="POST" action="">

                    <div class="w-full rounded shadow-lg p-8 overflow-hidden mb-8 space-y-4">
                        <?php if (isset($error_message) && $error_message != '') { ?>
                            <div id="alert-warning" class="alert alert-warning" role="alert">
                                <i class="fa fa-user-times" aria-hidden="true"></i> <?php echo $error_message ?>
                            </div>
                        <?php } ?>
                        <?php $section_count = 1; ?>
                        <h2 class="text-2xl flex items-center gap-3 font-bold">
                            <span class="w-12 h-12 flex rounded-full items-center justify-center border border-brand-green text-brand-green text-center">1</span><?php echo translate("Your Information", $_GET['lang']) ?>
                        </h2>
                        <div class="flex gap-4">
                            <div class="w-6/12 space-y-2">
                                <label class="text-xs font-semibold"
                                       for="first_name"><?php echo translate("First name (Optional)", $_GET['lang']) ?></label>
                                <input class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-1.5"
                                       id="first_name" type="text" name="first_name"
                                       value="<?php if (isset($profile_info['first_name']) && $profile_info['first_name'] != '') {
                                           echo $profile_info['first_name'];
                                       } else {
                                           echo htmlentities($_POST['first_name']);
                                       } ?>">
                            </div>
                            <div class="w-6/12 space-y-2">
                                <label class="text-xs font-semibold"
                                       for="last_name"><?php echo translate("Last name (Optional)", $_GET['lang']) ?></label>
                                <input id="last_name"
                                       class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-1.5"
                                       type="text" name="last_name"
                                       value="<?php if (isset($profile_info['last_name']) && $profile_info['last_name'] != '') {
                                           echo $profile_info['last_name'];
                                       } else {
                                           echo htmlentities($_POST['last_name']);
                                       } ?>">
                            </div>
                        </div>

                        <?php if (count($plan['supplementary_questions'])) { ?>

                            <div class="grid grid-flow-row-dense grid-cols-2 gap-4">
                                <?php foreach ($plan['supplementary_questions'] as $key => $question) {
                                    if (empty($question['user_answer'])) {
                                        if ($question['type'] == "checkbox_list" || $question['type'] == "radio_list") {
                                            $user_supplementary_answer = implode(',', $_POST['sup_' . $question['id']]);
                                        } else {
                                            $user_supplementary_answer = $_POST['sup_' . $question['id']];
                                        }
                                    } else {
                                        $user_supplementary_answer = $question['user_answer'];
                                    }
                                    ?>
                                    <div class="col-span-1">

                                        <?php if ($question['type'] == "single_line_text") { ?>
                                            <label class="text-xs font-semibold"
                                                   for="<?php echo $question['id']; ?>"><?php echo $question['title']; ?><?php if ($question['required'] == "yes") {
                                                    echo ' *';
                                                } ?></label>
                                            <input class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-1.5"
                                                   value="<?php if (!isset($user_supplementary_answer) || $user_supplementary_answer == '') {
                                                       echo htmlentities($_POST['sup_' . $question['id']]);
                                                   } else {
                                                       echo $user_supplementary_answer;
                                                   } ?>" type="text"
                                                   name="sup_<?php echo $question['id']; ?>"
                                                   id="<?php echo $question['id']; ?>" <?php if ($question['required'] == "yes") {
                                                echo 'required="required"';
                                            } ?>>
                                        <?php }
                                        if ($question['type'] == "text_box") { ?>
                                            <label class="text-xs font-semibold"><?php echo $question['title']; ?><?php if ($question['required'] == "yes") {
                                                    echo ' *';
                                                } ?></label>
                                            <textarea
                                                    class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full resize-none p-1.5"
                                                    rows="5"
                                                    name="sup_<?php echo $question['id']; ?>" <?php if ($question['required'] == "yes") {
                                                echo 'required="required"';
                                            } ?>><?php if (!isset($user_supplementary_answer) || $user_supplementary_answer == '') {
                                                    echo htmlentities($_POST['sup_' . $question['id']]);
                                                } else {
                                                    echo $user_supplementary_answer;
                                                } ?></textarea>
                                        <?php } elseif ($question['type'] == "radio_yes_no") {
                                            if (isset($user_supplementary_answer) && $user_supplementary_answer != '') {
                                                if ($user_supplementary_answer == "Yes") {
                                                    $pre = 'checked="checked"';
                                                    $pre_no = "";
                                                } elseif ($user_supplementary_answer == "No") {
                                                    $pre_no = 'checked="checked"';
                                                    $pre = "";
                                                }
                                            } else {
                                                if ($_POST['sup_' . $question['id']] == "Yes") {
                                                    $pre = 'checked="checked"';
                                                    $pre_no = "";
                                                } elseif ($_POST['sup_' . $question['id']] == "No") {
                                                    $pre_no = 'checked="checked"';
                                                    $pre = "";
                                                }
                                            }
                                            ?>


                                            <label class="text-xs font-semibold"><?php echo $question['title']; ?><?php if ($question['required'] == "yes") {
                                                    echo ' *';
                                                } ?></label>
                                            <div class="radio">
                                                <label class="text-xs font-semibold"><input type="radio" value="Yes"
                                                                                            class="text-brand-blue bg-gray-100 border-gray-300 rounded-full focus:ring-brand-blue/60 dark:focus:ring-brand-blue dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                                                            name="sup_<?php echo $question['id']; ?>" <?php echo $pre ?> <?php if ($question['required'] == "yes") {
                                                        echo 'required="required"';
                                                    } ?>>Yes </label>
                                            </div>
                                            <div class="radio">
                                                <label class="text-xs font-semibold"><input type="radio" value="No"
                                                                                            class="text-brand-blue bg-gray-100 border-gray-300 rounded-full focus:ring-brand-blue/60 dark:focus:ring-brand-blue dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                                                            name="sup_<?php echo $question['id']; ?>" <?php echo $pre_no ?> <?php if ($question['required'] == "yes") {
                                                        echo 'required="required"';
                                                    } ?>>No </label>
                                            </div>
                                        <?php } elseif ($question['type'] == "checkbox") { //print_r($question);
                                            $pre = '';
                                            if (isset($user_supplementary_answer) && $user_supplementary_answer != '') {
                                                if ($user_supplementary_answer == "Yes") {
                                                    $pre = 'checked="checked"';
                                                    $pre_no = "";
                                                }
                                            } else {
                                                if ($_POST['sup_' . $question['id']] == "Yes") {
                                                    $pre = 'checked="checked"';
                                                    $pre_no = "";
                                                }
                                            }
                                            ?>
                                            <div class="flex items-center mb-4">
                                                <input id="default-checkbox" type="checkbox" value="Yes"
                                                       name="sup_<?php echo $question['id']; ?>" <?php if ($question['required'] == "yes") {
                                                    echo 'required="required"';
                                                } ?>
                                                       class="w-4 h-4 text-brand-blue bg-gray-100 border-gray-300 rounded focus:ring-brand-blue/60 dark:focus:ring-brand-blue dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                                <label for="default-checkbox"
                                                       class="ms-2 text-xs font-semibold text-gray-900 dark:text-gray-300"><?php echo $question['title']; ?><?php if ($question['required'] == "yes") {
                                                        echo ' *';
                                                    } ?></label>
                                            </div>

                                        <?php } elseif ($question['type'] == "dropdown_list") { //print_r($question);
                                            $str_answers = $question['answers'];
                                            // turn string into array
                                            $list = explode(',', $str_answers);
                                            //$category = str_replace(" ", "&nbsp;", $list);
                                            ?>

                                            <label class="text-xs font-semibold"><?php echo $question['title']; ?><?php if ($question['required'] == "yes") {
                                                    echo ' *';
                                                } ?></label>

                                            <label class="text-xs font-semibold"><select
                                                        class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full p-1.5"
                                                        name="sup_<?php echo $question['id']; ?>">;
                                                    <?php foreach ($list as $key => $val) {
                                                        // get predefined field
                                                        // 1 = current value, 2 = current in loop
                                                        $pre_sel = '';
                                                        if (!empty($user_supplementary_answer)) {
                                                            if (trim($user_supplementary_answer) == trim($val)) {
                                                                $pre_sel = "selected=selected";
                                                            }
                                                        } else {
                                                            if ($_POST['sup_' . $question['id']] == $val) {
                                                                $pre_sel = "selected=selected";
                                                            }
                                                        }
                                                        ?>
                                                        <option
                                                                value="<?php echo $val ?>" <?php echo $pre_sel ?>><?php echo $val ?></option>
                                                    <?php } ?>
                                                </select></label>
                                        <?php } elseif ($question['type'] == "checkbox_list") { //print_r($question);

                                            $str_answers = $question['answers'];
                                            // turn string into array
                                            $list = explode(',', $str_answers);
                                            //$category = str_replace(" ", "&nbsp;", $list);
                                            ?>

                                            <label class="text-xs font-semibold"><?php echo $question['title']; ?><?php if ($question['required'] == "yes") {
                                                    echo ' *';
                                                } ?></label>

                                            <ul>
                                                <?php foreach ($list as $k => $val) {
                                                    // get predefined field
                                                    // 1 = current value, 2 = current in loop
                                                    $pre_sel = '';
                                                    if (!empty($user_supplementary_answer)) {
                                                        if (strpos($user_supplementary_answer, trim($val)) !== false) {
                                                            $pre_sel = "checked=checked";
                                                        }
                                                    } else {
                                                        if (strpos(implode(',', $_POST['sup_' . $question['id']]), $val) !== false) {
                                                            $pre_sel = "checked=checked";
                                                        }
                                                    }

                                                    ?>
                                                    <li class="flex items-center mb-4">
                                                        <input id="list-checkbox<?= $k ?>" type="checkbox"
                                                               value="<?= $val ?>"
                                                               name="sup_<?php echo $question['id']; ?>[]" <?= $pre_sel ?>
                                                               class="w-4 h-4 text-brand-blue bg-gray-100 border-gray-300 rounded focus:ring-brand-blue/60 dark:focus:ring-brand-blue dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
                                                        <label for="list-checkbox<?= $k ?>"
                                                               class="ms-2 text-xs text-gray-900 dark:text-gray-300"><?php echo $val ?></label>
                                                    </li>
                                                <?php } ?>
                                            </ul>
                                        <?php } elseif ($question['type'] == "radio_list") { //print_r($question);
                                            $str_answers = $question['answers'];
                                            // turn string into array
                                            $list = explode(',', $str_answers);
                                            //$category = str_replace(" ", "&nbsp;", $list);
                                            ?>

                                            <label class="text-xs font-semibold"><?php echo $question['title']; ?><?php if ($question['required'] == "yes") {
                                                    echo ' *';
                                                } ?></label>

                                            <ul>
                                                <?php

                                                foreach ($list as $key => $val) {
                                                    // get predefined field
                                                    // 1 = current value, 2 = current in loop
                                                    $pre_sel = '';
                                                    if (!empty($user_supplementary_answer)) {
                                                        if (strpos($user_supplementary_answer, trim($val)) !== false) {
                                                            $pre_sel = "checked=checked";
                                                        }
                                                    } else {
                                                        if (strpos(implode(',', $_POST['sup_' . $question['id']]), $val) !== false) {
                                                            $pre_sel = "checked=checked";
                                                        }
                                                    }
                                                    ?>
                                                    <li><label class="text-xs"><input
                                                                    name="sup_<?php echo $question['id']; ?>[]"
                                                                    class="text-brand-blue bg-gray-100 border-gray-300 rounded-full focus:ring-brand-blue/60 dark:focus:ring-brand-blue dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                                    value="<?php echo $val ?>"
                                                                    type="radio" <?php echo $pre_sel ?>> <?php echo $val ?>
                                                        </label></li>

                                                <?php } ?>
                                            </ul>
                                        <?php }

                                        ?>
                                    </div>
                                <?php } ?>

                            </div>
                        <?php } ?>

                        <?php if (!is_user_logged_in()) {
                            $section_count++; ?>
                            <hr>
                            <h2 class="text-2xl flex items-center gap-3 font-bold">
                                <span class="w-12 h-12 flex rounded-full items-center justify-center border border-brand-green text-brand-green text-center"><?php echo $section_count; ?></span><?php echo translate("Your account credentials", $_GET['lang']) ?>
                            </h2>

                            <div class="grid grid-cols-2 gap-4">
                                <div class="col-span-2 flex flex-col space-y-4">
                                    <label for="email"
                                           class="text-xs font-semibold <?php if ($errors["email"]) echo "text-danger"; ?>"><?php echo translate("Email address", $_GET['lang']) ?>
                                        *</label>
                                    <input autocomplete="off"
                                           class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full resize-none py-1.5 px-2"
                                           type="email" name="email" id="email"
                                           value="<?php if (!empty($_POST['eligibility_email_address'])) {
                                               echo htmlentities($_POST['eligibility_email_address']);
                                           } else {
                                               echo htmlentities($_POST['email']);
                                           } ?>" required>
                                </div>
                                <div class="col-span-1 flex flex-col gap-2">
                                    <label
                                            class="text-xs font-semibold <?php if ($errors["password"]) echo "text-danger"; ?>"><?php echo translate("Choose a password, or use your existing password if you have one", $_GET['lang']) ?>
                                        *<br/>
                                        <small>
                                            (<?php echo translate("at least 8 alphanumeric characters", $_GET['lang']) ?>
                                            )
                                        </small>
                                    </label>
                                    <input autocomplete="off"
                                           class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full resize-none py-1.5 px-2"
                                           type="password" name="password"
                                           required>
                                </div>
                                <div class="col-span-1 flex flex-col gap-2">
                                    <label class="text-xs font-semibold"><?php echo translate("Confirm password", $_GET['lang']) ?>
                                        *
                                        <br/>
                                        <small>
                                            (<?php echo translate("at least 8 alphanumeric characters", $_GET['lang']) ?>
                                            )
                                        </small>
                                    </label>
                                    <input autocomplete="off"
                                           class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full resize-none py-1.5 px-2"
                                           type="password"
                                           name="password_confirm" required>
                                </div>
                                <div class="clear-both"></div>

                                <div class="col-span-2 flex flex-col gap-2">
                                    <label class="text-xs font-semibold"><span class="hotspot" data-toggle="tooltip"
                                                                               data-placement="right"
                                                                               title="<?php echo translate("Please retype the number below to demonstrate that you are a human being", $_GET['lang']) ?>"><i
                                                    class="fa fa-lg fa-info-circle"
                                                    aria-hidden="true"></i></span><?php echo translate("Authentication Code", $_GET['lang']) ?>
                                        *</label>
                                    <blockquote
                                            style="font-size: xx-small;padding: 8px; background:none; min-height:auto;"><?= translate("Retype the authentication number shown below", $_GET['lang']) ?></blockquote>
                                    <input class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full resize-none py-1.5 px-2"
                                           type="number" name="usercode" id="usercode"
                                           placeholder="Enter authentication code here..."/>
                                    <img src="<?php echo website_url ?>/reg_inc_pngimg.php" class="w-1/5"
                                         align="middle"/>
                                </div>
                            </div>
                        <?php } ?>

                        <?php if (count($plan['challenge_questions'])) {
                            $section_count++; ?>
                            <hr>

                            <h2 class="text-2xl flex items-center gap-3 font-bold">
                                <span class="w-12 h-12 flex rounded-full items-center justify-center border border-brand-green text-brand-green text-center"><?php echo $section_count; ?></span><?php echo translate("Plan Questions", $_GET['lang']) ?>
                            </h2>
                            <div class="space-y-4">
                                <?php foreach ($plan['challenge_questions'] as $question) { ?>
                                    <?php if (strrpos($question['title'], 'postcode') != false || strrpos($question['title'], 'zip code') != false) {

                                        $pattern = "pattern ='^(([gG][iI][rR] {0,}0[aA]{2})|((([a-pr-uwyzA-PR-UWYZ][a-hk-yA-HK-Y]?[0-9][0-9]?)|(([a-pr-uwyzA-PR-UWYZ][0-9][a-hjkstuwA-HJKSTUW])|([a-pr-uwyzA-PR-UWYZ][a-hk-yA-HK-Y][0-9][abehmnprv-yABEHMNPRV-Y]))) {0,}[0-9][abd-hjlnp-uw-zABD-HJLNP-UW-Z]{2}))$' oninvalid='setCustomValidity(\"You have entered an invalid postcode format\")' oninput= 'setCustomValidity(\"\")' ";
                                        ?>
                                        <div class="p-4 mb-4 text-sm text-brand-blue rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400"
                                             role="alert">
                                            Please enter a valid <b> postcode format </b>
                                        </div>
                                    <?php } ?>
                                    <div class="w-1/2 flex flex-col space-y-4">
                                        <label for="challenge_<?php echo $question['id']; ?>" class="text-xs
                                        font-semibold"><?php echo $question['title']; ?> *</label>
                                        <?php if ($question['user_answer_status'] == 'Valid') {
                                            echo '<span class="validated_entry"><div class="validated_entry_circle"></div><div class="validated_entry_stem"></div><div class="validated_entry_kick"></div></span>';
                                        }
                                        if ($question['user_answer_status'] == 'Invalid') {
                                            //echo '<span class="invalidated_entry"><div class="invalidated_entry_circle">X</div></span>';
                                        } ?>
                                        <input class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-full focus:ring-gray-500 focus:border-gray-500 block w-full resize-none p-1.5"
                                               value="<?php if (!isset($question['user_answer']) || $question['user_answer'] == '' || (!empty($_POST['eligibility_postcode']))) {
                                                   if (!empty($_POST['eligibility_postcode'])) {
                                                       echo htmlentities($_POST['eligibility_postcode']);
                                                   } else {
                                                       echo htmlentities($_POST['challenge_' . $question['id']]);
                                                   }
                                               } else {
                                                   echo $question['user_answer'];
                                               } ?>" <?php echo $pattern ?> required="required"
                                               name="challenge_<?php echo $question['id']; ?>"
                                               id="challenge_<?php echo $question['id']; ?>"
                                               type="text">
                                    </div>
                                <?php } ?>

                            </div>
                        <?php } ?>
                        <?php if ($_GET['error_message'] != $error_message_blocked) { ?>
                            <?php if (!isset ($student_id) || $student_id == '' || $plan['user_challenge_validity'] == "Invalid" || $plan['user_challenge_validity'] == "Blocked" || $plan['user_supplementary_validity'] == "Invalid") { ?>
                                <div class="text-center" style="padding-top: 40px;">
                                    <input type="hidden" name="action" value="register_user">
                                    <button type="submit"
                                            class="block bg-white border-2 border-brand-green text-brand-green p-1 w-1/4 mx-auto hover:bg-brand-green hover:text-white rounded-full font-bold text-center"><?php echo translate("Continue", $_GET['lang']) ?></button>
                                </div>
                            <?php } else { ?>
                                <div class="text-center" style="padding-top: 40px;">
                                    <input type="hidden" name="action" value="register_user">
                                    <button type="submit"
                                            class="text-brand-blue hover:text-white border border-brand-blue hover:bg-brand-blue w-1/4 mx-auto focus:ring-4 focus:outline-none mx-auto focus:ring-blue-300 font-semibold rounded-full text-sm px-5 py-2.5 text-center me-2 mb-2"><?php echo translate("Update", $_GET['lang']) ?></button>
                                </div>

                            <?php } ?>

                        <?php } ?>

                    </div>
                </form>

            </div>
            <div class="w-4/12 bg-white">


                <script type="text/javascript">
                    $(document).ready(function () {
                        $(".show_code_form a").click(function () {
                            $(".discount_code_input").removeClass('hide');
                            $(".discount_code_input .form-control").focus();
                            $(".show_code_form").hide();
                        });
                    });
                </script>


                <div class="rounded shadow-lg p-8 overflow-hidden mb-8 space-y-4">
                    <?php if ($plan['price'] != '' && $plan['price'] != '0' && $plan['price'] != '0.00') { ?>
                        <h2 class="font-bold text-2xl mt-5"><?php echo translate("Order summary", $_GET['lang']) ?></h2>
                    <?php } ?>
                    <?php if (isset($_GET['payment_error']) && $_GET['payment_error'] != '') { ?>
                        <div id="alert-warning" class="alert alert-warning" role="alert">
                            <i class="fa fa-user-times" aria-hidden="true"></i> <?php echo $_GET['payment_error'] ?>
                        </div>
                    <?php } ?>
                    <?php if (!$_SESSION['valid_coupon_id']) {
                        if (isset($_GET['coupon_error']) && $_GET['coupon_error'] != '') { ?>
                            <div id="alert-warning" class="alert alert-warning" role="alert">
                                <i class="fa fa-user-times" aria-hidden="true"></i> <?php echo $_GET['coupon_error'] ?>
                            </div>
                        <?php } ?>
                    <?php } ?>
                    <div class="flex gap-2">
                        <?php if ($plan['price'] == '' || $plan['price'] == '0' || $plan['price'] == '0.00') { ?>
                            <div class="w-full">
                                <div class="font-bold text-xl"><?php echo $plan['title']; ?></div>
                            </div>
                        <?php } else { ?>
                            <div class="w-7/12">
                                <div class="font-bold text-xl"><?php echo $plan['title']; ?></div>
                            </div>
                            <div class="w-5/12 text-right">
                                <div class="plan_price">&pound;<?php echo number_format($plan['price'], 2); ?></div>
                            </div>
                        <?php } ?>
                    </div>
                    <?php if ($plan['price'] != '' && $plan['price'] != '0' && $plan['price'] != '0.00') { ?>
                        <?php if ($plan['include_vat'] == "yes") { ?>
                            <div class="flex gap-2">
                                <div class="w-7/12">
                                    <div class="font-bold text-2xl"><?php echo translate("VAT", $_GET['lang']) ?>
                                        (<?php echo $plan['vat_percent']; ?>&percnt;)
                                    </div>
                                </div>
                                <div class="w-5/12 text-right">
                                    <div
                                            class="plan_price">
                                        &pound;<?php echo number_format($plan['price_including_vat'] - $plan['price'], 2); ?></div>
                                </div>
                            </div>
                        <?php } ?>
                    <?php } ?>
                    <ul class="flex flex-col">
                        <?php foreach ($plan['courses'] as $course) { ?>
                            <li class="text-xs"><?php echo $course['title']; ?></li>
                        <?php } ?>
                    </ul>
                    <?php if ($plan['price'] != '' && $plan['price'] != '0' && $plan['price'] != '0.00') { ?>
                        <?php if (!$_SESSION['valid_coupon_id']) { ?>

                            <hr>
                            <p class="show_code_form"><a
                                        href="#"><?php echo translate("Have a discount code? Click here to enter it", $_GET['lang']) ?></a>
                            </p>

                            <form method="POST">
                                <div class="input-group discount_code_input hide">
                                    <input type="text" class="form-control" placeholder="Discount code" name="coupon"
                                           required="required">
                                    <span class="input-group-btn">
                                <button class="btn btn-primary"
                                        type="submit"><?php echo translate("Apply", $_GET['lang']) ?></button>
                              </span>
                                </div>
                                <input type="hidden" name="action" value="apply_coupon">
                            </form>

                        <?php } ?>

                        <hr>
                        <div class="flex gap-2">
                            <div class="w-7/12">
                                <div class="total_title"><?php echo translate("Subtotal", $_GET['lang']) ?></div>
                            </div>
                            <div class="w-5/12 text-right">
                                <div
                                        class="total_value">
                                    &pound;<?php echo number_format($plan['price_including_vat'], 2); ?></div>
                            </div>
                            <?php if ($_SESSION['valid_coupon_id']) { ?>
                                <div class="w-7/12">
                                    <div class="total_title"><?php echo translate("Discount", $_GET['lang']) ?> (<a
                                                href="<?php echo current_url() . '&remove_discount=true'; ?>"><?php echo translate("Remove", $_GET['lang']) ?></a>)
                                    </div>
                                </div>
                                <div class="w-5/12 text-right">
                                    <div
                                            class="total_value">
                                        &pound;<?php echo number_format($coupon['total_discount'], 2); ?></div>
                                </div>
                                <div class="w-7/12">
                                    <div class="total_title"><?php echo translate("Total", $_GET['lang']) ?></div>
                                </div>
                                <div class="w-5/12 text-right">
                                    <div
                                            class="total_value main">
                                        &pound;<?php echo $plan['price_including_vat'] - $coupon['total_discount']; ?></div>
                                </div>
                            <?php } else { ?>
                                <div class="w-7/12">
                                    <div class="total_title"><?php echo translate("Total", $_GET['lang']) ?></div>
                                </div>
                                <div class="w-5/12 text-right">
                                    <div class="total_value main">
                                        £<?php echo number_format($plan['price_including_vat'], 2); ?></div>
                                </div>
                            <?php } ?>

                        </div>
                        <?php if ($plan['user_access_plan_status'] != "2") { ?>
                            <?php if (isset ($student_id) && $student_id != '' && $plan['user_challenge_validity'] != "Invalid" && $plan['user_challenge_validity'] != "Blocked" && $plan['user_supplementary_validity'] != "Invalid" && $plan['user_sponsor_validity'] != "Invalid") {
                                $student_username_id = pull_field("core_students", "username_id", "Where id = $student_id") ?>

                                <div class="text-center">
                                    <?php if ($_SESSION['usergroup'] == '47') { ?>
                                    <form method="post" id="buy-now-form_47">
                                        <?php } else { ?>
                                        <form method="post" id="buy-now-form">
                                            <?php } ?>
                                            <div class="row user-info">
                                                <span class="payment-errors"></span>
                                            </div>
                                            <input type="hidden" name="action" id="action" value="process_payment">
                                            <input type="hidden" name="uap" id="uap"
                                                   value="<?php echo $plan['user_access_plan_id'] ?>">
                                            <input id="total_amount" name="total_amount" type="hidden"
                                                   value="<?php echo ($plan['price_including_vat'] - $coupon['total_discount']) * 100 ?>"/>
                                            <input id="total_amount_vat" name="total_amount_vat" type="hidden"
                                                   value="<?php echo($plan['price_including_vat'] - $plan['price']) ?>"/>
                                            <input id="total_amount_excl_vat" name="total_amount_excl_vat" type="hidden"
                                                   value="<?php echo $plan['price'] - $coupon['total_discount'] ?>"/>
                                            <input id="discount_amount" name="discount_amount" type="hidden"
                                                   value="<?php echo $coupon['total_discount'] ?>"/>
                                            <input id="plan_id" name="plan_id" type="hidden"
                                                   value="<?php echo $plan['id'] ?>"/>
                                            <?php if ($plan['price_including_vat'] - $coupon['total_discount'] <= 0) { ?>
                                                <?php if ($_SESSION['usergroup'] == '47') { ?>
                                                    <button id="checkout_now_button_47"
                                                            class="btn btn-lg btn-success"><?php echo translate("It's FREE! Click here to access", $_GET['lang']) ?></button>
                                                <?php } else { ?>
                                                    <button id="checkout_now_button"
                                                            class="btn btn-lg btn-success"><?php echo translate("It's FREE! Click here to access", $_GET['lang']) ?></button>
                                                <?php } ?>

                                            <?php } else { ?>
                                                <?php if ($_SESSION['usergroup'] == '47') { ?>
                                                    <button id="buy_now_button_47"
                                                            data-student=<?php echo $student_username_id ?> data-plan="<?php echo $_GET['plan']; ?>"
                                                            title="We don’t store your details on our servers. We use Stripe (https://stripe.com/gb/features#seamless-security). It is secure."
                                                            class="btn btn-lg btn-success"><?php echo translate("Pay by Card", $_GET['lang']) ?></button>
                                                <?php } else { ?>
                                                    <button id="buy_now_button"
                                                            data-student=<?php echo $student_username_id ?> data-plan="<?php echo $_GET['plan']; ?>"
                                                            title="We don’t store your details on our servers. We use Stripe (https://stripe.com/gb/features#seamless-security). It is secure."
                                                            class="btn btn-lg btn-success"><?php echo translate("Pay by Card", $_GET['lang']) ?></button>
                                                <?php } ?>

                                            <?php } ?>

                                        </form>
                                </div>

                            <?php } ?>
                        <?php } ?>
                    <?php } ?>
                </div>
            </div>
        </div>

    </div>

<?php

if ($_SESSION['usergroup'] == '47') {

    ?>
    <script>
        $(document).ready(function () {
            var handler = StripeCheckout.configure({
                key: '<?php echo $stripe['publishable_key']; ?>',
                locale: 'auto',
                closed: function () {
                    // if user clicks close button
                    $('#buy-now-form_47').find('button').prop('disabled', false);
                },
                token: function (token) {
                    // Use the token to create the charge with a server-side script.
                    // You can access the token ID with `token.id`
                    var $form = $('#buy-now-form_47');

                    if (token.error) {
                        // Show the errors on the form
                        $form.find('.payment-errors').text(token.error.message);

                        $form.find('button').prop('disabled', false);
                    } else {
                        // response contains id and card, which contains additional card details
                        var token_id = token.id;
                        // Insert the token into the form so it gets submitted to the server
                        $form.append($('<input type="hidden" name="stripeToken" />').val(token.id));
                        $form.append($('<input type="hidden" name="stripeEmail" />').val(token.email));
                        // and submit

                        $form.submit();
                    }
                }
            });

            $('#buy_now_button_47').on('click', function (e) {
                var $form = $('#buy-now-form_47');
                // Open Checkout with further options
                // Disable the submit button to prevent repeated clicks
                $form.find('button').prop('disabled', true);
                var plan_id = $(this).data('plan');
                var student_id = $(this).data('student');
                var link = "https://stripe.com/gb/features#seamless-security";
//		swal({
//				title: "We use Stripe",
//				text:  'We do not store your details on our servers. We use <a href="'+ link+'" target="_blank"> Stripe</a> . It is secure.',
//				html:true,
//				type: "info",
//				showCancelButton: true,
//				confirmButtonColor: "#DD6B55",
//				confirmButtonText: "Continue", cancelButtonText: "Cancel",
//				closeOnConfirm: true,
//				closeOnCancel: true
//			},
//			function (isConfirm) {
//				if (isConfirm) {
                var total_amount = $('#total_amount').val();
                handler.open({
                    name: 'Stripe.com',
                    description: 'Access Plan ' + plan_id + ' Student ' + student_id,
                    amount: total_amount,
                    locale: 'auto',
                    currency: 'GBP'
                });
                e.preventDefault();
//				}  else {
//					$form.find('button').prop('disabled', false);
//				}
//			}
//		);

            });

            $('#checkout_now_button_47').on('click', function (e) {
                var $form = $('#buy-now-form_47');
                // Open Checkout with further options
                // Disable the submit button to prevent repeated clicks
                $form.find('button').prop('disabled', true);
                $form.submit();
            });

            // Close Checkout on page navigation
            $(window).on('popstate', function () {
                handler.close();
            });
        });
    </script>
    <?php
}
?>