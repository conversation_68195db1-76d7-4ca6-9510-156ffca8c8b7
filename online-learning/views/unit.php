<?php


if ($_GET['show_errors']) {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

ini_set('memory_limit', '-1');

global $db;
$form_templates = new FormTemplates;

define('ACTIVE_UNIT', $slugs[5]);
$course_args = array(
    'id' => $slugs[3],
    'student_id' => $_SESSION['student_id'],
    'single_module' => $slugs[4],
    'single_unit_description' => $slugs[4]);
$course = $OL->get_courses($course_args);
// echo '<pre>***!!';
// print_r($course);
// exit();

$_SESSION['entry_language_id'] = $course['language']['id'];
$_SESSION['entry_language_direction'] = $course['language_direction'];

$module = $course['modules'];


$unit_count = 0;
foreach ($module['units'] as $unit_info) {
    if (!$unit_count) {
        $first_unit = $unit_info;
    }

    if ($unit_info['id'] == $slugs[5]) {
        $unit = $unit_info;
        $next_unit = $module['units'][$unit_count + 1];
        $previous_unit = $module['units'][$unit_count - 1];
    }
    $unit_count++;
}


//Check for the question
if (is_user_logged_in()) {

    if ($unit['questionnaire_form_id']) {

        $form_templates = new FormTemplates;
        $page_args = array("id" => $unit['questionnaire_form_id'], "system_abbrv" => "inst", "school_id" => $school_info['id']);
        $form = $form_templates->get($page_args);

        if (count($form['fields'])) {

            $answers = $form_templates->answers(array('form_name' => $form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
            if (count($form['fields']) > 0) {
                if (!$answers['id']) {
                    //redirect the user to unit 1
                    echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/questionnaire/" . $course['id'] . "/" . $module['id'] . "/" . $unit['id']) . "\">";
                    exit();
                }
            }
        }
    }
}

if (!$unit['id']) {
    header("Location: " . home_url("/404"));
    exit();
}


//Unit comment
if ($_POST['action'] == "unit_comment") {
    $comment = array(
        'entry_id' => $unit['id'],
        'student_id' => $_SESSION['student_id'],
        'category' => 'Unit',
        'title' => $_POST['title'],
        'description' => $_POST['description']
    );

    $comment = $OL->insert_update_comment($comment);
}


//Check if course offers trail units
if (!$unit['trail'] && !$course['bought']) {
    $trail_marker = $course['modules'][0]['units'][0]['trail'];

    if (!is_user_logged_in()) {
        echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/register/") . "?trial_end=$trail_marker&course=" . $course[username_id] . "\">";
        exit();
    } else {
        echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . home_url("/buy-course/") . "?trial_end=$trail_marker&course=" . $course[username_id] . "\">";
        exit();
    }
}

//Redo
if ($_GET['redo_quiz']) {
    if (is_user_logged_in()) {

        //Get the form information
        $page_args = array("id" => $unit['quiz']['id'], "system_abbrv" => 'inst');
        $form = $form_templates->get($page_args);

        //Delete the student record from quiz
        $db->delete($form['table_name'], array('rel_id' => $_SESSION['student_id']));

        echo "<meta http-equiv=\"refresh\" content=\"0;URL=" . $unit["href"] . "#quiz" . "\">";
        //header("Location: ".$unit["href"]."#quiz");
        exit();
    }
}

$show_next_unit_btn = false;
if (!$previous_unit['id']) {
    $show_next_unit_btn = true;
    $next_button_style = "";
}

if (is_user_logged_in()) {
    $show_next_unit_btn = true;
    $next_button_style = "";
}


if ($show_next_unit_btn) {
    $next_button_style = "";
    if ($next_unit['id']) {
        $show_next_unit_btn = true;
    } else {
        $show_next_unit_btn = false;
        $show_complete_module_unit_btn = true;
    }
}


if ($course['next_unit_visibility'] == "completed_only") {
    //check if previous unit is completed
    if ($previous_unit['completed_date'] && $previous_unit['completed_date'] != '') {
        $show_next_unit_btn = true;
        if ($unit['completed_date'] && $unit['completed_date'] != '') {
            $next_button_style = "";
        } else {
            $next_button_style = "style = display:none";
        }
    } else {

        if (!$previous_unit['id']) {
            $show_next_unit_btn = true;
            if ($unit['completed_date'] && $unit['completed_date'] != '') {
                $next_button_style = "";
            } else {
                $next_button_style = "style = display:none";
            }
            $show_previous = false;
        } else {
            $show_next_unit_btn = false;
            $show_previous = false;
        }
    }
}


//exit();


//HIDDEN UNIT MESSAGE
if ($unit['visibility'] == "hidden") {
    ?>
    <div class="container">

        <div id="single_course">
            <div class="course_info text-center">
                <i class="fa fa-hand-o-left" aria-hidden="true" style="font-size: 44px;"></i>
                <h3><?php echo text_translate("This unit is hidden", $course['language']['id']); ?></h3>
                <a class="btn btn-primary"
                   href="<?php echo $course['href']; ?>"><?php echo text_translate("Back to course main page", $course['language']['id']); ?></a>
                <br><br>
            </div>
        </div>
    </div>
    <?php
    exit();

}


if ($previous_unit['quiz_mandetory'] && $previous_unit['quiz']) {
    if (!$previous_unit['quiz_response']) {

        $page_args = array("id" => $previous_unit['quiz']['id'], "system_abbrv" => "inst", "school_id" => $school_info['id']);
        $form = $form_templates->get($page_args);
        if (count($form['fields']) > 0) {
            ?>
            <div class="container">

                <div id="single_course">
                    <div class="course_info text-center">
                        <i class="fa fa-hand-o-left" aria-hidden="true" style="font-size: 44px;"></i>
                        <h3><?php echo text_translate("Complete the previous unit quiz", $course['language']['id']); ?></h3>
                        <p style="font-size: 16px; margin-bottom: 30px;"> <?php echo text_translate("You need to answer all the questions in the previous unit before you can proceed to the next unit", $course['language']['id']); ?></p>
                        <a class="btn btn-primary"
                           href="<?php echo $previous_unit['href']; ?>"><?php echo text_translate("Back to previous unit", $course['language']['id']); ?></a>
                        <br><br>
                    </div>
                </div>
            </div>
            <?php
            exit();
        }
    }
}


// echo $first_unit[id];
// echo "*****";
// echo $slugs[5];
// exit();

// echo '<pre>';
// print_r($course['language']['direction']);
// exit();


//echo $show_complete_module_unit_btn."-----";
if (($show_next_unit_btn || $show_complete_module_unit_btn || $_GET['trial'] == 1)) {
} else {
    //echo $course['next_unit_visibility']."*****";
    if ($course['next_unit_visibility'] == "completed_only") {
        ?>
        <div class="container">

            <div dir="<?php echo $course['language_direction']; ?>" id="single_course"
                 class="<?php echo $course['language_direction']; ?>">
                <div class="course_info text-center">
                    <i class="fa fa-hand-o-left" aria-hidden="true" style="font-size: 44px;"></i>
                    <h3><?php echo text_translate("Complete previous unit", $course['language']['id']); ?></h3>
                    <p style="font-size: 16px; margin-bottom: 30px;"><?php echo text_translate("You need to mark the previous unit as &ldquo;complete&rdquo; before you can proceed to the next unit", $course['language']['id']); ?></p>
                    <a class="btn btn-primary"
                       href="<?php echo $previous_unit['href']; ?>"><?php echo text_translate("Back to previous unit", $course['language']['id']); ?></a>
                    <br><br>
                </div>
            </div>
        </div>
        <?php exit();
    }
} ?>
<?php
//Record Unit view
$view_args = array('course_id' => $slugs[3], 'module_id' => $slugs[4], 'unit_id' => $slugs[5], 'student_id' => $_SESSION['student_id']);
$OL->record_unit_view($view_args);
?>
<div class="w-screen overflow-x-hidden">
    <div id="main_bread_crumb" dir="<?php echo $course['language_direction']; ?>" class="w-3/4 mx-auto py-4">
        <a href="<?php echo $course['href']; ?>"><i class="fa fa-arrow-left"
                                                    aria-hidden="true"></i> <?php echo $course['title']; ?></a>
        / <?php echo $module['title']; ?>
    </div>
    <div dir="<?php echo $course['language_direction']; ?>" id="single_course"
         class="<?php echo $course['language_direction']; ?> w-3/4 mx-auto shadow-xl font-varela">
        <div class="intro" id="small" style="background-color: <?php echo $course['colour']; ?>">
            <div class="top" dir='ltr'>
                <ul>
                    <?php
                    $unit_count = 0;
                    $total_units = count($module['units']);
                    foreach ($module['units'] as $unit_info) {
                        if ($unit_count != 0) {
                            $pu = $previous_unit_info;
                        } else {
                            $pu['completed_date'] = 1;
                        }

                        $href = $unit_info['href'];
                        if ($unit_info['id'] == $unit['id']) {
                            $icon = 'fa-circle-o-notch';
                        } else if ($unit_info['completed_date']) {
                            $icon = "fa-check-circle";
                        } else {
                            if ($course['next_unit_visibility'] == "completed_only") {
                                if (!$pu['completed_date']) {
                                    $icon = 'fa-circle';
                                    $href = "javascript:void(0)";
                                } else {
                                    $icon = 'fa-play-circle-o';
                                }
                            } else {
                                $icon = 'fa-play-circle-o';
                            }
                        }
                        $unit_count++;
                        $previous_unit_info = $unit_info;
                        ?>
                        <li>
                            <a href="<?php echo $href ?>">
                                <i class="fa <?php echo $icon; ?>"
                                   data-unit=<?php echo $unit['id']; ?> data-unit-info=<?php echo $unit_info['id']; ?>
                                   aria-hidden="true" data-container="body" data-toggle="popover"
                                   data-tooltip-placement="bottom"
                                   data-tooltip-style="light"
                                   data-tooltip-target="tooltip-<?php echo $unit_info['id']; ?>"></i>
                            </a>
                            <div id="tooltip-<?php echo $unit_info['id']; ?>" role="tooltip"
                                 class="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-gray-600 bg-white rounded-lg shadow-sm opacity-0 tooltip">
                                Unit <?php echo $unit_count . "/" . $total_units; ?>: <?php echo $unit_info['title']; ?>
                                <div class="tooltip-arrow" data-popper-arrow></div>
                            </div>
                        </li>
                        <?php if ($unit_count != $total_units) { ?>
                            <li class="divider"></li> <?php } ?>
                    <?php } ?>
                </ul>
            </div>
            <div class="text">
                <h1><?php echo $unit['title']; ?></h1>
                <h5>
                    <i><?php echo text_translate('Scroll to the bottom to "Mark as Completed" to progress through the Units', $course['language']['id']); ?></i>
                </h5>
            </div>
            <?php
            if (is_user_logged_in()) {
                //AFY 9/8/18 $user_info[id] is now blank so change $course_audio = pull_field("core_students", "db25618", "where id = $user_info[id]");
                $course_audio = pull_field("core_students", "db25618", "where id = $_SESSION[student_id]");
                if ($course_audio == 'on') {
                    if ($unit['mp3_auto_play'] == '1') {
                        $course_audio = 'on';
                    } else {
                        $course_audio = 'off';
                    }
                }
            } else {
                if ($unit['mp3_auto_play'] == '1') {
                    $course_audio = 'on';
                } else {
                    $course_audio = 'off';
                }
            }

            if ($unit['mp3_file']) {
                ?>
                <div class="audio_wrap">
                    <audio <?php if ($course_audio != 'off') {
                        echo 'autoplay';
                    } ?> id="player1" style="width:100%; opacity: 0;" controls preload="none">
                        <source src="<?php echo $unit['mp3_file']; ?>" type="audio/mpeg"/>
                        <?php echo text_translate("Your browser does not support the audio element.", $course['language']['id']); ?>
                    </audio>
                </div>
            <?php } ?>

        </div>


        <div class="course_info <?php echo $course['language_direction']; ?>">
            <div class="flex gap-4 justify-center">
                <div class="w-8/12">
                    <div class="page_text about">
                        <?php echo $unit['description']; ?>
                    </div>

                    <?php
                    if (is_user_logged_in()) {
                        if ((!$unit['quiz_response'] && $unit['quiz']) || ($unit['quiz'] && $unit['quiz_answerable_multiple_times'])) {

                            $page_args = array("id" => $unit['quiz']['id'], "system_abbrv" => "inst", "school_id" => $school_info['id']);
                            $form = $form_templates->get($page_args);
                            if (count($form['fields']) > 0) {
                                ?>
                                <a name="quiz"></a>
                                <div class="unit_quiz">
                                    <?php
                                    if ($unit['quiz']['id']) {
                                        $form_args = array(
                                            "form_id" => $unit['quiz']['id'],
                                            "id" => $_POST['id'],
                                            "system_abbrv" => 'inst',
                                            "school_id" => $school_info['id'],
                                            "submit_btn_class" => 'btn-default',
                                            "rel_id" => $_SESSION['student_id'],
                                            "submit_btn_text" => text_translate("Save Questionnaire", $course['language']['id']),
                                        );

                                        if ($unit['quiz_answerable_multiple_times']) {
                                            $form_args['dont_overwrite_responses'] = "yes";
                                        }

                                        if (isset($_GET['temp_debug'])) {
                                            echo "quiz debug<pre>" . print_r($form_args, 1) . "</pre>";
                                        }

                                        echo '<h3>' . $unit['quiz']['title'] . '</h3><br>';
                                        $form_templates->form_html($form_args);
                                    }
                                    ?>
                                </div>
                            <?php }
                        }
                    }
                    ?>
                    <?php if (isset($_GET['temp_debug'])) {
                        echo "unit : <pre>" . print_r($unit, 1) . "</pre>";
                    } ?>

                    <?php if ($unit['quiz_response'] && ($unit['show_user_answer'])) { ?>
                        <div class="unit_quiz_responses">
                            <a class="pull-right"
                               href="?redo_quiz=1&href=<?php echo $unit["href"]; ?>#quiz"><?php echo text_translate('Redo unit quiz', $course['language']['id']); ?></a>
                            <h3><?php echo text_translate('Your Quiz Answers', $course['language']['id']); ?></h3>
                            <ul>
                                <?php foreach ($unit['quiz_response'] as $question) { ?>
                                    <li>
                                        <div class="icon">
                                            <?php if (str_replace(' ', '_', $question['response']) == str_replace(' ', '_', $question['answer'])) { ?>
                                                <i class="fa fa-check-circle" aria-hidden="true"></i>
                                            <?php } else { ?>
                                                <i class="fa fa-times-circle" aria-hidden="true"></i>
                                            <?php } ?>
                                        </div>
                                        <div class="info">
                                            <div class="unit_question">
                                                <strong><?php echo text_translate('Question', $course['language']['id']); ?>
                                                    : </strong><?php echo $question['question']; ?></div>
                                            <div class="your_answer">
                                                <strong><?php echo text_translate('Your answer', $course['language']['id']); ?>
                                                    : </strong><?php echo str_replace('_', ' ', $question['response']); ?>
                                            </div>
                                            <?php if (str_replace(' ', '_', $question['response']) != str_replace(' ', '_', $question['answer'])) { ?>
                                                <?php if (isset($question['incorrect_reason']) && $question['incorrect_reason'] != '') { ?>
                                                    <div class="incorrect_reason">
                                                        <strong><?php echo text_translate('Incorrect Feedback', $course['language']['id']); ?>
                                                            : </strong><?php echo $question['incorrect_reason']; ?>
                                                    </div>
                                                <?php } ?>
                                            <?php } ?>
                                            <div class="correct_answer">
                                                <strong><?php echo text_translate('Correct answer', $course['language']['id']); ?>
                                                    : </strong><?php echo $question['answer']; ?></div>
                                            <?php if (isset($question['answer_reason']) && $question['answer_reason'] != '') { ?>
                                                <div class="answer_reason">
                                                    <strong><?php echo text_translate('Correct Feedback', $course['language']['id']); ?>
                                                        : </strong><?php echo $question['answer_reason']; ?></div>
                                            <?php } ?>
                                        </div>
                                    </li>
                                <?php } ?>
                            </ul>
                        </div>
                    <?php } ?>


                    <?php
                    $data_args = array("db45504" => $slugs[3], 'db45505' => $slugs[4], 'db45506' => $slugs[5]);
                    $attached_quiz = $OL->get_attached_quiz($data_args);
                    if (isset($attached_quiz[0]['page_id'])) {
                        $student_survey_answers = $OL->survey_answers(array('rec_id' => $_SESSION['uid'], 'unit_id' => $slugs[5], 'attached_quiz' => $attached_quiz[0]['page_id']));
                    } else {
                        $student_survey_answers = $OL->survey_answers(array('rec_id' => $_SESSION['uid'], 'unit_id' => $slugs[5]));
                    }

                    //echo "<pre>".print_r($attached_quiz,1)."</pre>";
                    ?>
                    <?php
                    if (is_user_logged_in()) {
                        if (isset($attached_quiz[0]['id']) && ((!isset($student_survey_answers)) || (empty($student_survey_answers)))) {
                            if (isset($_GET['temp_debug'])) {
                                echo "printing quiz ";
                            }
                            $page_args = array("id" => $attached_quiz[0]['page_id'], "system_abbrv" => "inst", "school_id" => $school_info['id']);
                            $form = $form_templates->get($page_args);
                            //echo "<pre>".print_r($form,1)."</pre>";
                            if (count($form['fields']) > 0) {
                                ?>
                                <a name="quiz"></a>
                                <div class="unit_quiz">
                                    <?php
                                    if ($attached_quiz[0]['id']) {
                                        $form_args = array(
                                            "form_id" => $attached_quiz[0]['page_id'],
                                            "id" => $_POST['id'],
                                            "system_abbrv" => 'inst',
                                            "school_id" => $school_info['id'],
                                            "submit_btn_class" => 'btn-default',
                                            "rel_id" => $_SESSION['student_id'],
                                            "new_implementation" => "yes",
                                            "submit_btn_text" => text_translate("Save Questionnaire", $course['language']['id']),
                                        );

                                        if ($unit['quiz_answerable_multiple_times']) {
                                            $form_args['dont_overwrite_responses'] = "yes";
                                        }

                                        if (isset($_GET['temp_debug'])) {
                                            echo "quiz debug<pre>" . print_r($form_args, 1) . "</pre>";
                                        }

                                        echo '<h3>' . $attached_quiz[0]['title'] . '</h3><br>';

                                        $form_templates->form_html($form_args);
                                    }
                                    ?>
                                </div>
                                <?php
                            }
                        }
                    } ?>

                    <?php if (isset($student_survey_answers) && (!empty($student_survey_answers))) {
                        if (isset($_GET['temp_debug'])) {
                            echo "printing responses ";
                        }
                        ?>
                        <div class="unit_quiz_responses">
                            <h3><?php echo text_translate('Your Answers to Feedback Questions', $course['language']['id']); ?></h3>
                            <ul>
                                <?php foreach ($student_survey_answers as $answer) { ?>
                                    <li>
                                        <div class="info">
                                            <div class="unit_question">
                                                <strong><?php echo text_translate('Question', $course['language']['id']); ?>
                                                    : </strong><?php echo $answer['question']; ?></div>
                                            <div class="your_answer">
                                                <strong><?php echo text_translate('Your answer', $course['language']['id']); ?>
                                                    : </strong><?php echo $answer['response']; ?></div>
                                        </div>
                                    </li>
                                <?php } ?>
                            </ul>
                        </div>
                    <?php } ?>

                    <?php if (is_user_logged_in()) {
                        if ($show_next_unit_btn || $show_complete_module_unit_btn) { ?>
                            <style type="text/css">
                                .mark_as_completed .fa {
                                    font-size: 2.3rem;
                                }
                            </style>
                            <div class="mark_as_completed_div">
                                <?php if ($unit['completed_date'] && $unit['completed_date'] != '') { ?>
                                    <p class="flex gap-4 items-center"><?php echo text_translate('You completed this Unit on', $course['language']['id']); ?> <?php echo date('d/m/Y', strtotime($unit['completed_date'])); ?>
                                        <img src="/online-learning/assets/images/check-mark.png" width="48">
                                    </p>
                                <?php } else { ?>
                                    <div class="flex gap-2">
                                        <div class="w-2/3" close-on-completed>
                                            <span><?php echo text_translate('Have you completed this Unit? ', $course['language']['id']); ?><?php echo text_translate('Give yourself a tick!', $course['language']['id']); ?></span>
                                        </div>
                                        <div class="w-1/3">
                                            <button class="btn btn-success mark_as_completed"
                                                    data-unit=<?php echo $unit['id']; ?>><i class="fa fa-check-circle"
                                                                                            aria-hidden="true"
                                                                                            style="font-size: 2.5rem"></i>
                                                <span style="vertical-align: text-bottom;"> <?php echo text_translate('Mark as completed', $course['language']['id']); ?>
                                        </span>
                                            </button>
                                        </div>
                                    </div>
                                <?php } ?>
                            </div>
                        <?php }
                    } ?>

                    <div class="next_module" dir="ltr">
                        <?php if ($_GET['trial'] == 1) {
                            $uri_parts = explode('?', $_SERVER['REQUEST_URI'], 2);
                            $unit_href = 'https://' . $_SERVER['HTTP_HOST'] . $uri_parts[0];
                            $pos = array_search($unit_href, $course['trial_units']);
                            if ($pos !== false) {
                                if ($pos != 0) {
                                    echo '<a href="' . $course['trial_units'][$pos - 1] . '?trial=1" class="btn btn-primary access_unit"><i class="fa fa-arrow-left" aria-hidden="true"></i> Previous Trial Unit </a>&nbsp;';
                                }
                                if ($pos == count($course['trial_units']) - 1) {
                                    //end of trial
                                    if (!is_user_logged_in()) {
                                        $href = home_url("/register/") . "?trial_end=1>";

                                    } else {
                                        $href = home_url("/buy-course/") . "?trial_end=1>";

                                    }
                                    echo '<a href="' . $href . '" class="btn btn-primary">Trial Finished <i class="fa fa-arrow-right" aria-hidden="true"></i></a>';
                                } else {
                                    if ($pos != 0) {
                                        echo '<a href="' . $course['trial_units'][$pos - 1] . '?trial=1" class="btn btn-primary access_unit"><i class="fa fa-arrow-left" aria-hidden="true"></i> Previous Trial Unit </a>&nbsp;';
                                    }
                                    echo '<a  href="' . $course['trial_units'][$pos + 1] . '?trial=1" class="btn btn-primary access_unit">Next Trial Unit <i class="fa fa-arrow-right" aria-hidden="true"></i></a>';

                                }

                            }

                        } else { ?>
                            <div class="flex justify-between gap-2">
                                <?php if ($previous_unit) { ?>
                                    <div class="w-1/4">
                                        <a data-unit="<?php echo $previous_unit['id']; ?>"
                                           href="<?php echo $previous_unit['href']; ?>"
                                           class="btn btn-primary access_unit"><i class="fa fa-arrow-left"
                                                                                  aria-hidden="true"></i> <?php echo text_translate('Previous Unit', $course['language']['id']); ?>
                                        </a>
                                    </div>
                                <?php } ?>


                                <?php if ($show_complete_module_unit_btn) { ?>
                                    <div class="w-1/4">
                                        <span id="display_next" <?php echo $next_button_style ?>><a
                                                    href="<?php echo $course['href'] . "/" . $module['id'] . "/complete"; ?>"
                                                    class="btn btn-primary"><?php echo text_translate('Complete Module', $course['language']['id']); ?> <i
                                                        class="fa fa-arrow-right" aria-hidden="true"></i></a></span>
                                    </div>
                                <?php } else { ?>
                                    <?php if ($show_next_unit_btn) { ?>
                                        <div class="w-1/4">
                                            <span id="display_next" <?php echo $next_button_style ?>><a
                                                        data-unit="<?php echo $next_unit['id']; ?>"
                                                        href="<?php echo $next_unit['href']; ?>"
                                                        class="btn btn-primary access_unit"
                                                        dir="ltr"><?php echo text_translate('Next Unit', $course['language']['id']); ?> <i
                                                            class="fa fa-arrow-right" aria-hidden="true"></i></a></span>
                                        </div>
                                    <?php } ?>
                                <?php } ?>
                            </div>
                        <?php } ?>
                    </div>


                    <?php if ($course['show_support_on_each_unit'] == 1) { ?>
                        <form id="unit_support" method="POST" class="">

                            <?php
                            if (is_user_logged_in()) {
                                $s_args = array('entry_id' => $unit['id'], 'student_id' => $_SESSION['student_id'], 'category' => 'Unit', 'order_asc' => true);
                                $support_comments = $OL->get_support_comments($s_args);

                                // echo '<pre>';
                                // print_r($support_comments);
                                // echo '</pre>';


                                ?>
                                <h4>Unit Support</h4>

                                <div class="ticket_comments">
                                    <?php foreach ($support_comments as $comment) { ?>
                                        <div class="box <?php if ($comment['user']['id'] == $_SESSION['uid']) {
                                            echo 'green';
                                        } ?>">
                                            <div class="title">
                                                <?php echo $comment['user']['first_name'] . " " . $comment['user']['last_name']; ?>
                                                <span><?php echo date("F j, Y, g:i a", strtotime($comment['date'])); ?></span>
                                            </div>
                                            <div class="description"><?php echo $comment['description']; ?></div>
                                        </div>
                                    <?php }
                                    if (count($support_comments)) { ?>
                                        <hr>
                                    <?php } ?>
                                </div>

                                <p><?php echo text_translate("Ask a question and our Tutor will reply you shortly.", $course['language']['id']); ?></p>


                                <label><?php echo text_translate("Your Question:", $course['language']['id']); ?></label>
                                <textarea class="form-control" name="description" type="text"
                                          required="required"></textarea>

                                <input class="btn btn-block btn-primary"
                                       type='<?php echo text_translate("submit", $course['language']['id']); ?>'>
                                <input class="btn btn-block btn-primary" type="hidden" name="action"
                                       value="unit_comment">
                            <?php } ?>
                        </form>


                        <style type="text/css">
                            #unit_support {
                            }

                            #unit_support .form-control {
                                background: #e8ebed;
                                border: 2px solid #e8ebed;
                                -webkit-box-shadow: none;
                                box-shadow: none;
                                margin-bottom: 20px;
                                height: 42px;
                            }

                            #unit_support .btn {
                                background: #888;
                                height: 42px;
                                border: none;
                            }

                            #unit_support .btn:hover {
                                background-color: #777;
                            }

                            #unit_support .form-control:focus {
                                background: #fff;
                            }

                            #unit_support textarea {
                                background: #fff;
                                height: 150px !important;
                            }
                        </style>
                    <?php } ?>

                </div>
            </div>
        </div>
    </div>
</div>


<?php if (is_user_logged_in()) { ?>
    <div id="side_notes" class="<?php echo $course['language_direction']; ?>"
         dir="<?php echo $course['language_direction']; ?>">
        <div class="notes_icons"><i class="fa fa-pencil-square-o" aria-hidden="true"></i></div>
        <form method="POST" id="note_form">
            <textarea placeholder='<?php echo text_translate("Write a note..", $course['language']['id']); ?>'
                      class="note_description"></textarea>
            <input class="btn" type="submit" value="<?php echo text_translate('Save', $course['language']['id']); ?>">

            <input type="hidden" class="note_id">
            <input type="hidden" class="unit_id" value="<?php echo $unit['id']; ?>">
            <input type="hidden" class="current_unit_id" value="<?php echo $unit['id']; ?>">
        </form>
        <div class="content">
            <div class="unit_top_info" style="margin-bottom: 8px;">
                <a onclick="window.open('/online-learning/print_notes/?course=<?php echo $slugs[3] ?>', '_blank', 'location=yes,height=570,width=800,scrollbars=yes,status=yes');"
                   class="btn btn-info"><?php echo text_translate('Print', $course['language']['id']); ?></a>
                <a href="#"
                   class="btn btn-warning close_notes"><?php echo text_translate('Close Notes', $course['language']['id']); ?></a>
            </div>
            <ul id="notes_ul">
                <?php
                $unit_notes = $OL->unit_notes(array('student_id' => $_SESSION['student_id']));
                foreach ($unit_notes as $note) { ?>
                    <li class="note_<?php echo $note['id']; ?>_wrap">
                        <div class="unit_top_info">
                            <i class="fa fa-pencil edit_note" aria-hidden="true"
                               description="<?php echo $note['description']; ?>" id="<?php echo $note['id']; ?>"
                               data-unit_id="<?php echo $note['unit']['id']; ?>"></i>
                            <i class="fa fa-trash-o delete_note" aria-hidden="true" id="<?php echo $note['id']; ?>"></i>
                            <?php echo $note['description']; ?>
                        </div>

                        <div class="unit_bottom_info">
                            From: <a
                                    href="<?php echo $note['unit']['href']; ?>"><?php echo $note['unit']['title']; ?></a>
                        </div>
                    </li>
                <?php } ?>
            </ul>
        </div>
    </div>
<?php } ?>

<?php if (is_user_logged_in() && $course['show_feedback_on_each_unit'] == 1) { ?>
    <div id="side_feedback" class="<?php echo $course['language_direction']; ?>"
         dir="<?php echo $course['language_direction']; ?>">
        <div class="feedback_icons"><i class="fa fa-comment-o" aria-hidden="true"></i></div>
        <form method="POST" id="feedback_form">
            <textarea
                    placeholder='<?php echo text_translate("Want to tell us something? Please provide your feedback here.", $course['language']['id']); ?>'
                    class="feedback_description"></textarea>
            <input class="btn" type="submit" value="<?php echo text_translate('Submit', $course['language']['id']); ?>">
            <input type="hidden" class="feedback_id">
            <input type="hidden" class="unit_id" value="<?php echo $unit['id']; ?>">
            <input type="hidden" class="current_unit_id" value="<?php echo $unit['id']; ?>">
        </form>
    </div>
<?php } ?>
</div>


<?php if ($unit['mp3_file']) { ?>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/normalize/5.0.0/normalize.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/mediaelement@4.2.17/build/mediaelementplayer.min.css">
    <link rel="stylesheet"
          href="<?php echo home_url(); ?>/assets/other/mediaelement/dist/jump-forward/jump-forward.css">
    <link rel="stylesheet" href="<?php echo home_url(); ?>/assets/other/mediaelement/dist/skip-back/skip-back.css">
    <link rel="stylesheet" href="<?php echo home_url(); ?>/assets/other/mediaelement/dist/speed/speed.css">
    <link rel="stylesheet" href="<?php echo home_url(); ?>/assets/other/mediaelement/dist/chromecast/chromecast.css">
    <link rel="stylesheet" href="<?php echo home_url(); ?>/assets/other/mediaelement/dist/ads/ads.css">
    <link rel="stylesheet"
          href="<?php echo home_url(); ?>/assets/other/mediaelement/dist/context-menu/context-menu.css">
    <style type="text/css">
        .audio_wrap {
            background-color: rgba(0, 0, 0, .3);
        }

        .mejs__container {
            background-color: transparent;
        }

        .mejs__controls:not([style*='display: none']) {
            background: transparent;
            background: -webkit-linear-gradient(transparent, rgba(0, 0, 0, 0));
            background: linear-gradient(transparent, rgba(0, 0, 0, 0));
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/mediaelement@4.2.17/build/mediaelement-and-player.min.js"></script>
    <script src="<?php echo home_url(); ?>/assets/other/mediaelement/dist/jump-forward/jump-forward.js"></script>
    <script src="<?php echo home_url(); ?>/assets/other/mediaelement/dist/skip-back/skip-back.js"></script>
    <script src="<?php echo home_url(); ?>/assets/other/mediaelement/dist/markers/markers.js"></script>
    <script src="<?php echo home_url(); ?>/assets/other/mediaelement/dist/speed/speed.js"></script>
    <script src="<?php echo home_url(); ?>/assets/other/mediaelement/dist/chromecast/chromecast.js"></script>
    <script src="<?php echo home_url(); ?>/assets/other/mediaelement/dist/ads/ads.js"></script>
    <script src="<?php echo home_url(); ?>/assets/other/mediaelement/dist/ads-vast-vpaid/ads-vast-vpaid.js"></script>
    <script src="<?php echo home_url(); ?>/assets/other/mediaelement/dist/context-menu/context-menu.js"></script>
    <script id="mejs-code">

        new MediaElementPlayer('player1', {
            // This is needed to make Jump Forward to work correctly
            autoRewind: false,
            features: ['playpause', 'current', 'progress', 'duration', 'speed', 'skipback', 'jumpforward', 'tracks',
                'markers', 'volume', 'chromecast', 'ads', 'vast', 'contextmenu'],

        });
    </script>

<?php } ?>

<?php if (!empty($_POST)) {
// scroll to top on submitting a quiz response ?>
    <script type="text/javascript">
        $(document).ready(function () {
            var target = $(".unit_quiz_responses:first");

            if (target.length) {
                event.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top
                }, 0);
            }
        })
    </script>

<?php } ?>
