<?php

global $db;


if ($_POST['action'] == "register_student") {

    $users = new Users;

    $pass1 = sanitise($_POST['pass1']);// pass1
    $pass2 = sanitise($_POST['pass2']);    // pass2
    $fname = sanitise($_POST['fname']);// pass1
    $surname = sanitise($_POST['surname']);    // pass2
    $usercode = sanitise($_POST['usercode']);
    $email_add = sanitise($_POST['email_add']);

    if (check_email($email_add) == 0) {
        $msg = "ERROR: Incorrect email. Please enter valid email address.";
        $error_msg .= '<div class="required_field"><strong>* Error! </strong>- \' ' . $msg . ' \'</div>';
    }

    // check for duplicates
    $dbh = get_dbh();
    $sql = "select id FROM form_users where db119='$email_add' AND usergroup='$_SESSION[usergroup]' and (rec_archive is null or rec_archive = '')";
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    $duplicates = $stmt->rowCount();
    //check for duplicates
    if ($duplicates == 0) {

        //first name
        //if (!$fname || empty($fname)){
        //	$msg="ERROR: Your first name is missing.";
        //	$error_msg_user.='<div class="required_field"><strong>* Error! </strong>- \' '.$msg.' \'</div>';
        //	$error_msg.='<div class="required_field"><strong>* Error! </strong>- \' '.$msg.' \'</div>';
        //}

        //surname
        //if (!$surname || empty($surname)){
        //	$msg="ERROR: Your surname is missing.";
        //	$error_msg_user.='<div class="required_field"><strong>* Error! </strong>- \' '.$msg.' \'</div>';
        //	$error_msg.='<div class="required_field"><strong>* Error! </strong>- \' '.$msg.' \'</div>';
        //}

        //check passwords
        if (strcmp($pass1, $pass2) || empty($pass1)) {
            $msg = "Error - Password fields do not match or are empty";
            $error_msg_user .= '<div class="required_field"><strong>* Error! </strong>- \' ' . $msg . ' \'</div>';
            $error_msg .= '<div class="required_field"><strong>* Error! </strong>- \' ' . $msg . ' \'</div>';
            $error_msg .= "select id FROM form_users where db119='$email_add' AND usergroup='$_SESSION[usergroup]' and (rec_archive is null or rec_archive = '')" . "**" . $duplicates . "**";
            $pass_identical = 1;
        }

        //check passwords length
        if (strlen($pass2) < 8 && $pass_identical !== 1) {
            $msg = "ERROR: Password too short. Your password must be at least 8 alphanumeric characters";
            $error_msg_user .= '<div class="required_field"><strong>* Error! </strong>- \' ' . $msg . ' \'</div>';
            $error_msg .= '<div class="required_field"><strong>* Error! </strong>- \' ' . $msg . ' \'</div>';
        }

        //check spam proof code
        if (strcmp(md5(trim($usercode)), $_SESSION['ckey'])) {
            $msg = "ERROR: Invalid authentication code entered. Please enter the correct code as shown in the image";
            $error_msg_user .= '<div class="required_field"><strong>* Error! </strong>- \' ' . $msg . ' \'</div>';
            $error_msg .= '<div class="required_field"><strong>* Error! </strong>- \' ' . $msg . ' \'</div>';
        }


        //SET VALUES
        $db112 = 4; //position parent
        $db111 = $surname; //surname
        $db106 = $fname; //first name
        $db119 = $email_add; //email
        $pass2_encrypted = md5($pass2);// encrypt password
        $db222 = $pass2_encrypted; //password
        $db223 = "no"; //can delete
        $db307 = 1; //suspend acc 2 is suspended
        $db308 = $fname . $surname;
        $db1034 = $_SESSION['usergroup'];
        $randomvalue = random();

        if ($error_msg_user == "") {

            if ($_SESSION['usergroup'] == 42) {
                //Course level

                $user_info = array(
                    'school_id' => $_SESSION['usergroup'],
                    'first_name' => $fname,
                    'middle_name' => null,
                    'last_name' => $surname,
                    'email' => $email_add,
                    'type_id' => 4,
                    'password' => $pass2,
                    'institution' => null,
                    'application_stage' => null,
                    'gender' => null,
                    'level_of_entry' => null,
                    'date_of_birth' => null,
                    'source' => 'Direct',
                    'course_id' => null,
                    'country' => null,
                    'telephone' => null,
                    'internal_reference' => null,
                    'cohort' => null,
                    'cohort_intake' => null, #double check
                    'ucas' => null,
                    'application_route' => null,
                    'send_ucas_letter' => null,
                    'create_student_account' => true,
                    'custom_ulevel' => '68'
                );
                if (isset($_GET['temp_debug'])) {
                    echo "<pre>" . print_r($user_info, 1) . "</pre>";
                }


                $new_user_info = $users->create($user_info);
                if (isset($_GET['temp_debug'])) {
                    echo "<pre>" . print_r($new_user_info, 1) . "</pre>";
                }
                if ($new_user_info['student_id']) {

                    $email_args = array('to' => $email_add, 'first_name' => $fname);
                    $OL->send_student_welcome_email($email_args);
                    $done = 1;


                } else {
                    $acc_created = 0;//account created
                    // echo "new user failed";
                }
            } else {

                $filename = base_path . '/' . path_to_engine . "/models/scpt_core_students_register.php";
                //// Run the processing file //////
                if (file_exists($filename)) {
                    include($filename); // insert script
                }
                echo $msg_response;
            }
        }// end error check
        else {
            $new_act_id = $_SESSION['uid'];
        }

    } else {

        if (!$_SESSION['uid']) {
            $msg = 'Oops! It looks like you already have an account with us! Simply go to <a href="' . home_url("/login") . '">Login</a> to login or reset your password';
        }
    }


}//end of run function


// check if already logged in
if ($_SESSION['uid']) {
    echo " <p>You are already registered and currently logged in....</p>.";
    echo '<br/><a href="' . home_url("/login") . '">Login</a> <strong>Continue with application</strong> </a>';

} elseif ($done == 1) {

    //Login the user

    $user_info = array(
        'email' => $_POST['email_add'],
        'password' => $_POST['pass1'],
        'school_id' => $school_info['id'],
    );


    $login = $users->login($user_info);

    if ($login['success']) {
        if ($_POST['trial_ended']) {
            header("Location: " . home_url("/plans"));
        } else {
            header("Location: " . home_url("/dashboard"));
        }
        exit();
    }


    ?>


    <div class="w-full h-[calc(100vh-7.5rem)] flex justify-center items-center" id="register-app-success">

        <div
                class="w-1/4 flex flex-col rounded-xl bg-white shadow-xl overflow-hidden p-4">
            <?php echo terminology("<h1 class='font-bold text-2xl '>Account Created</h1>
		<p>Your account has been created successfully.</p>
		<p>You will shortly be receiving a welcome email from us containing additional information relating to the application process. If you don't receive the email within 10 mins, please check your spam box.</p>
		<p>You can now use your email address and password to</p>", $_SESSION['url'], 'task wording on online learning registration page', true); ?>
            <div class="flex flex-col mt-2">
                <a href="<?php echo home_url("/login"); ?>"
                   class="bg-white border border-brand-blue text-brand-blue p-2.5 w-full hover:bg-brand-blue hover:text-white rounded-lg font-bold">Login
                    Here</a>
            </div>
        </div>
    </div>


<?php } else { ?>
    <div class="w-full min-h-[calc(100vh-7.5rem)] flex justify-center items-center" id="register-app">
        <?php if ($_SESSION['usergroup'] == 42) { ?>
            <form method="POST" class="w-1/2 flex flex-col rounded-xl bg-white shadow-xl overflow-hidden">
                <?php if ($_GET['trial_end']) { ?>
                    <div class="flex items-center flex-col rounded-t-xl bg-brand-orange gap-3">
                        <h1 class="font-bold text-2xl text-white">Trial has ended</h1>
                        <p class="text-sm text-white">You have come to the end of the trial for this material. To gain
                            access to all material and save
                            progress as you go, please register and buy access to the Course, or visit <a
                                    href="https://<?= $_SESSION['subdomain'] ?>.heiapply.com/online-learning/plans">https://<?= $_SESSION['subdomain'] ?>
                                ..heiapply.com/online-learning/plans</a></p>
                    </div>
                <?php } else { ?>

                    <h1 class="font-bold text-2xl text-center p-4">Register</h1>
                <?php } ?>
                <div class="grid grid-cols-2 gap-2 p-4 w-full">
                    <?php if (!empty($msg)) { ?>
                        <div class="col-span-2 p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
                             role="alert">
                            <span class="font-medium">Oops!</span> <?php echo $msg ?>
                        </div>
                    <?php } elseif (!empty($error_msg)) { ?>
                        <div class="col-span-2 p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
                             role="alert">
                            <span class="font-medium">Oops!</span> <?php echo $error_msg ?>
                        </div>
                    <?php } ?>

                    <div class="col-span-1 flex flex-col gap-2 mt-2">
                        <label for="fname1">First Name (Optional)</label>
                        <input type="text"
                               class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                               name="fname" id="fname1" autofocus=""
                               value="<?php echo $_POST['fname']; ?>">
                    </div>

                    <div class="col-span-1 flex flex-col gap-2 mt-2">
                        <label for="surname1">Last Name (Optional)</label>
                        <input type="text"
                               class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                               name="surname" id="surname1"
                               value="<?php echo $_POST['surname']; ?>">
                    </div>
                    <div class="col-span-2 flex flex-col gap-2 mt-2">
                        <label for="email_add1">Email</label>
                        <input type="email"
                               class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                               name="email_add"
                               value="<?php echo $_POST['email_add']; ?>" id="email_add1"
                               required>
                    </div>
                    <div class="col-span-1 flex flex-col gap-2 mt-2">
                        <label for="password1">Password</label>
                        <input type="password"
                               class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                               name="pass1" id="password1" required>
                    </div>
                    <div class="col-span-1 flex flex-col gap-2 mt-2">
                        <label for="password2">Confirm Password</label>
                        <input type="password"
                               class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                               name="pass2" id="password2" required>
                    </div>
                    <div class="col-span-2 flex flex-col gap-2 mt-2">
                        <label for="usercode">Please validate you are a person by entering the characters shown
                            below</label>
                        <div class="flex flex-row-reverse gap-2">
                            <input name="usercode" type="text" id="usercode" data-validation="required"
                                   data-validation-error-msg="You must enter a authentication code"
                                   class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-3/4 p-2.5"
                                   placeholder="Enter authentication code">
                            <div style='background-image: url("<?= website_url ?>/reg_inc_pngimg.php")'
                                 class="bg-cover bg-center w-1/4 rounded-lg"></div>
                        </div>
                    </div>
                    <div class="col-span-2 flex justify-center items-center py-4">
                        <p class="w-1/2 text-center"><a href="<?php echo $OL->home_url("/login"); ?>">Already
                                have an
                                account?1</a></p>

                    </div>
                    <div class="form-section terms-container" style="margin-top: 10px;">
                        <input type="checkbox" class="form-check-input" id="terms" required>
                        <label for="terms">
                            I confirm that I have read, understood and agree with the <a href="#">terms and conditions</a> and <a href="#">privacy policy</a>.
                        </label>
                    </div>

                    <div class="col-span-2 flex items-center justify-center  mt-2">
                        <button type="submit"
                                class="bg-white border border-brand-blue text-brand-blue p-2.5 w-1/3 hover:bg-brand-blue hover:text-white rounded-lg font-bold">
                            Register
                        </button>
                    </div>
                    <input type="hidden" value="register_student" name="action">
                    <?php if ($_GET['trial_end']) { ?>
                        <input type="hidden" value="true" name="trial_ended">
                    <?php } ?>
                </div>
            </form>
        <?php } else {
            //get the course_level for online_learning
            $db50 = pull_field("core_courses_level", "id", "WHERE db16595 = 'online' AND (rec_archive is NULL OR rec_archive = '') ORDER BY db887 LIMIT 1");
            $db889 = pull_field("core_courses", "id", "WHERE db341 = '$db50' AND (rec_archive is NULL OR rec_archive = '') LIMIT 1");
            ?>


            <form method="POST" class="w-1/2 flex flex-col rounded-xl bg-white shadow-xl overflow-hidden">
                <?php if ($_GET['trial_end']) { ?>
                    <div class="flex items-center flex-col rounded-t-xl bg-brand-orange gap-3">
                        <h1 class="font-bold text-2xl text-white">Trial has ended</h1>
                        <p class="text-sm text-white">You have come to the end of the trial for this material. To gain
                            access to all material and save
                            progress as you go, please register and buy access to the Course, or visit <a
                                    href="https://<?= $_SESSION['subdomain'] ?>.heiapply.com/online-learning/plans">https://<?= $_SESSION['subdomain'] ?>
                                ..heiapply.com/online-learning/plans</a></p>
                    </div>
                <?php } else { ?>

                    <h1 class="font-bold text-2xl text-center p-4">Register</h1>
                <?php } ?>
                <div class="w-full flex flex-col p-4">
                    <div class="grid grid-cols-2 gap-2 p-4 w-full">
                        <?php if (!empty($msg)) { ?>
                            <div class="col-span-2 p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
                                 role="alert">
                                <span class="font-medium">Oops!</span> <?php echo $msg ?>

                            </div>
                        <?php } elseif (!empty($error_msg)) { ?>
                            <div class="col-span-2 p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400"
                                 role="alert">
                                <span class="font-medium">Oops!</span> <?php echo $error_msg ?>
                            </div>
                        <?php } ?>

                        <div class="col-span-1 flex flex-col gap-2 mt-2">
                            <label for="fname1">First Name (Optional)</label>
                            <input type="text"
                                   class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                                   id="fname1"
                                   name="db106" autofocus=""
                                   value="<?php echo $_POST['db106']; ?>">
                        </div>

                        <div class="col-span-1 flex flex-col gap-2 mt-2">
                            <label for="surname1">Last Name (Optional)</label>
                            <input type="text"
                                   class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                                   name="db111" id="surname1"
                                   value="<?php echo $_POST['surname']; ?>">
                        </div>
                        <div class="col-span-2 flex flex-col gap-2 mt-2">
                            <label for="email_add1">Email</label>
                            <input type="email"
                                   class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                                   name="db119"
                                   value="<?php echo $_POST['email_add']; ?>" id="email_add1"
                                   required>
                        </div>
                        <div class="col-span-1 flex flex-col gap-2 mt-2">
                            <label for="password1">Password</label>
                            <input type="password"
                                   class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                                   name="pass1" id="password1" required>
                        </div>
                        <div class="col-span-1 flex flex-col gap-2 mt-2">
                            <label for="password2">Confirm Password</label>
                            <input type="password"
                                   class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-full p-2.5"
                                   name="pass2" id="password2" required>
                        </div>
                        <div class="col-span-2 flex flex-col gap-2 mt-2">
                            <label for="usercode">Please validate you are a person by entering the characters shown
                                below</label>
                            <div class="flex flex-row-reverse gap-2">
                                <input name="usercode" type="text" id="usercode" data-validation="required"
                                       data-validation-error-msg="You must enter a authentication code"
                                       class="bg-transparent border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-gray-500 focus:border-gray-500 block w-3/4 p-2.5"
                                       placeholder="Enter authentication code">
                                <div style='background-image: url("<?= website_url ?>/reg_inc_pngimg.php")'
                                     class="bg-cover bg-center w-1/4 rounded-lg"></div>
                            </div>
                        </div>
                        <div class="col-span-2 flex items-center justify-center py-4">
                            <a href="<?php echo $OL->home_url("/login"); ?>" class="text-center">Already have an
                                account?</a>
                        </div>


                        <div class="col-span-2 flex mt-2 items-center justify-center">
                            <button type="submit"
                                    class="bg-white border border-brand-blue text-brand-blue p-2.5 w-1/3 hover:bg-brand-blue hover:text-white rounded-lg font-bold">
                                Register
                            </button>
                        </div>
                        <input type="hidden" value="register_student" name="action">
                        <input name="option4" type="hidden" value="4">
                        <input class="form-control" value="133" type="hidden" name="db50" id="db50">
                        <input class="form-control" value="1449" type="hidden" name="db889" id="db889">
                        <input type="hidden" value="1" name="process">
                        <?php if ($_GET['trial_end']) { ?>
                            <input type="hidden" value="true" name="trial_ended">
                        <?php } ?>
                    </div>
            </form>
        <?php } ?>
    </div>
<?php } ?>
