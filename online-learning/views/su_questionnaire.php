<?php
global $OL;
/*function get_responses_list($form,$answers){
    $responses_list = array();
    if(count($answers)){
        foreach ($form['fields'] as $field) {

            $weight = 0;
            if($field['answer']==$answers[$field['db_field_name']]){
                $correct_response = true;
                $correct_response_count++;
            }else{
                $correct_response = false;
            }

            if(in_array($field['type'], array('radio_fromlist','dropdown'))){
                $oi=0;
                error_log("ANITA ANSWER".$answers[$field['db_field_name']]);
                foreach ($field['options'] as $option){
                    error_log("ANITA OPTION".$option['title']);
                    if($option['title']==$answers[$field['db_field_name']]){
                        $weight = $option['weighting'];
                        error_log("ANITA WEIGHT".$option['weighting']);
                    }
                    $oi++;
                }
            }elseif($field['type']=="radio_yes_no"){
                if("yes"==$answers[$field['db_field_name']]){ $weight = $field['weighting'][0]; }
                if("no"==$answers[$field['db_field_name']]){ $weight = $field['weighting'][1]; }
            }else{
                error_log("ANITA IN HERE");
                if($answers[$field['db_field_name']]){
                    $weight = $field['weighting'][0];
                }
            }

            $responses_list[] = array(
                'answer_id'=> $answers['id'],
                'question'=> $field['title'],
                'type'=> $field['type'],
                'weight'=> $weight,
                'response'=> $answers[$field['db_field_name']],
            );
        }
    }

    return $responses_list;
}*/
//error_log("ANITA PRE 0.0 Questionnaire");
$fields = new Fields;
$html = '';
//error_log("ANITA PRE 0.1 Questionnaire");
//Course Info
//$OL = new OnlineLearning;

$course_args = array('id' => $su_course_id);
if (empty($OL)) {
    //$_GET['debug_mode']="yes";
    $OL = new \OnlineLearning();
}
$course = $OL->get_course_info_for_questionnaires($course_args);

/*if($slugs[2]=="pre_questions"){
    $form_id_type = "pre_questions_form_id";
}else{
    $form_id_type = "post_questions_form_id";
}*/


$form_templates = new FormTemplates;

dev_debug("Form_id_type $form_id_type ID $course[$form_id_type]");
$page_args = array("id" => $course[$form_id_type], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
$form = $form_templates->get($page_args);
$answers = $form_templates->answers(array('form_name' => $form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));

//Max score
if ($answers['id']) {
    $latest_max = 0;
    if (count($form['fields'])) {
        foreach ($form['fields'] as $field) {
            $latest_max = $latest_max + max($field['weighting']);
        }
    }
    $responses = $OL->get_responses_list($form, $answers);
    //Total score
    $latest_total_score = 0;
    if (count($responses)) {
        foreach ($responses as $ans) {

            $latest_total_score = $latest_total_score + $ans['weight'];
        }
    }
}


if ($answers['id'] && $form_id_type == "pre_questions_form_id") {

    $html .= '<div class="page_text">';
    $html .= '<div class="pre_post_questions">
			<h3>' . text_translate('Questionnaire', $course['language']['id']) . '</h3>';

    $html .= '<div class="alert alert-info">' . text_translate('You completed this pre questionnaire on ', $course['language']['id']) . '  ' . date('d/m/Y', strtotime($answers['date'])) . '.</div>';
    $html .= '</div></div>';


} else if ($answers['id'] && $form_id_type == "post_questions_form_id") {

    $html .= '<div class="page_text">';
    $html .= '<div class="pre_post_questions">
			<h3>' . text_translate('Questionnaire ', $course['language']['id']) . '</h3>';

    $html .= '<div class="alert alert-info">' . text_translate(' Thank you for submitting this post questionnaire on  ', $course['language']['id']) . '' . date('d/m/Y', strtotime($answers['date'])) . '.</div>';
    $html .= '</div></div>';

    //show them some feedback
    $questionnaire_type = $course['questionnaire_type'];

    if (isset($questionnaire_type) && $questionnaire_type != '') {
        /*if ($questionnaire_type == "1" || $questionnaire_type == "4" || $questionnaire_type == "5") {
            //pre and post score questionnaire
            //1. add all the pre scores
            //2. Add all the post scores
            //3. Post - Pre = score

            //1. Pre answers
            $page_args = array("id" => $course['pre_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
            $pre_form = $form_templates->get($page_args);
            $pre_answers = $form_templates->answers(array('form_name' => $pre_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
            $pre_total_score = 0;
            if ($pre_answers['id']) {
                $responses = $OL->get_responses_list($pre_form, $pre_answers);

                //Total score
                if (count($responses)) {
                    foreach ($responses as $ans) {
                        $pre_total_score += $ans['weight'];
                    }
                }
            }

            //2. Post answers
            $page_args = array("id" => $course['post_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
            $post_form = $form_templates->get($page_args);
            $post_answers = $form_templates->answers(array('form_name' => $post_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
            $post_total_score = 0;
            if ($post_answers['id']) {
                $responses = $OL->get_responses_list($post_form, $post_answers);
                //Total score
                if (count($responses)) {
                    foreach ($responses as $ans) {
                        $post_total_score += $ans['weight'];
                    }
                }
            }
            //3. Post - Pre = score
            $total_score = $post_total_score - $pre_total_score;
            dev_debug("Questionnaire type  $questionnaire_type ");
            if ($questionnaire_type == "1") {
                //4. Show the result
                if ($total_score > 5) {
                    $score_text = $course['score_1_text'];
                } else if ($total_score >= 0 && $total_score <= 5) {
                    $score_text = $course['score_2_text'];
                } else if ($total_score < 0 && $total_score > -5) {
                    $score_text = $course['score_3_text'];
                } else {
                    $score_text = $course['score_4_text'];
                }
                $chart = text_translate('Confidence', $course['language']['id']);
            } elseif ($questionnaire_type == "4") {
                //4. Show the result
                if ($total_score < 0) {
                    $score_text = $course['score_1_text'];
                } else if ($total_score == 0) {
                    $score_text = $course['score_2_text'];
                } else if ($total_score > 0) {
                    $score_text = $course['score_3_text'];
                }
                $chart = text_translate('Score', $course['language']['id']);
            } elseif ($questionnaire_type == "6") {
                //4. Show the result
                if ($total_score <= -5 & $post_total_score <= 6) {
                    $score_text = $course['score_1_text'];
                } else if ($total_score <= -5) {
                    $score_text = $course['score_2_text'];
                } else if ($total_score <= -4 || $total_score <= 4) {
                    $score_text = $course['score_3_text'];
                } else if ($total_score >= 5) {
                    $score_text = $course['score_3_text'];
                }
                $chart = text_translate('Score', $course['language']['id']);
            } else {
                //4. Show the result
                if ($total_score > 6) {
                    $score_text = $course['score_1_text'];
                } else if ($total_score >= 1 && $total_score <= 6) {
                    $score_text = $course['score_2_text'];
                } else if ($total_score == 0 && $total_score == -1) {
                    $score_text = $course['score_3_text'];;
                } else if ($total_score <= -1 && $total_score >= -6) {
                    $score_text = $course['score_4_text'];;
                } else {
                    $score_text = $course['score_5_text'];;
                }
                $chart = text_translate('Score', $course['language']['id']);
            }
            $html .= '<div class="page_text">
                <table class="table w-full mt-3">
                <thead>
                <tr>
                
                    <td></td>
                    <td></td>
                    <td class="font-bold">' . text_translate('Pre', $course['language']['id']) . '</td>
                    <td class="font-bold">' . text_translate('Post', $course['language']['id']) . '</td>
                    <td class="font-bold">' . text_translate('Diff', $course['language']['id']) . '</td>
                    <td></td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td class="col-sm-2 col-centered"></td>
                    <td class="col-sm-2 col-centered font-bold">' . $chart . '</td>
                    <td class="col-sm-2 col-centered">' . $pre_total_score . '</td>
                    <td class="col-sm-2 col-centered">' . $post_total_score . '</td>
                    <td class="col-sm-2 col-centered">' . $total_score . '</td>
                    <td class="col-sm-2 col-centered"></td>
                </tr>
                </tbody>
                </table>
                </div>';
            $html .= '
            <div class="page_text">&nbsp;</div>';
            $html .= '<div class="page_text">
                <div class="row row-centered">
    
                <div class="col-sm-12">
            
                <div class="alert alert-success">' . $score_text . '</div>';

            $html .= '</div>
                </div>
                </div>';

        }
        elseif ($questionnaire_type == "2") {
            //questionnaire is conflict/closeness questionnaire

            //pre and post score questionnaire
            //1. Add all the pre conflict and pre closeness scores
            //2. Add all the post conflict and post closeness scores
            //3  conflict score = post conflict - pre conflict
            //4 closeness score post closeness - pre closeness


            //1. Pre answers
            $closeness_questions = explode(',', $course['closeness_questions']);
            $conflict_questions = explode(',', $course['conflict_questions']);
            $page_args = array("id" => $course['pre_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
            $pre_form = $form_templates->get($page_args);
            $pre_answers = $form_templates->answers(array('form_name' => $pre_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
            $pre_closeness_score = 0;
            $pre_conflict_score = 0;
            if ($pre_answers['id']) {
                $responses = $OL->get_responses_list($pre_form, $pre_answers);
                //Total score
                if (count($responses)) {

                    foreach ($responses as $ans) {

                        if (in_array($ans['question'], $closeness_questions)) {
                            $pre_closeness_score += $ans['weight'];
                        }
                        if (in_array($ans['question'], $conflict_questions)) {
                            $pre_conflict_score += $ans['weight'];
                        }
                    }
                }
            }

            //2. Post answers
            $page_args = array("id" => $course['post_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
            $post_form = $form_templates->get($page_args);
            $post_answers = $form_templates->answers(array('form_name' => $post_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
            $post_closeness_score = 0;
            $post_conflict_score = 0;

            if (!empty($post_answers['id'])) {
                $responses = $OL->get_responses_list($post_form, $post_answers);
                //Total score
                if (count($responses) > 0) {
                    foreach ($responses as $ans) {
                        if (in_array($ans['question'], $closeness_questions)) {
                            $post_closeness_score += $ans['weight'];
                        }
                        if (in_array($ans['question'], $conflict_questions)) {
                            $post_conflict_score += $ans['weight'];
                        }
                    }
                }
            }
            //3. Post - Pre = score
            $total_conflict_score = $post_conflict_score - $pre_conflict_score;
            $total_closeness_score = $post_closeness_score - $pre_closeness_score;

            //4. Show the result
            if ($total_conflict_score < 0) {
                $conflict_score_text = $course['score_1_text'];
            } else if ($total_conflict_score == 0) {
                $conflict_score_text = $course['score_2_text'];
            } else if ($total_conflict_score == 1) {
                $conflict_score_text = $course['score_3_text'];;
            } else {
                $conflict_score_text = $course['score_4_text'];;
            }
            //4. Show the result
            if ($total_closeness_score > 0) {
                $closeness_score_text = $course['score_5_text'];
            } else if ($total_closeness_score == 0) {
                $closeness_score_text = $course['score_6_text'];
            } else if ($total_closeness_score == -1) {
                $closeness_score_text = $course['score_7_text'];;
            } else {
                $closeness_score_text = $course['score_8_text'];;
            }


            $html .= '
				<div class="page_text">
				<table class="table w-full mt-3">
                <thead>
                <tr>
                
                    <td></td>
                    <td></td>
                    <td class="font-bold">' . text_translate('Prepp', $course['language']['id']) . '</td>
                    <td class="font-bold">' . text_translate('Postpp', $course['language']['id']) . '</td>
                    <td class="font-bold">' . text_translate('Diffpp', $course['language']['id']) . '</td>
                    <td></td>
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td class="col-sm-2 col-centered"></td>
                    <td class="col-sm-2 col-centered font-bold">Closeness</td>
                    <td class="col-sm-2 col-centered">' . $pre_closeness_score . '</td>
                    <td class="col-sm-2 col-centered">' . $post_closeness_score . '</td>
                    <td class="col-sm-2 col-centered">' . $total_closeness_score . '</td>
                    <td class="col-sm-2 col-centered"></td>
                </tr>
                <tr>
                    <td class="col-sm-2 col-centered"></td>
                    <td class="col-sm-2 col-centered font-bold">Conflict</td>
                    <td class="col-sm-2 col-centered">' . $pre_conflict_score . '</td>
                    <td class="col-sm-2 col-centered">' . $post_conflict_score . '</td>
                    <td class="col-sm-2 col-centered">' . $total_conflict_score . '</td>
                    <td class="col-sm-2 col-centered"></td>
                </tr>
                </tbody>
                </table>
		
            	</div>';
            $html .= '
				<div class="page_text mt-5">&nbsp;</div>';
            $html .= '
				<div class="page_text">
					<div class="row row-centered">
            			<div class="col-sm-12">
           					<div class="alert alert-success">' . $closeness_score_text . '</div>
						</div>
					</div>
           		</div>';

            $html .= '
				<div class="page_text">
            		<div class="row row-centered">
            			<div class="col-sm-12">
            				<div class="alert alert-success">' . $conflict_score_text . '</div>
           
            			</div>
            		</div>
            	</div>';
        } elseif ($questionnaire_type == "6") {
            //questionnaire is conflict/closeness questionnaire

            //pre and post score questionnaire
            /*Scoring and Interpretation
           The total score represents overall distress (0 to 30), with higher scores indicating more severe
           distress or a greater number of symptoms. Two subscales are presented:

           Anxiety-Stress: Items 1, 4, 6, 7, 8, 9 (raw score range = 0 to 18)
           Depression: Items 2, 3, 5, 10 (raw score range = 0 to 12)

           Overall scores can be classified into three severity groups:

           Mild/subclinical (raw score = 6 or less, average score 0.6 or less; which is equivalent to a
           percentile of 83 or less)
           Moderate (raw score between 7 and 12, average between 0.7 and 1.2; which is equivalent to
           a percentile of between 84 and 99.8)
           Severe (raw score 13 or more, average between 1.3 and 3; which is equivalent to a percentile
           of between of 99.9 or greater)(asterix here)/


            //1. Pre answers
            $closeness_questions = explode(',', $course['closeness_questions']);
            $conflict_questions = explode(',', $course['conflict_questions']);
            $page_args = array("id" => $course['pre_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
            $pre_form = $form_templates->get($page_args);
            $pre_answers = $form_templates->answers(array('form_name' => $pre_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
            $pre_anx_score = 0;
            $pre_dep_score = 0;
            if ($pre_answers['id']) {
                $responses = $OL->get_responses_list($pre_form, $pre_answers);
                //Total score
                if (count($responses)) {

                    foreach ($responses as $ans) {

                        if (in_array($ans['question'], $closeness_questions)) {
                            $pre_anx_score += $ans['weight'];
                        }
                        if (in_array($ans['question'], $conflict_questions)) {
                            $pre_dep_score += $ans['weight'];
                        }
                    }
                }
            }

            //2. Post answers
            $page_args = array("id" => $course['post_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
            $post_form = $form_templates->get($page_args);
            $post_answers = $form_templates->answers(array('form_name' => $post_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
            $post_anx_score = 0;
            $post_dep_score = 0;

            if (!empty($post_answers['id'])) {
                $responses = $OL->get_responses_list($post_form, $post_answers);
                //Total score
                if (count($responses) > 0) {
                    foreach ($responses as $ans) {
                        if (in_array($ans['question'], $closeness_questions)) {
                            $post_anx_score += $ans['weight'];
                        }
                        if (in_array($ans['question'], $conflict_questions)) {
                            $post_dep_score += $ans['weight'];
                        }
                    }
                }
            }
            //3. Post - Pre = score
            $total_pre_score = $pre_anx_score + $pre_dep_score;
            $total_post_score = $post_anx_score + $post_dep_score;
            $total_score_difference = $total_post_score - $total_pre_score;

            //4. Show the result
            if ($total_post_score <= 6 && $total_score_difference <= -5) {
                $score_text = $course['score_1_text'];
            } else if ($total_score_difference <= -5) {
                $score_text = $course['score_2_text'];
            } else if ($total_score_difference >= 5) {
                $score_text = $course['score_4_text'];;
            } else {
                $score_text = $course['score_3_text'];;
            }

            $html .= '
				<div class="page_text">
					<div class="row row-centered">
            			<div class="col-sm-12">
           					<div class="alert alert-success">' . $score_text . '</div>
						</div>
					</div>
           		</div>';


        } else {*/
        if ($questionnaire_type == "3") {
            //questionnaire type = 3 just print out the results
            $page_args = array("id" => $course['post_questions_form_id'], "system_abbrv" => "inst", "school_id" => $_SESSION['usergroup']);
            $post_form = $form_templates->get($page_args);
            $post_answers = $form_templates->answers(array('form_name' => $post_form['table_name'], 'rel_id' => $_SESSION['student_id'], 'single_answer' => true));
            dev_debug("pre form");
            dev_debug(print_r($post_form, 1));
            dev_debug("pre_answers");
            dev_debug(print_r($post_answers, 1));


            $html .= '<div class="page_text">
                <div class="row row-centered">';
            foreach ($post_form['fields'] as $fields) {
                $html .= '
                            <div class="col-sm-12">
                                <div ><strong>' . text_translate('Question', $course['language']['id']) . ': </strong>' . $fields['title'] . '</div>
                                <div ><strong>' . text_translate('Your answer', $course['language']['id']) . ': </strong>' . $post_answers[$fields['db_field_name']] . '</div>
                            </div>';

            }
            $html .= '</div></div>';
        }


    }


} else {

    if (count($form['fields']) > 0) {
        $html .= '<div class="page_text">';

        if (!$answers['id']) {
            if (!empty($course['text_before_questionnaire'])) {
                $html .= $course['text_before_questionnaire'];
            }
            $html .= '<div class="pre_post_questions">
					<h3>' . text_translate('Questionnaire ', $course['language']['id']) . '</h3>';

            $form_args = array(
                "form_id" => $form['id'],
                "submit_btn_text" => text_translate("Save Questionnaire", $course['language']['id']),
                "system_abbrv" => 'inst',
                "school_id" => $_SESSION['usergroup'],
                "submit_btn_class" => 'btn-default',
                "rel_id" => $_SESSION['student_id'],
                "question_size" => '7',
                "answer_size" => '5',
            );
            dev_debug("GET HTML for $form[id]");
            $html .= $form_templates->return_form_html($form_args);

            $html .= '
</div>

<style type="text/css">
	.pre_post_questions{ max-width: 800px; margin: 0 auto; padding: 30px; box-shadow: 0 2px 0 rgba(0,0,0,0.06);  background-color: #fff; border-radius: 5px; margin-bottom: 50px; }
	.pre_post_questions h3{ color: #333;  text-align: center; margin-bottom: 50px; }

	.completed_pre_post_questions{ text-align: center; box-shadow: 0 2px 0 rgba(0,0,0,0.06); width: 600px; margin: 0 auto; padding: 30px;  background-color: #fff; border-radius: 5px; margin-bottom: 50px; }
	.completed_pre_post_questions h3{ font-size: 22px; color: #333; padding-bottom: 20px; }
	.completed_pre_post_questions .fa-check-circle-o{ font-size: 94px; color: #5cb85c; margin-bottom: 0px; }
</style>';
        }
    } else {
        $html .= "Field count is 0";
    }
}



