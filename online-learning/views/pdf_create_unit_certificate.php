<?php
include("../../engine/admin/inc/lib.inc.php");
// Certificate class
include_once 'pdf_certificates.inc.php';

if (!empty($_GET['uid'])) {
	$form_user_id=decode($_GET['uid'],'unsalted');
}else{
	$form_user_id=$_SESSION['uid'];
}

// Grab the certificate from the parameter
$certificateID = $_GET['certificate'];

// #### PREVIEW - Has a preview been requested? Is the user logged in and is permitted to preview.
if ('preview' == $certificateID)
{
		// Generate certificate
		$cert = new Online_Learning_Certificate();
		$cert->generatePDF('Test Student', 'This is an example course...', '', '', false, 'browser');
		die();
}

// No, appears to be a proper certificate
else 
{
	//Check database for the certificate by the ID
	$block_name = pull_field ("ols_course_units", "db66585", "WHERE username_id ='$certificateID'");
	//$course_name = pull_field ("ols_online_courses", "db21909", "WHERE username_id ='$certificateID'");
	//$certificateDetails = WPCW_certificate_getCertificateDetails_byAccessKey($certificateID);

	//$courseDetails 	= WPCW_courses_getCourseDetails($certificateDetails->cert_course_id);
	//$userInfo 		= get_userdata($certificateDetails->cert_user_id);
	
	// Not a valid course or user data
	//if (!$certificateDetails || !$userInfo) {
	//	WPCW_certificate_notFound();
	//}
	
	// Generate certificate to download
	$cert = new Online_Learning_Certificate();
	$user_name = pull_field("form_users", "CONCAT(db106, ' ', db111)","WHERE id = '$form_user_id'");
	$cert->generatePDF($user_name, $block_name, date('F dS, Y'), $logo_url, $organisation_name, 'download');
	die();
}



/**
 * Show a generic error, details not found.
 */
function WPCW_certificate_notFound()
{
	_e('No certificate was found.', 'wp_courseware');
	die();
}

die();

?>