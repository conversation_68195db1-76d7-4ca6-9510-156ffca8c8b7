
<?php

if (!function_exists('sanitize_input_recursive')) {
    function sanitize_input_recursive($input) {
        if (is_array($input)) {
            foreach ($input as $key => $value) {
                $input[$key] = sanitize_input_recursive($value);
            }
        } elseif (is_string($input)) {
            // Trim, strip slashes, convert HTML entities
            $input = trim($input);
            $input = stripslashes($input);
            $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
            // Remove basic SQL keywords
            $input = preg_replace('/\b(select|insert|update|delete|drop|union|sleep|benchmark|--|#)\b/i', '', $input);
        }
        return $input;
    }
}


$_POST = sanitize_input_recursive($_POST);
$_GET = sanitize_input_recursive($_GET);
$_REQUEST = sanitize_input_recursive($_REQUEST);

include_once("../engine/admin/inc/lib.inc.php");

if (!function_exists('get_id_by_slug')) {
    function get_id_by_slug($slug, $type) {
        $dbh = get_dbh();

        // Map type to table and slug field
        $map = [
            'course' => ['table' => 'ols_online_courses', 'slug_field' => 'db361083'],
            'module' => ['table' => 'ols_course_modules', 'slug_field' => 'db360683'],
            'unit'   => ['table' => 'ols_course_units',   'slug_field' => 'db361093'],
        ];

        // Validate type
        if (!isset($map[$type])) {
            throw new InvalidArgumentException("Invalid type: $type");
        }

        $table = $map[$type]['table'];
        $slug_field = $map[$type]['slug_field'];

        // Prepare and run the query
        $sql = "SELECT id FROM $table WHERE $slug_field = :slug AND (rec_archive IS NULL OR rec_archive = '') LIMIT 1";
        $stmt = $dbh->prepare($sql);
        $stmt->execute([':slug' => $slug]);

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row ? $row['id'] : null;
    }
}
$useragent = $_SERVER['HTTP_USER_AGENT'];
$mobile = 0;
$is_resetting_password = strpos($_SERVER['REQUEST_URI'], 'online-learning/forgot-password') || strpos($_SERVER['REQUEST_URI'], 'online-learning/reset-password');
$ol_rl="online-learning";

if (!empty($_GET['sessions_debug'])) {
   echo "<pre>".print_r($_SESSION,1)."</pre>";exit();
}
if (!function_exists('getRequestProtocol')) {
    function getRequestProtocol()
    {
        if (!empty($_SERVER['HTTP_X_FORWARDED_PROTO']))
            return $_SERVER['HTTP_X_FORWARDED_PROTO'];
        else
            return !empty($_SERVER['HTTPS']) ? "https" : "http";
    }
}
if (preg_match('/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i', $useragent) || preg_match('/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i', substr($useragent, 0, 4))) {
    $mobile = 1;
}

if (!function_exists('redirectLink')) {
    function redirectLink($link) {
        // Get the current URL
        $currentUrl = $_SERVER['REQUEST_URI']; // This retrieves the path from the URL

        // Check if the current URL contains '/online-learning-dev'
        if (strpos($currentUrl, '/online-learning-dev') !== false) {
            // Replace '/online-learning' with '/online-learning-dev' in the link
            $link = str_replace('/online-learning', '/online-learning-dev', $link);
        }

        // Return the updated or original link
        return $link;
    }
}

/*------------------------------
  // Engine URL
  ---------------------------------*/
if (!function_exists('engine_url')) {
    function engine_url($path = "")
    {

        if ($path) {
            $first_character = substr($path, 0, 1);

            if ($first_character == "/") {
                $path = ltrim($path, '/');
            }
        }

        $domain = $_SERVER['HTTP_HOST'];
        if (strpos($domain, 'heiapplylocal.co.uk') !== false) {
            return 'http://' . $domain . '/engine/' . $path;
        } else {
            return 'https://' . $domain . '/engine/' . $path;
        }
        return getRequestProtocol() . '://' . $domain . '/engine/' . $path;
    }
}
/** ===================================
 * Home URL
 * ====================================    */
function home_url($path = "")
{
    global $ol_rl;
    if ($path) {
        $first_character = substr($path, 0, 1);

        if ($first_character == "/") {
            $path = ltrim($path, '/');
        }
    }

    $domain = $_SERVER['HTTP_HOST'];
    $http = (string)$domain == 'inourplace.heiapplylocal.co.uk' ? 'http' : 'https';
    return $http . '://' . $domain . ($ol_rl!=''?"/$ol_rl/":'/online-learning/') . $path;
}

/** ===================================
 * Home URL
 * ====================================    */
function application_url($path = "")
{
    if ($path) {
        $first_character = substr($path, 0, 1);

        if ($first_character == "/") {
            $path = ltrim($path, '/');
        }
    }

    $domain = $_SERVER['HTTP_HOST'];
    $http = (string)$domain == 'inourplace.heiapplylocal.co.uk' ? 'http' : 'https';
    return $http . '://' . $domain . '/application/' . $path;
}

/** ===================================
 * Current URL
 * ====================================    */
function current_url()
{
    $url = getRequestProtocol() . "://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]";
    return $url;
}


function is_user_logged_in()
{
    return $_SESSION['uid'];
}

/** ===================================
 * Get the header
 * ====================================    */
// include("../web_header_core.php");


/** ===================================
 * Get the core Classes
 * ====================================    */
require_once($_SERVER['DOCUMENT_ROOT'] . "/admin/config.php");

$OL = new OnlineLearning;
$schools = new Schools;
$students = new Students;
//$pub_key = "";
$pub_key = pull_field("lead_preferences", "db37349", "WHERE usergroup='{$_SESSION['usergroup']}' AND rec_archive is null OR rec_archive=''");
//$_GET['debug_mode']='yes';
$ols_preferences=$OL->preferences($_SESSION['usergroup']);

//echo "1234<pre>".print_r($ols_preferences,1)."</pre>";exit();
if (!empty($ols_preferences->db25676)) {
    $ols_preferences->logo=$OL->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($ols_preferences->db25676, "unsalted")));
}

if (!empty($ols_preferences->db253406)) {
    $ols_preferences->library_image=$OL->point_to_prod(engine_url("/media/dl.php?a=yes&fl=" . encode($ols_preferences->db253406, "unsalted")));
}

$ols_preferences->hide_free_access_checker=$ols_preferences->db337163;

$ols_preferences->hide_profile_editing=$ols_preferences->db359753;

$ols_preferences->hide_unlocking_courses_for_professionals=$ols_preferences->db359763;

$ols_preferences->hide_course_library_link =$ols_preferences->db359773;

$ols_preferences->website_home_url= $ols_preferences->db359783;


$ols_preferences->hide_all_courses_tab= $ols_preferences->db359793;


//ini_set('display_errors', 1);
//ini_set('display_startup_errors', 1);
//error_reporting(E_ALL ^ E_NOTICE ^ E_DEPRECATED ^ E_WARNING);

/** ===================================
 * School Info
 * =====================================*/
$school_args = array('id' => $_SESSION['usergroup']);
$school_info = $schools->get($school_args);


/** ===================================
 * System Form Submission
 * =====================================*/
if ($_POST['action'] == "submit_system_form") {
    $db = new Db_helper();
    $fields = new Fields;
    $form_templates = new FormTemplates;

    //Get the Form Info
    $form_args = array("id" => $_POST['form_id'], "system_abbrv" => 'inst');
    $form = $form_templates->get($form_args);

    //Get the posted information
    $posted_info = array();
    foreach ($form['fields'] as $question) {
        // echo "<pre>".print_r($question,1)."</pre>";
        // echo "<pre>".print_r($_POST,1)."</pre>";
        $question["answer"] = $template_answers[$question["db_field_name"]];
        if (isset($_POST['new_implementation']) && $_POST['new_implementation'] == "yes") {
            $slugs_url = $_SERVER["REQUEST_URI"];
            $slugs_url = strtok($slugs_url, '?');
            $slugs = explode("/", $slugs_url);
            if ($_POST[$question["db_field_name"]]) {
                $posted_info['response'] = $_POST[$question["db_field_name"]];
            } else {
                $posted_info['response'] = '';
            }
            $posted_info['db_field_name'] = $question["db_field_name"];
            $posted_info['question'] = $question["title"];
            $posted_info['course_id'] = $slugs[3];
            $posted_info['module_id'] = $slugs[4];
            $posted_info['unit_id'] = $slugs[5];

            $acees_plans = $OL->get_member_access_plan($_SESSION['student_id'], $slugs[3]);
            if (isset($acees_plans[0]['id'])) {
                $posted_info['access_plan_id'] = $acees_plans[0]['id'];
            }
            //echo "<pre>".print_r($_SESSION,1)."</pre>";
            //echo "<pre>".print_r($posted_info,1)."</pre>";
            define('SHOW_SQL_ERROR', 1);
            if (isset($_GET['temp_debug'])) {
                echo "<pre> inserting into inst responses</pre>";
            }
        } else {
            $db->system_table_insert_or_update('inst_responses', $posted_info);
            if (!is_array($_POST[$question["db_field_name"]])) {
                $posted_info[$question["db_field_name"]] = $_POST[$question["db_field_name"]];
            } else {
                $posted_info[$question["db_field_name"]] = implode("| ", $_POST[$question["db_field_name"]]);
            }
        }

    }


    if ($_POST['id']) {
        $posted_info['id'] = $_POST['id'];
    }
    if ($_POST['rel_id']) {
        $posted_info['rel_id'] = $_POST['rel_id'];
    }
    //Insert or update record
    if (isset($_GET['temp_debug'])) {
        echo "<pre>" . print_r($form['table_name'], 1) . "</pre>";
    }
    if (isset($_POST['new_implementation']) && $_POST['new_implementation'] == "yes") {
        //exit();
    } else {
        if (isset($_GET['temp_debug'])) {
            echo "<pre> inserting into " . $form['table_name'] . "</pre>";
        }
        $db->system_table_insert_or_update($form['table_name'], $posted_info);
    }

}
$header_google = true;
/** ===================================
 * Define the view file
 * =====================================*/
$slugs_url = $_SERVER["REQUEST_URI"];
$slugs_url = strtok($slugs_url, '?');
$slugs = explode("/", $slugs_url);
$path = $slugs[2];
switch ($path) {
    case "dashboard":
        $view_file = "./accessibility/account/dashboard_new.php";
        break;
    case "dashboard_new":
        $view_file = "./accessibility/account/dashboard_new.php";
        break;
    case "home":
    case "landing":
    case "courses":
        $view_file = "./accessibility/course_library.php";
        break;
    case "free-access-checker":
        $view_file = "./accessibility/free_access_checker.php";
        break;
    case "buy-course":
        $view_file = "./accessibility/account/pricing.php";
        break;
    case "faqs":
        $view_file = "./accessibility/account/faqs.php";
        break;
    case "no-access":
        include("accessibility_header.php");
        $view_file = "accessibility_views/no_access.php";
        break;
    case "course-old":
        if ($slugs[4]) {
            $view_file = "./accessibility/unit.php";
        } else {
            $view_file = "./accessibility/course_details.php";
        }
        break;

     case "course":
        if ($slugs[4]) {
            $og_slug_3=$slugs[3];
            $og_slug_4=$slugs[4];
            $og_slug_5=$slugs[5];
            $slugs[3]=get_id_by_slug(urldecode($slugs[3]),'course');
            $slugs[4]=get_id_by_slug(urldecode($slugs[4]),'module');
            $slugs[5]=get_id_by_slug(urldecode($slugs[5]),'unit');
            if (empty($slugs[3])) {
                $slugs[3]=$og_slug_3;
            }
            if (empty($slugs[4])) {
                $slugs[4]=$og_slug_4;
            }
            if (empty($slugs[5])) {
                $slugs[5]=$og_slug_5;
            }
            $view_file = "./accessibility/unit.php";
        } else {
            $og_slug_3=$slugs[3];
             $slugs[3]=get_id_by_slug(urldecode($slugs[3]),'course');
            if (empty($slugs[3])) {
                $slugs[3]=$og_slug_3;
            }
           
            $view_file = "./accessibility/course_details.php";
        }
        break;
    case "support":
        $protected_path = true;
        if ($slugs[3]) {
            if ($slugs[3] == "new") {
                $view_file = "./accessibility/account/support_form.php";
            } else {
                $view_file = "./accessibility/account/support_ticket.php";
            }
        } else {
            $view_file = "./accessibility/account/support_desk.php";
        }
        break;
    case "post_questions_results":
        include("accessibility_header.php");
        $protected_path = true;
        $view_file = "accessibility_views/pre_post_questions_results.php";
        break;
    case "questionnaire":
        include("accessibility_header.php");
        $protected_path = true;
        $view_file = "accessibility_views/questionnaire.php";
        break;
    case "profile":
        $protected_path = true;
        $view_file = "./accessibility/account/profile.php";
        break;
    case "change-password-v4":
        // include("accessibility_header.php");
        $protected_path = true;
        $view_file = "./accessibility/account/change_password.php";
        break;
    case "billing-history-v4":
        $protected_path = true;
        $view_file = "./accessibility/account/billing.php";
        break;
    case "access_plan_details-v4":
        //include("accessibility_header.php");
        $protected_path = true;
        $view_file = "./accessibility/account/access_plan_details.php";
        break;
    case "my-access-plans-v4":
        $protected_path = true;
        $view_file = "./accessibility/account/access_plans.php";
        break;
    case "register":
        $view_file = "./accessibility/auth/register.php";
        break;
    case "register-success":
        $view_file = "./accessibility/auth/register-success-view.php";
        break;
    case "register_vue":
        $view_file = "./accessibility/auth/register_vue.php";;
        break;
    case "register_new":
    case "register_new_new": 
        $view_file = "./accessibility/auth/register_new_new.php";
        break;
    case "login":
        $view_file = "./accessibility/auth/login.php";
        break;
    case "logout":
        session_start();
        session_destroy();
        header("Location:" . home_url("/login/"));
        exit();
        break;
    case "forgot-password":
        $view_file = "./accessibility/auth/forgot_password.php";
        break;
    case "reset-password":
        //include("accessibility_header.php");
        $view_file = "./accessibility/auth/reset-password.php";
        break;
    case "print_notes":
        //include("accessibility_header.php");
        $view_file = "accessibility_views/print_notes.php";
        break;

    case "javascript-required":
        //include("accessibility_header.php");
        $view_file = "accessibility_views/javascript_required.php";
        break;

        
    case "professional":


        if (is_user_logged_in()) {
            include("accessibility_header.php");
            $view_file = "accessibility_views/dashboard.php";
        } else {
            $_SESSION['ulevel'] = 19;
            include("accessibility_header.php");
            $view_file = "accessibility_views/courses.php";
        }

        break;
    case "professional_registration":
        $view_file = "./accessibility/auth/professional_registration.php";
    break;
    default:

        if (!$path) {
            $_SESSION['ulevel'] = 4;
            $view_file = "./accessibility/course_library.php";;
        } else {
            include("accessibility_header.php");
            $view_file = "accessibility_views/404.php"; 
        }
}
dev_debug("VIEW File $view_file");
/** ===================================
 * Check for user login info
 * ====================================*/
// if (is_user_logged_in()) {
//     if (!$_SESSION['student_id']) {
//         $_SESSION['student_id'] = pull_field("core_students", "id", "WHERE usergroup='{$_SESSION['usergroup']}' AND rel_id = '{$_SESSION['uid']}'");
//     }
//     if (!$_SESSION['student_id']) {
//         $generic_message = "No student ID found for this user! User ID: " . $_SESSION['uid']. " You may sign out and sign in again";
//         $view_file = "./accessibility/generic_message.php";
//     } else {
//         $user_args = array(
//             'get_student_info' => true,
//             'get_last_worked_on_course' => true,
//             'remove_application_stage_sql' => true,
//             'id' => $_SESSION['student_id']
//         );
//         $_SESSION['user_info'] = $_SESSION['user_info'] ?: $students->get($user_args);
//         $user_info = $_SESSION['user_info'];
//     }
// }


/** ===================================
 * Include the view
 * ====================================    */
dev_debug("View File Exists " . file_exists($view_file));
if (file_exists($view_file)) {
    include($view_file);
} else {
    echo "View file not found. <strong>" . $view_file . "<strong>";
}


