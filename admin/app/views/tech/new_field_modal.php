<div class="modal fade" id="create_field_modal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
<div class="modal-dialog" role="document">
<div class="modal-content">
<div class="modal-header">
<button type="button" class="close" data-dismiss="modal" aria-label="Close">
<span aria-hidden="true">&times;</span>
</button>
<h4 class="modal-title" id="modalLabel">Create Field</h4>
</div>
<div class="modal-body">
<div class="row">
      <div class="col-md-12">

    

       <div class="form-group">
            <label for="label">Field Label :</label>
            <textarea class="form-control"  v-model="new_field.name"></textarea>
      </div>

      <div class="form-group">
            <label for="label">Type :</label>
           <select name="type[]" class="form-control" id="type[]" v-model="new_field.type">
                <option value="not specified">Select....</option>
                <option v-for="t in field_types" v-bind:value="t.value">{{t.title}}</option>
            </select>
      </div>

      <div class="form-group">
            <label for="label">Desciption :</label>
            <textarea class="form-control"  v-model="new_field.description"></textarea>
      </div>

      <div class="form-group">
            <label for="label">Conditions :</label>
            <textarea class="form-control"  v-model="new_field.conditional"></textarea>
      </div>

      <div class="form-group">
            <label for="label">Required :</label>
            <div class="checkbox">
              <label>
                <input v-if="new_field.required.includes('required')" checked="checkbox"  type="checkbox" name="required_96" value="required" v-on:click="setnewrequired('required')">
                <input v-if="!new_field.required.includes('required')"   type="checkbox" name="required_96" value="required" v-on:click="setnewrequired('required')">Required</label>
            </div>
            <div class="checkbox">
                  <label><input type="checkbox" v-if="new_field.required.includes('disabled')" checked="checkbox"  name="disabled_96" value="disabled" v-on:click="setnewrequired('disabled')">
                  <input type="checkbox" v-if="!new_field.required.includes('disabled')"   name="disabled_96" value="disabled" v-on:click="setnewrequired('disabled')">Disabled</label>
            </div>
            <div class="checkbox">
                  <label><input type="checkbox" v-if="new_field.required.includes('read_only')" checked="checkbox" name="read_only_96" value="read_only" v-on:click="setnewrequired('read_only')">
                  <input type="checkbox" v-if="!new_field.required.includes('read_only')" name="read_only_96" value="read_only" v-on:click="setnewrequired('read_only')">Read Only</label>
            </div>
            <div class="checkbox">
                  <label><input type="checkbox" v-if="new_field.required.includes('read_only_for_applicants')" checked="checkbox"  name="read_only_for_applicants_96" value="read_only_for_applicants" v-on:click="setnewrequired('read_only_for_applicants')">
                  <input type="checkbox" v-if="!new_field.required.includes('read_only_for_applicants')"   name="read_only_for_applicants_96" value="read_only_for_applicants" v-on:click="setnewrequired('read_only_for_applicants')">Read Only For Applicants</label>
            </div>
            <div class="checkbox">
                  <label><input type="checkbox" v-if="new_field.required.includes('disabled_when_editing')" checked="checkbox" name="disabled_when_editing_96" value="disabled_when_editing" v-on:click="setnewrequired('disabled_when_editing')">
                  <input type="checkbox" v-if="!new_field.required.includes('disabled_when_editing')"  name="disabled_when_editing_96" value="disabled_when_editing" v-on:click="setnewrequired('disabled_when_editing')">Disable on editing</label>
            </div>
      </div>


      <div class="form-group">
            <label for="label">Lock :</label>
            <select name="locked[]" class="form-control" id="locked[]" v-model="new_field.locked">
                <option value="not specified">Select....</option>
                <option v-for="t in locks" v-bind:value="t.value">{{t.title}}</option>
            </select>
      </div>

      <div class="form-group">
            <label for="label">Default Values :</label>
            <textarea class="form-control"  v-model="new_field.figures"></textarea>
      </div>


       <div class="form-group">
            <label for="label">Decision :</label>
            <textarea class="form-control"  v-model="new_field.decision"></textarea>
      </div>

       <div class="form-group">
            <label for="label">Extra Option :</label>
            <textarea class="form-control"  v-model="new_field.extra_options"></textarea>
      </div>

       <div class="form-group">
            <label for="label">Default State :</label>
            <textarea class="form-control"  v-model="new_field.default_state"></textarea>
      </div>
      


      </div>
</div>
</div>
<div class="modal-footer"><input v-if="!creating_field" class="btn btn-success" name="button2" type="submit" class="button" id="create_field_button" value="Create Field" v-on:click="create_field()">
      <input v-if="creating_field" class="btn btn-success" name="button2" type="submit" class="button" id="create_field_button" value="Create Field" disabled>
<button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
</div>
</div>
</div>
</div>