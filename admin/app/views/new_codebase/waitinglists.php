<?php
/**
 * Created by PhpStorm.
 * User: andrew
 * Date: 9/6/19
 * Time: 12:48 PM
 */
?>
<div class="inner_container">
    <?php if ($data['view']['show_breadcrumbs'] !== "no") { ?>
        <div class="breadcrumbs">
            <a href="<?php echo $this->base_url('settings'); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i>
                Settings</a>
        </div>
    <?php } ?>
    <div class="top_section">
        <div class="buttons_wrap">
        </div>
        <h1>Waiting Lists</h1>
    </div>
    <div class="stnrd_box">
        <?php
        // select the task from task templated
        //include_once("../admin/inc/lib.inc.php");

        $_token = generateCsrfToken();
        ///////////////////////////
        // PRODUCE DYNAMIC HTML FROM RESULTS
        //////////////////////////////////
        function array2table_waiting($arr, $width, $edit_link = '', $show_filters = '')
        {

            $count = count($arr);

            //if show filters is on add id
            if ($show_filters == "yes") {
                $show_filter_id = 'id="data_table"';
                dev_debug("filter show = " . $show_filter_id);
            }

            if ($count > 0) {
                reset($arr);
                $num = count(current($arr));
                echo "<div class=\"table-responsive\">";
                echo "<table id=\"data_table_reorder\" border=\"0\" cellpadding=\"5\" cellspacing=\"0\" width=\"$width\" class=\"sortable table table-striped table-condensed\">";

//footer
                echo "<tfoot><tr>";
                $col2 = 1;
                while ($col2 <= $num) {

                    // calculate if row 1
                    if ($col2 == 2 || $col2 == 3) {
                        echo "<th style=\"display:none\">";
                        echo $key . "&nbsp;";
                        echo "</th>";
                    } else {
                        echo "<th>";
                        echo $key . "&nbsp;";
                        echo "</th>";
                    }
                    $col2++;
                }
                echo "</tr></tfoot>";

                //head
                echo "<thead><tr>";

                $colhead = 1;
                foreach (current($arr) as $key => $value) {

                    // calculate if row 1
                    if ($colhead == 2 || $colhead == 3) {
                        echo "<th style=\"display:none\">";
                        echo $key . "&nbsp;";
                        echo "</th>";
                    } else {
                        echo "<th align=\"left\" class=\"borders_btm\">";
                        echo $key . "&nbsp;";
                        echo "</th>";
                    }
                    $colhead++;
                }
                echo "</tr></thead>";

                echo "<tbody>";
                while ($curr_row = current($arr)) {
                    echo "<tr>";

                    $col = 1;
                    foreach ($curr_row as $curr_field) {

                        // calculate if row 1
                        if ($col == 1) {

                            echo "<td style='cursor:move'>";
                            echo $curr_field . "&nbsp;";
                            echo "</td>";

                            //reset
                            $link = '';
                            // calculate if row 1
                        } elseif ($col == 2) {

                            echo "<td style=\"display:none\">";
                            echo $curr_field . "&nbsp;";
                            echo "</td>";

                            // capture student id
                            $student_id_capture = $curr_field;

                            //reset
                            $link = '';
                        } elseif ($col == 3) {

                            echo "<td style=\"display:none\">";
                            echo "&nbsp;";
                            echo "</td>";

                            // capture student id
                            $student_vw_capture = $curr_field;

                            //reset
                            $link = '';
                        } elseif ($col == 4) {

                            // if edit buttons are switched off then show normal
                            //if($edit_link!=='no'){
                            $link = "<a href=\"" . engine_url . "/direct/proc?pg=4&vw=$student_vw_capture&ref=$student_id_capture&dest=mkp\" target=\"_blank\">$curr_field</a>";

                            echo "<td>";
                            echo $link . "&nbsp;";
                            echo "</td>";

                            //reset
                            $link = '';
                            $student_id_capture = '';
                            $student_vw_capture = '';
                        } else {

                            echo "<td>";
                            echo $curr_field . "&nbsp;";
                            echo "</td>";
                        }
                        // calculate if row 1 end

                        next($curr_row);
                        $col++;
                    }
                    while ($col <= $num) {
                        echo "<td>&nbsp;</td>";
                        $col++;
                    }
                    echo "</tr>";
                    next($arr);
                }
                echo "</tbody>";

                echo "</table></div>";
            }

        }//end function

        // THIS FUNCTION DOES THE SELECTING
        function get_waiting_records($wdt = "95%", $sql, $edit_link = 'no', $show_filters = 'no')
        {
            $sql = session_floating($sql);
            $query = "$sql";
            ##	echo $query;
            $result = mysqli_query($dbcon, $query);
            $num = mysqli_num_rows($result);

            if ($num == "0") {
                echo '<div class="alert alert-warning"><b>Sorry,</b> it looks like no applicants have been added to the waitling list for this course</div>';
            }

            while ($row = mysqli_fetch_assoc($result)) {
                $array[] = $row;
            }

            array2table_waiting($array, $wdt, $edit_link, $show_filters); // Will output a table of 600px width
        }

        $dbh = get_dbh();
        $sql = $dbh->prepare("SELECT id, db232 AS 'course_name' FROM core_courses WHERE usergroup = ?");
        $sql->execute(array($_SESSION['usergroup']));
        $courses = $sql->fetchAll(PDO::FETCH_OBJ);

        echo "
<h5>Select a programme to view the associated waiting list</h5>
<form action='#' method='post' class='alert'>
<select name='course' class='form-control'>";

        foreach ($courses as $course) {
            echo "<option value='$course->id'>$course->course_name</option>";
        }

        echo "
</select>
<input type='hidden' id='hdn_engine_url' value='" . engine_url . "'/>
<input type='hidden' id='_token' value='" . $_token . "'/>
<input type='hidden' name='process' value='1'/>
<input type='submit' value='Select' class='btn btn-warning'/>
</form>";

        if ($_POST['process'] == 1) {
            if (!verifyCsrfToken($_POST['_token'])) {
                die("CSRF token validation failed.");
            }
            $dbh = get_dbh();

// get the query from the view entry
            $individual_shortlist_view_id = pull_field("form_create_view", "id", "WHERE usergroup='$_SESSION[usergroup]' AND db2='141' AND db3!=''");

//if there is a personal view then show it otherwise default
            if ($individual_shortlist_view_id) {
                list($create_view_view_name, $create_view_display_heading, $create_view_category, $create_view_full_view_mysql_query, $create_view_and_query, $create_view_limit_result, $create_view_only_show_results_for_logged_in_person, $create_view_default_order, $create_view_order_by, $create_view_subpage_pop_page, $create_view_subpage_parent_page, $create_view_sub_page_order, $create_view_custom_script, $create_view_show_add_button, $create_view_show_edit_button, $create_view_show_view_button, $create_view_publish, $create_view_show_copy_botton, $create_view_show_export_button, $create_view_where_over_ride) = get_create_view($individual_shortlist_view_id);

                $sql = get_floating($create_view_full_view_mysql_query);
            } else {

                $sql = "
	SELECT  
		w.db15885 AS 'Order Position',
		s.id AS 'student_id',
		s.username_id AS 'vw_id',
  		concat(s.db39, ' ', s.db40) AS 'Name',
		c.db232 AS 'Programme',
		s.db890 AS 'Cohort'
	FROM core_waitinglist w
	INNER JOIN core_courses c ON w.db16146 = c.id
	INNER JOIN core_students s ON w.rel_id = s.id
	WHERE w.db16146 = '$_POST[course]' 
	AND (s.rec_archive IS NULL OR s.rec_archive = '')
	";
            }
            //echo $sql;
            echo "<p>Simply hover over the 'Order Position' field to drag and re-arrange the order</p>";
            get_waiting_records("95%", $sql);

            ?>
            <script>
                $(document).ready(function () {
                    var engine_url = $('#hdn_engine_url').val();

//            $('#data_table_reorder tfoot th').each( function () {
//                var title = $('#data_table thead th').eq( $(this).index() ).text();
//                $(this).html( '<input type="text" class="form-control input-sm" title="start typing to activate filters" placeholder="filter '+title+'" />' );
//            } );

                    var reorder_table = $('#data_table_reorder').DataTable({
                        colReorder: true,
                        stateSave: true,
                        fixedHeader: true,
                        fixedFooter: true,
                        rowReorder: true
                    });
                    reorder_table.columns().every(function () {
                        var that = this;

                        $('input', this.footer()).on('keyup change', function () {
                            that
                                .search(this.value)
                                .draw();
                        });
                    });
                    reorder_table.on('row-reorder', function (e, diff, edit) {
//                var result = 'Reorder started on row: '+edit.triggerRow.data()[1]+'<br>';
//
//                for ( var i=0, ien=diff.length ; i<ien ; i++ ) {
//                    var rowData = table.row( diff[i].node ).data();
//
//                    result += rowData[1]+' updated to be in position '+
//                        diff[i].newData+' (was '+diff[i].oldData+')<br>';
//                }
//
//                $('#result').html( 'Event result:<br>'+result );,
                        var change_array = [];
                        for (var i = 0, ien = diff.length; i < ien; i++) {
                            var rowData = reorder_table.row(diff[i].node).data();
                            var row = {id: rowData[1], order: diff[i].newData}
                            change_array.push(row);
                        }

                        $.ajax({
                            type: 'POST',
                            dataType: 'json',
                            url: engine_url + '/ajax/task_reorder.php',
                            data: {changes: change_array},
                            success: function (data) {
                                var result = "";
                                $.each(data, function (key, value) {
                                    result += value;
                                });
                                // console.log(result);
                            }
                        });
                    });

                });
            </script>
            <?php


        }
        ?>
    </div>
</div>