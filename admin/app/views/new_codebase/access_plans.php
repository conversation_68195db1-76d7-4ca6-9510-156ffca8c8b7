<?php
require_once ('./utilities/lib_inc_duplicate.php');

/*--------------------------------
// FUNCTION TO GET_LEAD_BILLS
---------------------------------*/
//check prior to processing
//check if I am allowed to see this page
if ($_SESSION['usergroup'] == 2 || $_SESSION['usergroup'] == 0) {
    $free_domain_check = "AND rec_id='$_SESSION[uid]'";
}
?>
<div class="inner_container">
  <?php if($data['view']['show_breadcrumbs']!=="no"){ ?>
      <div class="breadcrumbs">
          <a href="<?php echo $this->base_url('settings'); ?>"><i class="fa fa-chevron-left" aria-hidden="true"></i> Settings</a>
      </div>
  <?php } ?>
    <div class="top_section">
        <div class="buttons_wrap">
        </div>
        <h1>Access Plans</h1>
    </div>
    <ul class="next-tab_list">
        <li>
            <a class="active" href="<?php echo $this->base_url("manage/access_plans"); ?>">
                All results
            </a>
        </li>
    </ul>
    <div class="stnrd_box dynamic_wrap">
        <div class="row">
            <div class="col-md-12">
                <section id="filters_wrap"></section>
              <?php if(count($data['results'])){ ?>
                  <div class="row">
                      <div class="col-sm-12 tableFixHead" style="overflow-x: scroll;">
                          <table class="table table-condensed table-striped table-bordered table-hover" style="width: 100%;">
                              <thead>
                              <tr>
                                <?php foreach($data['results'][0] as $column=>$value){
                                  echo "<th nowrap class='".$column."'>";
                                  echo "<a href='".(isset($_GET['filter']) ? "?filter=$_GET[filter]&" : "?")."order=".$column."+".((int)strpos($_GET['order'], $column) === 0 && strpos($_GET['order'], 'asc') ? 'desc': 'asc' )."'>";
                                  echo ($column !=="ID" ? ucwords(str_replace("_", " ", $column)): $column);
                                  echo "</a>";
                                  echo "</th>";
                                } ?>
                              </tr>
                              </thead>
                              <tbody class="sortable_objects">
                              <?php
                              $total = count($data['results']);
                              for($i = 0; $i < $total ; $i++){
                                $record = $data['results'][$i];
                                echo "<tr>";
                                echo "<td>
                                <a href='" . engine_url('controller.php?pg=161&rec='.$record['Manage'].'&width=850&height=600&jqmRefresh=true') ."' class='thickbox btn btn-default btn-xs'><i class='glyphicon glyphicon-pencil'></i></a>
                                <a href='" . engine_url('view_only.php?pg=161&rec='.$record['Manage'].'&width=850&height=600&jqmRefresh=true'). "' class='thickbox btn btn-default btn-xs' title='View Record' alt='View Record'>
                                        <span class='glyphicon glyphicon-file' aria-hidden='true'></span>
                                    </a>
                            </td>";
                                foreach($record as $key => $value) {
                                    if($key!='Manage') echo "<td nowrap ><a>".$value . "</a></td>";
                                }
                                echo "</tr>";
                              }
                              ?>
                              </tbody>
                          </table>
                      </div>
                  </div>


                <?php global $paginator; echo $paginator->links(); ?>
              <?php }else{ ?>

                  <div class="no_results">
                      <span class="glyphicon glyphicon-briefcase"></span>
                    <?php if($_GET['filter'] || $_GET['search'] || $_POST['search'] ){ ?>
                        <h2>No match for your criteria was found</h2>
                    <?php }else{?>
                        <h2>You haven't added access plans yet</h2>
                    <?php }?>
                  </div>
              <?php } ?>

            </div>
        </div>
    </div>

</div>



