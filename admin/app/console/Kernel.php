<?php

namespace App\console;

use App\core\console\scheduling\Schedule;
use App\core\console\Kernel as ConsoleKernel;


class Kernel extends ConsoleKernel
{
    protected $commands = [

    ];

    public function getCommands()
    {
        return $this->commands;
    }

    public function schedule(Schedule $schedule)
    {
        //10/04/2025 Switched to do immediately $schedule->command('applicants:update-stage')->everyThirtyMinutes();
        $schedule->command('applicants:fix-status')->everyThirtyMinutes();
        $schedule->command('files:delete-temp-files')->dailyAt('01:00');
        $schedule->command('system:check-health')->hourly();
        //$schedule->command('applicants:fix-stages')->everyMinute();
        $schedule->command('notifications:process-weekly-task-reminders --run=1 --past=yes')->dailyAt('02:00');
        $schedule->command('notifications:inactive-learners-reminder')->dailyAt('03:00');
        $schedule->command('notifications:process-start-course-reminder')->dailyAt('04:00');

        //$schedule->command('applicants:xml-export')->hourly();//todo:going to be jobs now
        //$schedule->command('applicants:cron_curl')->hourly();//todo:going to be jobs now
        //$schedule->command('enquiries:send-bulk-email')->everyFifteenMinutes();//todo:going to be jobs now
        //$schedule->command('reports:export-custom-report')->everyTenMinutes();
        $schedule->command('emails:send')->everyFiveMinutes();
        $schedule->command('events:notify-start')->everyMinute();
        $schedule->command('bookings:remind-booking-incomplete')->fridays()->at('05:00');
        $schedule->command('cohort:update')->yearly();
        $schedule->command('notifications:archive')->hourly();
        $schedule->command('notifications:process-reference-reminder')->weeklyOn(1, '8:00');
        $schedule->command('tasks:make_ready_active')->dailyAt('05:00');
        $schedule->command('reminders:send-schedule-course-reminders')->hourly();
        $schedule->command('alert:send-schedule-course-minimum-not-met')->dailyAt('08:00');
        $schedule->command('files:move-to-vault')->dailyAt('03:00');
        $schedule->command('system:check-slow-queries')->everyTwoMinutes();

    }

    protected function commands()
    {
        $this->load(__DIR__ . '/commands');
    }

}
