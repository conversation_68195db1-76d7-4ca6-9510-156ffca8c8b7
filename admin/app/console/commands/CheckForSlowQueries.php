<?php


namespace App\console\commands;


use App\core\support\facades\DB;
use App\enums\SlackChannelsEnum;
use App\services\SlackService;
use Carbon\Carbon;
use App\core\console\Command;

class CheckForSlowQueries extends Command
{
    protected $signature = 'system:check-slow-queries {--threshold=60}';
    protected $description = 'Checks for slow queries';


    public function handle(SlackService  $slack)
    {
        $threshold=$this->option('threshold');
        $this->info('Checking for slow queries');
        $slowQueries = DB::select("SELECT * FROM information_schema.processlist WHERE TIME >$threshold");
        if (count($slowQueries) > 0) {
            $this->info('Found ' . count($slowQueries) . ' slow queries');
            //send to slack
            $slack->sendMessage(SlackChannelsEnum::ALERTS_CHANNEL, 'Found ' . count($slowQueries) . ' slow queries');
            foreach ($slowQueries as $query) {
                //format the message to show query info and time
                $slack->sendMessage(SlackChannelsEnum::ALERTS_CHANNEL, "Query: \r\n" . $query->INFO . "\r\n has been running for " . $query->TIME . ' seconds');
                $slack->sendMessage(SlackChannelsEnum::ALERTS_CHANNEL, 'Query: ' . $query->INFO);
                $this->info($query->INFO);
            }
        } else {
            $this->info('No slow queries found');
        }

    }
}