<?php
/// DO NOT USE global $db in this class. User get_dbh() instead;
	class FilterFields{
		public function get($args=array()){
			$dbh = get_dbh();
			$params = array();
			$and_sql ="";
			$params['usergroup'] = $_SESSION['usergroup'];
			$SQL = "";
			$stmt = $dbh->prepare($SQL);
			$stmt->execute($params);
			dev_debug(__METHOD__." - ". $SQL ." : ". json_encode($params));
			$final = array();
			foreach ($stmt->fetchAll(PDO::FETCH_ASSOC) as $key => $row) {
				$final[] = $this->prepare_row($row);
			}
			if($args['one'])
				return $final[0];
			return $final;
		}
	}

?>
