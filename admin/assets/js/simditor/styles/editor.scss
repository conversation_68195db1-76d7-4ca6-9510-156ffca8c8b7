@charset "UTF-8";

$simditor-button-height: 40px;
$simditor-button-width: 46px;

.simditor {
  position: relative;
  border: 1px solid #c9d8db;

  .simditor-wrapper {
    position: relative;
    background: #ffffff;

    & > textarea {
      display: none !important;
      width: 100%;
      box-sizing: border-box;
      font-family: monaco;
      font-size: 16px;
      line-height: 1.6;
      border: none;
      padding: 22px 15px 40px;
      min-height: 300px;
      outline: none;
      background: transparent;
      resize: none;
    }

    .simditor-placeholder {
      display: none;
      position: absolute;
      left: 0;
      z-index: 0;
      padding: 22px 15px;
      font-size: 16px;
      font-family: arial, sans-serif;
      line-height: 1.5;
      color: #999999;
      background: transparent;
    }

    &.toolbar-floating {
      .simditor-toolbar {
        position: fixed;
        top: 0;
        z-index: 10;
        box-shadow: 0 0 6px rgba(0,0,0,0.1);
      }
    }

    .simditor-image-loading {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;

      .progress {
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.4);
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }

  .simditor-body {
    padding: 22px 15px 40px;
    min-height: 300px;
    outline: none;
    cursor: text;
    position: relative;
    z-index: 1;
    background: transparent;

    a.selected {
      background: #b3d4fd;
    }

    a.simditor-mention {
      cursor: pointer;
    }

    .simditor-table {
      position: relative;

      &.resizing {
        cursor: col-resize;
      }

      .simditor-resize-handle {
        position: absolute;
        left: 0;
        top: 0;
        width: 10px;
        height: 100%;
        cursor: col-resize;
      }
    }

    pre {
      /*min-height: 28px;*/
      box-sizing: border-box;
      -moz-box-sizing: border-box;
      word-wrap: break-word!important;
      white-space: pre-wrap!important;
    }

    img {
      cursor: pointer;

      &.selected {
        box-shadow: 0 0 0 4px #cccccc;
      }
    }
  }

  .simditor-paste-bin {
    position: absolute;
    width: 1px;
    height: 20px;
    font-size: 1px;
    line-height: 1px;
    overflow: hidden;
    padding: 0;
    margin: 0;
    opacity: 0;
    -webkit-user-select: text;
  }

  .simditor-toolbar {
    border-bottom: 1px solid #eeeeee;
    background: #ffffff;
    width: 100%;

    & > ul {
      margin: 0;
      padding: 0 0 0 6px;
      list-style: none;

      & > li {
        position: relative;
        display: inline-block;
        font-size: 0;

        & > span.separator {
          display: inline-block;
          background: #cfcfcf;
          width: 1px;
          height: 18px;
          margin: ($simditor-button-height - 18px) / 2 15px;
          vertical-align: middle;
        }

        & > .toolbar-item {
          display: inline-block;
          width: $simditor-button-width;
          height: $simditor-button-height;
          outline: none;
          color: #333333;
          font-size: 15px;
          line-height: $simditor-button-height;
          vertical-align: middle;
          text-align: center;
          text-decoration: none;

          span {
            opacity: 0.6;

            &.simditor-icon {
              display: inline;
              line-height: normal;
            }
          }

          &:hover span {
            opacity: 1;
          }

          &.active {
            background: #eeeeee;

            span {
              opacity: 1;
            }
          }

          &.disabled {
            cursor: default;

            span {
              opacity: 0.3;
            }
          }

          &.toolbar-item-title {
            span:before {
              content: "H";
              font-size: 19px;
              font-weight: bold;
              font-family: 'Times New Roman';
            }

            &.active-h1 span:before {
              content: 'H1';
              font-size: 18px;
            }

            &.active-h2 span:before {
              content: 'H2';
              font-size: 18px;
            }

            &.active-h3 span:before {
              content: 'H3';
              font-size: 18px;
            }
          }

          &.toolbar-item-image {
            position: relative;
            overflow: hidden;

            & > input[type=file] {
              position: absolute;
              right: 0px;
              top: 0px;
              opacity: 0;
              font-size: 100px;
              cursor: pointer;
            }
          }
        }

        &.menu-on {
          .toolbar-item {
            position: relative;
            z-index: 20;
            background: #ffffff;
            box-shadow: 0 1px 4px rgba(0,0,0,0.3);

            span {
              opacity: 1;
            }
          }

          .toolbar-menu {
            display: block;
          }
        }
      }
    }

    .toolbar-menu {
      display: none;
      position: absolute;
      top: $simditor-button-height;
      left: 0;
      z-index: 21;
      background: #ffffff;
      text-align: left;
      box-shadow: 0 0 4px rgba(0,0,0,0.3);

      &:before {
        content: '';
        display: block;
        width: $simditor-button-width;
        height: 4px;
        background: #ffffff;
        position: absolute;
        top: -3px;
        left: 0;
      }

      ul {
        min-width: 160px;
        list-style: none;
        margin: 0;
        padding: 10px 1px;

        & > li {

          .menu-item {
            display: block;
            font-size:16px;
            line-height: 2em;
            padding: 0 10px;
            text-decoration: none;
            color: #666666;

            &:hover {
              background: #f6f6f6;
            }

            &.menu-item-h1 {
              font-size: 24px;
              color: #333333;
            }

            &.menu-item-h2 {
              font-size: 22px;
              color: #333333;
            }

            &.menu-item-h3 {
              font-size: 20px;
              color: #333333;
            }

            &.menu-item-h4 {
              font-size: 18px;
              color: #333333;
            }

            &.menu-item-h5 {
              font-size: 16px;
              color: #333333;
            }
          }

          .separator {
            display: block;
            border-top: 1px solid #cccccc;
            height: 0;
            line-height: 0;
            font-size: 0;
            margin: 6px 0;
          }
        }

      }

      &.toolbar-menu-color {
        width: 96px;

        .color-list {
          height: 40px;
          margin: 10px 6px 6px 10px;
          padding: 0;

          min-width: 0;

          li {
            float: left;
            margin: 0 4px 4px 0;

            .font-color {
              display: block;
              width: 16px;
              height: 16px;
              background: #dfdfdf;
              border-radius: 2px;

              &:hover {
                opacity: 0.8;
              }

              &.font-color-default {
                background: #333333;
              }
            }

            $font-colors: #E33737 #e28b41 #c8a732 #209361 #418caf #aa8773 #999999;
            $i: 1;
            @each $color in $font-colors {
              .font-color-#{$i} {
                background: $color;
              }
              $i: $i + 1;
            }
          }
        }
      }

      &.toolbar-menu-table {
        .menu-create-table {
          background: #ffffff;
          padding: 1px;

          table {
            border: none;
            border-collapse: collapse;
            border-spacing: 0;
            table-layout: fixed;

            td {
              padding: 0;
              cursor: pointer;

              &:before {
                width: 16px;
                height: 16px;
                border: 1px solid #ffffff;
                background: #f3f3f3;
                display: block;
                content: ''
              }

              &.selected:before {
                background: #cfcfcf;
              }
            }
          }
        }

        .menu-edit-table {
          display: none;

          ul {
            li {
              white-space: nowrap;
            }
          }
        }
      }

      &.toolbar-menu-image {
        .menu-item-upload-image {
          position: relative;
          overflow: hidden;

          input[type=file] {
            position: absolute;
            right: 0px;
            top: 0px;
            opacity: 0;
            font-size: 100px;
            cursor: pointer;
          }
        }
      }

      &.toolbar-menu-alignment {
        width: 100%;
        ul {
          min-width: 100%;
        }
        .menu-item {
          text-align: center;
        }
      }
    }
  }

  .simditor-popover {
    display: none;
    padding: 5px 8px 0;
    background: #ffffff;
    box-shadow: 0 1px 4px rgba(0,0,0,0.4);
    border-radius: 2px;
    position: absolute;
    z-index: 2;

    .settings-field {
      margin: 0 0 5px 0;
      font-size: 12px;
      height: 25px;
      line-height: 25px;

      label {
        display: inline-block;
        margin: 0 5px 0 0;
      }

      input[type=text] {
        display: inline-block;
        width: 200px;
        box-sizing: border-box;
        font-size: 12px;

        &.image-size {
          width: 83px;
        }
      }

      .times {
        display: inline-block;
        width: 26px;
        font-size: 12px;
        text-align: center;
      }
    }

    &.link-popover .btn-unlink,
    &.image-popover .btn-upload,
    &.image-popover .btn-restore {
      display: inline-block;
      margin: 0 0 0 5px;
      color: #333333;
      font-size: 14px;
      outline: 0;

      span {
        opacity: 0.6;
      }

      &:hover span {
        opacity: 1;
      }
    }

    &.image-popover .btn-upload {
      position: relative;
      display: inline-block;
      overflow: hidden;
      vertical-align: middle;

      input[type=file] {
        position: absolute;
        right: 0px;
        top: 0px;
        opacity: 0;
        height: 100%;
        width: 28px;
      }
    }
  }

  &.simditor-mobile {
    .simditor-wrapper.toolbar-floating .simditor-toolbar {
        position: absolute;
        top: 0;
        z-index: 10;
        box-shadow: 0 0 6px rgba(0,0,0,0.1);
    }
  }
}



.simditor .simditor-body, .editor-style {
  font-size: 16px;
  font-family: arial, sans-serif;
  line-height: 1.6;
  color: #333;
  outline: none;
  word-wrap: break-word;

  & > :first-child {
    margin-top: 0!important;
  }

  a{ color: #4298BA; text-decoration: none; word-break: break-all;}
  a:visited{ color: #4298BA; }
  a:hover{ color: #0F769F; }
  a:active{ color:#9E792E; }
  a:hover, a:active{ outline: 0; }

  h1,h2,h3,h4,h5,h6 {
    font-weight: normal;
    margin: 40px 0 20px;
    color: #000000;
  }

  h1 { font-size: 24px; }
  h2 { font-size: 22px; }
  h3 { font-size: 20px; }
  h4 { font-size: 18px; }
  h5 { font-size: 16px; }
  h6 { font-size: 16px; }

  p, div {
    word-wrap: break-word;
    margin: 0 0 15px 0;
    color: #333;
    word-wrap: break-word;
  }

  b, strong {
    font-weight: bold;
  }

  i, em {
    font-style: italic;
  }

  u {
    text-decoration: underline;
  }

  strike, del {
    text-decoration: line-through;
  }

  ul, ol {
    list-style:disc outside none;
    margin: 15px 0;
    padding: 0 0 0 40px;
    line-height: 1.6;

    li {
      list-style-type: inherit;
    }

    ul, ol {
      padding-left: 30px;
    }

    ul {
      list-style: circle outside none;

      ul {
        list-style: square outside none;
      }
    }
  }

  ol {
    list-style:decimal;
  }

  blockquote {
    border-left: 6px solid #ddd;
    padding: 5px 0 5px 10px;
    margin: 15px 0 15px 15px;

    & > :first-child {
      margin-top: 0;
    }
  }

  code {
    display: inline-block;
    padding: 0 4px;
    margin: 0 5px;
    background: #eeeeee;
    border-radius: 3px;
    font-size: 13px;
    font-family: 'monaco', 'Consolas', "Liberation Mono", Courier, monospace;
    word-break: break-all;
    word-wrap: break-word;
  }

  pre {
    padding: 10px 5px 10px 10px;
    margin: 15px 0;
    display: block;
    line-height: 18px;
    background: #F0F0F0;
    border-radius: 3px;
    font-size:13px;
    font-family: 'monaco', 'Consolas', "Liberation Mono", Courier, monospace;
    white-space: pre;
    word-wrap: normal;
    overflow-x: auto;

    code {
      display: block;
      padding: 0;
      margin: 0;
      background: none;
      border-radius: 0;
    }
  }

  hr {
    display: block;
    height: 0px;
    border: 0;
    border-top: 1px solid #ccc;
    margin: 15px 0;
    padding: 0;
  }

  table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;
    margin: 15px 0;

    thead {
      background-color: #f9f9f9;
    }

    td, th {
      min-width: 40px;
      height: 30px;
      border: 1px solid #ccc;
      vertical-align: top;
      padding: 2px 4px;
      text-align: left;
      box-sizing: border-box;

      &.active {
        background-color: #ffffee;
      }
    }
  }


  img {
    margin: 0 5px;
    vertical-align: middle;
  }

}
