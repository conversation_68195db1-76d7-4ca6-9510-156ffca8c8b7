<?php

$header_output = $word_out = $footer_output = '';
include_once("../admin/inc/lib.inc.php");
chk_login(); //check if user is logged in
$root = realpath(__DIR__ . '/../../');
$header_out = true;

//if accessed from admin show this
if ($_GET['ref']) {
    $uids = pull_field("dir_appli_checklist", "id", "WHERE rel_id = '$_GET[ref]'");
    $candidate_id = $_GET['ref'];
    // pull in bootstrap
    ?>
    <!-- Bootstrap core CSS -->
    <link href="../../static/<?php echo $_SESSION['subdomain']; ?>/resources/css/bootstrap.min.css" rel="stylesheet">
    <?php
}

if ($_GET['rec']) {
    $letter_sent = sanitise($_GET['rec']);
    $sql = "SELECT * FROM dir_letters_sent WHERE usergroup= ? AND id = ?";
}

if ($_GET['ud_nme']) {
    $letter_sent = sanitise($_GET['ud_nme']);
    $sql = "SELECT * FROM dir_letters_sent WHERE usergroup= ? AND username_id = ?";
}

$dbh = get_dbh();
// grab the letter text
dev_debug($sql);
$sth = $dbh->prepare($sql);
$sth->execute(array($_SESSION['usergroup'], $letter_sent));
$letter = $sth->fetch();

////Stop gap measure to print offer letters
////KT
if (empty($letter['db31870']) && filter_var($letter['db20300'], FILTER_VALIDATE_URL)) {
    die(header("Location: {$letter['db20300']}"));
}

$sis_letter_library_content = $letter['db31870'];
$sis_letter_library_letter_title = $letter['db20301'];
$dload_date = time();
$title = str_replace(' ', '-', addslashes($sis_letter_library_letter_title));

if ($_GET['letter']) {
    echo $sis_letter_library_content;
    die();
}

//mrn users get into the block below
if (pull_field('form_schools', 'db30', "WHERE id={$_SESSION['usergroup']}") == '12') {
    $sis_letter_content = $sis_letter_library_content;
    $dload_date = date('d-m-Y_His');

    if ($_GET['pdf_orientation']) {
        $orientation = $_GET['pdf_orientation'];
    } else if ($_SESSION['pdf_orientation']) {
        $orientation = $_SESSION['pdf_orientation'];
    } else {
        $orientation = 'P';

    }

    $letter_signature = pull_field("lead_preferences", "db213245", "WHERE usergroup='{$_SESSION['usergroup']}'");
    $file_folder = '/var/www/vhosts/' . env('APP_URL') . '/private/media/';

    $header_image = pull_field("lead_preferences", "db213233", "WHERE usergroup='{$_SESSION['usergroup']}'");
    $header_image = str_replace($file_folder, '', $header_image);
    $header_image = engine_url . "/media/dl.php?a=yes&fl=" . encode(trim($header_image), 'unsalted');


    $footer_image = pull_field("lead_preferences", "db213236", "WHERE usergroup='{$_SESSION['usergroup']}'");
    $footer_image = str_replace($file_folder, '', $footer_image);
    $footer_image = engine_url . "/media/dl.php?a=yes&fl=" . encode(trim($footer_image), 'unsalted');


    $header_logo_height = '';
    $header_logo_width = '';
    $align = '';

    $footer_logo_height = '';
    $footer_logo_width = '';

    if (!empty($header_image) && getimagesize($header_image) !== false) {
        $header_logo = '<img style="float:right;" src="' . $header_image . '" width="' . $header_logo_width . '" height="' . $header_logo_height . '" />
			<div class="clearfix"></div>';
    } else {
        $header_logo = '';
    }

    if (!empty($footer_image) && getimagesize($footer_image) !== false) {
        $footer_logo = '<img src="' . $footer_image . '" width="' . $footer_logo_width . '" height="' . $footer_logo_height . '" style="padding:10px;" />';
    } else {
        $footer_logo = '';
    }

    if ($_GET['pdf'] == 1) {
        $folder = $_SESSION['subdomain'];
        $resources = $root . "/static/" . $folder . "/resources/";
        $stylesheet = "";
        require_once $root . '/vendor/autoload.php';

        $defaultConfig = (new Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];
        $defaultFontConfig = (new Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

        if (!file_exists($root . '/storage/')) mkdir($root . '/storage');
        if (!file_exists($root . '/storage/temp')) mkdir($root . '/storage/temp');

        $config = [
            'fontDir' => array_merge($fontDirs, [$resources . '/fonts/']),
            'fontdata' => $fontData,
            'default_font' => 'syntax-roman',
            'tempDir' => realpath($root . '/storage/temp/'),
            'orientation' => $orientation
        ];
        $final_letter = $sis_letter_content . $letter_signature;
        //exit($certificate);
        $mpdf = new \Mpdf\Mpdf($config);
        header('Content-Type: application/pdf');
        $filename = $title . ' ' . $dload_date . '.pdf';
        $mpdf->autoPageBreak = true;
        $mpdf->setAutoTopMargin = 'stretch';
        $mpdf->setAutoBottomMargin = 'stretch';
        $mpdf->setTitle($title);
        $mpdf->SetAuthor($_SESSION['school_name']);
        $mpdf->SetCreator('HeiApply.com');
        if (!empty($header_logo)) {
            $mpdf->SetHTMLHeader($header_logo);
        }
        if (!empty($footer_logo)) {
            $mpdf->SetHTMLFooter($footer_logo);
        }
        $mpdf->SetSubject($title);
        $mpdf->SetKeywords("course_booking_confirmation, {$title}");
        $mpdf->WriteHTML($stylesheet, \Mpdf\HTMLParserMode::HEADER_CSS);
        $mpdf->WriteHTML($final_letter, \Mpdf\HTMLParserMode::HTML_BODY);
        $mpdf->Output($filename, 'D');
    } elseif ($_GET['word'] == 1) {
        $header_logo_height = '125';
        $header_logo_width = '500';
        $align = '';

        $footer_logo_height = '125';
        $footer_logo_width = '500';

        $folder = $_SESSION['subdomain'];
        $resources = $root . "/static/" . $folder . "/resources/";
        $filename = $title . ' ' . $dload_date;
        $filename = str_replace(',', '', $filename);
        header("Content-type: application/vnd.ms-word", $header_out);
        header("Content-Disposition: attachment;Filename=$filename.doc", $header_out);

        $word_header_output_file = $root . "/static/" . $_SESSION['subdomain'] . "/admin/word_header_file.php";// link to external out file
        // then check if it has a custom rejection function
        if (!file_exists($word_header_output_file)) {
            $word_header_output_file = $root . "/static/word_header_output.php";// link to external out file
        }
        include("$word_header_output_file");// call to config
        echo $header_output;


        $word_output_file = $root . "/static/" . $_SESSION['subdomain'] . "/admin/word_output_file.php";// link to external out file
        // then check if it has a custom rejection function
        if (!file_exists($word_output_file)) {
            $word_output_file = $root . "/static/word_output_file.php";// link to external out file
        }
        include("$word_output_file");// call to config
        echo $word_out;

        $word_footer_output_file = $root . "/static/word_footer_output.php";// link to external out file
        include("$word_footer_output_file");// call to config
        echo $footer_output;

    } else {
        $sis_letter_content = "$header_logo $sis_letter_content $letter_signature $footer_logo";

        ?>

        <style>
            .letter_print {
                padding: 5px;
                margin: 0 auto;
                border: 1px solid #f0f0f0;
                font-family: Helvetica, Arial, sans-serif;
            }

            .side_text {
                color: #AAA096;
                font-size: 0.8em
            }

            ;
        </style>
        <div class="row letter_print">
            <div class="col-md-12"><?php echo $sis_letter_content; ?></div>
        </div>

    <?php }

} else {


    $logo .= '<div>
      <div style=" float:right;">';

    //$logo_file = base_path.'/engine/'.$_SESSION['school_logo'];
    if (!is_null($_SESSION['school_logo']) and $_SESSION['school_logo'] !== '') {

        if ($_GET['pdf'] == '1') {
            $logo_file = str_replace(".co.uk", ".com", $_SERVER['DOCUMENT_ROOT'] . "/../private/media/" . $_SESSION['school_logo']);
            if (file_exists($logo_file)) {
                $logo .= '<img src="' . $logo_file . '" style="padding:20px;" />';
            } else {
                $logo = '';
            }

        } else {
            $logo_file = engine_url . "/media/dl.php?fl=" . encode($_SESSION['school_logo']);
            if (file_exists($logo_file)) {
                $logo .= '<img src="' . $logo_file . '" style="padding:20px;" class="img-responsive" />';
            } else {
                $logo = '';
            }
        }
    }
    $logo .= '</div> <div style="clear:both"></div>';


    if ($_GET['pdf'] == '1') {
        //SEND TO PDF
        $html = '<style>
	    @media only screen and (max-width: 599px) {
	        img.pattern {
	            max-width: 100%;
	            height: auto !important;
	        }
	    }
	    table {
	        width:600px;
	    }
	
	    </style>
	
	    <table  cellpadding="0" cellspacing="0">
	            
	            <tr>
	                   <td bgcolor="#ffffff" style="padding: 30px 30px 30px 30px;">' . $sis_letter_library_content . '</td>
	            </tr>
	            
	    </table>';

        //check if they want logo to be shown
        $show_logo = pull_field("form_schools", "db295", "WHERE id='$_SESSION[usergroup]' ");
        if ($show_logo == 'no') {
            // set some session variable to be added to pdf
            $_SESSION['print_msg'] = "$html";
        } else {
            // set some session variable to be added to pdf
            $_SESSION['print_msg'] = "$logo $html";
        }

        $_SESSION['file_title'] = $title . "-" . $dload_date;
        $_SESSION['letters_sent'] = true;
        // send to PDF creator
        $debbugurl = '';


        $debbugurl = '';
        if (!empty($_GET['dump_message'])) {
            $debbugurl = '?dump_message=yes';
        }

        header('Location: ' . engine_url . '/tools/pdf_convert/convert.php' . $debbugurl);


    } elseif ($_GET['print'] == 'yes') {
        $sis_letter_library_content = "$sis_letter_library_content";
        ?>
        <style>
            .letter_print {
                padding: 5px;
                margin: 0 auto;
                border: 1px solid #f0f0f0;
                font-family: Helvetica, Arial, sans-serif;
            }

            .side_text {
                color: #AAA096;
                font-size: 0.8em
            }

            ;
        </style>


        <table width="1200" border="0" cellspacing="2" cellpadding="20" class="row letter_print">
            <tr class="container">
                <td valign="top"><?= $sis_letter_library_content ?></td>
            </tr>
        </table>
        <?php

    } else {
        //DISPLAY IN VIEW
        $sis_letter_library_content = "$logo $sis_letter_library_content";
        ?>
        <style>
            .letter_print {
                padding: 5px;
                margin: 0 auto;
                border: 1px solid #f0f0f0;
                font-family: Helvetica, Arial, sans-serif;
            }

            .side_text {
                color: #AAA096;
                font-size: 0.8em
            }

            ;
        </style>
        <div class="row letter_print">
            <div class="col-md-12"><?php echo $sis_letter_library_content; ?></div>
        </div>
        <?php
        echo '</div>'; // end letter print
    }
}


//reset some values
$logo = '';
$sis_letter_library_content = '';


