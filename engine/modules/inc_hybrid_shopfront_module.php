<?php
$enable_apply = $_GET['apply'];
$usergroups = usergroups_management();

$dbh = get_dbh();
$sql = "SELECT
(SELECT db343 FROM core_course_level WHERE core_course_level.id=db341) as 'course_level',
db232 as 'course_title',
db231 as 'course_code'
FROM core_courses WHERE db235='public' 
AND (rec_archive IS NULL OR rec_archive = '') 
AND db231!=''
AND usergroup=? 
ORDER BY db232 ASC
";

dev_debug($sql);
$sth = $dbh->prepare($sql);
$sth->execute(array($_SESSION['usergroup']));
$courses = $sth->fetchAll(PDO::FETCH_ASSOC);

?>
<div class="col-xs-12 col-sm-6 col-lg-6">
    <div class="box">
        <div class="icon">
            <h3 class="title">Apply Direct</h3>

            <p>Select an option &amp; apply to us directly in a few easy steps</p>
            <?php
            foreach ($courses as $course) {
                echo "<div class=\"more\"><a href=\"" . website_url . '/' . front_header_file_location . "/registration_redirect.php?muid=$course[course_code]\" class=\"btn btn-success\" alt=\"Apply now\" title=\"Apply Now\">$course[course_title]</a></div>";
            }
            ?>
            <!--<div class="more"><a class="btn btn-success btn-block" href="https://hedemo.heiapply.com/application/shopfront" title="Direct Applications">Continue</a></div>-->
            <br/>
            &nbsp;
        </div>
    </div>

    <div class="space">&nbsp;</div>
</div>

<div class="col-xs-12 col-sm-6 col-lg-6">
    <div class="box">
        <div class="icon">
            <h3 class="title">Already Applied?</h3>

            <p>Login to continue with an application you have started</p>

            <div class="more"><a class="btn btn-success btn-block" href="https://hedemo.heiapply.com/application/login"
                                 title="Login">Login</a></div>
            <br/>
            &nbsp;
        </div>
    </div>
</div>
