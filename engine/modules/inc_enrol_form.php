<?php
//get globals
global $candidate_id, $core_students_application_status, $core_students_email_address, $core_students_rec_id;


//check if pre-enrolment form has been setup, if not then set it up
if ($_POST['default_reg_enrol']) {
    default_setup("enrol_enrolment_form", $candidate_id);
    echo "<div class=\"warning\">Internal enrolment ready. Please proceed to enrol the student</div>";
}
?>
    <style>
        .enrol h2 {
            padding-bottom: 5px;
            border-bottom: 1px dotted #ccc;
            color: #045f95;
        }
    </style>
    <h2>Enrolment</h2>
<?php


//CHECK IF THE APPLICANT IS AT LEVEL 7 YET
if ($core_students_application_status == 9) {
    echo "<div class=\"success\"> Student has been fully enrolled. No further action required</div>";
}

$enrol_stages = array("7", "8");

if (!in_array($core_students_application_status, $enrol_stages)) {
    echo "<div class=\"warning\">Sorry this applicant is not ready for enrolment. They will need to have got to stage 7(CAS Issued) of the application processing stages before they can pre-enrol</div>";
} elseif ($_POST['applicant_id']) {

    //move applicant to stage 9 enrolled on clicking this button
    $candidate_id_pulled = $_POST['applicant_id'];

    $dbh = get_dbh();
    //update student
    $sql = "Update core_students SET db41='9' WHERE id='$_POST[applicant_id]'";
    $stmt = $dbh->prepare($sql);
    $stmt->execute();

    //update checklist
    $sql2 = "Update dir_appli_checklist SET db1115='yes' WHERE rel_id='$_POST[applicant_id]'";
    $stmt = $dbh->prepare($sql2);
    $stmt->execute();

    // insert tracker
    track_application_stages(9, $candidate_id_pulled);
    track_use("checklist_update|$candidate_id_pulled");

    echo "<div class=\"success\"> Student has been fully enrolled.</div>";

} else {
    ?>
    <div class="enrol">
        <h2>1) Enrolment settings</h2>

        <?php
        $check_for_invite_sent = explode("|", pull_field("form_invite_tracker", "CONCAT(date,'|',db1080)", "WHERE db1077='$core_students_email_address' AND db1089='enrol_invite'")); // get application id

        $check_for_invite_sent_date = $check_for_invite_sent[0];
        $check_for_invite_sent_status = $check_for_invite_sent[1];

        $enrolled_form = pull_field("enrol_enrolment_form", "id", "WHERE rel_id='$candidate_id'");
        $int_rec1 = pull_field("form_users", "id", "WHERE db119='$core_students_email_address'");
        //if self registred then use this
        $int_rec = pull_field("enrol_enrolment_form", "id", "WHERE rec_id='$int_rec1' AND rec_id!=''");

        echo $int_rec;

        if ($enrolled_form || $int_rec) {
            ?>
            <?php
            // function to pull page to surface
            $pick_page = 152;
            // if non self then use this
            if (!$int_rec) {
                $int_rec = $enrolled_form;
            }

            // $record_pull='0';
            $appli_submited_check = '0';
            $totally_kill_proceed_buttons = 1;
            $totally_remove_button_message = 1;
            include("view_only.php"); // the controler build the page in real time based on the values in the
            ?>
            <?php
        } else {
            ?>
            Use the botton below to enrol the student internally and complete the enrolment document for them.<br/>
            <form method="post">
                <input type="submit" name="submit" id="submit" class="addbutton" value="Internally enrol the applicant">
                <input type="hidden" value="1" name="default_reg_enrol">
            </form>

            <?php
            //if invite already sent, then kill button
            if ($check_for_invite_sent_date == '') {
                ?>
                <br/>Use the button below to send an email invitation to the current applicant asking them to pre-enrol.
                <br/>
                <a title="Sending invite" class="thickbox remove_underline positive"
                   href="<?php echo engine_url; ?>/models/scpt_custom_pre-enrol_invite.php?width=500&height=300&ref=<?php echo $candidate_id; ?>&jqmRefresh=true">
                    <div style="margin: 10px 0;" class="addbutton">Send invite to pre-enrol</div>
                </a>

                <?php
            } else { //end invite sent check
                echo '<div class="warning">Invite sent on ' . format_date("F d, Y", $check_for_invite_sent_date), '. Student has not yet logged in. - <a title="Re-sending invite" class="thickbox" href="' . engine_url . '/models/scpt_custom_pre-enrol_invite.php?width=500&height=300&ref=' . $candidate_id . '&resent_invite=yes&jqmRefresh=true"><div style="margin: 10px 0;" class="addbutton">Resend invite</div></a></div>';
            }

        }
        ?>

        <div class="clear"></div>
        <h2>2) Scan and upload files</h2>
        Use this section to upload all the required scanned documents.
        <?php
        // DISPLAY FILES
        // ---------------- 2 <USER>
        <GROUP>("views/view_of_enrolment_folders.php");
        ?>

        <div class="clear"></div>
        <h2>3) Enrolment checklist</h2>
        <?php
        // function to pull page to surface
        $pick_page = 150;
        $int_rec1 = $_GET['ref'];
        $int_rec = pull_field("enrol_enrolment_checklist", "id", "WHERE rel_id='$int_rec1'");
        // $record_pull='0';
        $appli_submited_check = '0';
        $totally_kill_proceed_buttons = 1;
        $totally_remove_button_message = 1;
        include("view_only.php"); // the controler build the page in real time based on the values in the
        ?>
    </div>

    <div class="clear"></div>
    <h2>4) Enrol student</h2>
    <form method="post">
        <input type="submit" name="submit" id="submit" class="addbutton" value="Enrol the applicant">
        <input type="hidden" value="<?php echo $_GET['ref']; ?>" name="applicant_id">
    </form>

    <?php
}// end of if else
