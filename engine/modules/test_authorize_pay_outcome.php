<?php

// error_log("ANITA PAYMENT PROCESSINGg");
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);


include("../admin/inc/lib.inc.php");
require_once('../tools/authorize_net/vendor/autoload.php');


use net\authorize\api\contract\v1 as AnetAPI;
use net\authorize\api\controller as AnetController;

define("AUTHORIZENET_LOG_FILE", "phplog");

//check if the file exists
//$external_payments_url = base_path."/static/".$_SESSION['subdomain']."/admin/authorize_pay_config.php";// link to external admin file

// then check if it has a custom rejection function
//if(file_exists($external_payments_url)){
//
//    //get stripe files
//    define('CURL_SSLVERSION_TLSv1_2',true);
//    //require_once(base_path.'engine/tools/stripe/stripe-php-5.7.0/init.php');
//
//    // $curl = new \Stripe\HttpClient\CurlClient(array(CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2));
//    // \Stripe\ApiRequestor::setHttpClient($curl);
//
//    //get config file
//    require_once("$external_payments_url");// call custom rejection function
//}else{
//    die("sorry payments config file was not found...");
//}
if (isset($_POST["amount_to_pay"]) && !empty($_POST["amount_to_pay"])) {
    $amount = $_POST["amount_to_pay"];
}


createAnAcceptPaymentTransaction($amount, $authorize);
//Insert into the DB
//$dbh = get_dbh();
//$sth = $dbh->prepare("INSERT INTO sis_student_fees (username_id, rec_id, usergroup, rel_id, db1492, db1493, db1494, db1495,
// db15459,db34450)VALUES (?,?,?,?,?,?,?,?,?,?)");
//$sth->execute(array(random(), session_info("uid"), session_info("access"), floating_info("ref"),
//'authorize', 'Registration Fee', $token, $normalised_amount, $payment_date,'Payment'));


function createAnAcceptPaymentTransaction($amount, $authorize)
{
    error_log(" PAYMENT PROCESSING");
    /* Create a merchantAuthenticationType object with authentication details
       retrieved from the constants file */
    $merchantAuthentication = new AnetAPI\MerchantAuthenticationType();
    $merchantAuthentication->setName($authorize['data_apiLoginID']);
    $merchantAuthentication->setTransactionKey($authorize['data_transactionKey']);

    // Set the transaction's refId
    $refId = 'ref' . time();

    // Create the payment object for a payment nonce
    $opaqueData = new AnetAPI\OpaqueDataType();
    $opaqueData->setDataDescriptor($_POST['dataDescriptor']);
    $opaqueData->setDataValue($_POST['dataValue']);


    // Add the payment data to a paymentType object
    $paymentOne = new AnetAPI\PaymentType();
    $paymentOne->setOpaqueData($opaqueData);

    // Create order information
    $order = new AnetAPI\OrderType();
//    $order->setInvoiceNumber($_POST['invoice_reference']);
    $order->setInvoiceNumber("000");
    $order->setDescription($authorize['data_button_text']);
    error_log("HEIApply PROCESSING 1");
    // Set the customer's Bill To address
    $customerAddress = new AnetAPI\CustomerAddressType();
    $customerAddress->setFirstName($_POST["first_name"]);
    $customerAddress->setLastName($_POST["last_name"]);
    $customerAddress->setCompany("");
    $customerAddress->setAddress($_POST["home_address"]);
    $customerAddress->setCity($_POST["home_city"]);
    $customerAddress->setState($_POST["home_state"]);
    $customerAddress->setZip($_POST["home_postcode"]);
    $customerAddress->setCountry($_POST["home_country"]);

    // Set the customer's identifying information
    $customerData = new AnetAPI\CustomerDataType();
    $customerData->setType("individual");
//    $customerData->setId("99999456654");
    $customerData->setEmail($_POST["email"]);

    // Add values for transaction settings
    $duplicateWindowSetting = new AnetAPI\SettingType();
    $duplicateWindowSetting->setSettingName("duplicateWindow");
    $duplicateWindowSetting->setSettingValue("60");

    // Add some merchant defined fields. These fields won't be stored with the transaction,
    // but will be echoed back in the response.
    /*$merchantDefinedField1 = new AnetAPI\UserFieldType();
    $merchantDefinedField1->setName("customerLoyaltyNum");
    $merchantDefinedField1->setValue("1128836273");

    $merchantDefinedField2 = new AnetAPI\UserFieldType();
    $merchantDefinedField2->setName("favoriteColor");
    $merchantDefinedField2->setValue("blue");*/

    // Create a TransactionRequestType object and add the previous objects to it
    $transactionRequestType = new AnetAPI\TransactionRequestType();
    $transactionRequestType->setTransactionType("authCaptureTransaction");
    $transactionRequestType->setAmount($amount);
    $transactionRequestType->setOrder($order);
    $transactionRequestType->setPayment($paymentOne);
    //$transactionRequestType->setBillTo($customerAddress);
    $transactionRequestType->setCustomer($customerData);
    $transactionRequestType->addToTransactionSettings($duplicateWindowSetting);
    //$transactionRequestType->addToUserFields($merchantDefinedField1);
    //$transactionRequestType->addToUserFields($merchantDefinedField2);

    // Assemble the complete transaction request
    $request = new AnetAPI\CreateTransactionRequest();
    $request->setMerchantAuthentication($merchantAuthentication);
    $request->setRefId($refId);
    $request->setTransactionRequest($transactionRequestType);

    // Create the controller and get the response
    $controller = new AnetController\CreateTransactionController($request);
    $response = $controller->executeWithApiResponse(\net\authorize\api\constants\ANetEnvironment::SANDBOX);

    error_log("HEIApply PROCESSING 3");
    if ($response != null) {
        // Check to see if the API request was successfully received and acted upon
        if ($response->getMessages()->getResultCode() == "Ok") {
            // Since the API request was successful, look for a transaction response
            // and parse it to display the results of authorizing the card
            $tresponse = $response->getTransactionResponse();

            if ($tresponse != null && $tresponse->getMessages() != null) {

                ////now add the details onto sis_student_fees
                //Insert into the DB
                $dbh = get_dbh();
                $payment_date = date("Y-m-d");
                $reference = $_POST['application_id'];
                if (!isset($reference)) {
                    $reference = floating_info("ref");
                }
                $sth = $dbh->prepare("INSERT INTO sis_student_fees (username_id, rec_id, usergroup, rel_id, db1492, db1493, db37345, db1495,
				 db15459,db34450)VALUES (?,?,?,?,?,?,?,?,?,?)");
                $sth->execute(array(random(), $_SESSION["uid"], $_SESSION["usergroup"], $reference,
                    'authorize.net', $authorize['data_button_text'], $tresponse->getAuthCode(), $amount, $payment_date, 'Payment'));
                echo " Successfully created transaction with Transaction ID: " . $tresponse->getTransId() . "\n";
                echo " Transaction Response Code: " . $tresponse->getResponseCode() . "\n";
                //echo " Message Code: " . $tresponse->getMessages()[0]->getCode() . "\n";
                echo " Auth Code: " . $tresponse->getAuthCode() . "\n";
                //echo " Description: " . $tresponse->getMessages()[0]->getDescription() . "\n";
                header('Location: ' . $_SERVER["HTTP_REFERER"]);
                exit;
            } else {
                echo "Transaction Failed1 \n";
                if ($tresponse->getErrors() != null) {
                    echo " Error Code  : " . $tresponse->getErrors()[0]->getErrorCode() . "\n";
                    echo " Error Message : " . $tresponse->getErrors()[0]->getErrorText() . "\n";
                }
            }
            // Or, print errors if the API request wasn't successful
        } else {
            echo "Transaction Failed2 \n";
            $tresponse = $response->getTransactionResponse();

            if ($tresponse != null && $tresponse->getErrors() != null) {
                echo " Error Code  : " . $tresponse->getErrors()[0]->getErrorCode() . "\n";
                echo " Error Message : " . $tresponse->getErrors()[0]->getErrorText() . "\n";
            } else {
                echo " Error Code  : " . $response->getMessages()->getMessage()[0]->getCode() . "\n";
                echo " Error Message : " . $response->getMessages()->getMessage()[0]->getText() . "\n";
            }
        }
    } else {
        echo "No response returned \n";
    }
    error_log("HEIApply PROCESSING 4");
    return $response;
}


