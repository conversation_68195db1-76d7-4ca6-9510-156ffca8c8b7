<?php
//TODO get domain name from form
//TODO check it against domain names in the db
//TODO if it exists, return alert
//TODO if it is unique, send back an ok so the process can continue
include("../admin/inc/lib.inc.php");
function table_exists($table, $dbh)
{
    $dbh = get_dbh();
    $query = $dbh->prepare("SHOW TABLES LIKE :table");
    $query->execute([':table' => $table]);

    return $query->rowCount() > 0;
}


function check_domain($domain)
{
    $invalid_chars = array(".", " ", ",", "?", "%", "!");
    $valid = true;
    foreach ($invalid_chars as $char) {
        if (strpos($domain, $char) !== false) {
            $valid = false;
            array_push($bad_chars, $char);
        }
    }


    return $valid;
}

function clean_domain($domain)
{
    return strtolower(preg_replace("/[^A-Za-z0-9 ]/", '', "$domain"));
}

$args = array();
//FIELDS FOR SCHOOL
$args['db81'] = "7";
$args['db989'] = sanitise($_POST['plan']);
$args['db985'] = clean_domain(sanitise($_POST['domain']));
$args['db27'] = sanitise($_POST['company']);
$args['db991'] = "1";
$args['db326'] = sanitise($_POST['plan_settings']);
$args['db36'] = date("Y");
$args['db33'] = "97,167";
$args['db32'] = sanitise($_POST['website']);
$args['db37'] = "yes";
$args['db90'] = "not specified";
$args['db295'] = "not specified";
$args['db1117'] = "<EMAIL>";
$args['db1118'] = "<EMAIL>";
$args['db1257'] = "1";
$args['db296'] = "yes";
//FIELDS FOR USER
$email = sanitise($_POST['email']);
$bad_chars = array();
$dbh = get_dbh();
$sql = $dbh->prepare('SELECT count(*) FROM form_schools where db985 like \'' . $args['db985'] . '\'');
$sql->execute();
$domain_exists = $sql->fetchColumn();


$sql = $dbh->prepare('SELECT count(*) FROM form_users where db119 like \'' . $email . '\' AND usergroup!=61');
$sql->execute();
$username_exists = $sql->fetchColumn();
$return = array();
if ($username_exists > 0) {
    //duplicate username
    array_push($return, 0);
    array_push($return, 'Email address already exists, please <a href="http://eduagentcrm.com/contact">contact support </a> to finalise your account');
} else if ($domain_exists > 0) {
    //duplicate domain
    array_push($return, 1);
    array_push($return, 'Subdomain already exists');
}
//else if(!check_domain($args['db985'])){
//    $chars = implode(",", $bad_chars);
//    array_push($return,1);
//    array_push($return,'Invalid subdomain, please remove the following character(s):'.$chars);
//}
else {
    //setup school
    $fields = array('db81', 'db989', 'db985', 'db27', 'db1093', 'db30', 'db991', 'db326', 'db36', 'db33', 'db1257', 'db1259', 'db1260', 'db1261', 'db1258', 'db294', 'db32', 'db1117', 'db1118', 'db28', 'db35', 'db34', 'db90', 'db1095', 'db296', 'db37', 'db9639', 'db1242', 'db1243', 'db61', 'db295', 'db136');
    $option4 = "5";
    $page_table = "form_schools";
    $i = 0;
    $sql = $dbh->prepare("INSERT INTO form_schools 
                          (username_id, rec_id, usergroup, rel_id, db81, db989, db985, db27, db1093, db30, db991, db326, db36, db33, db1257, db1259, db1260, db1261, db1258, db294, db32, db1117, db1118, db28, db35, db34, db90, db1095, db296, db37, db9639, db1242, db1243, db61, db295, db136) 
                          VALUES ('" . random() . "','1','1',null,'" . $args[$fields[$i++]] . "','" . $args[$fields[$i++]] . "','" . $args[$fields[$i++]] . "','" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "')");
    $sql->execute();
    include('../models/custom/core/school_creation.php');
    $args['id'] = pull_field('form_schools', 'id', 'WHERE db985 = \'' . $args['db985'] . '\'');
    \school_creation\init_registration_forms($args, "", "");
    $_SESSION['uid'] = 1;
    \school_creation\init_checklist($args, "", "");

    //setup user
    $args = array();
    global $server_domain_name;
    $args['db112'] = "2";
    $args['db111'] = sanitise($_POST['sname']);
    $args['db106'] = sanitise($_POST['fname']);
    $args['db119'] = sanitise($_POST['email']);
    $args['db222'] = md5("bla2!asaou");
    $args['db1140'] = sanitise($_POST['company']);
    $args['db1262'] = ""; //timezone
    $args['db307'] = "no";
    $args['db1034'] = pull_field("form_schools", "id", "where  db985 = '" . clean_domain($_POST['domain']) . "'");
    $setup_domain = pull_field("form_schools", 'db985', "WHERE id='$args[db1034]'");
    $args['username_id'] = random();
    $args['db308'] = "";
    $args['db223'] = "";
    $args['db1730'] = "";
    $args['host'] = "https://$setup_domain.$server_domain_name";
    $args['usergroup'] = $args['db1034'];
    $option4 = "12";
    $page_table = "form_users";
    include('../models/custom/form/users.php');

    $fields = array('db1140', 'db106', 'db308', 'db111', 'db119', 'db112', 'db1262', 'db222', 'db223', 'db307', 'db1034', 'db1730');
    $i = 0;
    $sql = "INSERT INTO form_users
    (username_id, rec_id, usergroup, rel_id, db1140, db106, db308, db111, db119, db112, db1262, db222, db223, db307, db1034, db1730)
                          VALUES ('" . random() . "','1','" . $args['usergroup'] . "',null,'" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "', '" . $args[$fields[$i++]] . "')";
    error_log("user sql: " . $sql);
    $sql = $dbh->prepare($sql);
    $sql->execute();
    $args['id'] = pull_field("form_users", "id", "where db119 = '$args[db119]'");
    \form_users\send_email($args, "", "");
    $args['send_password_reset_link'] = 'yes';
    \form_users\send_pass_reset_link($args, "", "");

    $sql = $dbh->prepare('SELECT count(*) FROM form_schools where db985 like \'' . clean_domain($_POST['domain']) . '\'');
    $sql->execute();
    $domain_exists = $sql->fetchColumn();


    $sql = $dbh->prepare('SELECT count(*) FROM form_users where db119 like \'' . $_POST['email'] . '\'');
    $sql->execute();
    $username_exists = $sql->fetchColumn();

    if ($domain_exists > 0 && $username_exists > 0) {
        array_push($return, 2);
        array_push($return, 'Your account has been set up successfully. We have just emailed all your account details over<br/><br/> <a href="' . $_SESSION[domain] . '/application/checklist" class="btn btn-info">Move to Step 3 - Configure Your Solution</a>');
    } else {
        array_push($return, 0);
        array_push($return, 'Failed to create account, please check your details and try again');
    }
}

print json_encode($return);