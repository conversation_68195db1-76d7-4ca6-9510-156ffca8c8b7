<?php
$enable_apply = $_GET['apply'];
$usergroups = usergroups_management();

$dbh = get_dbh();
$sql = "SELECT
(SELECT db343 FROM core_course_level WHERE core_course_level.id=db341) as 'Level',
db232 as 'Course Title',
db231 as 'Course Code'
FROM core_courses WHERE db235='public' 
AND (rec_archive IS NULL OR rec_archive = '') 
AND db231!=''
AND usergroup='$_SESSION[usergroup]' 
ORDER BY db232 ASC
";

dev_debug($sql);

$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$num = $stmt->rowCount();

//echo "<p><strong>Total Records Displayed:</strong> $num</p>";

if (!$num) {
    //die("");
    echo "no results";
} else {

    $fields_num = $stmt->columnCount();

    echo '<table class="sortable table table-striped settings_data_table" width="100%" cellspacing="0">
		<thead><tr>';
    // printing table headers
    for ($i = 0; $i < $fields_num; $i++) {
        $field = $stmt->getColumnMeta($i);

        echo '<th>' . $field['name'] . '</th>';
    }
    echo "</tr></thead><tbody>";
    // printing table rows

    $i = 0;
    foreach ($results as $row) {

        if ($row[1] == "yes" && $update_link == '43') {
            $alert_bkg = 'style="background-color:#fcc"';
        }// highlight the column if the alert is called for
        echo '<tr style="background:' . row_color($i) . '" ' . $alert_bkg . '>';

        $k = 1;
        foreach ($row as $cell) {

            if ($k == "3") {

                // check if they have this feature
                // if ($enable_apply) {

                echo "<td style='width:20%'><a href=\"" . website_url . '/' . front_header_file_location . "/registration_redirect.php?muid=$row[2]\" class=\"btn btn-success\" alt=\"Apply now\" title=\"Apply Now\">Apply Now</a></td>";

                //}else{

                //	echo "<td>$row[2]</td>";

                //	}//end  check


                echo "</td>";

            } else {
                if ($k == 1) {
                    echo '<td style="width:20%">' . $cell . '</td>';
                } else {
                    echo '<td style="width:60%">' . $cell . '</td>';
                }
            }

            $k++;
        }

        $i++;
        $alert_bkg = "";// reset colour
        echo "</tr>";
    }
    echo "</tbody><tfoot></tfoot></table>";


}// end of if else