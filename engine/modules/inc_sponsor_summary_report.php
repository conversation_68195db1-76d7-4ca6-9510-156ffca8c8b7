<?php

//error_reporting(E_ALL); ini_set('display_errors', '1');
require_once(__DIR__ . "/../../admin/config.php");

$OL = new OnlineLearning;

/** ===================================
 * Access Plan Results
 * ====================================  */

$dbh = get_dbh();
if (!$_SESSION['sponsor_id']) {
    $sponsor_query = "SELECT id, db21861 as'name' from ols_sponsors where usergroup = ? and (rec_archive is null or rec_archive ='') ORDER BY db21861";
    $sponsor_sth = $dbh->prepare($sponsor_query);
    $sponsor_sth->execute(array($_SESSION['usergroup']));
    $sponsors = $sponsor_sth->fetchAll(PDO::FETCH_ASSOC);
}

$report_sponsor_id = $_SESSION['sponsor_id'];
if ($_POST['select_sponsor']) {
    $report_sponsor_id = $_POST['select_sponsor'];
}
?>
    <div class="row">
        <form action="" method="post" class="form-horizontal">
            <?php
            if (!$_SESSION['sponsor_id']) {
                echo "<h2>Report 3: Learners, course progression & 'Your Information' Report</h2>";
                echo "<p>Shows number of learners, progress through course and answers to supplementary questions (e.g. demographic questions)</p>";
            }
            ?>
            <div class="col-sm-4">
                <div class="form-group">

                    <?php
                    if (!$_SESSION['sponsor_id']) {
                        echo '<select class="form-control list_of_views selectize" name="select_sponsor" id="select_sponsor">';
                        echo "<option value=''>Select...</option>";
                        $selected = '';
                        foreach ($sponsors as $sponsor) {
                            $selected = '';
                            if ($sponsor['id'] == $report_sponsor_id) {
                                $selected = 'selected =selected';
                            }
                            echo "<option value='$sponsor[id]' $selected>$sponsor[name]</option>";
                        }

                        echo '</select>';
                    } else {
                        echo "<input type = 'hidden' id='ref' name='ref' value='$sponsor_id'>";
                    } ?>
                </div>
            </div>

            <?php if (!$_SESSION['sponsor_id']) { ?>
                <div class="col-sm-2">
                    <div class="form-group">
                        <button class="btn pull btn-info" type="submit"><i class="fa fa-search"></i> Report</button>
                    </div>

                </div>
            <?php } ?>
        </form>

    </div>
    <div class="clearfix"></div>

<?php
if ($report_sponsor_id && $report_sponsor_id != '') {
    $total_no_of_registered_users = 0;
    $access_plans_args = array("school_id" => $_SESSION['usergroup'], "sponsor_id" => $report_sponsor_id, "course_stats_required" => true, "completion_stage_required" => true, "transitioned_courses" => true);
    $access_plans = $OL->get_plans($access_plans_args);
    foreach ($access_plans as $access_plan_temp) {

        foreach ($access_plan_temp['courses'] as $access_plan_course_temp) {
            $total_no_of_registered_users += $access_plan_course_temp['no_registered_users'];
        }
    }
    ?>

    <div class="col-xs-12 col-md-12">
        <div class="separator clearfix"></div>
        <div class="stats_dash" id="tour_stats">
            <?php
            foreach ($access_plans as $access_plan) {

                foreach ($access_plan['courses'] as $access_plan_course) {
                    $access_plan_course_title = $access_plan_course['alternative_title'] . ' - ' . $access_plan['access_code']; ?>
                    <!--<h1><?php echo $access_plan_course_title; ?></h1>-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class='table-responsive'>
                                <table style="display: block;overflow-x: auto;" class="table table-striped ">
                                    <thead>
                                    <tr>
                                        <th nowrap="" width="300px;" style="width:300px;">
                                            <h2><?php echo $access_plan_course['alternative_title'] . ' - <br/>' . $access_plan['access_code'] ?></h2>
                                        </th>
                                        <th colspan="13"></th>
                                    </tr>
                                    <tr>
                                        <th style="width:300px;">&nbsp;</th>
                                        <th>No. of new users last month</th>
                                        <th>Total number of learners</th>
                                        <th>Total number of learners(%)</th>
                                        <th>No. learners who have started</th>
                                        <th>No. learners who have started(%)</th>
                                        <th>Units completed greater than 70%</th>
                                        <th>Units completed greater than 70% (%)</th>
                                        <th>Units completed between 51% and 69%</th>
                                        <th>Units completed between 51% and 69% (%)</th>
                                        <th>Units completed between 10% and 50%</th>
                                        <th>Units completed between 10% and 50% (%)</th>
                                        <th>Units completed less than 10%</th>
                                        <th>Units completed less than 10% (%)</th>

                                    </tr>
                                    </thead>

                                    <tbody>
                                    <tr>
                                        <td style="width:300px;">&nbsp;</td>
                                        <td><?php echo $access_plan_course['no_of_registered_users_last_month']; ?></td>
                                        <td><?php echo $access_plan_course['no_registered_users'] ?></td>
                                        <td><?php echo round($access_plan_course['no_registered_users'] / $total_no_of_registered_users * 100) . '%'; ?></td>
                                        <td><?php echo $access_plan_course['no_started_learners'] ?></td>
                                        <td><?php echo round($access_plan_course['no_started_learners'] / $access_plan_course['no_registered_users'] * 100) . '%'; ?></td>
                                        <td><?php echo $access_plan_course['count_learners_no_units_completed_greater_than_70']; ?></td>
                                        <td><?php echo round($access_plan_course['count_learners_no_units_completed_greater_than_70'] / $access_plan_course['no_started_learners'] * 100) . '%'; ?></td>
                                        <td><?php echo $access_plan_course['count_learners_no_units_completed_between_51_and_69']; ?></td>
                                        <td><?php echo round($access_plan_course['count_learners_no_units_completed_between_51_and_69'] / $access_plan_course['no_started_learners'] * 100) . '%'; ?></td>
                                        <td><?php echo $access_plan_course['count_learners_no_units_completed_between_10_and_50']; ?></td>
                                        <td><?php echo round($access_plan_course['count_learners_no_units_completed_between_10_and_50'] / $access_plan_course['no_started_learners'] * 100) . '%'; ?></td>
                                        <td><?php echo $access_plan_course['count_learners_no_units_completed_less_than_10']; ?></td>
                                        <td><?php echo round($access_plan_course['count_learners_no_units_completed_less_than_10'] / $access_plan_course['no_started_learners'] * 100) . '%'; ?></td>

                                    </tr>

                                    <?php
                                    $course_name = '';
                                    $access_plan_unlock_code = '';

                                    ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>


                    <?php
                    $supplementary_questions = $access_plan['supplementary_questions'];
                    foreach ($supplementary_questions as $supplementary_question) {
                        $supplementary_question_title = $supplementary_question['title']; ?>
                        <!--<h2><?php echo $supplementary_question_title; ?></h2>-->
                        <div class="row">
                            <div class="col-md-12">
                                <div class='table-responsive'>
                                    <table style="display: block;overflow-x: auto;" class="table table-striped">
                                        <thead>
                                        <tr>
                                            <th nowrap="" width="300px;"
                                                style="width:300px;"><?php echo $supplementary_question_title; ?></th>
                                            <?php $answers = explode(',', $supplementary_question['answers']);
                                            foreach ($answers as $answer) {

                                                ?>
                                                <th><i><?php echo $answer; ?></i></th>
                                                <th><i><?php echo $answer; ?>(%)</i></th>

                                                <?php $supplementary_question_title = '';

                                            } ?>

                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <td style="width:300px;">&nbsp;</td>
                                            <?php
                                            $supp_questions_answers = array();

                                            $supplementary_question_title = $supplementary_question['title'];
                                            $answers = explode(',', $supplementary_question['answers']);
                                            $answers_count = array();
                                            foreach ($answers as $answer) {
                                                $supp_questions_answers[$supplementary_question_title][$answer] = 0;

                                            }

                                            $access_plan_learner_supp_answers = $access_plan['access_plan_learner_supp_answers'];
                                            foreach ($access_plan_learner_supp_answers as $access_plan_learner_supp_answer) {
                                                foreach ($access_plan_learner_supp_answer['supplementary_questions'] as $supplementary_question_learner) {
                                                    $supplementary_question_learner_title = $supplementary_question_learner['title'];
                                                    $answer = $supplementary_question_learner['user_answer'];
                                                    $answer_array = explode(',', $answer);
                                                    foreach ($answer_array as $single_answer) {

                                                        dev_debug("Question:" . $supplementary_question_title . ' Answer:' . $single_answer);
                                                        $supp_questions_answers[$supplementary_question_title][trim($single_answer)]++;
                                                    }
                                                    //$supp_questions_answers[$supplementary_question_learner_title][$answer]++;
                                                }
                                            }


                                            $answers = explode(',', $supplementary_question['answers']);
                                            $answers_count = array();

                                            foreach ($answers as $answer) {
                                                $answer = trim($answer);
                                                //if ($supplementary_question_title !='') echo '<div class="col-sm-12">&nbsp;</div>';
                                                $total_answer = $supp_questions_answers[$supplementary_question['title']][$answer];
                                                $total_answer_percentage = round($total_answer / $access_plan_course['no_registered_users'] * 100);
                                                ?>


                                                <td><i><?php echo $total_answer ?></i></td>
                                                <td><?php echo $total_answer_percentage ?>%</td>


                                                <?php //$access_plan_course_title ='';

                                            }
                                            ?>
                                        </tr>


                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    <?php } ?>


                    <?php


                }
            } ?>
        </div>
    </div>

<?php }