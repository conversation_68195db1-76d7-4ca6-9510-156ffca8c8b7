<?php
include_once(engine_url . "/admin/inc/lib.inc.php");
function send_email($student_id)
{

    // ALLOCATE TO THE REVIEWER
    //////////////////////////////////////////////////////////////////////////////////////////////
    // send email notification to academic & admin
    //////////////////////////////////////////////////////////////////////////////////////////////

    list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study, $core_students_level_of_entry, $core_students_country_of_origin, $core_students_application_status, $core_students_has_applied, $core_students_archive_record, $unique_id, $core_student_application_route, $core_students_cohort_intake) = get_core_students($student_id);// if second value is defined then use it

    // get the id relating to the review team based on the assign table and find them
    $check_users = explode(",", pull_field("core_assignations", "db69836", "WHERE rel_id='$core_students_id' AND db69887 = '$_SESSION[school_cycle]'"));

    //get template
    $select_email_template = pull_field("coms_template", "id", "WHERE 1 AND (db1320='allocate_to_reviewer' OR db1147='25') AND usergroup='$_SESSION[usergroup]'") ?: pull_field("coms_template", "id", "WHERE 1 AND (db1320='allocate_to_reviewer' OR db1147='25') AND usergroup='1'");
    list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($select_email_template);

    //THRee from now date
    $Date = date("Y-m-d");
    $three_weeks_after_this_email = date('d M,Y', strtotime($Date . ' + 3 weeks'));

    foreach ($check_users as $user_detail) {

        $user_details = explode("|", pull_field("form_users", "concat(db106,'|',db119)", "WHERE id ='$user_detail'"));

        $email_to = $user_details[1];
        $email_rec_name = $user_details[0];

        //check if email already sent
        $check_if_already_sent = pull_field("form_email_log", "count(*)", "WHERE db1153='" . $email_to . "' AND rel_id='$unique_id' AND db1152='allocate_to_reviewer'");// get email from owner

        if ($check_if_already_sent == "0") {

            if (!$email_from) {
                $email_from = master_email;
                $email_from_title = "$email_add";
            } else {
                $email_from = master_email;
                //$email_from=pull_field("form_schools","db1118","WHERE id='$_SESSION[usergroup]'");//Main International Email Address
                $email_from_title = "$_SESSION[school_name]";
            }

            if ($select_email_template) {

                $subject = email_template_replace_values("{{cohort_id}}", $student_intake_id, $coms_template_subject_line);
                $message_html = $coms_template_html_version;

                $message_html = email_template_replace_values("{{reviewer}}", $email_rec_name, $message_html);
                $message_html = email_template_replace_values("{{course}}", $db_course_name, $message_html);
                $message_html = email_template_replace_values("{{name}}", $core_students_first_name . " " . $core_students_surname, $message_html);
                $message_html = email_template_replace_values("{{3weeks_after_this_email}}", $three_weeks_after_this_email, $message_html);
                $message_html = email_template_replace_values("{{deadline_date}}", $deadline_date, $message_html);
                $message_html = email_template_replace_values("{{link_url}}", $link_url, $message_html);
                $message_html = email_template_replace_values("{{allocated_by}}", $_SESSION['fullname'], $message_html);
                $message_html = email_template_replace_values("{{school_name}}", $_SESSION['school_name'], $message_html);


            } else {

                $subject = "An application has been allocated to you for review on the admissions portal";
                $link_url = $_SESSION['domain'] . "/engine/direct/proc?pg=4&vw=$unique_id&ref=$core_students_id";
                $message_plain = "Dear $email_rec_name
		
A new application has been allocated to you for review.
		
Here are the details
-----------------------------------------
Allocated to you by: $_SESSION[fullname]
Applicant's name: $core_students_first_name $core_students_surname
		
To view the application follow the link below:
(if the link is not clickable, please copy and paste it into the address bar in your browser.)
$link_url
		
Warm regards
		
$_SESSION[school_name] - Admissions Team 
";

                $message_html = text_to_html($message_plain);
            }//end if else

            // error checks
            if (!$email_to) {
                echo '<div class="warning">Sorry, you have not allocated any Reviewers to this application</div>';
            } else {

                log_email($email_to, $subject, $message_plain, $message_html, $email_from, "allocate_to_reviewer", $unique_id);
                echo "Alert email has been sent to the Nominated Reviewer ($email_to)<br/>";
            }
        }// end of already sent check
        else {
            echo "Email already Sent to $email_to<br/>";
        }

    }//end of for each loop
}

//add assign crud
if ($_POST['process'] == 'yes') {
    if (!verifyCsrfToken($_POST['_token'])) {
        die("CSRF token validation failed.");
    }
    $core_assign_id = $row['id'];
    $core_assign_rec_id = $_SESSION['uid'];
    $core_assign_usergroup = $_SESSION['usergroup'];
    $core_assign_rel_id = $_POST['applicant'];
    $core_assign_module = 2;//direct
    $core_assign_assigned_to = checkbox_to_string('reviewers');

    // connect to the database
    $dbh = get_dbh();

    //check if user already exists
    $num_actual = pull_field("core_assignations", "count(*)", "WHERE rel_id='$core_assign_rel_id' AND db69887='$_SESSION[school_cycle]' AND db69884='2'");

    if ($num_actual == 0) {
        //insert
        $sql = "INSERT INTO core_assignations 
	(id,username_id,rec_id,usergroup,rel_id,db69884,db69836,db69887) VALUES ('','" . random() . "','$core_assign_rec_id', '$core_assign_usergroup','$core_assign_rel_id','$core_assign_module','$core_assign_assigned_to','$_SESSION[school_cycle]')";
    } else {
        //update
        $sql = "UPDATE core_assignations SET db69836='$core_assign_assigned_to' WHERE rel_id='$core_assign_rel_id' AND db69887 = '$_SESSION[school_cycle]'";
    }

    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    dev_debug($sql);
    send_email($core_assign_rel_id);
    echo '<div class="alert alert-success">Assigments successfully applied</div>';

}//end if check
?>

<div class="row">

    <form method="post" name="process_form" action="">
        <div class="col-md-8">
            <h3>Applicants that have not yet been assigned</h3>
            <div class="table-responsive">
                <table class="sortable table  table-striped table-condensed">
                    <thead>
                    <tr>
                        <td>Applicants</td>
                        <td>Assigned Status</td>
                        <td>Country</td>
                        <td>Programme</td>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    $dbh = get_dbh();
                    $get_sql_applicants = "Select *,a.id as stuid,
			 (SELECT db232 FROM core_courses WHERE id=db889) as Programme
			 FROM core_students a LEFT JOIN core_assignations b ON a.id=b.rel_id WHERE a.usergroup='$_SESSION[usergroup]' AND db890='$_SESSION[school_cycle]' AND db41='12' AND db69836 IS NULL";
                    $stmt = $dbh->prepare($get_sql_applicants);
                    $stmt->execute();
                    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

                    dev_debug($get_sql_applicants);

                    foreach ($results as $row_a) {
                        ?>
                        <tr>
                            <td>
                                <input type="radio" name="applicant"
                                       value="<?= $row_a['stuid'] ?>"> <?= $row_a['db39'] . ' ' . $row_a['db40'] ?>
                            </td>
                            <td><?= ($row_a['db69836'] ? 'Yes' : 'No') ?></td>
                            <td><?= $row_a['db763'] ?></td>
                            <td><?= $row_a['Programme'] ?></td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                    <tfoot>
                    <tr>
                        <td></td>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>

        <div class="col-md-4 bg-info">
            <br/>
            <button type="submit" class="btn btn-warning pull-right">Assign</button>
            <h3>Select Reviewers</h3>
            <div class="table-responsive">
                <table class="sortable table  table-striped table-condensed">
                    <thead>
                    <tr>
                        <td>Reviewers</td>
                    </tr>
                    </thead>
                    <tbody>
                    <?php
                    $dbh = get_dbh();
                    $get_sql = "Select * FROM form_users WHERE usergroup='$_SESSION[usergroup]' AND db112=8 AND (rec_archive IS NULL OR rec_archive = '')";
                    $stmt = $dbh->prepare($get_sql);
                    $stmt->execute();
                    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    $add_prefix = pull_field('lead_preferences', 'db106910', "WHERE usergroup = {$_SESSION['usergroup']}");
                    foreach ($results as $row) {
                        ?>
                        <tr>
                            <td><input type="checkbox" name="reviewers[]"
                                       value="<?= $row['id'] ?>"> <?= $row['db106'] . ' ' . $row['db111'] . ($add_prefix == 'yes' ? ' - ' . $row['db1262'] : '') ?>
                            </td>
                        </tr>
                        <?php
                    }
                    ?>
                    </tbody>
                    <tfoot>
                    <tr>
                        <td></td>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>
        <input type="hidden" name="process" value="yes">
        <input type="hidden" name="_token" value="<?= generateCsrfToken() ?>">
    </form>

</div>
