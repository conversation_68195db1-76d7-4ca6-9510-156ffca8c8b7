<?php


// 	ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);


include("../admin/inc/lib.inc.php");


//check if the file exists
$external_payments_url = base_path . "/static/" . $_SESSION['subdomain'] . "/admin/pay_config.php";// link to external admin file

// then check if it has a custom rejection function
if (file_exists($external_payments_url)) {

    //get stripe files
    define('CURL_SSLVERSION_TLSv1_2', true);
    require_once(base_path . 'engine/tools/stripe/stripe-php-5.7.0/init.php');

    // $curl = new \Stripe\HttpClient\CurlClient(array(CURLOPT_SSLVERSION => CURL_SSLVERSION_TLSv1_2));
    // \Stripe\ApiRequestor::setHttpClient($curl);

    //get config file
    require_once("$external_payments_url");// call custom rejection function
} else {
    die("sorry payments config file was not found...");
}

//trigger exception in a "try" block
try {

    $token = $_POST['stripeToken'];
    $email = $_POST['stripeEmail'];
    $payment_date = date("Y-m-d");

    $customer = \Stripe\Customer::create(array(
        'email' => $email,
        'source' => $token
    ));

    $charge = \Stripe\Charge::create(array(
        'customer' => $customer->id,
        'amount' => 5000,
        'currency' => 'usd'
    ));

    $normalised_amount = number_format($charge['amount'] / 100);
    // echo "Your payment of ".$normalised_amount." $charge[currency] was successful";
    // echo "<br/>$follow on message";


    //Insert into the DB
    $dbh = get_dbh();
    $sth = $dbh->prepare("INSERT INTO sis_student_fees (username_id, rec_id, usergroup, rel_id, db1492, db1493, db37345, db1495,
		   db15459,db34450)VALUES (?,?,?,?,?,?,?,?,?,?)");
    $sth->execute(array(random(), session_info("uid"), session_info("access"), floating_info("ref"),
        'stripe', 'Registration Fee', $token, $normalised_amount, $payment_date, 'Payment'));


} //catch exception
catch (Exception $e) {
    echo 'Message: ' . $e->getMessage();
}

	

