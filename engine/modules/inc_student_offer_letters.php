<?php /** @noinspection DuplicatedCode */

include_once("../admin/inc/lib.inc.php");
include_once(base_path . "/admin/config.php");
chk_login(); //check if user is logged in


//Get TWIG
$file = ABSPATH . "app/libs/vendor/autoload.php";
if (file_exists($file)) {
    require_once($file);
}

//GET STUDENT DETAILS
//------------------------------------
$data = explode(",", $_GET['data']);
$data_title = $data[0]; // title
$data_name = $data[1]; // name
$data_surname = $data[2]; // surname
$data_course = $data[3]; // subject offered

$data_date = $data[4]; // date

$dload_date = date('l jS \of F Y');
//if accessed from admin show this
if ($_GET['ref']) {
    $uids = pull_field("dir_appli_checklist", "id", "WHERE rel_id = '$_GET[ref]'");
    $candidate_id = $_GET['ref'];
    // pull in bootstrap
    ?>

    <?php if(empty($_GET['admin'])) : ?>
        <!-- Bootstrap core CSS -->
        <link href="../../static/<?php echo $_SESSION['subdomain']; ?>/resources/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    <?php
}

if ($_GET['rec']) {
    $final_offer1 = $_GET['rec'];
}

if (!empty($_GET['preview_offer'])){

    $dir_offers_id = '';
    $dir_offers_date = date('Y-m-d H:i:s');
    $dir_offers_rel_id = $_REQUEST['ref'];
    $dir_offers_offer_title = $_REQUEST['db1812'];
    $dir_offers_offer_type = $_REQUEST['db1799'];
    $dir_offers_select_template = $_REQUEST['db1811'];
    $dir_offers_terms_of_offer = $_REQUEST['db1800'];
    $dir_offers_course_offered = $_REQUEST['db1801'];
    $dir_offers_course_code = $_REQUEST['db1802'];
    $dir_offers_course_level = $_REQUEST['db1803'];
    $dir_offers_level_of_entry = $_REQUEST['db1804'];
    $dir_offers_ucas_code = $_REQUEST['db1805'];
    $dir_offers_course_commencement_date = $_REQUEST['db1806'];
    $dir_offers_duration_of_course = $_REQUEST['db1807'];
    $dir_offers_enrolment_and_inductions_date = $_REQUEST['db1808'];
    $dir_offers_academic_session = $_REQUEST['db1809'];
    $dir_offers_course_fee = $_REQUEST['db1810'];
    $dir_offers_course_completion = $_REQUEST['db1814'];
    $dir_offers_intake = $_REQUEST['db18720'];
    $dir_offers_additional_1 = str_replace("_", " ", $_REQUEST['db26221']);
    $dir_offers_additional_2 = str_replace("_", " ", $_REQUEST['db26223']);
    $dir_offers_additional_3 = str_replace("_", " ", $_REQUEST['db26224']);
    $dir_offers_additional_4 = str_replace("_", " ", $_REQUEST['db26225']);
    $dir_offers_terms_and_conditions = str_replace("_", " ", $_REQUEST['db33623']);
}else{
    list($dir_offers_id, $dir_offers_date, $dir_offers_rec_id, $dir_offers_usergroup, $dir_offers_rel_id, $dir_offers_offer_title, $dir_offers_offer_type, $dir_offers_select_template, $dir_offers_terms_of_offer, $dir_offers_course_offered, $dir_offers_course_code, $dir_offers_course_level, $dir_offers_level_of_entry, $dir_offers_ucas_code, $dir_offers_course_commencement_date, $dir_offers_duration_of_course, $dir_offers_enrolment_and_inductions_date, $dir_offers_academic_session, $dir_offers_course_fee, $dir_offers_course_completion, $dir_offers_intake, $dir_offers_username_id, $dir_offers_additional_1, $dir_offers_additional_2, $dir_offers_additional_3, $dir_offers_additional_4, $dir_offers_terms_and_conditions, $dir_offers_date_time_signing) = get_dir_offers("unique_id", $final_offer1);
}
// list pull
// FUNCTION TO GET_DIR_OFFERS
$dir_offers_enrolment_and_inductions_date = str_replace('_', ' ', $dir_offers_enrolment_and_inductions_date);
if ($dir_offers_intake && $dir_offers_intake != '') {
    $course_offered = pull_field("dir_cohorts", "db18719", "WHERE id=$dir_offers_intake");
    $dir_offers_course_commencement_date = pull_field("dir_cohorts", "DATE_FORMAT(db1678,'%d %M %Y')", "WHERE id=$dir_offers_intake");
    $dir_offers_course_end_date = pull_field("dir_cohorts", "DATE_FORMAT(db1679,'%d %M %Y')", "WHERE id=$dir_offers_intake");
    $dir_offers_year = pull_field("dir_cohorts", "db1680", "WHERE id=$dir_offers_intake");
    $dir_offers_term = pull_field("dir_cohorts", "db32333", "WHERE id=$dir_offers_intake");
    $dir_offers_intake_value = pull_field("dir_cohorts", "db1681", "WHERE id=$dir_offers_intake");
} else {
    $course_offered = $dir_offers_course_offered; //$dir_appli_checklist_conditional_course_offered;
}
$offer_conditions = $dir_offers_terms_of_offer;//$dir_appli_checklist_terms_of_offer;
$course_level_id = $dir_offers_course_level;

//$offer_letter_id = $dir_offers_select_template;

// get all the details of the student
//------------------------------------
// GET LETTER_LIBRARY
//------------------------------------
//
//see if the template is passed in
if ($_GET['pid'] && $_GET['pid'] != '') {
    $lid = $_GET['pid'];
} else {
    $lid = $dir_offers_select_template;
}

if (!$lid) {
    die("Sorry, there seems to be a problem. Please use the <a href=\"checklist\">communication inbox</a> to notify admin");
}

/*if($_SESSION['uid']=='332'){
echo $offer_letter_id;
}*/

$usergroups = usergroups_management();
$dbh = get_dbh();

// get the student data

// grab the letter
$sql = "SELECT * FROM core_letter_library WHERE $usergroups AND id = '$lid'";
dev_debug($sql);
$stmt = $dbh->prepare($sql);
$stmt->execute();
$row = $stmt->fetch(PDO::FETCH_ASSOC);

$sis_letter_library_id = $row['id'];
$sis_letter_library_rec_id = $row['rec_id'];
$sis_letter_library_usergroup = $row['usergroup'];
$sis_letter_library_rel_id = $row['rel_id'];
$sis_letter_library_letter_title = $row['db1487'];
$sis_letter_library_content = $row['db1488'];
$sis_letter_library_description = $row['db1489'];
$sis_letter_library_publish = $row['db1490'];
$sis_letter_library_date = $row['date'];
$sis_include_terms_and_conditions = $row['db49181'];

$logo = '<div><div style=" float:right;">';

//$logo_file = base_path.'/engine/'.$_SESSION['school_logo'];
//echo json_encode($_SESSION);
//exit();
if (!empty($_SESSION['school_logo'])) {
    if (!empty($_GET['pdf'])) {
        $logo_file = str_replace(".co.uk", ".com", $_SERVER['DOCUMENT_ROOT'] . "/../private/media/" . $_SESSION['school_logo']);
        if (file_exists($logo_file)) $logo .= '<img src="' . $logo_file . '" style="padding:20px;max-height:150px;" />';
    } else {
        $logo_file = engine_url . "/media/dl.php?fl=" . encode($_SESSION['school_logo']);
        $logo .= '<img src="' . $logo_file . '" style="padding:20px;max-height:150px;" class="img-responsive" />';
    }
}
// if(file_exists($logo_file)){

//else{
//	 $logo .= '<img src="'.engine_url.'/images/acestar_logo.gif" width="390" height="226" />';
// }
/*
$logo_file = base_path.'/engine/images/logos/'.$_SESSION['school_logo'];
$logo .= '<img src="'.engine_url.'/images/logos/'.$_SESSION['school_logo'].'" style="padding:20px;" />';

$logo.='
<div>
      <div style=" float:right;">';
$logo_file=base_path.'/engine/images/logos/'.$_SESSION['subdomain'].'.gif';
if(file_exists($logo_file)){
	$logo.='<img src="'.$logo_file.'" style="padding:20px;" />';
}
$logo_file = base_path.'/engine/images/logos/'.$_SESSION['school_logo'];
$logo .= '<img src="'.engine_url.'/images/logos/'.$_SESSION['school_logo'].'" style="padding:20px;" />';

$logo.='</div>
<div style="clear:both"></div>';
*/
$logo .= '</div>
<div style="clear:both"></div>';
if ($dir_offers_rel_id && $dir_offers_rel_id != '') {

    // get student full name
    $student_variables = explode("|", pull_field("core_students", "concat_ws('|',db39,db40,rec_id,db888)", "WHERE id=$dir_offers_rel_id"));

    $full_name = $student_variables[0] . ' ' . $student_variables[1];
    $firstname = $student_variables[0];
    $surname = $student_variables[1];
    $student_rec_id = $student_variables[2];
    $student_number = $student_variables[3];

    if ($_SESSION['usergroup'] == 27) {
        $line1 = "db1167,";
        $line1 .= "db846,";
        $line1 .= "db845,";
        $line1 .= "db1168,";
        $line1 .= "db1169,";
        $line1 .= "db1170";
        $address = pull_field("dir_registration1", "concat_ws(',',$line1)", "WHERE rel_id=$dir_offers_rel_id");
        $address_lines = explode(",", $address);

        foreach ($address_lines as $lines) {
            if ($lines) {
                $address_data .= $lines . '<br/>';
            }
        }

        $address = $address_data;

    } else {
        $address = pull_field("dir_registration1", "concat(db1167,\"<br/>\", db1168, \"<br/>\", db1169, \"<br/>\" ,db1170)", "WHERE rel_id=$dir_offers_rel_id");
    }
    $country = pull_field("dir_registration1", "db1171", "WHERE rel_id=$dir_offers_rel_id");
    $course_deposit = pull_field("form_payment_options", "db15877", "WHERE rel_id = $course_offered");
    $include_VAT = pull_field("form_payment_options", "db15879", "WHERE rel_id = $course_offered");
    //$location = pull_field("core_course_locations","CONCAT(db32336,', ',db32200)","WHERE rel_id = $course_offered");
    $location = pull_field("core_course_locations_rel a,core_course_locations b", "CONCAT(db32336,', ',db32200)", "WHERE a.db32202=b.id AND a.rel_id='$course_offered' "); //db32194 = location

    // parents
    $dbh = get_dbh();
    $sql_parents_info = "SELECT id as data_id, db106 as parent_name, db111 as parent_surname FROM form_users WHERE db112=7 AND id=$student_rec_id";
    dev_debug($sql_parents_info);
    $sql_parent = $dbh->prepare($sql_parents_info);
    $sql_parent->execute();
    $parent_info = $sql_parent->fetchAll(\PDO::FETCH_OBJ);
    $parent_infos = $parent_info[0];
    $parent_id = $parent_infos->data_id;
    $parent_name = $parent_infos->parent_name;
    $parent_surname = $parent_infos->parent_surname;

    if ($include_VAT == 'yes') {
        $vat_percent = pull_field("lead_preferences", "db15030", "WHERE usergroup = $_SESSION[usergroup]");
        $course_deposit = $course_deposit + ($course_deposit * $vat_percent) / 100;
    }

    $course_deposit = '£' . number_format($course_deposit, 2);
    $course = pull_field("core_courses", "db232", "WHERE id=$course_offered");

    // grab the course location
    $sql = "SELECT
      *,db1009 as main_contact_details
    FROM
      core_course_locations
    LEFT JOIN core_course_locations_rel on core_course_locations_rel.db32202 = core_course_locations.id
    LEFT JOIN core_contact u on u.id=db37244
    WHERE
      1
    AND core_course_locations_rel.rel_id='$course_offered'";
    //echo $sql;exit();
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

    $location_title = $row['db37260'];
    $location_main_contact = $row['db37244'];
    $location_telephone = $row['db37243'];
    $location_address_line_1 = $row['db37240'];
    $location_address_line_2 = $row['db37241'];
    $location_city = $row['db32336'];
    $location_country = $row['db32200'];
    $location_additional_information = $row['db41794'];


    if ($data_date && $data_date != '') {
        $dir_offers_date = $data_date;
    }
} else {
    if (!$data_date || $data_date == '') {
        //$data_date = date('l jS \of F Y');
        $data_date = date('d-m-Y');
    }
    $firstname = $data_name;
    $course = $data_course;
    $dir_offers_date = $data_date;
}

if (!$student_number || $student_number == '') {
    $student_number = $_GET['ref'];
}
$sis_letter_terms_conditions = '';
if($_SESSION['usergroup'] == 389){
	//intro in this column db1802
	$introID =  pull_field('dir_offers', 'db1802', "where id =$dir_offers_id");
    if (!empty($_GET['preview_offer'])){
        $introID = implode(',',$_REQUEST['db1802']);
    }
	$intro= pull_field('dir_offer_meta', "group_concat(db302345 SEPARATOR '\n' )", "WHERE id in ({$introID}) order by db304910");
	
	//terms in this column db1800
	$termsID =  pull_field('dir_offers', 'db1800', "where id =$dir_offers_id");
    if (!empty($_GET['preview_offer'])){
        $termsID = implode(',',$_REQUEST['db1800']);
    }
	$terms = pull_field('dir_offer_meta', "group_concat(db302345 SEPARATOR '\n' )", "WHERE id in ({$termsID}) order by db304910");
	
	//deposit in this column db1805
	$depositID =  pull_field('dir_offers', 'db1805', "where id =$dir_offers_id");
    if (!empty($_GET['preview_offer'])){
        $depositID = implode(',',$_REQUEST['db1805']);
    }
	$deposit = pull_field('dir_offer_meta', "group_concat(db302345 SEPARATOR '\n' )", "WHERE id in ({$depositID}) order by db304910");
	
	//priority tag
	$priorityID = pull_field('dir_offers', 'db1809', "where id =$dir_offers_id");
    if (!empty($_GET['preview_offer'])){
        $priorityID = implode(',',$_REQUEST['db1809']);
    }
	$priority = pull_field('dir_offer_meta', "group_concat(db302345 SEPARATOR '\n' )", "WHERE id in ({$priorityID})order by db304910");
	
	//acceptance db1807
	$acceptanceID = pull_field('dir_offers', 'db1807', "where id =$dir_offers_id");
    if (!empty($_GET['preview_offer'])){
        $acceptanceID = implode(',',$_REQUEST['db1807']);
    }
	$acceptance = pull_field('dir_offer_meta', "group_concat(db302345 SEPARATOR '\n' )", "WHERE id in ({$acceptanceID}) order by db304910");
	
	//sign off on this column db1804
	$signoffID = pull_field('dir_offers', 'db1804', "where id =$dir_offers_id");
    if (!empty($_GET['preview_offer'])){
        $signoffID = implode(',',$_REQUEST['db1804']);
    }
	$signoff = pull_field('dir_offer_meta', "group_concat(db302345 SEPARATOR '\n' )", " WHERE id in ({$signoffID}) order by db304910");
	
	$sis_letter_library_content = $sis_letter_library_content. $intro . $terms . $deposit .$priority . $acceptance. $signoff;
	
	$applicantID =    $dir_offers_rel_id ;

    $yearGroup = pull_field("core_students", "db889", "where id={$applicantID}");
    $type = pull_field("core_students", "db510", "where id={$applicantID}");
	$school_type = 'Wymondham College';
	$termsAndConditionsID = '';
	if ($yearGroup == "24719" || $yearGroup == "24722") {
		$school_type = 'Wymondham College Prep School';
        if ($type == 'Boarding'){
	        $termsAndConditionsID = '3392';
        }
	}elseif($yearGroup == '24725' || $yearGroup == '24728' || $yearGroup == '24731' || $yearGroup == '24734' || $yearGroup == '24737'){
		if ($type == 'Boarding'){
			$termsAndConditionsID = '3395';
		}
	}else{
		if ($type == 'Boarding'){
			$termsAndConditionsID = '3398';
		}
    }
    
    if (!empty($termsAndConditionsID)){
	    $sis_letter_terms_conditions = pull_field("core_letter_library", "db1488", "WHERE id='{$termsAndConditionsID}' AND usergroup='$_SESSION[usergroup]' AND (rec_archive = '' OR rec_archive IS NULL) ORDER BY id DESC LIMIT 1");
    }
	
	//parent salutation
	$parentSalutation = pull_field('core_parents',"concat_ws(' ', db12158,db12157,db12170)", "WHERE rel_id={$applicantID}");
	$sis_letter_library_content = str_replace("{{parent_salutation}}", $parentSalutation, $sis_letter_library_content);
	
    //year group
    $appliedYearGroup =  pull_field('core_courses', 'db232', "WHERE id={$yearGroup}");
	$sis_letter_library_content = str_replace("{{year_group}}", $appliedYearGroup, $sis_letter_library_content);
	
	//school_type Wymondham College/Wymondham College Prep School based on year group
	$sis_letter_library_content = str_replace("{{school_type}}", $school_type, $sis_letter_library_content);
	
	//scholarship_type
	//scholarship_percentage
	//gender_noun
	$gender = pull_field("core_students", "db44", "where id={$applicantID}");
	$gender_noun = 'hers';
	if ($gender == "Male") {
		$gender_noun = 'his';
	}
	$sis_letter_library_content = str_replace("{{gender_noun}}", $gender_noun, $sis_letter_library_content);
 
	
}
 if(empty($sis_letter_terms_conditions)){
	 $sis_letter_terms_conditions = ($sis_include_terms_and_conditions == 'no') ? FALSE : pull_field("core_letter_library", "db1488", "WHERE db33619='2' AND usergroup='$_SESSION[usergroup]' AND (rec_archive = '' OR rec_archive IS NULL) ORDER BY id DESC LIMIT 1"); //
 }

if ($sis_letter_terms_conditions) {

    $sql_1 = 'SELECT count(*) FROM dir_offers WHERE db1813 LIKE\'%Accepted%\' AND id=\'' . $dir_offers_id . '\'';
    $sql_2 = 'SELECT count(*) FROM dir_offers WHERE db1813 LIKE\'%Declined%\' AND id=\'' . $dir_offers_id . '\'';
    $dbh = get_dbh();
    $sql = $dbh->prepare($sql_1);
    $sql->execute();
    $has_accepted = $sql->fetchColumn();

    $dbh = get_dbh();
    $sql = $dbh->prepare($sql_2);
    $sql->execute();
    $has_rejected = $sql->fetchColumn();

    if ($has_rejected == 1 || $has_accepted == 1) {
        $appli_offer_submited = 0;
    } else {
        $appli_offer_submited = 1;
    }

    if ($dir_offers_terms_and_conditions !== '') {
        $sis_letter_library_content = $sis_letter_library_content . ' ' . $dir_offers_terms_and_conditions;
    } else {
        $sis_letter_library_content = $sis_letter_library_content . '<div id="accept_terms"><br pagebreak="true"/>' . $sis_letter_terms_conditions . '</div>';
    }

    if ($appli_offer_submited == 1) {
        ob_start();
        terminology('Review Terms and Accept Offer', curPageURL(), 'Review Terms and Accept Offer Button');
        $accept_button_text_overide = ob_get_contents();
        ob_end_clean();
    } else {
        //Append accept button
        $accept_button_text_overide = "Review Terms";
    }
} else {
    $sis_letter_terms_conditions = "";
}


if ($_SESSION['usergroup'] == 92) {
    //PERIOD OF STUDY

    //Home Institution
    $home_institution = pull_field("dir_registration3 INNER JOIN core_universities ON db805 = core_universities.id", "db20163", "WHERE dir_registration3.rel_id='$dir_offers_rel_id' and (dir_registration3.rec_archive is null or dir_registration3.rec_archive ='')");
    $sis_letter_library_content = str_replace("{{home_institution}}", $home_institution, $sis_letter_library_content);

    $custom_student_data = explode("|", pull_field("core_students", "CONCAT(db763,'|',db44,'|',db1682,'|',db28467,'|',db1105,'|',db53,'|',db25618)", "WHERE id='$dir_offers_rel_id' AND usergroup='$_SESSION[usergroup]'"));
    $period_of_study = str_replace("_", " ", $custom_student_data[6]);
    $sis_letter_library_content = str_replace("{{period_of_study}}", $period_of_study, $sis_letter_library_content);

}
if ($_SESSION['usergroup'] == 49) {
    $student_coodinators_data = explode("|", pull_field("core_students b,dir_offers m, form_users u", "CONCAT_WS('|',db106,db111)", "WHERE m.rel_id=b.id AND m.db26221=u.id AND b.id = '$dir_offers_rel_id' AND b.usergroup='$_SESSION[usergroup]' "));
    $school_coodinators_data = explode("|", pull_field("core_students b,dir_offers m, form_users u", "CONCAT_WS('|',db106,db111)", "WHERE m.rel_id=b.id AND m.db26223=u.id AND b.id = '$dir_offers_rel_id' AND b.usergroup='$_SESSION[usergroup]' "));
    $intake_data = explode("/", pull_field("dir_cohorts a,core_students b", "concat(db1678,'/',db1679)", "  WHERE a.id=b.db1682 AND b.id = '$dir_offers_rel_id' AND b.usergroup='$_SESSION[usergroup]' "));
    $sis_letter_library_content = str_replace("{{offer_student_coordinator_first_name}}", $student_coodinators_data[0], $sis_letter_library_content);
    $sis_letter_library_content = str_replace("{{offer_student_coordinator_last_name}}", $student_coodinators_data[1], $sis_letter_library_content);
    $sis_letter_library_content = str_replace("{{offer_school_coordinator_first_name}}", $school_coodinators_data[0], $sis_letter_library_content);
    $sis_letter_library_content = str_replace("{{offer_school_coordinator_last_name}}", $school_coodinators_data[1], $sis_letter_library_content);
    $sis_letter_library_content = str_replace("{{applicant.intake.start_date}}", format_date("j F Y", $intake_data[0]), $sis_letter_library_content);
    $gurantordata = explode("|", pull_field("core_contact", "CONCAT_WS('|',concat(db211,' ',db1092),db42881,db42882,db42883,db42884,db42885,db42886,db42887)", "WHERE id = '$location_main_contact' AND usergroup='$_SESSION[usergroup]' "));
    $offerterms = pull_field("core_students b,dir_offers m", "db1800", "WHERE m.rel_id=b.id AND b.id = '$dir_offers_rel_id' AND b.usergroup='$_SESSION[usergroup]' ");

    $sis_letter_library_content = str_replace("{{offer_terms_go_here}}", $offerterms, $sis_letter_library_content);
    //echo "<pre>".print_r($gurantordata,1)."</pre>";exit();
    $temp_keysarray = ["Name:", "Tel:", "Address:", "Date of birth:", "Sex:", "Relationship to applicant:", "Profession or occupation and position:", "Nationality and immigration status:"];
    $info = "";
    foreach ($gurantordata as $key => $value) {
        if ($value != "") {
            $info .= $temp_keysarray[$key] . " " . $value . "\n";
        }
    }
    $location_main_contact = $info;
}

//if VAT is on add the vat to the course deposit)

//echo $full_name.'-';
if (77 == $_SESSION['usergroup']) {
    $ug77dob = pull_field("core_students b,dir_offers m", "db53", "WHERE m.rel_id=b.id AND b.id = '$dir_offers_rel_id' AND b.usergroup='$_SESSION[usergroup]' ");
    $sis_letter_library_content = str_replace("{{dob}}", format_date("j F Y", $ug77dob), $sis_letter_library_content);
}

if (94 == $_SESSION['usergroup']) {
    $dir_offers_additional_1 = pull_field('sis_module', 'db1428', "WHERE id='{$dir_offers_additional_1}'");
    $dir_offers_additional_2 = pull_field('sis_module', 'db1428', "WHERE id='{$dir_offers_additional_2}'");
    $dir_offers_additional_3 = pull_field('sis_module', 'db1428', "WHERE id='{$dir_offers_additional_3}'");
    $dir_offers_additional_4 = pull_field('sis_module', 'db1428', "WHERE id='{$dir_offers_additional_4}'");
}



// fill the template
$sis_letter_library_content = str_replace("{{date}}", format_date("j F Y", $dir_offers_date), $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{DATE}}", format_date("j F Y", $dir_offers_date), $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{offer_letter_date}}", format_date("j F Y", $sis_letter_library_date), $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{course_commencement_date}}", format_date("j F Y", $dir_offers_course_commencement_date), $sis_letter_library_content);

$sis_letter_library_content = str_replace("{{course_intake_start_date}}", format_date("j F Y", $dir_offers_course_commencement_date), $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{course_intake}}", $dir_offers_intake_value, $sis_letter_library_content);
//replace with correct date format course_commencement_date}}

$sis_letter_library_content = str_replace("{{course_end_date}}", $dir_offers_course_end_date, $sis_letter_library_content);//replace with correct date format course_commencement_date}}
//$dir_offers_course_completion date is already formatted so don't format
$sis_letter_library_content = str_replace("{{course_completion}}", $dir_offers_course_completion, $sis_letter_library_content);//replace with correct date $dir_offers_course_completion
$sis_letter_library_content = str_replace("{{full_name}}", $full_name, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{firstname}}", $firstname, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{first_name}}", $firstname, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{surname}}", $surname, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{child_name}}", $firstname, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{student_id}}", ($student_number ?: $candidate_id), $sis_letter_library_content);

$sis_letter_library_content = str_replace("{{course}}", $course, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{COURSE}}", $course, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{offer_condition}}", $offer_conditions, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{offer_type}}", $dir_offers_offer_type, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{course_code}}", $dir_offers_course_code, $sis_letter_library_content);
//$dir_offers_enrolment_and_inductions_date date is already formatted so don't format
$sis_letter_library_content = str_replace("{{enrolment_induction_date}}", $dir_offers_enrolment_and_inductions_date, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{address}}", $address, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{country}}", $country, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{student_number}}", $student_number, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{applicant_id}}", $student_number, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{course_deposit}}", $course_deposit, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{course_deposit_value}}", $course_deposit, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{course_option_1}}", $dir_offers_additional_1, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{course_option_2}}", $dir_offers_additional_2, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{course_option_3}}", $dir_offers_additional_3, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{course_option_4}}", $dir_offers_additional_4, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{year}}", $dir_offers_year, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{term}}", $dir_offers_term, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{location}}", $location, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{parent_name}}", $parent_name, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{parent_surname}}", $parent_surname, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{date_time_signing}}", $dir_offers_date_time_signing, $sis_letter_library_content);

#$sis_letter_library_content = str_replace("{{company_name}}",$company_name,$sis_letter_library_content);//Name of Placement Organisation
#$sis_letter_library_content = str_replace("{{student_university_name}}",$home_university,$sis_letter_library_content); //Home University


$sis_letter_library_content = str_replace("{{applicant.course.locations[0].title}}", $location_title, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{applicant.course.locations[0].main_contact}}", nl2br($location_main_contact), $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{applicant.course.locations[0].telephone}}", $location_telephone, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{applicant.course.locations[0].address_line_1}}", $location_address_line_1, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{applicant.course.locations[0].address_line_2}}", $location_address_line_2, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{applicant.course.locations[0].city}}", $location_city, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{applicant.course.locations[0].country}}", $location_country, $sis_letter_library_content);
$sis_letter_library_content = str_replace("{{applicant.course.locations[0].additional_information}}", $location_additional_information, $sis_letter_library_content);
$parent = explode("|", pull_field('form_users', 'concat_ws("|", db106,db111,db119)', "WHERE id= (SELECT rec_id FROM core_students WHERE id='{$candidate_id}') AND db112='7'"));
$child_opts = array('is_child' => false);
if (!empty($parent)) {
    $child_opts['is_child'] = true;
}
if (strrpos($sis_letter_library_content, '{{preferred_letter_salutation}}') != false) {
    if ($child_opts['is_child']) {
        $preferred_letter_salutation = pull_field('core_student_parent_links', 'db74258', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' ORDER BY db73805 DESC");
    } else {
        $preferred_letter_salutation = pull_field('form_communication_option', 'db57293', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]'");
    }
    if ($preferred_letter_salutation == "yes") {
        //Use Joint Letter Salutation
        if ($child_opts['is_child']) {
            $preferred_letter_salutation = pull_field('core_student_parent_links', 'db74261', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' ORDER BY db73805 DESC");
        } else {
            $preferred_letter_salutation = pull_field('form_communication_option', 'db57289', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]'");
        }
    } else {
        //Use Single  Letter Salutation
        if ($child_opts['is_child']) {
            $preferred_letter_salutation = pull_field('core_student_parent_links', 'db74252', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' ORDER BY db73805 DESC");
        } else {
            $preferred_letter_salutation = pull_field('form_communication_option', 'db57287', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]'");
        }
    }
    $sis_letter_library_content = str_replace("{{preferred_letter_salutation}}", $preferred_letter_salutation, $sis_letter_library_content);

}

if (strrpos($sis_letter_library_content, '{{preferred_label_salutation}}') != false) {
    if ($child_opts['is_child']) {
        $preferred_label_salutation = pull_field('core_student_parent_links', 'db74258', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' ORDER BY db73805 DESC");
    } else {
        $preferred_label_salutation = pull_field('form_communication_option', 'db57293', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]'");
    }

    if ($preferred_label_salutation == "yes") {
        //Use Joint Label Salutation
        if ($child_opts['is_child']) {
            $preferred_label_salutation = pull_field('core_student_parent_links', 'db73808', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' ORDER BY db73805 DESC");
        } else {
            $preferred_label_salutation = pull_field('form_communication_option', 'db57290', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]'");
        }
    } else {
        //Use Single  Label Salutation
        if ($child_opts['is_child']) {
            $preferred_label_salutation = pull_field('core_student_parent_links', 'db74255', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' ORDER BY db73805 DESC");
        } else {
            $preferred_label_salutation = pull_field('form_communication_option', 'db57288', "WHERE rel_id= '$candidate_id' AND  usergroup = '$_SESSION[usergroup]'");
        }
    }
    $sis_letter_library_content = str_replace("{{preferred_label_salutation}}", $preferred_label_salutation, $sis_letter_library_content);

}

if (strrpos($sis_letter_library_content, '{{primary_contact_first_name}}') != false || strrpos($sis_letter_library_content, '{{primary_contact_surname}}') != false || strrpos($sis_letter_library_content, '{{primary_contact_second_name}}') != false) {
    $contact_info = explode('|', pull_field('core_student_parent_links l', "concat_ws('|', db12157 ,db12159,db12160)", "LEFT JOIN core_parents p  ON l.db64491=p.id WHERE l.rel_id= '$candidate_id' AND  l.usergroup = '$_SESSION[usergroup]' AND db73805 ='yes' ORDER BY db73805 DESC"));
    $sis_letter_library_content = str_replace('{{primary_contact_first_name}}', $contact_info[0], $sis_letter_library_content);
    $sis_letter_library_content = str_replace('{{primary_contact_second_name}}', $contact_info[1], $sis_letter_library_content);
    $sis_letter_library_content = str_replace('{{primary_contact_surname}}', $contact_info[2], $sis_letter_library_content);
}
$signature = '';
$imageName = '';
//check for signature
$currentSignature = pull_field("dir_offers", "db221606", "WHERE id = '$dir_offers_id'");
if ($currentSignature) {
    $svg = sigJsonToImage($currentSignature, array('penWidth' => 2));
    $imageName = env('FILES_STORAGE_PATH') . "/media/reports/" . random() . '.png';
    imagepng($svg, $imageName);
    $type = pathinfo($imageName, PATHINFO_EXTENSION);
    $data = file_get_contents($imageName);
    $base64 = 'data:image/' . $type . ';base64,' . base64_encode($data);
    if (!empty($_GET['pdf'])) {
        //for tcpdf
        $signature .= '<img src="@' . preg_replace('#^data:image/[^;]+;base64,#', '', $base64) . '">';
    } else {
        $signature .= '<img src="' . $base64 . '">';
    }

    @unlink($imageName);
}
$sis_letter_library_content = str_replace('{{signature}}', $signature, $sis_letter_library_content);
//Load TWIG
// $loader = new Twig_Loader_Array(array('index' => $sis_letter_library_content));
// $twig = new Twig_Environment($loader, array('strict_variables' => false));

// $dbh = new DB(array(
//   'host'=>HOSTNAME,
//   'username'=>DB_USERNAME,
//   'password'=>DB_PASSWORD,
//   'db'=>DB_NAME
// ));
// $students_model = new Students;
// $variables = array(
//   "applicant"=>$students_model->get(array('id'=>$dir_offers_rel_id)),
// );


// $sis_letter_library_content = $twig->render('index', $variables);

// echo '<pre>';
// print_r($sis_letter_library_content);
// exit();


$use_twig = pull_field("lead_preferences", "db62990", "WHERE usergroup=" . $_SESSION['usergroup']);
if ("on" == $use_twig) {
    $template_args = array(
        'email_html' => $sis_letter_library_content,
        "student_id" => $candidate_id,
        "including_defaults" => true
    );
    $sis_letter_library_content = \process_email_variables($template_args);
} else {
    $sis_letter_library_content = email_template_replace_values_from_db($sis_letter_library_content, $candidate_id, "applicant", "dir_offers|$dir_offers_id");//search replace mail merge section/function
}


//get todays date

/// fill the terms and conditions template... held separately here for rendering and saving into the offer
$sis_letter_terms_conditions = str_replace("{{date}}", format_date("j F Y", $dir_offers_date), $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{DATE}}", format_date("j F Y", $dir_offers_date), $sis_letter_terms_conditions);

$sis_letter_terms_conditions = str_replace("{{course_commencement_date}}", format_date("j F Y", $dir_offers_course_commencement_date), $sis_letter_terms_conditions);//replace with correct date format course_commencement_date}}

$sis_letter_terms_conditions = str_replace("{{course_end_date}}", $dir_offers_course_end_date, $sis_letter_terms_conditions);//replace with correct date format course_commencement_date}}
//$dir_offers_course_completion date is already formatted so don't format
$sis_letter_terms_conditions = str_replace("{{course_completion}}", $dir_offers_course_completion, $sis_letter_terms_conditions);//replace with correct date $dir_offers_course_completion
$sis_letter_terms_conditions = str_replace("{{full_name}}", $full_name, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{firstname}}", $firstname, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{course}}", $course, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{COURSE}}", $course, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{offer_condition}}", $offer_conditions, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{offer_type}}", $dir_offers_offer_type, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{course_code}}", $dir_offers_course_code, $sis_letter_terms_conditions);
//$dir_offers_enrolment_and_inductions_date date is already formatted so don't format
$sis_letter_terms_conditions = str_replace("{{enrolment_induction_date}}", $dir_offers_enrolment_and_inductions_date, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{address}}", $address, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{country}}", $country, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{student_number}}", $student_number, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{applicant_id}}", $student_number, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{course_deposit}}", $course_deposit, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{course_deposit_value}}", $course_deposit, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{course_option_1}}", $dir_offers_additional_1, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{course_option_2}}", $dir_offers_additional_2, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{course_option_3}}", $dir_offers_additional_3, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{course_option_4}}", $dir_offers_additional_4, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{year}}", $dir_offers_year, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{term}}", $dir_offers_term, $sis_letter_terms_conditions);
$sis_letter_terms_conditions = str_replace("{{location}}", $location, $sis_letter_terms_conditions);

//only show this tag if offer accepted
if ($has_accepted == 1) {
    $sis_letter_terms_conditions = str_replace("{{date_time_signing}}", $dir_offers_date_time_signing, $sis_letter_terms_conditions);
} else {
    $sis_letter_terms_conditions = str_replace("{{date_time_signing}}", '', $sis_letter_terms_conditions);
}


$use_twig = pull_field("lead_preferences", "db62990", "WHERE usergroup=" . $_SESSION['usergroup']);
if ("on" == $use_twig) {
    $template_args = array(
        'email_html' => $sis_letter_terms_conditions,
        "student_id" => $candidate_id,
        "including_defaults" => true
    );
    $sis_letter_terms_conditions = \process_email_variables($template_args);
} else {
    $sis_letter_terms_conditions = email_template_replace_values_from_db($sis_letter_terms_conditions, $candidate_id, "applicant", "dir_offers|$dir_offers_id");//search replace mail merge section/function
}


$rendered_terms_and_conditions_text = htmlspecialchars("$sis_letter_terms_conditions");
//check if they want logo to be shown
$show_logo = pull_field("form_schools", "db295", "WHERE id='$_SESSION[usergroup]' ");
if ($show_logo != 'no') {
    $sis_letter_library_content = "$logo $sis_letter_library_content";
}
if ($_GET['pdf'] == '1') {
    //SEND TO PDF


// set some session variable to be added to pdf
    $_SESSION['print_msg'] = "$sis_letter_library_content";

    $title = str_replace(' ', '-', $sis_letter_library_letter_title);
    $_SESSION['file_title'] = $title . "-" . $dload_date;

    if ($_GET['get_offer']) {
        echo $_SESSION['print_msg'];
        die();
    }

    // send to PDF creator
    if(!empty($_GET['use_other'])){
        $root = realpath(__DIR__ . '/../../');
        $folder = $_SESSION['subdomain'];
        $resources = $root . "/static/" . $folder . "/resources/";
        $stylesheet = "table, th, td {
            border:1px solid black;
            }
         ";
        require_once $root . '/vendor/autoload.php';

        $defaultConfig = (new Mpdf\Config\ConfigVariables())->getDefaults();
        $fontDirs = $defaultConfig['fontDir'];

        $defaultFontConfig = (new Mpdf\Config\FontVariables())->getDefaults();
        $fontData = $defaultFontConfig['fontdata'];

//    $myFonts = [
//        'syntax-roman' => ['R' => 'syntax-regular.ttf', 'I' => 'SyntaxItalic.ttf'],
//        'vladimir' => ['R' => 'vladimir-script-regular.ttf', 'I' => 'vladimir-script-regular.ttf']
//    ];

        if (!file_exists($root . '/storage/')) mkdir($root . '/storage');
        if (!file_exists($root . '/storage/temp')) mkdir($root . '/storage/temp');

        $config = [
            'fontDir' => array_merge($fontDirs, [$resources . '/fonts/']),
            'fontdata' => $fontData,
            'default_font' => 'syntax-roman',
            'tempDir' => media_store . '/reports/',
            'orientation' => 'P'
        ];
        //exit($certificate);
        $mpdf = new \Mpdf\Mpdf($config);
        header('Content-Type: application/pdf');
        $filename = "Offer Letter - " . date('d-m-Y H:i:s') . '.pdf';
        $mpdf->autoPageBreak = true;
        $mpdf->setTitle( $_SESSION['file_title']);
        $mpdf->SetAuthor($_SESSION['school_name']);
        $mpdf->SetCreator('HeiApply.com');
        $mpdf->SetSubject( $_SESSION['file_title']);
        $mpdf->SetKeywords("offer_letter, {$_SESSION['file_title']}");
        $mpdf->WriteHTML($stylesheet, \Mpdf\HTMLParserMode::HEADER_CSS);
        $mpdf->WriteHTML($_SESSION['print_msg'], \Mpdf\HTMLParserMode::HTML_BODY);
        $mpdf->Output($filename, 'D');
    }else{
        header('Location: ' . engine_url . '/tools/pdf_convert/convert.php');

    }

} elseif ($_GET['print'] == 'yes') {
    $sis_letter_library_content = "$sis_letter_library_content";
    ?>
    <style>
        .letter_print {
            padding: 5px;
            margin: 0 auto;
            border: 1px solid #f0f0f0;
            font-family: Helvetica, Arial, sans-serif;
        }

        .side_text {
            color: #AAA096;
            font-size: 0.8em
        }

        ;
    </style>


    <table width="1200" border="0" cellspacing="2" cellpadding="20" class="row letter_print">
        <tr class="container">
            <td valign="top"><?= $sis_letter_library_content ?></td>
        </tr>
    </table>
    <?php

} else {
    //DISPLAY IN VIEW
    ?>
    <style>
        .letter_print {
            padding: 5px;
            margin: 0 auto;
            border: 1px solid #f0f0f0;
            font-family: Helvetica, Arial, sans-serif;
        }

        .side_text {
            color: #AAA096;
            font-size: 0.8em
        }

        ;
    </style>
    <div class="row letter_print">
        <div class="col-md-12"><?php echo $sis_letter_library_content; ?></div>
    </div>
    <?php
    echo '</div>'; // end letter print
}

//reset some values
$logo = '';
$sis_letter_library_content = '';

?>
<script src="<?= website_url ?>/admin/assets/js/signature-pad/jquery.signaturepad.min.js"></script>
<link href="<?php echo website_url; ?>/admin/assets/js/signature-pad/jquery.signaturepad.css" rel="stylesheet">

<script src="<?= website_url ?>/admin/assets/js/signature-pad/json2.min.js"></script>