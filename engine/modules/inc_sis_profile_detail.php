<?php
//ini_set('display_errors', 1);
//ini_set('display_startup_errors', 1);
//error_reporting(E_ALL);
global $data;

$dbh = get_dbh();
$url = 'http://' . $_SERVER['SERVER_NAME'] . $_SERVER['REQUEST_URI'];
$profile_id = $_GET['profile_id'];

$students = new Students;
$form_templates = new FormTemplates;
$form_cms = new FrontendPages;
$fields = new Fields;
$profile = new mrnProfilesModel;

/** ===================================
 * Get the pages to view
 * ====================================    */
$pages_args = array('student_id' => $profile_id);
$pages_to_show = $students->get_student_pages($pages_args);
//echo "pages to show are as follows";
//print_r($pages_to_show);

$pages_args = array("id_in" => $pages_to_show, "school_id" => $_SESSION['usergroup'], "order_by" => "db748");
$pages = $form_cms->get($pages_args);
//echo "<pre>"."pages are"."</pre>";
//print_r($pages);
//exit();
/** ===================================
 * Check if student is using new forms
 * ====================================    */
$new_form_ids_found = false;
foreach ($pages as $cms_page) {
    if ($cms_page['new_form']) {
        $new_form_ids_found = true;
    }
}
//if (!function_exists('get_new_form_data')) {
//    echo "in here";
function get_new_form_data($args = array())
{
    $form_templates = new FormTemplates;

    $fields_args = array(
        "form_id" => $args['form_id'],
        "school_id" => $args['school_id'],
    );

    $fields_list = $form_templates->get_custom_form_fields($fields_args);
    $template_answers = $args['answers'];

    ?>
    <div class="panel panel-default no-padding ">
        <div class="panel-body">
            <h1 style="margin: 0; padding: 0; font-weight: 300; font-size: 24px; margin-bottom: 30px;"><?php echo str_replace("- ", "", $args['page']['navigation_title']); ?></h1>
            <table class="table table-striped">
                <tbody class="sortable_objects">
                <?php
                foreach ($fields_list as $question) {
                    ?>
                    <tr id="row_<?php echo $entry["id"]; ?>">
                        <?php if ($question["type"] == "title") { ?>
                            <td colspan="2"><h2 class="next-heading"
                                                style="margin-bottom: 0; padding-top: 20px;"><?php echo $question["title"]; ?></h2>
                            </td>
                        <?php } elseif ($question["type"] == "subtitle") { ?>
                            <td colspan="2"><h3 class="next-heading"
                                                style="margin-bottom: 0; padding-top: 20px;"><?php echo $question["title"]; ?></h3>
                            </td>
                        <?php } elseif ($question["type"] == "instruction") { ?>
                            <td colspan="2" style="display: none;">
                                <div class="alert alert-warning"
                                     style="margin: -12px;"><?php echo $question["title"]; ?></div>
                            </td>
                        <?php } elseif ($question["type"] == "textonly") { ?>
                            <td colspan="2"><?php echo $question["title"]; ?></td>
                        <?php } else { ?>
                            <td width="50%;"><?php if ($question["title"]) {
                                    echo $question["title"];
                                } else {
                                    echo "---";
                                } ?></td>
                            <td><?php if ($template_answers[$question["db_field_name"]]) {
                                    echo noHTML($template_answers[$question["db_field_name"]]);
                                } else {
                                    echo '<span style="color: #999;">none provided</span>';
                                } ?></td>
                        <?php } ?>
                    </tr>
                <?php } ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php
}

//}
?>
    <div class="btn-group pull-right" style="margin-right: 20px;">
        <!--                        <button type="button" class="btn btn-primary dropdown-toggle" id="" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">-->
        <a href="<?php
        echo engine_url
        ?>/controller.php?pg=597&rec=<?php echo $profile_id; ?>&module_id=2&width=850&height=600&jqmRefresh=true"
           class="thickbox btn btn-primary" title="Edit Record" alt="Edit Record">
            Edit Entry
        </a>
        <!--                        </button>-->
    </div>
    <div class="btn-group pull-right" style="margin-left:50px;margin-right: 20px;">
        <button type="button" class="btn btn-primary dropdown-toggle" id="" data-toggle="dropdown" aria-haspopup="true"
                aria-expanded="false">Hide all empty fields
        </button>
    </div>
    <!--<h2 class="next-heading">Full Student Details </h2>-->
<?php
foreach ($pages as $cms_page) {
//                           echo "cms_form =".$cms_page['form'];
    if ($cms_page['form'] == 597) {
//                                echo "in here";
//                        print_r($cms_page);
        /** ===================================
         * GENERATE NEW* FORM DATA VIEW
         * ====================================    */
        if ($cms_page['new_form'] && $new_form_ids_found) {
            $form_args = array(
                'form_id' => $cms_page['new_form'],
                'school_id' => $_SESSION['usergroup'],
                'student_id' => $profile_id,
                'answers' => $profile->get_profile_answers($profile_id, $_SESSION['usergroup']),
                'page' => $cms_page
            );
            get_new_form_data($form_args);
        } else {
            //echo $cms_page['multi_page'];
//            $form_args = array(
//                'form_id' => $cms_page['form'],'school_id' => $this->school_info['id'],
//                'student_id' => $data['profile']['id'],
//                'page' => $cms_page);
//            get_form_data($form_args);
        }
    }//
}
