<?php

//check if the file exists
$external_payments_url = front_header_file_location . "/admin/pay_config.php";// link to external admin file

// then check if it has a custom rejection function
if (file_exists($external_payments_url)) {


    //get stripe files
    require_once(base_path . 'engine/tools/stripe/stripe-php-5.7.0/init.php');

    //get config file
    require_once("$external_payments_url");// call custom rejection function
} else {
    die("sorry payments config file was not found...");
}

?>


<script src="https://checkout.stripe.com/checkout.js"></script>

<button id="customButton" class="btn btn-primary">Pay with card</button>

<div id="after_message" class="hide">
    <div class="alert alert-success" role="alert">
        Your payment was successfully processed.
        <?php echo $after_successful_payment; ?>
    </div>
</div>

<script>
    var handler = StripeCheckout.configure({
        key: '<?php echo $stripe['publishable_key']; ?>',
        image: '<?php echo $stripe['payment_logo']; ?>',
        locale: 'auto',
        currency: '<?php echo $stripe['currency']; ?>',
        token: function (token) {

            // You can access the token ID with `token.id`.
            // Get the token ID to your server-side code for use.
            $("#customButton").hide();
            $("#after_message").removeClass("hide");

            post_data = {
                'stripeToken': token.id,
                'stripeEmail': token.email,
                'title': '<?php echo $stripe['payment_towards']; ?>'
            };
            $.post("<?php echo engine_url; ?>/modules/stripe_pay_outcome.php", post_data, function (data) {
                //$( ".result" ).html( data );
            });
        }
    });


    document.getElementById('customButton').addEventListener('click', function (e) {
        // Open Checkout with further options:
        handler.open({
            name: '<?php echo $stripe['payment_org_name']; ?>',
            description: '<?php echo $stripe['payment_towards']; ?>',
            zipCode: false,
            email: '<?=$_SESSION['user']?>',
            amount: <?php echo $stripe['payment_amt']; ?>
        });
        e.preventDefault();
    });

    // Close Checkout on page navigation:
    window.addEventListener('popstate', function () {

        handler.close();

    });
</script>