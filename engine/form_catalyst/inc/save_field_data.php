<?php
require("../../admin/inc/lib.inc.php");
require("site_lib.inc.php");
$dbh = get_dbh();

// GET ALL VALUES
$db_field_name = $_POST['db_field_name'];
$form_id = $_POST['form_id'];
$pg_id = $_POST['page_id'];
$name = addslashes($_POST['name']);
$type = $_POST['type'];
$box_size = $_POST['box_size'];
$description = $_POST['description'];
$required = isset($_POST['required']);
$figures = addslashes($_POST['figures']);
$locked = $_POST['locked'];
$conditional = $_POST['conditional'];
$decision = $_POST['decision'];

if ($_POST['action'] == "add") {
    // select field name
    $query = "SELECT form_id FROM $inst_table ORDER BY form_id DESC";
    $stmt = $dbh->prepare($query);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $db_field_name = "db" . $row['form_id']++;

    // db field name entry
    if ($type == "title") {
        $db_field_name = "";
    }

    // get the last form_order for this form
    $query = "SELECT MAX(form_order) FROM $inst_table 
        WHERE pg_id = '$pg_id'";
    $stmt = $dbh->prepare($query);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    $form_order = $row[0] + 1;

    // defaults
    $query = "INSERT INTO $inst_table VALUES (''
        ,'$name'
        ,'$db_field_name'
        ,'$type'
        ,'$box_size'
        ,'$description'
        ,'$required'
        ,'$figures'
        ,'$pg_id'
        ,'$form_order'
        ,'$locked'
        ,'$conditional'
        ,'$decision')";
    $stmt = $dbh->prepare($query);
    $stmt->execute();
    echo "added";

} elseif ($_POST['action'] == "update") {

    $query = "UPDATE $inst_table SET name = '$name',
        type = '$type',
        box_size = '$box_size',
        description = '$description',
        required = '$required',
        figures = '$figures',
        locked = '$locked',
        conditional = '$conditional',
        decision = '$decision'			
        WHERE form_id='$form_id'";
    error_log(str_replace("\n", "", $query));
    $stmt = $dbh->prepare($query);
    $stmt->execute();

    // if the table exists update fields			
    $table_name_for_alter = "form_" . $_SESSION['proj_page'];
    if (table_exists($table_name_for_alter, $database)) {//check if table exits

        $sql_it = "ALTER TABLE `form_client_profile` CHANGE `$db_field_name` `$db_field_name` VARCHAR(255)  NOT NULL COMMENT '$name'";
        $stmt = $dbh->prepare($sql_it);
        $stmt->execute();
        $log_query = "INSERT INTO system_version_control (user,date_created,action) VALUES (
                        '" . $_SESSION['uid'] . "',
                        '" . date('Y-m-d H:i:s') . "',
                        '" . addslashes($sql_it) . "'
                    )";
        $stmt = $dbh->prepare($log_query);
        $stmt->execute();
    }

    echo "updated";
}


