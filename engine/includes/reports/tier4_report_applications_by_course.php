<style>
    table td. {
        text-align: right
    }
</style>

<?php
$t1 = time();
print_page("Print report", 'style="float:right; margin-left:10px"');

////////////////////////////////////////////////////////
// WEEK BY WEEK FUNCTION
////////////////////////////////////////////////////////
$current = time();
$weekNumber = date("W");
$wk = find_slug(1);

//F d, Y
?>
<?php
if ($_POST['print'] !== "yes") {
    ?>
    <?php
} // end if else
?>
<H1><?php echo $_SESSION['school_name']; ?>'s Applications By Course</H1><br/>
<div class="alert alert-info">
    Report showing breakdown of the All Courses originally applied for at point of registering the profile (core_student
    table), grouped by Course Level, against the status of the profile and their application form (Profile Created,
    Application Submitted, and other relevant stages in your Checklist linked to your Route) where they have reached
    that stage at any point in the applicant journey.
    <a href="#bottom">Continue reading</a>
</div>
<?php

/////////////////////
//WORK OUT PERCENTAGE
/////////////////////////

function percentage($part, $whole = 100)
{
    settype($part, "float");
    settype($whole, "float");
    $formule = ($part / $whole) * 100;
    return round($formule, 0) . " %";
}

function total_up2($query, $tot)
{
    //echo $tot.'<br/>';
    $dbh = get_dbh();
    //$query="".$query."";
    //echo $query;
    //echo "<br><hr/>";
    $stmt = $dbh->prepare($query);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row['' . $tot . ''] == "") {
        return '0';
    } else {
        return $row['' . $tot . ''];//TYPE 1
    }
    //echo '$row['.$tot.']';
}

/*--------------------------------
// FUNCTION TO GET_CORE_COURSES
---------------------------------*/
function get_core_courses_info($level, $level_name, $type = 'all')
{

    $dbh = get_dbh();
    $sql = "
        SELECT db41,db232,db40,q.id AS ids, db235 FROM applicatalyst_lite.core_students as p LEFT JOIN applicatalyst_lite.core_courses as q
		ON q.id=p.db889
		WHERE p.usergroup='$_SESSION[usergroup]'
AND db341='$level'
AND db890='$_SESSION[school_cycle]'
Group by db232
order by db232 ASC
	";

    //$sql="SELECT * FROM core_courses WHERE usergroup='221' AND db341='$level' AND db235='public' order by db232 ASC";//core_courses WHERE usergroup=[SESSION]usergroup[/SESSION] AND db235='public' order by db232 ASC,db231
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

    $i = 1;
    $subtotal = 0;
    foreach ($results as $row) {

        $core_courses_id = $row['ids'];
        $core_courses_course_category = $row['db232'];
        $course_status = $row['db235'];

        $where_query = "AND core_students.usergroup='$_SESSION[usergroup]' AND (db135!='yes' OR db135 IS NULL) AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '') AND db889='$core_courses_id' AND db890 = '$_SESSION[school_cycle]'";

        $total_profiles = pull_field("core_students", "count(id)", "WHERE id!='' $where_query");
        $total_applications = pull_field("core_students INNER JOIN dir_stage_tracker t ON t.rel_id = core_students.id", "count(DISTINCT core_students.id)", "WHERE db1142 IN('12') $where_query");
        $total_received = pull_field("core_students INNER JOIN dir_stage_tracker t ON t.rel_id = core_students.id", "count(core_students.id)", "WHERE db1142 IN('db16065') $where_query");
        $total_academic_review = pull_field("core_students INNER JOIN dir_stage_tracker t ON t.rel_id = core_students.id", "count(core_students.id)", "WHERE db1142 IN('db16088') $where_query");
        $total_interview = pull_field("core_students INNER JOIN dir_stage_tracker t ON t.rel_id = core_students.id", "count(core_students.id)", "WHERE db1142 IN('db16115') $where_query");
        $total = $total_profiles + $total_applications + $total_received + $total_academic_review + $total_interview;

        //get the total list that wil be sent to the custom report
        $total_profiles_list = pull_field("core_students", "group_concat(DISTINCT core_students.id)", "WHERE id!='' $where_query");
        $total_applications_list = pull_field("core_students INNER JOIN dir_stage_tracker t ON t.rel_id = core_students.id", "group_concat(DISTINCT core_students.id)", "WHERE db1142 IN('12') $where_query");

        if ($type == 'all') { // show tables and all
            //echo "<tr><td>$core_courses_course_category</td>";
        }

        echo '
	  <tr>
	  	<td>' . $core_courses_course_category . ' <span class="label label-' . ($course_status == "internal" ? 'default' : 'info') . ' pull-right" title="Course Status">' . $course_status . ' </span></td>
		<td>';
        ?>
        <form action="<?= $_SESSION['domain'] ?>/engine/direct/report-custom" method="post" name="form" target="_blank">
            <input name="print_submit" type="submit" value="<?= $total_profiles ?>">
            <input name="query" type="hidden"
                   value="select id as Manage, db39 as 'First Name',db40 as Surname from core_students WHERE usergroup='<?= $_SESSION['usergroup'] ?>' and id IN(<?= $total_profiles_list ?>)">
        </form>
        <?php
        echo '</td>
		<td>';
        ?>
        <form action="<?= $_SESSION['domain'] ?>/engine/direct/report-custom" method="post" name="form" target="_blank">
            <input name="print_submit" type="submit" value="<?= $total_applications ?>">
            <input name="query" type="hidden"
                   value="select id as Manage, db39 as 'First Name',db40 as Surname from core_students WHERE usergroup='<?= $_SESSION['usergroup'] ?>' and id IN(<?= $total_applications_list ?>)">
        </form>
        <?php echo '</td>
		<td>' . $total_received . '</td>
		<td>' . $total_academic_review . '</td>
		<td>' . $total_interview . '</td>
	  </tr>
	  ';
        $subtotal = $subtotal + $total;

        $sub_total_profiles = $sub_total_profiles + $total_profiles;
        $sub_total_applications = $sub_total_applications + $total_applications;
        $sub_total_received = $sub_total_received + $total_received;
        $sub_total_academic_review = $sub_total_academic_review + $total_academic_review;
        $sub_total_interview = $sub_total_interview + $total_interview;
        $i++;
    }

    echo '
	  <tr>
		<td></td>
		<td><strong>' . $sub_total_profiles . '</strong></td>
		<td><strong>' . $sub_total_applications . '</strong></td>
		<td><strong>' . $sub_total_received . '</strong></td>
		<td><strong>' . $sub_total_academic_review . '</strong></td>
		<td><strong>' . $sub_total_interview . '</strong></td>
	  </tr>
	  ';
}

?>
<table width="100%" border="1" class="table table-striped">

    <tr>
        <th style="background-color:#fff" width="47%">&nbsp;</th>
        <th width="10%"><strong>Profiles Created</strong></th>
        <th style="background-color:#E6E2E2" width="10%"><strong>Of Which Applications Submitted</strong></th>
        <th width="10%"><strong>Of Which Received & Checked</strong></th>
        <th style="background-color:#E6E2E2" width="5%"><strong>Of Which Academic Review</strong></th>
        <th width="5%"><strong>Of Which Interviewed</strong></th>
    </tr>
    <tr>
        <td colspan="9"><strong>Further Education</strong></td>
    </tr>
    <tr>
        <td colspan="9"><?php get_core_courses_info(1, "Further Education"); ?></td>
    </tr>

    <tr>
        <td colspan="9">&nbsp;</td>
    </tr>

    <tr>
        <th style="background-color:#fff" width="47%">&nbsp;</th>
        <th width="10%"><strong>Profiles Created</strong></th>
        <th style="background-color:#E6E2E2" width="10%"><strong>Of Which Applications Submitted</strong></th>
        <th width="10%"><strong>Of Which Received & Checked</strong></th>
        <th style="background-color:#E6E2E2" width="5%"><strong>Of Which Academic Review</strong></th>
        <th width="5%"><strong>Of Which Interviewed</strong></th>
    </tr>
    <tr>
        <td colspan="9"><strong>Undergraduate</strong></td>
    </tr>
    <tr>
        <td colspan="9"><?php get_core_courses_info(2, "Undergraduate"); ?></td>
    </tr>

    <tr>
        <td colspan="9"><strong>Postgraduate</strong></td>
    </tr>

    <tr>
        <th style="background-color:#fff" width="47%">&nbsp;</th>
        <th width="10%"><strong>Profiles Created</strong></th>
        <th style="background-color:#E6E2E2" width="10%"><strong>Of Which Applications Submitted</strong></th>
        <th width="10%"><strong>Of Which Received & Checked</strong></th>
        <th style="background-color:#E6E2E2" width="5%"><strong>Of Which Academic Review</strong></th>
        <th width="5%"><strong>Of Which Interviewed</strong></th>
    </tr>
    <tr>
        <td colspan="9"><?php get_core_courses_info(4, "Postgraduate"); ?></td>
    </tr>
    <tr>
        <td colspan="9">&nbsp;</td>
    </tr>
    <tr>
        <th style="background-color:#fff" width="47%">&nbsp;</th>
        <th width="10%"><strong>Profiles Created</strong></th>
        <th style="background-color:#E6E2E2" width="10%"><strong>Of Which Applications Submitted</strong></th>
        <th width="10%"><strong>Of Which Received & Checked</strong></th>
        <th style="background-color:#E6E2E2" width="5%"><strong>Of Which Academic Review</strong></th>
        <th width="5%"><strong>Of Which Interviewed</strong></th>
    </tr>
    <?php
    // get totals
    $where_query = "AND core_students.usergroup='$_SESSION[usergroup]' AND (db135!='yes' OR db135 IS NULL) AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '') AND db50!='' AND db50!='not specified' AND db890 = $_SESSION[school_cycle]";

    //echo $where_query;

    $total_profiles = pull_field("core_students", "count(id)", "WHERE id!='' $where_query");
    $total_applications = pull_field("core_students INNER JOIN dir_stage_tracker t ON t.rel_id = core_students.id", "count(distinct core_students.id)", "WHERE db1142 IN('12') $where_query");
    $total_received = pull_field("core_students INNER JOIN dir_stage_tracker t ON t.rel_id = core_students.id", "count(distinct core_students.id)", "WHERE db1142 IN('db16065') $where_query");
    $total_academic_review = pull_field("core_students INNER JOIN dir_stage_tracker t ON t.rel_id = core_students.id", "count(distinct core_students.id)", "WHERE db1142 IN('db16088') $where_query");
    $total_interview = pull_field("core_students INNER JOIN dir_stage_tracker t ON t.rel_id = core_students.id", "count(distinct core_students.id)", "WHERE db1142 IN('db16115') $where_query");
    //$total=$total_conditional+$total_unconditional+$total_offers_accepted+$total_tutition+$total_cas+$total_enrolled;
    ?>
    <tr>
        <th style="background-color:#fff">&nbsp;</th>
        <th><strong><?php echo $total_profiles; ?></strong></th>
        <th style="background-color:#E6E2E2"><strong><?php echo $total_applications; ?></strong></th>
        <th><strong><?php echo $total_received; ?></strong></th>
        <th style="background-color:#E6E2E2"><strong><?php echo $total_academic_review; ?></strong></th>
        <th><strong><?php echo $total_interview; ?></strong></th>
    </tr>
    <td colspan="9">&nbsp;</td>
    </tr>
</table>

<div class="alert alert-warning">
    <h3>Data Inconsistencies Affecting Your Report</h3>
    <p>Profile(s) missing a course/programme entry</p>
    <?php
    $query_vi = "select 
id as manage,
db40 as 'Surname',
db39 as 'First Name'
from core_students where (db50='' or db50 is null) AND db890 = '$_SESSION[school_cycle]' AND usergroup=$_SESSION[usergroup]
AND (db135!='yes' OR db135 IS NULL) AND (core_students.rec_archive IS NULL OR core_students.rec_archive = '')";
    $table_name = "core_students";// NAME OF TABLE HOLDING STUDENT DATA
    show_report_view($query_vi, 4, 500, 0, 'direct');
    ?>
</div>

<a name="bottom"></a>
<div class="col-md-12">
    <h3>Continued From Above</h3>
    <br/><br/>-All Courses are included whether “public” or “internal”, as shown in the breakdown.
    <br/><br/>-Only course is the current Cohort are shown, to match with your Dashboard.
    <br/><br/>-Archived profiles are not included, to match with your Dashboard.

    <br/><br/>-This is useful to tell how far the applicants progressed over the applicant journey, rather than the
    current status of the applicant. This report does not reflect the applicants current status. If you want the current
    status using live data then please use “Applicant Current Status by Course” report.
    <br/><br/>-At the bottom you will see any Profiles that do not have a Course populated. This can happen if you
    delete a Course from “Manage Programmes” or edit and save the Profile without a Course selected. As soon as you
    allocate a Course then they will be included in the breakdown figures.
    <br/><br/>-For Application Submitted we count the number of Applications that have ever been submitted, even if you
    reset the Application Form for the applicant to edit and add to after submission.
    <br/><br/>-At the bottom you will see the number of Applications that have been reset at any point.

</div>
<?php
$t2 = time();
$t_lapsed = $t2 - $t1;
//echo "Total time lapsed = $t_lapsed";
//print_r($_SESSION);

