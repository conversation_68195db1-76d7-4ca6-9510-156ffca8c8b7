<style>
    table td. {
        text-align: right
    }

    .dark {
        background: #ccc;
        font-weight: bold
    }
</style>

<?php
/////////////////////////////////
// Display a selected Cycle
/////////////////////////////////
if ($_POST['report_cycle']) {
    $_SESSION['reporting_cycle'] = $_POST['report_cycle'];
}

if ($_SESSION['reporting_cycle'] == '') {
    $_SESSION['reporting_cycle'] = $_SESSION['school_cycle'];
}

$reporting_cycle = $_SESSION['reporting_cycle'];
////////// END DECLARATION /////

$t1 = time();
print_page("Print report", 'style="float:right; margin-left:10px"');

////////////////////////////////////////////////////////
// WEEK BY WEEK FUNCTION
////////////////////////////////////////////////////////
$current = time();
$weekNumber = date("W");
$wk = find_slug(1);

//F d, Y
?>

<H1>Application Tracker Snapshot Report</H1>
<h4>This report gives you a current view of your entire application funnel with a focus on the current cohort</h4>

<?php
if ($_POST['print'] !== "yes") {
    ?>
    <!--<table style="border:2px solid #ccc">-->
    <!--<tr>-->
    <!--
<td width="285">
<form name="form" id="form" class="float_left" method="post">
    Recruitment Cycle
    <select name="report_cycle" id="report_cycle">
        <option value="2013" <?php //if($reporting_cycle=="2013"){ echo "selected"; }
    ?>>2013</option>
        <option value="2014" <?php //if($reporting_cycle=="2014"){ echo "selected"; }
    ?>>2014</option>
    </select>
    <input type="submit" class="addbutton" value="Select Cycle">
</form>
</td>-->
    <!--<td width="285">-->
    <!--<form name="form" id="form" class="float_left">Select Week-->
    <!--<select name="jumpMenu" id="jumpMenu" onchange="MM_jumpMenu('parent',this,0)">-->
    <!--      <option value="--><?php //echo engine_url.'/direct/tier4weeklytracker_report';
    ?><!--/--><?php //echo $weekNumber;
    ?><!--" selected="selected">Current Week [--><?php //echo $weekNumber;
    ?><!-- ]</option>-->
    <!--      <option>--------------------------</option>-->
    <?php
    //loop through the weeks
    $total_weeks = $weekNumber;
    $i = 1;
    while ($i < $total_weeks) {

        if ($wk == $i) {
            $selected = ' selected="selected"';
        }
        ?>
        <!-- <option value="<?php echo engine_url . '/direct/tier4weeklytracker_report'; ?>/<?php echo $i; ?>" <?php echo $selected; ?>>week <?php echo $i; ?> of <?php echo date('Y'); ?></option> -->
        <?php
        $selected = '';
        $i++;
    }
    ?>
    <!--  </select>-->
    <!--</form>-->
    <!--</td>-->
    <!--</tr>-->
    <!--</table>  -->
    <?php
} // end if else
?>
<div class="clear"></div>
<?php

/////////////////////
//WORK OUT PERCENTAGE
/////////////////////////

function percentage($part, $whole = 100)
{
    settype($part, "float");
    settype($whole, "float");
    $formule = ($part / $whole) * 100;
    return round($formule, 0) . " %";
}

function total_up2($query, $tot)
{
    //echo $tot.'<br/>';
    $dbh = get_dbh();
    //$query="".$query."";
    //echo $query;
    //echo "<br><hr/>";
    $stmt = $dbh->prepare($query);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row['' . $tot . ''] == "") {
        return '0';
    } else {
        return $row['' . $tot . ''];//TYPE 1
    }
    //echo '$row['.$tot.']';
}

function week_start_date($wk_num, $yr, $first = 1, $format = 'Y-m-d H:i')
{
    $wk_ts = strtotime('+' . $wk_num . ' weeks', strtotime($yr . '0101'));
    $mon_ts = strtotime('-' . date('w', $wk_ts) + $first . ' days', $wk_ts);
    return date($format, $mon_ts);
}

function week_start_date_new($week, $year, $first = 1, $format = 'Y-m-d H:i')
{

    $time = strtotime("1 January $year", time());
    $day = date('w', $time);
    $time += ((7 * $week) + 1 - $day) * 24 * 3600;
    $return = date($format, $time);
    //$time += 6*24*3600;
    // $return[1] = date('Y-n-j', $time);
    return $return;
}

if ($wk == "tier4weeklytracker_report") {
    $current_week = $weekNumber;
} else {
    $current_week = $wk;
}

$last_week = $current_week;
$last_last_week = $current_week - 1;
$last_last_last_week = $current_week - 2;
$last_last_last_last_week = $current_week - 3;

$last_week_title = $current_week - 1;
$last_last_week_title = $current_week - 2;
$last_last_last_week_title = $current_week - 3;
$last_last_last_last_week_title = $current_week - 4;

$curr_yr = date('Y'); //current year
$week1 = week_start_date($last_week, $curr_yr) . '<br/>';
$week2 = week_start_date($last_last_week, $curr_yr) . '<br/>';
$week3 = week_start_date($last_last_last_week, $curr_yr) . '<br/>';
$week4 = week_start_date($last_last_last_last_week, $curr_yr) . '<br/>';
$week1_name = week_start_date($last_week_title, $curr_yr, 1, "l dS \of M, Y") . '<br/>';
$week2_name = week_start_date($last_last_week_title, $curr_yr, 1, "l dS \of M, Y") . '<br/>';
$week3_name = week_start_date($last_last_last_week_title, $curr_yr, 1, "l dS \of M, Y") . '<br/>';

/////////////////////////////////
// PULL A SPECIFIC field from db
////////////////////////////////


/*--------------------------------
// FUNCTION TO GET_CORE_STUDENTS
---------------------------------*/
function get_stages($wk)
{

    global $reporting_cycle;

    // Get yearweek
    $yw = date('Y') . str_pad((intval($wk)), 2, '0', STR_PAD_LEFT);

    $category_value = array("1", "2", "4"); // study levels fe,UG,PG

    $dbh = get_dbh();

    //rewrite to pull all the stages from system table

    $sql = "SELECT id, db1131 AS 'name' FROM dir_appli_stages WHERE id in(0,10,12)
UNION
SELECT db_field_name AS 'id', name AS 'name'
    FROM system_table
      WHERE pg_id = 266 AND name LIKE 'Stage%' AND db_field_name NOT IN ('db16087')
UNION SELECT id, db1131 AS 'name' FROM dir_appli_stages WHERE id in(-1)
UNION SELECT id, db1131 AS 'name' FROM dir_appli_stages WHERE id in(-2)
UNION SELECT '', 'Not Specified' AS 'name' FROM dir_appli_stages WHERE id =''
";

//SELECT * FROM dir_appli_stages order by floor(db1130)
    $stmt = $dbh->prepare($sql);
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    dev_debug($sql);

    foreach ($results as $row) {


        $dir_appli_stages_id = $row['id'];
        //$dir_appli_stages_id = $row['db_field_name'];
        $dir_appli_stages_rec_id = $row['rec_id'];
        $dir_appli_stages_usergroup = $row['usergroup'];
        $dir_appli_stages_rel_id = $row['rel_id'];
        //$dir_appli_stages_stage_name = $row['db1131'];
        $dir_appli_stages_stage_name = $row['name'];

        //show other row based on new listing
        //Profile
        if ($dir_appli_stages_id == "0") {
            echo '<tr>';
            echo '<td colspan="7" class="dark">Profile</td>';
            echo '</tr>';
        }
        //Dormant Profile
        if ($dir_appli_stages_id == 10) {
            echo '<tr>';
            echo '<td colspan="7" class="dark">Dormant Profile</td>';
            echo '</tr>';
        }
        //Applicant
        if ($dir_appli_stages_id == 12) {
            echo '<tr>';
            echo '<td colspan="7" class="dark">Applicant</td>';
            echo '</tr>';
        }
        //Enrolled
        //"db16133"
        if ($dir_appli_stages_id == "db16133") {
            echo '<tr>';
            echo '<td colspan="7" class="dark">Enrolled</td>';
            echo '</tr>';
        }
        //Withdrawn/Rejected
        if ($dir_appli_stages_id == "-1") {
            echo '<tr>';
            echo '<td colspan="7" class="dark">Withdrawn/Rejected</td>';
            echo '</tr>';
        }

        /* //null & blank
         if($dir_appli_stages_id=="" || $dir_appli_stages_id === NULL){
             echo '<tr>';
             echo '<td colspan="7" class="dark">Not Specified</td>';
             echo '</tr>';
         }	*/


        echo '<tr>';
        echo '<td colspan="2">' . $dir_appli_stages_stage_name . '</td>';

        // overall total total
        $where = " WHERE usergroup='$_SESSION[usergroup]' AND (db135!='yes' OR db135 IS NULL) AND db890='$reporting_cycle'";
        $addup2 = pull_field("core_students", "count(id)", $where);

        $k = 1;
        foreach ($category_value as $cat_value) {
            //use db_field_name from previous query to check dir_stage_tracker and get counts, not much needs rewriting
            $sql4 = "SELECT COUNT(*) AS tot
			FROM core_students
                WHERE core_students.usergroup='$_SESSION[usergroup]'
                AND db30487='$dir_appli_stages_id' 
                AND (db135!='yes' OR db135 IS NULL) AND db890='$reporting_cycle' 
				AND (rec_archive is NULL or rec_archive='')
                AND db50='$cat_value'
                ORDER BY db30487 DESC";
            //error_log($sql4);
            dev_debug($sql4);
            //echo $sql4.'<br/>';
            $stmt = $dbh->prepare($sql);
            $stmt->execute();
            $row4 = $stmt->fetch(PDO::FETCH_ASSOC);

            $total = $row4['tot'];


            echo '<td colspan="1">';
            echo $total;
            echo "</td>";


            $week_addup[$k][] = $total;//create array to add up
            $week_addup_overall[$k][] = $total;//create array to add up for ovaeralls
            $k++;
            $total = '';//reset

        }

        // addup arrays
        $nam_sum1_overall = array_sum($week_addup_overall[1]);//row 1
        $nam_sum2_overall = array_sum($week_addup_overall[2]);//row 2
        $nam_sum3_overall = array_sum($week_addup_overall[3]);//row 3

        //unset and reset to avaoid contaminating the next loop
        unset($week_addup_overall[1]);//reset
        unset($week_addup_overall[2]);//reset
        unset($week_addup_overall[3]);//reset

        /// addup all arrays for overall total
        $addup = $nam_sum1_overall + $nam_sum2_overall + $nam_sum3_overall;

        // addup arrays
        $nam_sum1 = array_sum($week_addup[1]);//row 1
        $nam_sum2 = array_sum($week_addup[2]);//row 2
        $nam_sum3 = array_sum($week_addup[3]);//row 3

        $total_all_nam_sums = $nam_sum1 + $nam_sum2 + $nam_sum3;

        //only run once
        if (!$total_all_nam_sums_overall) {
            $total_all_nam_sums_overall = pull_field("core_students", "count(id)", "WHERE core_students.usergroup='$_SESSION[usergroup]'
                $date_stop
                AND (db135!='yes' OR db135 IS NULL) AND (rec_archive is NULL or rec_archive='') AND db890='$reporting_cycle' AND db50 IN (1,2,4)
                order by db30487 DESC");
        }
        dev_debug("total_all_nam_sums_overall = " . $total_all_nam_sums_overall);
        dev_debug("total_all_nam_sums = " . $total_all_nam_sums);

        echo '<td colspan="1">';
        echo $addup;
        echo "</td>";
        echo '<td colspan="1">';
        echo round(($addup / $total_all_nam_sums_overall) * 100, 2) . '%';
        echo "</td>";
        echo "</tr>";
        $stage_tot = '';//reset
        $addup = '';

    } // end while loop

    //addthem all up for total
    //$total_all_nam_sums = $nam_sum1+$nam_sum2+$nam_sum3;
    echo '<tr>
        <th colspan="2">Total All Stages</th>
        <th colspan="1">' . $nam_sum1 . '</th>
        <th colspan="1">' . $nam_sum2 . '</th>
        <th colspan="1">' . $nam_sum3 . '</th>
        <th colspan="1">' . $total_all_nam_sums . '</th>
        <th colspan="1">100%</th>
        <tr/>';

}

?>
<table class="table table-striped">
    <tbody>
    <tr>
        <td colspan="2">&nbsp;</td>
        <th colspan="1"><strong>Further Education</strong></th>
        <th colspan="1"><strong>Undergraduate</strong></th>
        <th colspan="1"><strong>Postgraduate</strong></th>
        <th colspan="1"><strong>All Levels</strong></th>
        <th colspan="1"><strong>%of Total</strong></th>
    </tr>
    <tr>
        <td colspan="7"><?php get_stages($current_week); ?></td>
    </tr>
    </tbody>
</table>

<?php
$t2 = time();
$t_lapsed = $t2 - $t1;
//echo "Total time lapsed = $t_lapsed";
//print_r($_SESSION);

