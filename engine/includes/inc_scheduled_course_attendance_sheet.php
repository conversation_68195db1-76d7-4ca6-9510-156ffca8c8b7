<?php

include_once("../admin/inc/lib.inc.php");
chk_login(); //check if user is logged in

//Get Scheduled Course Details
$show_surnames = $_GET['sur'];
if (empty($show_surnames)) {
	$show_surnames = 1;
}
else {
	$show_surnames = 0;
}
list($course_schedule_id, $course_schedule_rec_id, $_course_schedule_usergroup, $course_schedule_rel_id, $course_schedule_course_id, $course_schedule_course_name, $course_schedule_start_date, $course_schedule_start_time, $course_schedule_end_date, $course_schedule_end_time, $course_schedule_last_booking_date,  $course_schedule_venue, $course_schedule_venue_name,  $course_schedule_max_attendees, $course_schedule_min_attendees, $course_schedule_price, $course_schedule_status, $course_schedule_status_name, $course_schedule_additional_information, $course_schedule_publish_to_website,$sis_course_schedule_payment_methods)=get_sis_course_schedule($_GET['r']);
$usergroups=usergroups_management();
$dbh = get_dbh();

//Get Scheduled Course Bookings Applicants
//We should not be looking at the booking stages / financial status for attendees it should be the booking status
$sql = "SELECT concat(db15054,' ',db15055) as 'student_name' FROM sis_sched_booking_detail JOIN sis_scheduled_booking ON sis_sched_booking_detail.db15052 = sis_scheduled_booking.id WHERE sis_scheduled_booking.db14977 = $course_schedule_id AND (sis_scheduled_booking.rec_archive IS NULL OR sis_scheduled_booking.rec_archive = '') AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') AND db14979 ='6'";
$course_details_label = "Course Details";
$course_details = "";
$show_known_as = false;
$mrn_school = pull_field("form_schools","db30","where id =$_SESSION[usergroup]");
if ($_SESSION['usergroup'] == '82') {
	$sql = "SELECT concat(db15054,' ',db15055) as 'student_name',db15054 as 'first_name', db58493 as job_title, db61482 as organisation,'' as middle_name,db15055 as 'last_name'
	FROM sis_sched_booking_detail 
	JOIN sis_scheduled_booking ON sis_sched_booking_detail.db15052 = sis_scheduled_booking.id 
	WHERE sis_scheduled_booking.db14977 = $course_schedule_id 
	AND (sis_scheduled_booking.rec_archive IS NULL OR sis_scheduled_booking.rec_archive = '')
	AND (sis_sched_booking_detail.rec_archive IS NULL OR sis_sched_booking_detail.rec_archive = '') 
	AND (db59978 ='2' OR db59978 = '3')
	";
	$course_details = pull_field("sis_scheduled_tutors JOIN sis_course_tutors on sis_course_tutors.id = db59798","GROUP_CONCAT(CONCAT(db52597,' ',db52598)  SEPARATOR '<br/>')","WHERE sis_scheduled_tutors.usergroup = '$_SESSION[usergroup]'  AND (sis_scheduled_tutors.rec_archive IS NULL OR sis_scheduled_tutors.rec_archive ='' ) AND sis_scheduled_tutors.rel_id='$_GET[r]'");
	$course_details_label = "Tutor(s)";
}elseif($mrn_school == '12'){
	$sql = "
		 SELECT 
		     concat(db15054,' ',db15055) as 'student_name',db15054 as 'first_name', IFNULL(db48567,'') as middle_name,db15055 as 'last_name'
 		FROM sis_course_schedule
		JOIN sis_scheduled_booking ON sis_scheduled_booking.db14977 = CAST(sis_course_schedule.id AS CHAR)
		JOIN sis_sched_booking_detail ON sis_sched_booking_detail.db15052 = CAST(sis_scheduled_booking.id  AS CHAR)
		LEFT JOIN core_students ON core_students.id = sis_sched_booking_detail.rel_id
		LEFT JOIN sis_profiles ON sis_profiles.rel_id = core_students.rel_id		
		WHERE sis_scheduled_booking.usergroup = {$_SESSION['usergroup']}  AND  sis_course_schedule.id = {$course_schedule_id} 
		AND (sis_scheduled_booking.rec_archive is NULL or sis_scheduled_booking.rec_archive = '')
		AND (sis_sched_booking_detail.rec_archive is NULL or sis_sched_booking_detail.rec_archive = '')
		AND (sis_profiles.rec_archive is NULL or sis_profiles.rec_archive = '')
		AND (db59978 ='2' OR db59978 = '3')
		ORDER BY cast(db59978 as unsigned) LIMIT 1000
	";

	$course_details = pull_field("sis_scheduled_tutors JOIN sis_course_tutors on sis_course_tutors.id = db59798","GROUP_CONCAT(CONCAT(db52597,' ',db52598)  SEPARATOR '<br/>')",
		"WHERE sis_scheduled_tutors.usergroup = '$_SESSION[usergroup]'  AND (sis_scheduled_tutors.rec_archive IS NULL OR sis_scheduled_tutors.rec_archive ='' ) 
		AND sis_scheduled_tutors.rel_id='$_GET[r]'");
	$course_details_label = "Tutor(s)";
	$known_as_name = pull_field("system_table","name","where db_field_name = 'db48567' and locked = '0' and usergroup = $_SESSION[usergroup]");
	if (!empty($known_as_name) && !strpos($known_as_name, 'middle', 0)) {
		$show_known_as = true;
	}
}
if ($_SESSION['usergroup'] == '82') {
	$show_job_title_and_organisation = true;

}
else {
	$show_job_title_and_organisation = false;
}

dev_debug($sql);
$sth = $dbh->prepare($sql);
$sth->execute();

//reset some values
$the_content_you_want_outputted='';

?>

<?php
// connect to the database
//include_once("../admin/inc/lib.inc.php");
//chk_login(); //check if user is logged in

// GET ALl THE DATA FROM YOUR DATA SOURCE

$the_content_you_want_outputted = '
<p align=center style="text-align:center"><b>Scheduled Course Attendance Sheet</b></p>

<table border=1 cellspacing=0 cellpadding=0 style="border-collapse:collapse;border:none">
 <tr>
  <td colspan=3 valign=top style="width:331pt;border-style:double double solid solid;padding:0cm 2.4pt 0cm 2.4pt"><b>Course Title:</b><br/><br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>{{course_title}}</b><br/>&nbsp;</td>
  <td valign=top style="width:156pt;border-style:double none solid double;padding:0cm 2.4pt 0cm 2.4pt"><b>{{course_details_label}}:</b>{{course_details}}<br/>&nbsp; </td>
 </tr>
 <tr>
  <td colspan=3 valign=top style="width:331pt;border-style:none double double solid;padding:0cm 2.4pt 0cm 2.4pt"><b>Course Date:</b><br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{course_date}}<br/>&nbsp;</td>
  <td valign=top style="width:156pt;border-style: none none double double;padding:0cm 2.4pt 0cm 2.4pt"><b>Course Code:</b><br/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{course_code}}<br/>&nbsp;</td>
 </tr>
 </table>
 
 <table cellspacing=0; cellpadding=2px; style="border-collapse:collapse;border:none;">
<tr>
  <th style="vertical-align:middle;text-align:center; width:50pt;border-style:double double double double;background-color:#EEECE1;"><b>Office Code</b></th>';
	if ($show_job_title_and_organisation) {
		$the_content_you_want_outputted .= '<th style="vertical-align:middle;text-align:center;width: 93pt;border-style:double double double double;background-color:#EEECE1;"><b>Student Name</b></th>
  <th style="vertical-align:middle;text-align:center;width: 94pt;border-style:double double double double;background-color:#EEECE1;"><b>Job Title</b></th><th style="vertical-align:middle;text-align:center;width: 94pt;border-style:double double double double;background-color:#EEECE1;"><b>Organisation</b></th>';
	} else {
    	$the_content_you_want_outputted .= '<th style="vertical-align:middle;text-align:center;width: 281pt;border-style:double double double double;background-color:#EEECE1;"><b>Student Name</b></th>';
  	}
		$the_content_you_want_outputted .= '<th style="vertical-align:middle;text-align:center;width:156pt;border-style:double double double double;background-color:#EEECE1;"><b>Student Signature</b></th>
 </tr>';
$number_of_lines = 0;
// run though loop
while ($student = $sth->fetch(PDO::FETCH_ASSOC)) {
	$number_of_lines= $number_of_lines + 1;
	$the_content_you_want_outputted .= '  
 <tr>
  <td style="vertical-align:middle;width:50pt;border-style:none solid solid solid;padding: 5px 5px 5px 5px;">&nbsp;</td>';
	if ($show_job_title_and_organisation) {
		$the_content_you_want_outputted .= '
	<td style="vertical-align:middle;width:93pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>'.$student['student_name'].'<br/></td>';
		$the_content_you_want_outputted .= '	
  	<td style="vertical-align:middle;width:94pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>'.$student['job_title'].'<br/></td>
  	<td style="vertical-align:middle;width:94pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>'.$student['organisation'].'<br/></td>';

	}
	else {
		if ($show_surnames) {
			if 	($show_known_as &&!empty($student['middle_name']) ) {
				$the_content_you_want_outputted .= '<td style="vertical-align:middle;width:281pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>' . $student['first_name'] . ' ('.$student['middle_name'].') ' .$student['last_name'] .'<br/></td>';
			} else {
				$the_content_you_want_outputted .= '
				<td style="vertical-align:middle;width:281pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>' . $student['student_name'] . '<br/></td>';
			}
		} else {
			if 	($show_known_as &&!empty($student['middle_name'])) {
				$the_content_you_want_outputted .= '<td style="vertical-align:middle;width:281pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>' . $student['first_name']  . ' ('.$student['middle_name'].')' .'<br/></td>';
			} else {
				$the_content_you_want_outputted .= '
	<td style="vertical-align:middle;width:281pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>' . $student['first_name'] . '<br/></td>';
			}

		}
	}

	$the_content_you_want_outputted .= '
  <td style="vertical-align:middle;width:156pt;border-style:none solid solid solid;padding:5px;">&nbsp;</td>
 </tr>
 ';
}

while ($number_of_lines < 10) {
	$the_content_you_want_outputted .= '  
 <tr>
  <td style="width:50pt;border-style:none solid solid solid;padding:5px 5px 5px 5px;">&nbsp;</td>';
	if ($show_job_title) {
		$the_content_you_want_outputted .= '
  <td style="width:140pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>&nbsp;<br/></td>
  <td style="width:141pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>&nbsp;<br/></td>';
	}
	else {
		$the_content_you_want_outputted .= '<td style="width:281pt;border-style:none solid solid none;padding:5px 5px 5px 5px;"><br/><br/>&nbsp;<br/></td>';
	}
	$the_content_you_want_outputted .= '<td style="width:156pt;border-style:none solid solid solid;padding:5px 5px 5px 5px;">&nbsp;</td>
 </tr>
 ';
	$number_of_lines= $number_of_lines + 1;
}
$the_content_you_want_outputted .= ' </table>';

/// this is just your content in HTML or text format

$course_schedule_start_time = substr($course_schedule_start_time,0,2 ).':'.substr($course_schedule_start_time,2,2);
$the_content_you_want_outputted = str_replace("{{course_title}}",$course_schedule_course_name,$the_content_you_want_outputted);
$the_content_you_want_outputted = str_replace("{{course_date}}",format_date('d-m-Y',$course_schedule_start_date).' '.$course_schedule_start_time,$the_content_you_want_outputted);
$the_content_you_want_outputted = str_replace("{{course_code}}",$course_schedule_id,$the_content_you_want_outputted);

$the_content_you_want_outputted = str_replace("{{course_details}}",$course_details,$the_content_you_want_outputted);
$the_content_you_want_outputted = str_replace("{{course_details_label}}",$course_details_label,$the_content_you_want_outputted);

error_log($the_content_you_want_outputted);

// SEND TO PDF	
// Set some session variable to be added to pdf
// ------------------------------------------------------
$_SESSION['print_msg'] = $the_content_you_want_outputted; // content to put in file
$_SESSION['file_title'] = "Course-Attendance-Sheet"; // title of the page
$_SESSION['letter_format'] = "No"; // if this is letter format

// Send to PDF creator
// ------------------------------------------------------
header('Location: '.engine_url.'/tools/pdf_convert/convert.php');