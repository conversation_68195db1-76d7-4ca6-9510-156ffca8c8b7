function findValue(li) {
if( li == null ) return alert("No match!");

// if coming from an AJAX call, let's use the CityId as the value
if( !!li.extra ) var sValue = li.extra[0];

// otherwise, let's just display the value in the text box
else var sValue = li.selectValue;

alert("The value you selected was: " + sValue);
}

function selectItem(li) {
findValue(li);
}

function formatItem(row) {
return row[0] + " (id: " + row[1] + ")";
}

function lookupAjax(){
var oSuggest = $("#CityAjax")[0].autocompleter;

oSuggest.findValue();

return false;
}

function lookupLocal(){
var oSuggest = $("#CityLocal")[0].autocompleter;

oSuggest.findValue();

return false;
}

$(document).ready(function() {
$("#CityAjax").autocomplete(
"autocomplete_ajax.cfm",
{
delay:10,
minChars:2,
matchSubset:1,
matchContains:1,
cacheLength:10,
onItemSelect:selectItem,
onFindValue:findValue,
formatItem:formatItem,
autoFill:true
}
);

$("#CityLocal").autocompleteArray(
[
<?php

$dbh = get_dbh();// connect to db

$sql = "SELECT DISTINCT source_name FROM budget_sheet_source";// WHERE source_userid='$_GET[id]'";
$stmt = $dbh->prepare($sql);
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);
$num = count($results);


if ($num == 0) {
    echo "<br><center>There are currently no orders to display</center>";
} else {
    $i = 0;
    foreach ($results as $row) {
        $source_name = $row["source_name"];
        $source_name_autovalues .= " \"" . $source_name . "\",";

        ++$i;
    }
};
$source_name_autovalues = substr($source_name_autovalues, 0, -1);
echo $source_name_autovalues;

?>


],
{
delay:10,
minChars:1,
matchSubset:1,
onItemSelect:selectItem,
onFindValue:findValue,
autoFill:true,
maxItemsToShow:10
}
);
});