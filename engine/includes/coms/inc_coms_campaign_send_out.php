<?php
include_once("../../admin/inc/lib.inc.php");
if (!class_exists('Email_helper')) load_helper('email');
$eh = new Email_helper();
chk_login();
?>

<link href="<?php echo engine_url; ?>/css/styles.css" rel="stylesheet" type="text/css"/>
<style>
    .email {
        margin: 0 auto;
        width: 650px;
        padding: 20px;
        margin-bottom: 10px;
        border: 5px dotted #f0f0f0;
        background-color: #FFC
    }

    .email div {
        background-color: #FFF;
        padding: 10px
    }

    .email h4 {
        float: right
    }

    .main_content h2 {
        text-align: center
    }
</style>
<div class="main_content">
    <?php

    //pull conversion info
    // FUNCTION TO GET_COMS_CAMPAIGNS
    list($coms_campaigns_id, $coms_campaigns_rec_id, $coms_campaigns_usergroup, $coms_campaigns_rel_id, $coms_campaigns_schedule, $coms_campaigns_when_should_the_message_be_sent, $coms_campaigns_recipients, $coms_campaigns_title, $coms_campaigns_email_subject, $coms_campaigns_from_name, $coms_campaigns_reply_to_email_address, $coms_campaigns_email_content_plain_text, $coms_campaigns_email_content_html_text, $coms_campaigns_html_header_image, $coms_campaigns_html_footer, $coms_campaigns_select_template, $coms_campaigns_status) = get_coms_campaigns($_REQUEST['ref']);

    // sort out images location
    $coms_campaigns_email_content_html_text = str_replace("images/", engine_url . "/images/", $coms_campaigns_email_content_html_text);

    // fix header locations
    $coms_campaigns_html_header_image = str_replace("<p>", "", $coms_campaigns_html_header_image);
    $coms_campaigns_html_header_image = str_replace("</p>", "", $coms_campaigns_html_header_image);
    $coms_campaigns_html_header_image = str_replace("../library/", engine_url . "/library/", $coms_campaigns_html_header_image);

    // fix footer locations
    $coms_campaigns_html_footer = str_replace("<p>", "", $coms_campaigns_html_footer);
    $coms_campaigns_html_footer = str_replace("</p>", "", $coms_campaigns_html_footer);
    $coms_campaigns_html_footer = str_replace("../library/", engine_url . "/library/", $coms_campaigns_html_footer);

    // START CAMPAIGN
    if ($_REQUEST['start_campaign'] == '1' || $_REQUEST['preview_campaign'] == '1') {

        if ($_REQUEST['start_campaign'] == '1') {
            echo "<h2>Campaign has been sent</h2>";
        } else {
            echo "<h2>Below is a preview of all the email to be sent out</h2>";
        }

        /////////////////////// EMAIL LOGING SCRIPT ///////////////////////
        //GET LIST OF USER IDS AND TYPE OF LIST (applicants,contacts,leads)
        $coms_lists_list_query = pull_field("coms_lists", "concat(db1339,'|',db1406)", "WHERE id = '$coms_campaigns_recipients'");

        #echo "step 1:$coms_lists_list_query<br/>";
        //EXPLODE PULLED DATA
        $coms_lists_list_query = explode('|', $coms_lists_list_query);

        //ALLOCATE FIELDS EXPLODED FIELDS
        $list_query = $coms_lists_list_query[0];
        $source_of_recepients = $coms_lists_list_query[1];

        //SET LIST VALUES BASED ON RESULT ABOVE
        if ($source_of_recepients == "applicants") {
            $source_of_recepients = "core_students";
            $email_position_in_table = "db764";

        } elseif ($source_of_recepients == "leads") {
            $source_of_recepients = "lead_profiles";
            $email_position_in_table = "db105";

        } elseif ($source_of_recepients == "emaillist") {
            $source_of_recepients = "coms_email_list_addresses";
            $email_position_in_table = "email_address";
        } else {
            $source_of_recepients = "core_contacts";
            $email_position_in_table = "db213";

        }

        #echo "step 2:$source_of_recepients / $source_of_recepients / $email_position_in_table<br/>";

        //RUN SQL PULLE DFROM LIST
        $dbh = get_dbh();
        $stmt_num = $dbh->prepare($list_query);
        $stmt_num->execute();
        $results = $stmt_num->fetchAll(PDO::FETCH_ASSOC);
        $num = count($results);

        //if the email addresses exist in a string list instead as as rows then check for the key 'Email Addresses'
        if ($results[0]['Email Addresses']) {
            $results = explode(";", $results[0]['Email Addresses']);
            //remove duplicate email addresses
            $results = array_unique($results);

            //check for the validity of each email address
            function check_if_valid_email($email)
            {
                return (strpos($email, '@'));
            }

            $results = array_filter($results, "check_if_valid_email");

            $num = count($results);
            $email_from_string = true;
        }
        //sif($_SESSION['usergroup'] ==3)
        //	echo 'asasasas='.$list_query;

        echo "<h2>Total Number Of Emails: $num</h2>";

        #echo "step 3: $sql<br/>";
        // loop though and log emails for each
        $i = 1;
        foreach ($results as $key => $row) {

            #echo "step 4:<br/>";
            //GET THE EMAIL ADDRESS FOR EACH CONTACT
            if ($email_from_string) {
                $email = $row;
            } else {
                $id = $row['Manage'];
                $recepient_email = pull_field("$source_of_recepients", "$email_position_in_table", "WHERE id='$id'");
                $email = $recepient_email ? $recepient_email : $row['Email Address'];
                $eh->setRelId($id);
                switch ($source_of_recepients) {
                    case 'core_students':
                        $eh->setUserLevel(Email_helper::APPLICANT);
                        break;
                    case 'lead_profiles':
                        $eh->setUserLevel(Email_helper::LEAD_PROFILE);
                        break;
                }
            }
            #echo $email.'<br/>';

            if ($email) {
                //
                //echo "$row[id]<br/>";
                //echo email_template_replace_values_from_db($coms_campaigns_email_content_html_text,$id);
                //echo "<hr/>";

                // SEND EMAIL
                $emailTo = $email;
                $subject = $coms_campaigns_email_subject;

                $message_plain = convert_html_to_text($coms_campaigns_email_content_html_text); //main body
                //prepare email start
                $message_html .= $coms_campaigns_html_header_image;  // header
                $message_html .= $coms_campaigns_email_content_html_text; //main body
                $message_html .= $coms_campaigns_html_footer; //footer
                //prepare email end
                $emailFrom = $coms_campaigns_reply_to_email_address;
                $emailFromName = $coms_campaigns_from_name;

                $message_html = email_template_replace_values_from_db($message_html, $id);
                $message_plain = email_template_replace_values_from_db($message_plain, $id);

                if ($_REQUEST['start_campaign'] == '1') {
                    //echo "$emailTo,$subject,$message_plain,$message_html,$emailFrom,COMS-test-$_GET[ref]-$_SESSION[usergroup]";
                    //log_email($emailTo,$subject,$message_plain,$message_html,$emailFrom,"COMS-$_GET[ref]-$_SESSION[usergroup]");

                    $eh->to($emailTo)->subject($subject)->plain($message_plain)->html($message_html);
                    if (!empty($emailFrom)) $eh->replyTo($emailFrom);
                    $eh->category("COMS-$_GET[ref]-$_SESSION[usergroup]");
                    $eh->send();
                    /////////////////////// EMAIL LOGGING SCRIPT ///////////////////////
                }

                /////////////////////// EMAIL LOGGING SCRIPT ///////////////////////
            }//end email check


            if ($_REQUEST['start_campaign'] == '1' || $_REQUEST['preview_campaign'] == '1') {
                echo '<div class="email"> <h4>Email number ' . $i . '</h4>';
                echo "<h3>To: $emailTo</h3>";
                //<h3>From: $emailFromName [$emailFrom]</h3>
                echo "<h3>Subject: $subject</h3><br/>
			<div>Message<br/>$message_html</div>
			";
                echo "</div>";
                //reset
                $emailTo = '';
                $emailFrom = '';
                $subject = '';
                $message_plain = '';
                $message_html = '';
            }


            $i++;
            //echo "number = $i";
        } // end while

        if ($_REQUEST['start_campaign'] == '1') {
            //update campaign database
            $dbh = get_dbh();
            $update_sql = "UPDATE coms_campaigns SET db1312='sent', rec_lstup='" . custom_date_and_time() . "', rec_lstup_id=" . $_SESSION['uid'] . " WHERE id=$_GET[ref]";
            $stmt = $dbh->prepare($update_sql);
            $stmt->execute();
        }

    }

    if ($_POST['test_email']) {
        /////////////////////// EMAIL LOGING SCRIPT ///////////////////////
        //TEST EMAIL LOGGING
        $emailTo = $_POST['test_email'];
        $subject = $coms_campaigns_email_subject;
        $message_plain = convert_html_to_text($coms_campaigns_email_content_html_text); //main body
        //prepare email start
        $message_html .= $coms_campaigns_html_header_image;  // header
        $message_html .= $coms_campaigns_email_content_html_text; //main body
        $message_html .= $coms_campaigns_html_footer; //footer
        //prepare email end
        $emailFrom = $coms_campaigns_reply_to_email_addresse;
        $emailFromName = $coms_campaigns_from_name;

        $message_html = email_template_replace_values_from_db($message_html, 570);
        $message_plain = email_template_replace_values_from_db($message_plain, 570);

        //echo "$emailTo,$subject,$message_plain,$message_html,$emailFrom,COMS-test-$_GET[ref]-$_SESSION[usergroup]";
        //log_email($emailTo,$subject,$message_plain,$message_html,$emailFrom,"COMS-test-$_GET[ref]-$_SESSION[usergroup]");
        $eh->to($emailTo)->subject($subject)->plain($message_plain)->html($message_html);
        if (!empty($emailFrom)) $eh->replyTo($emailFrom);
        $eh->category("COMS-test-$_GET[ref]-$_SESSION[usergroup]");
        $eh->send();
        /////////////////////// EMAIL LOGGING SCRIPT ///////////////////////

        echo "<div class=\"warning\">Test Email sent to $_POST[test_email]</div>";

    }

    ?>
</div>