<?php

namespace ug_58_custom;

//custom_internal_number generator 
function internal_number_generator($args, $static, $mappings)
{
    global $dbh;
    if ($_SESSION['usergroup'] == 58 && $args['db1813'] == 'Accepted') {

        $student_id = $args['rel_id'];

        //check if a student id is present if not them do something else...
        if (!$student_id) {
            $student_id = $_GET['ref'];
        }

        //check if they have a Cn number
        $student_number_exists = pull_field("core_students", "db888", "WHERE rec_id='$user_id'");

        //if not then create one
        if ($student_number_exists == '') {

            //get last one
            $dbh = get_dbh();
            $sql = "SELECT * FROM core_students WHERE usergroup='" . $_SESSION['usergroup'] . "' AND db888!='' ORDER BY db888 DESC LIMIT 1";
            $stmt = $dbh->prepare($sql);
            $stmt->execute();
            $user_info = $stmt->fetch(PDO::FETCH_ASSOC);
            $last_number = only_numbers($user_info['db888']);//	CNC1474

            if (!$last_number) {
                $last_number = "30000";
            }

            $internal_ref_number = $last_number + 1;
            $cncnumber = $internal_ref_number;

            $sql_v = "UPDATE core_students SET db888 = '$cncnumber' WHERE id='$student_id' AND usergroup='$_SESSION[usergroup]' ";
            $sth = $dbh->prepare($sql_v);
            $sth->execute();

        } else {

            $sql_v = "UPDATE core_students SET db888 = '$student_number_exists' WHERE id='$student_id' AND usergroup='$_SESSION[usergroup]' ";
            $sth = $dbh->prepare($sql_v);
            $sth->execute();
        }

        // email_error_msg("System Process Alert - This function (internal_number_generator) ran for $_SESSION[usergroup] and produced [$cncnumber]. Please login and check all went as expected","process run alert");

    }

    return array($args, $mappings);
}

