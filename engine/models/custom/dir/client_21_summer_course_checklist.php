<?php

namespace ug_21_summer_course_checklist;

function user_21_summer_course_checklist($args, $static, $mappings)
{

    $dbh = get_dbh();

    list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study) = get_core_students($_GET['ref'], $_GET['vw']);// if second value is defined then use it

    $template_version_103 = '96';
    $template_version_104 = '174';
    $template_version_105a = '168';
    $template_version_105b = '169';
    $template_version_106 = '170';
    $template_version_107a = '171';
    $template_version_107b = '172';
    $template_version_108 = '173';


    //Application Started - Email 103
    if ($args['db14408'] == 'yes') {
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_version_103);

        $email_name = $coms_template_template_name . '-' . $core_students_course_of_study;
        $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$email_name'");
        //current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
        if ($current_state == 0) {//checks to see if they haven't received an email so current state would be zero
            $message_html = $coms_template_html_version;
            $message_plain = $coms_template_plain_text_version;

            $message_html = str_replace('{{first_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{first_name}}', $core_students_first_name, $message_plain);
            $message_html = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
            $message_plain = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);
            $message_html = str_replace('{{applicant_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{applicant_name}}', $core_students_first_name, $message_plain);

            $message_html = str_replace('{{student_course}}', $core_students_course_of_study, $message_html);
            $message_plain = str_replace('{{student_course}}', $core_students_course_of_study, $message_plain);
            $message_html = str_replace('{{course}}', $core_students_course_of_study, $message_html);
            $message_plain = str_replace('{{course}}', $core_students_course_of_study, $message_plain);
            $message_html = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_html);
            $message_plain = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_plain);
            $message_plain = email_template_replace_values_from_db($message_plain, $core_students_id, "applicant");
            $message_html = email_template_replace_values_from_db($message_html, $core_students_id, "applicant");
            // $message_html = text_to_html($message_plain);
            $emailTo = $core_students_email_address;// student email

            if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
                $emailFrom = $coms_template_email_address_to_send_from;
            } else {
                $emailFrom = master_email;
            }

            log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, $email_name, $core_students_id);

        }

        //send_email($args, $static, $mappings, $template_version_105a);


    }

    //Application Submitted - Email 104
    if ($args['db14410'] == 'yes') {
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_version_104);

        $course_id = pull_field("core_students", "db889", "WHERE id=$core_students_id");
        $course_name = pull_field("core_courses", "db232", "WHERE id=$course_id");
        $email_name = $coms_template_template_name . '-' . $course_id;
        $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$email_name'");
        //current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
        if ($current_state == 0) {//checks to see if they haven't received an email so current state would be zero
            $message_html = $coms_template_html_version;
            $message_plain = $coms_template_plain_text_version;

            $message_html = str_replace('{{first_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{first_name}}', $core_students_first_name, $message_plain);
            $message_html = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
            $message_plain = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);
            $message_html = str_replace('{{applicant_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{applicant_name}}', $core_students_first_name, $message_plain);

            $message_html = str_replace('{{student_course}}', $course_name, $message_html);
            $message_plain = str_replace('{{student_course}}', $course_name, $message_plain);
            $message_html = str_replace('{{course_name}}', $course_name, $message_html);
            $message_plain = str_replace('{{course_name}}', $course_name, $message_plain);
            $message_html = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_html);
            $message_plain = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_plain);
            $message_plain = email_template_replace_values_from_db($message_plain, $core_students_id, "applicant");
            $message_html = email_template_replace_values_from_db($message_html, $core_students_id, "applicant");
            // $message_html = text_to_html($message_plain);
            $emailTo = $core_students_email_address;// student email

            if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
                $emailFrom = $coms_template_email_address_to_send_from;
            } else {
                $emailFrom = master_email;
            }

            log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, $email_name, $core_students_id);

        }

        //send_email($args, $static, $mappings, $template_version_105a);


    }

    //offer made more than 6 weeks prior to start date - email 105a
    if ($args['db14430'] == 'yes') {
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_version_105a);

        //$course_id= pull_field("core_students","db889","WHERE id=$core_students_id");
        $offer_id = $args['db14409'];
        //$course_id=pull_field("dir_offers","db1801","WHERE id=$offer_id");
        $dir_offers_intake = pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'");
        //get the course id
        if ($dir_offers_intake && $dir_offers_intake != '') {
            $course_id = pull_field("dir_cohorts", "db18719", "WHERE id= '" . pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'") . "'");
        } else {
            $course_id = pull_field("dir_offers", "db1801", "WHERE id = '$offer_id'");
        }
        $course_name = pull_field("core_courses", "db232", "WHERE id=$course_id");

        $deposit_payment = pull_field("form_payment_options", "db15877", "WHERE rel_id=$course_id");
        $include_VAT = pull_field("form_payment_options", "db15879", "WHERE rel_id = $course_id");
        if ($include_VAT == 'yes') {
            $vat_percent = pull_field("lead_preferences", "db15030", "WHERE usergroup = $_SESSION[usergroup]");
            $deposit_payment = $deposit_payment + ($deposit_payment * $vat_percent) / 100;
        }
        $deposit_payment = '£' . number_format($deposit_payment, 2);

        $email_name = $coms_template_template_name . '-' . $offer_id;
        $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$email_name'");
        //current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
        if ($current_state == 0) {//checks to see if they haven't received an email so current state would be zero
            $message_html = $coms_template_html_version;
            $message_plain = $coms_template_plain_text_version;

            $message_html = str_replace('{{first_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{first_name}}', $core_students_first_name, $message_plain);
            $message_html = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
            $message_plain = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);
            $message_html = str_replace('{{applicant_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{applicant_name}}', $core_students_first_name, $message_plain);

            $message_html = str_replace('{{student_course}}', $course_name, $message_html);
            $message_plain = str_replace('{{student_course}}', $course_name, $message_plain);
            $message_html = str_replace('{{course_name}}', $course_name, $message_html);
            $message_plain = str_replace('{{course_name}}', $course_name, $message_plain);
            $message_html = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_html);
            $message_plain = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_plain);
            $message_html = str_replace('{{course_deposit_value}}', $deposit_payment, $message_html);
            $message_plain = str_replace('{{course_deposit_value}}', $deposit_payment, $message_plain);
            $message_plain = email_template_replace_values_from_db($message_plain, $core_students_id, "applicant");
            $message_html = email_template_replace_values_from_db($message_html, $core_students_id, "applicant");
            // $message_html = text_to_html($message_plain);
            $emailTo = $core_students_email_address;// student email

            if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
                $emailFrom = $coms_template_email_address_to_send_from;
            } else {
                $emailFrom = master_email;
            }

            log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, $email_name, $core_students_id);

        }

        //send_email($args, $static, $mappings, $template_version_105a);
    }

    //offer made more less 6 weeks prior to start date - email 105b
    if ($args['db14430'] == 'no') {
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_version_105b);

        //$course_id= pull_field("core_students","db889","WHERE id=$core_students_id");
        $offer_id = $args['db14409'];
        //$course_id=pull_field("dir_offers","db1801","WHERE id=$offer_id");
        $dir_offers_intake = pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'");
        //get the course id
        if ($dir_offers_intake && $dir_offers_intake != '') {
            $course_id = pull_field("dir_cohorts", "db18719", "WHERE id= '" . pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'") . "'");
        } else {
            $course_id = pull_field("dir_offers", "db1801", "WHERE id = '$offer_id'");
        }
        $course_name = pull_field("core_courses", "db232", "WHERE id=$course_id");

        $full_payment = pull_field("dir_cohorts", "CONCAT('£',FORMAT(db18718,2))", "WHERE db18719=$course_id");
        //$full_payment =  pull_field("form_payment_options","CONCAT('£',FORMAT(db15875,2))","WHERE rel_id=$course_id");

        $email_name = $coms_template_template_name . '-' . $offer_id;

        $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$email_name'");
        //current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
        if ($current_state == 0) {//checks to see if they haven't received an email so current state would be zero
            $message_html = $coms_template_html_version;
            $message_plain = $coms_template_plain_text_version;

            $message_html = str_replace('{{first_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{first_name}}', $core_students_first_name, $message_plain);
            $message_html = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
            $message_plain = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);
            $message_html = str_replace('{{applicant_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{applicant_name}}', $core_students_first_name, $message_plain);

            $message_html = str_replace('{{student_course}}', $course_name, $message_html);
            $message_plain = str_replace('{{student_course}}', $course_name, $message_plain);
            $message_html = str_replace('{{course_name}}', $course_name, $message_html);
            $message_plain = str_replace('{{course_name}}', $course_name, $message_plain);
            $message_html = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_html);
            $message_plain = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_plain);
            $message_html = str_replace('{{course_full_value}}', $full_payment, $message_html);
            $message_plain = str_replace('{{course_full_value}}', $full_payment, $message_plain);
            $message_plain = email_template_replace_values_from_db($message_plain, $core_students_id, "applicant");
            $message_html = email_template_replace_values_from_db($message_html, $core_students_id, "applicant");
            // $message_html = text_to_html($message_plain);
            $emailTo = $core_students_email_address;// student email

            if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
                $emailFrom = $coms_template_email_address_to_send_from;
            } else {
                $emailFrom = master_email;
            }

            log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, $email_name, $core_students_id);

        }

//        send_email($args, $static, $mappings, $template_version_105b);
    }

    //applicant rejected - email 106
    if ($args['db14431'] == 'yes') {
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_version_106);
        $offer_id = $args['db14409'];
        $dir_offers_date = pull_field("dir_offers", "DATE_FORMAT(date,'%d %M %Y')", "WHERE id = '$offer_id'");
        $view_link = ",$core_students_first_name,$core_students_surname,$core_students_course_of_study,$dir_offers_date";
        $offer_username_id = pull_field("dir_offers", "username_id", "WHERE id =$offer_id");

        $http_link = engine_url . "/modules/inc_student_offer_letters.php?pid=4&ref=$_GET[ref]&rec=$offer_username_id&data=$view_link&pdf=1";
        $link_to_rejection_letter = "<a href='" . $http_link . "' title='View Letter - $core_students_course_of_study'> link</a>";
        //$link_to_rejection_letter =  "<a href='". engine_url."/modules/inc_template_letters_printable.php?pid=4&data=$view_link&word=1'> here </a>";

        $email_name = $coms_template_template_name . '-' . $core_students_course_of_study;
        $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$email_name'");
        //current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
        if ($current_state == 0) {//checks to see if they haven't received an email so current state would be zero
            $message_html = $coms_template_html_version;
            $message_plain = $coms_template_plain_text_version;

            $message_html = str_replace('{{first_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{first_name}}', $core_students_first_name, $message_plain);
            $message_html = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
            $message_plain = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);
            $message_html = str_replace('{{applicant_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{applicant_name}}', $core_students_first_name, $message_plain);

            $message_html = str_replace('{{student_course}}', $core_students_course_of_study, $message_html);
            $message_plain = str_replace('{{student_course}}', $core_students_course_of_study, $message_plain);
            $message_html = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_html);
            $message_plain = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_plain);
            $message_html = str_replace('{{link_to_rejection_letter}}', $link_to_rejection_letter, $message_html);
            $message_plain = str_replace('{{link_to_rejection_letter}}', $link_to_rejection_letter, $message_plain);
            $message_plain = email_template_replace_values_from_db($message_plain, $core_students_id, "applicant");
            $message_html = email_template_replace_values_from_db($message_html, $core_students_id, "applicant");
            // $message_html = text_to_html($message_plain);
            $emailTo = $core_students_email_address;// student email

            if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
                $emailFrom = $coms_template_email_address_to_send_from;
            } else {
                $emailFrom = master_email;
            }

            log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, $email_name, $core_students_id);
            log_letter_sent($coms_template_subject_line, $http_link, $core_students_id, $message_html);

        }

//        send_email($args, $static, $mappings, $template_version_106);
    }

    //deposit paid - email 107a
    if ($args['db14452'] == 'yes') {
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_version_107a);

        //$course_id= pull_field("core_students","db889","WHERE id=$core_students_id");
        $offer_id = $args['db14409'];
        //$course_id=pull_field("dir_offers","db1801","WHERE id=$offer_id");
        $dir_offers_intake = pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'");
        //get the course id
        if ($dir_offers_intake && $dir_offers_intake != '') {
            $course_id = pull_field("dir_cohorts", "db18719", "WHERE id= '" . pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'") . "'");
        } else {
            $course_id = pull_field("dir_offers", "db1801", "WHERE id = '$offer_id'");
        }
        $course_name = pull_field("core_courses", "db232", "WHERE id=$course_id");

        $email_name = $coms_template_template_name . '-' . $offer_id;

        $invoice_id = pull_field("dir_offers", "db16369", "WHERE id=$offer_id");
        $invoice_username_id = pull_field("lead_invoice_settings", "username_id", "WHERE id=$invoice_id");
        $invoice_link = "<a href='https://condenastcollege.heiapply.com/application/invoice/$invoice_username_id'>invoice</a>";

        $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$email_name'");
        //current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
        if ($current_state == 0) {//checks to see if they haven't received an email so current state would be zero
            $message_html = $coms_template_html_version;
            $message_plain = $coms_template_plain_text_version;

            $message_html = str_replace('{{first_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{first_name}}', $core_students_first_name, $message_plain);
            $message_html = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
            $message_plain = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);
            $message_html = str_replace('{{applicant_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{applicant_name}}', $core_students_first_name, $message_plain);

            $message_html = str_replace('{{student_course}}', $course_name, $message_html);
            $message_plain = str_replace('{{student_course}}', $course_name, $message_plain);
            $message_html = str_replace('{{course_name}}', $course_name, $message_html);
            $message_plain = str_replace('{{course_name}}', $course_name, $message_plain);
            $message_html = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_html);
            $message_plain = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_plain);
            $message_html = str_replace('{{invoice_link}}', $invoice_link, $message_html);
            $message_plain = str_replace('{{invoice_link}}', $invoice_link, $message_plain);
            $message_plain = email_template_replace_values_from_db($message_plain, $core_students_id, "applicant");
            $message_html = email_template_replace_values_from_db($message_html, $core_students_id, "applicant");
            // $message_html = text_to_html($message_plain);
            $emailTo = $core_students_email_address;// student email

            if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
                $emailFrom = $coms_template_email_address_to_send_from;
            } else {
                $emailFrom = master_email;
            }

            log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, $email_name, $core_students_id);

        }

        //send_email($args, $static, $mappings, $template_version_107a);
    }

    //paid in full - email 107b
    if ($args['db14453'] == 'yes') {
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_version_107b);

        //$course_id= pull_field("core_students","db889","WHERE id=$core_students_id");
        $offer_id = $args['db14409'];
        //$course_id=pull_field("dir_offers","db1801","WHERE id=$offer_id");
        $dir_offers_intake = pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'");
        //get the course id
        if ($dir_offers_intake && $dir_offers_intake != '') {
            $course_id = pull_field("dir_cohorts", "db18719", "WHERE id= '" . pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'") . "'");
        } else {
            $course_id = pull_field("dir_offers", "db1801", "WHERE id = '$offer_id'");
        }
        $course_name = pull_field("core_courses", "db232", "WHERE id=$course_id");

        $email_name = $coms_template_template_name . '-' . $offer_id;
        //get the offer associated with the course
        $invoice_id = pull_field("dir_offers", "db16369", "WHERE id=$offer_id");
        $invoice_username_id = pull_field("lead_invoice_settings", "username_id", "WHERE id=$invoice_id");
        $invoice_link = "<a href='https://condenastcollege.heiapply.com/application/invoice/$invoice_username_id'>invoice</a>";

        $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$email_name'");
        //current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
        if ($current_state == 0) {//checks to see if they haven't received an email so current state would be zero
            $message_html = $coms_template_html_version;
            $message_plain = $coms_template_plain_text_version;

            $message_html = str_replace('{{first_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{first_name}}', $core_students_first_name, $message_plain);
            $message_html = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
            $message_plain = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);
            $message_html = str_replace('{{applicant_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{applicant_name}}', $core_students_first_name, $message_plain);

            $message_html = str_replace('{{student_course}}', $course_name, $message_html);
            $message_plain = str_replace('{{student_course}}', $course_name, $message_plain);
            $message_html = str_replace('{{course_name}}', $course_name, $message_html);
            $message_plain = str_replace('{{course_name}}', $course_name, $message_plain);
            $message_html = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_html);
            $message_plain = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_plain);
            $message_html = str_replace('{{invoice_link}}', $invoice_link, $message_html);
            $message_plain = str_replace('{{invoice_link}}', $invoice_link, $message_plain);
            $message_plain = email_template_replace_values_from_db($message_plain, $core_students_id, "applicant");
            $message_html = email_template_replace_values_from_db($message_html, $core_students_id, "applicant");
            // $message_html = text_to_html($message_plain);
            $emailTo = $core_students_email_address;// student email

            if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
                $emailFrom = $coms_template_email_address_to_send_from;
            } else {
                $emailFrom = master_email;
            }

            log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, $email_name, $core_students_id);

        }

        //send_email($args, $static, $mappings, $template_version_107b);
    }

    //Send Unconditional Acceptance Letter - 108
    if ($args['db14457'] == 'yes') {
        list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_version_108);
        $offer_id = $args['db14409'];
        $dir_offers_date = date('d F Y'); //pull_field("dir_offers","DATE_FORMAT(date,'%d %M %Y')","WHERE id = '$offer_id'");
        $view_link = ",$core_students_first_name,$core_students_surname,$core_students_course_of_study,$dir_offers_date";


        //$course_id=pull_field("dir_offers","db1801","WHERE id=$offer_id");
        $dir_offers_intake = pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'");
        //get the course id
        if ($dir_offers_intake && $dir_offers_intake != '') {
            $course_id = pull_field("dir_cohorts", "db18719", "WHERE id= '" . pull_field("dir_offers", "db18720", "WHERE id = '$offer_id'") . "'");
        } else {
            $course_id = pull_field("dir_offers", "db1801", "WHERE id = '$offer_id'");
        }
        $course_name = pull_field("core_courses", "db232", "WHERE id=$course_id");
        $offer_username_id = pull_field("dir_offers", "username_id", "WHERE id =$offer_id");

        $http_link = engine_url . "/modules/inc_student_offer_letters.php?pid=5&ref=$_GET[ref]&rec=$offer_username_id&data=$view_link&pdf=1";
        $link_to_acceptance_letter = "<a href='" . $http_link . "' title='View Letter - $core_students_course_of_study'> here</a>";
        //$link_to_acceptance_letter =  "<a href='". engine_url."/modules/inc_template_letters_printable.php?pid=27&data=$view_link&word=1'> here </a>";

        $email_name = $coms_template_template_name . '-' . $offer_id;

        $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$email_name'");
        //current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
        if ($current_state == 0) {//checks to see if they haven't received an email so current state would be zero
            $message_html = $coms_template_html_version;
            $message_plain = $coms_template_plain_text_version;

            $message_html = str_replace('{{first_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{first_name}}', $core_students_first_name, $message_plain);
            $message_html = str_replace('{{applicant_name}}', $core_students_first_name, $message_html);
            $message_plain = str_replace('{{applicant_name}}', $core_students_first_name, $message_plain);
            $message_html = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_html);
            $message_plain = str_replace('{{name}}', $core_students_first_name . ' ' . $core_students_surname, $message_plain);

            $message_html = str_replace('{{student_course}}', $course_name, $message_html);
            $message_plain = str_replace('{{student_course}}', $course_name, $message_plain);
            $message_html = str_replace('{{course_name}}', $course_name, $message_html);
            $message_plain = str_replace('{{course_name}}', $course_name, $message_plain);
            $message_html = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_html);
            $message_plain = str_replace('{{student_portal}}', '<a href="https://condenastcollege.heiapply.com/application/login">student portal</a>', $message_plain);
            $message_html = str_replace('{{link_to_acceptance_letter}}', $link_to_acceptance_letter, $message_html);
            $message_plain = str_replace('{{link_to_acceptance_letter}}', $link_to_acceptance_letter, $message_plain);
            $message_plain = email_template_replace_values_from_db($message_plain, $core_students_id, "applicant");
            $message_html = email_template_replace_values_from_db($message_html, $core_students_id, "applicant");
            // $message_html = text_to_html($message_plain);
            $emailTo = $core_students_email_address;// student email

            if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
                $emailFrom = $coms_template_email_address_to_send_from;
            } else {
                $emailFrom = master_email;
            }

            log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, $email_name, $core_students_id);
            log_letter_sent($coms_template_subject_line, $http_link, $core_students_id, $message_html);
        }


        //send_email($args, $static, $mappings, $template_version_108);
    }


    if ($args['db14460'] == 'yes') {//level 8
        $sql = $dbh->prepare("UPDATE core_students SET db41 = 8 WHERE id = $_GET[ref];");
        $sql->execute();
    }

    if ($args['db14464'] == 'yes') {//level 9
        $sql = $dbh->prepare("UPDATE core_students SET db41 = 9 WHERE id = $_GET[ref];");
        $sql->execute();
    }
    return array($args, $mappings);
}

function send_email($args, $static, $mappings, $template_id)
{

    $dbh = get_dbh();

    //get template
    list($core_students_id, $core_students_rec_id, $core_students_usergroup, $core_students_rel_id, $core_students_first_name, $core_students_middle_name, $core_students_surname, $core_students_email_address, $core_students_telephone_number, $core_students_date_of_birth, $core_students_gender, $core_students_source_of_applicant, $core_students_cohort, $core_students_course_of_study) = get_core_students($_GET['ref'], $_GET['vw']);// if second value is defined then use it
    list($coms_template_id, $coms_template_rec_id, $coms_template_usergroup, $coms_template_rel_id, $coms_template_template_name, $coms_template_subject_line, $coms_template_plain_text_version, $coms_template_html_version, $coms_template_email_address_to_send_from) = get_coms_template($template_id);

    $current_state = pull_field("form_email_log", "count(*)", "WHERE db1153='$core_students_email_address' AND usergroup='$_SESSION[usergroup]' AND db1152='$coms_template_template_name''");
    //current state runs a select and counts all the fields where the email and usergroup are the same and that they have received an email with a particular category group
    if ($current_state == 0) {//checks to see if they haven't received an email so current state would be zero

        //search replace
        $message_plain = email_template_replace_values_from_db($coms_template_plain_text_version, $core_students_id);
        $message_html = email_template_replace_values_from_db($coms_template_html_version, $core_students_id);
        //$message_plain = email_template_replace_values("{{name}}",$_SESSION['name'],$coms_template_plain_text_version);
        //$message_html = email_template_replace_values("{{name}}",$_SESSION['name'],$coms_template_html_version);

        // $message_html = text_to_html($message_plain);
        $emailTo = $core_students_email_address;// student email

        if ($coms_template_email_address_to_send_from && $coms_template_email_address_to_send_from != '') {
            $emailFrom = $coms_template_email_address_to_send_from;
        } else {
            $emailFrom = master_email;
        }

        log_email($emailTo, $coms_template_subject_line, $message_plain, $message_html, $emailFrom, $coms_template_template_name, $core_students_id);
    }
    return array($args, $mappings);
}




