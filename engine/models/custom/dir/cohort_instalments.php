<?php

namespace dir_cohort_cohort_instalments;

//POST CREATE
function update_the_rel_id($args, $static, $mappings)
{
    if ($args['rel_id'] == '') {
        $dbh = get_dbh();
        $new_cohort_instalment_rel_id = $args['db20173'];
        // update the sis record with the new id
        $sth = $dbh->prepare("UPDATE dir_cohort_instalments SET rel_id=? WHERE username_id=? AND usergroup=?");
        $sth->execute(array($new_cohort_instalment_rel_id, $args['username_id'], session_info("access")));
    }
    return array($args, $mappings);

}


