<?php

namespace lead_52_custom;

function check_route($args, $static, $mappings)
{

    return array($args, $mappings);

}

function manage_assignments($args, $static, $mappings)
{
    return array($args, $mappings);
}

function manage_convert_student($args, $static, $mappings)
{
    $dbh = get_dbh();
    $lead_id = $args['record'];
    $student_id = $args['applicant'];
    $sth = $dbh->prepare('SELECT * FROM lead_profiles WHERE id = ?');
    $sth->execute(array($args['record']));
    $lead_profile = $sth->fetch();

    //I need to store how did you hear about us and Other  in student_profile
    $db785 = $lead_profile['db1049'];//hear about us
    $db1168 = $lead_profile['db30529'];//other, hear about us
    $sth_user_update = $dbh->prepare("UPDATE dir_registration1 SET db785 = ?, db1168 = ? WHERE rel_id = ? ");
    $sth_user_update->execute(array($db785, $db1168, $student_id));

    //set the intake on the student
    $sth_user_update = $dbh->prepare("UPDATE core_students SET db1682 = ? WHERE id = ? ");
    $sth_user_update->execute(array($lead_profile['db31774'], $student_id));

    //log_email('<EMAIL>', "MY SQL", "SELECT * FROM lead_profiles WHERE id = $args[record]"."**"."UPDATE dir_registration1 SET db785 = '$db785', db1168 = '$db1168' WHERE id = $args[applicant]", text_to_html("SELECT * FROM lead_profiles WHERE id = $args[record]"."**"."UPDATE dir_registration1 SET db785 = '$db785', db1168 = '$db1168' WHERE id = $args[applicant]"), '<EMAIL>', 'error_log');
    //any lead interactions that were in the enquiry i need to copy them over to the student
    $interaction_sql = "SELECT id, rec_id,  date, (SELECT db316 FROM core_contact_types where id = db1403) as 'interaction_type',  db1405 as 'date_of_interaction', db1404 as 'data'
    FROM lead_interactions
    WHERE rel_id=?
	AND (rec_archive IS NULL OR rec_archive = '')
	AND usergroup = ?";

    $sth_interactions = $dbh->prepare($interaction_sql);
    $sth_interactions->execute(array($lead_id, $_SESSION['usergroup']));
    $interactions = array();
    while ($row3 = $sth_interactions->fetch(\PDO::FETCH_ASSOC)) $interactions[] = $row3;
    foreach ($interactions as $interaction) {
        //add to core notes
        $date = $interaction['date_of_interaction'];
        if (!$date || $date == '') {
            $date = $interaction['date'];
        }

        $sql_notes = "INSERT INTO core_notes (username_id, rec_id, usergroup, rel_id, date, db73, db76, db77, db80, db79, db91, db139) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";// if users does not want alerts lock using this
        $sth_notes = $dbh->prepare($sql_notes);
        $sth_notes->execute(array(random(), session_info("uid"), session_info("access"), $student_id, $date, '10', $interaction['interaction_type'] . ' - ' . $interaction['data'], $interaction['rec_id'], 'no', '', '3', 'auto'));

    }

    //check if the route is develop a program
    //if it is it may application fee and deposit exempt
    $route = $args['route'];
    if ($route == '446') { //develop a program


    }

    return array($args, $mappings);
}


//first thing in the convert to applicatn
function initial_checks_before_converting_to_applicant($args, $static, $mappings)
{

    return array($args, $mappings);
}