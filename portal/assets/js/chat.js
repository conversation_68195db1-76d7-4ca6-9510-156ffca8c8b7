chat_data = {
	loading_messages: 1,
	messages: [],
	tags:[],
	current_tag: '',
	selectedTagTitle: '',
	student: {},
	chat_categories: [],
	partner_logged_id: false,
	current_user: $("#chat_current_user").val(),
	current_userlevel: $("#chat_current_userlevel").val(),
	new_message:'',
	file: '',
	select_chat_title:'',
	activeChat: 'all',
	modal: false,
	show_all: false,
	show_attach_button: false,
	dropped_files:[],
	searchItem: '',
	filteredItems: [],
	paginatedItems: [],
	partnerUsers: [],
}

chat = new Vue({
	el: '#chat_wrap',
	data: {},

	components: {
		chatHtml: {
			template: "#chatHtml",
			data: function(){
				return chat_data
			},
			mounted: function () {

				//Trigger Modal
				$(".start_chart").click(function(){
					var student_id = $(this).attr("id");
					chat.$refs.html.showChatModal(student_id);
				});

				//partner_logged_id
				this.partner_logged_id = $("#chat_wrap").attr("partner_logged_id");
				if(this.partner_logged_id){
					this.show_attach_button = true;
				}


				//Trigger Single chat
				var student_id = $("#chat_wrap").attr("student_id");
				if(student_id){
					this.activeChat = 0;
					this.showChat(student_id,"init");
				}

			},
			updated() {

				var vm = this;

				if (vm.loading_messages != '1' && vm.current_userlevel !='4'&& vm.current_userlevel !='7') {

					let iframe = document.getElementById('message_frame').contentWindow.document.body
					// Setup the config
					let config = {
						attributes: true,
						childList: true,
						characterData: true,
						subtree: true
					}
					// Create a callback
					let callback = function(mutationsList) {

						var message = document.getElementById('message_frame').contentWindow.document.body.innerHTML;

						var msg1 = message.replace(/line-height/g, "lineheightxxx");
						var msg2 = msg1.replace(/margin-top/g, "margin-topxxx");
						var msg = msg2.replace(/margin-bottom/g, "margin-bottomxxx");

						vm.new_message = msg;

					}

					// Watch the iframe for changes
					let observer = new MutationObserver(callback)
					observer.observe(iframe, config)
				}

			},
			methods: {
				addFile(e) {
					let droppedFiles = e.dataTransfer.files;
					if(!droppedFiles) return;
					([...droppedFiles]).forEach(f => {
						this.dropped_files.push(f);
						chat.$refs.html.file = f;
						chat.$refs.html.sendMessage();
					});
					console.log(this.dropped_files);
				},
				siteURL:function(){
					domain = document.URL;
					if(domain.indexOf("https") != -1){ protocol = "https"; }else{ protocol = "http"; }
					siteURL= protocol+"://"+document.domain;
					return siteURL;
				},
				monthName:function(month){
					if(month=="01"){ var month_name = "Jan"; }
					if(month=="02"){ var month_name = "Feb"; }
					if(month=="03"){ var month_name = "Mar"; }
					if(month=="04"){ var month_name = "Apr"; }
					if(month=="05"){ var month_name = "May"; }
					if(month=="06"){ var month_name = "Jun"; }
					if(month=="07"){ var month_name = "Jul"; }
					if(month=="08"){ var month_name = "Aug"; }
					if(month=="09"){ var month_name = "Sept"; }
					if(month=="10"){ var month_name = "Oct"; }
					if(month=="11"){ var month_name = "Nov"; }
					if(month=="12"){ var month_name = "Dec"; }
					return month_name;
				},
				cleanDate: function(f_date){
					var d = new Date(f_date);
					var ye = d.getFullYear();
					var mo = ((d.getMonth() + 1) > 9 ? '' : '0') + (d.getMonth() + 1);
					var da = (d.getDate() > 9 ? '' : '0') + d.getDate();
					var final_date = da+" "+this.monthName(mo)+" "+ye;
					return final_date;
				},
				setTagTitle: function(){
					$.each( this.tags, function( key, item ) {
						if(chat.$refs.html.current_tag==item.id){
							chat.$refs.html.selectedTagTitle = item.title;
						}
					});
				},
				showSingleConversation: function(conversation,ci){
					chat.$refs.html.activeChat = ci;
					chat.$refs.html.show_all = false;
					chat.$refs.html.partner_user = "";
					chat.$refs.html.select_chat_title = conversation.title;
					document.getElementById("dropdownChatMenuButton").style.display = "none";
					if(conversation.partner_user_id){
						//set the partner id
						chat.$refs.html.partner_user = conversation.partner_user_id;
						chat.$refs.html.showChat(conversation.student_id);
					}else{
						chat.$refs.html.showChat(conversation.student_id);
					}
				},
				showAllChats: function(conversation){
					chat.$refs.html.activeChat = "all";
					chat.$refs.html.show_all = true;
					chat.$refs.html.select_chat_title = conversation.title;
					chat.$refs.html.showChat(conversation.student_id);
				},
				pop_message: function(MSG,timeout=5000){
					$('.popup_message').html(MSG).removeClass("hide");
					if(timeout){
						setTimeout(function() { $('.popup_message').addClass('hide'); }, timeout);
					}
				},
				showChatModal: function(student_id){
					//Show modal
					$("#chat_modal").modal("show");
					chat.$refs.html.activeChat = "all";
					chat.$refs.html.partner_user = "";
					this.modal = true;
					this.showChat(student_id);
				},
				apply_tag: function(tag){
					chat.$refs.html.current_tag = tag.id;
					//Show modal
					axios
						.post(this.siteURL()+'/portal/api/tags/tag_link/'+this.student_unique_id+'/'+tag.tag_id,{
							headers: {
								'Content-Type': 'multipart/form-data'
							}
						})
						.then(response => {
							// new_message = response.data.data;
							this.pop_message('Tag added successfully');
							//Set current tag Title
							chat.$refs.html.setTagTitle();
						})
						.catch(error => {
							console.log(error);
							if(error.response.data){
								alert(error.response.data.error)
							}
							this.errored = true
						})
				},
				showChat: function (student_unique_id,type="") {
					let vm=this
					this.student_unique_id = student_unique_id;
					if(!this.student_unique_id){
						alert("Student id is required for the student chat");
						return false;
					}

					//get the messages
					this.loading_messages = 1;
					var messages_url = this.siteURL()+'/portal/api/chat/'+student_unique_id;

					if ("init"==type) {
						messages_url=messages_url+"/"+type;
					}


					//Check if conversation
					if($("#partner_messages").length){
						whr = $( window ).height();
						whr = whr-300;
						$(".loading_messages_wrap").css('min-height', whr);
						$(".loading_messages_wrap .fa").css('margin-top', (whr/2)-50);
					}

					// return false;

					//Get all message

					if(this.show_all){
						var messages_url = messages_url+"/all"
					}

					//Get messages with specific
					if(this.partner_user){
						var messages_url = messages_url+"/partner_user/"+chat.$refs.html.partner_user
					}

					axios.get(messages_url,this.new_message_data)
						.then(response => {

							chat.$refs.html.loading_messages = 2;
							chat.$refs.html.messages = response.data.data;
							chat.$refs.html.student = response.data.student;
							chat.$refs.html.chat_categories = response.data.chat_categories;
							chat.$refs.html.tags = response.data.tags;
							chat.$refs.html.current_tag = response.data.current_tag;
							chat.$refs.html.unread_count = response.data.unread_count;
							chat.$refs.html.selectedTagTitle = '';
							chat.$refs.html.partnerUsers =response.data.partnerUsers;
							// alert(response.data.conversation_type);
							this.filteredItems = this.messages

							if(!chat.$refs.html.partner_logged_id){
								if(response.data.conversation_type=="partner_user"){
									chat.$refs.html.show_attach_button = true;
								}else{
									chat.$refs.html.show_attach_button = false;
								}
							}

							if(response.data.chat_categories.length){
								$.each( response.data.chat_categories, function( key, chat_category ) {
									if(response.data.conversation_type=="partner_user"){
										if(chat.$refs.html.partner_user==response.data.partner_user_id){
											if ("init"==type) {
												//$("#chat_wrap").attr("student_id",chat.$refs.html.chat_categories[0].student_id)
												chat.$refs.html.select_chat_title = chat.$refs.html.chat_categories[0].title;
											}else{
												chat.$refs.html.select_chat_title = chat_category.title;
											}
										}

									}else{
										if((student_unique_id==chat_category.student_id) && (!chat_category.partner_user_id)){

											if ("init"==type) {
												vm.student_unique_id=chat.$refs.html.chat_categories[0].student_id
												$("#chat_wrap").attr("student_id",chat.$refs.html.chat_categories[0].student_id)
												$("#chat_current_user").val(response.data.chat_current_user)
												chat.$refs.html.select_chat_title = chat.$refs.html.chat_categories[0].title;
											}else{
												chat.$refs.html.select_chat_title = chat_category.title;
											}
										}

									}
								});
							}

							//Set current tag Title
							if(response.data.tags.length){
								this.setTagTitle();
							}

							//Config the show hide date
							$.each( chat.$refs.html.messages, function( key, msg ) {
								this_date = chat.$refs.html.cleanDate(msg.date);
								msg.date_format = this_date;

								//Only show the attachment icon
								if(msg.attachment){
									msg.attachment.extension = "pdf";
								}

								if(key>0){

									//SHow user info
									if(msg.sent_by.id!=previous_user){
										msg.show_user_info = true;
									}else{
										msg.show_user_info = false;
									}

									//Show date
									if(this_date!=previous_date){
										msg.show_date = true;
										msg.show_user_info = true;
									}else{
										msg.show_date = false;
									}



									previous_date = chat.$refs.html.cleanDate(msg.date);
									previous_user = msg.sent_by.id;
								}else{
									msg.show_date = true;
									msg.show_user_info = true;
									previous_date = this_date;
									previous_user = msg.sent_by.id;
								}
							});

							setTimeout(function(){
								//set the chat height
								wh = $( window ).height();

								if(this.modal){
									ch = wh-400;
									$("#chat_messsages_list").height(ch);
									$("#new_chat_message_txt").focus();
								}

								if($("#partner_messages").length){
									ch = wh-440;
									$("#chat_messsages_list").height(ch);
									ch = wh-250;
									$("#partner_messages .conversations_list").height(ch);
									$("#new_chat_message_txt").focus();
								}

								var messages_list = $("#communication_box .messages");
								messages_list.animate({ scrollTop: messages_list.prop("scrollHeight")}, 500);

								if($("#partner_messages").length){
									const element = $('li.active')[0];
									element.scrollIntoView();
								}
							}, 100);
						})
						.catch(error => {
							console.log(error)
							this.errored = true
						});
				},
				attachFile: function(){
					document.getElementById('chat_file').click()
				},
				markAsRead:function(id){
					axios.post(`${this.siteURL()}/portal/api/chat/read_post/${id}`).then(r => {
						console.log(r)
					}).catch(e => console.log(e));
				},
				handleFileUpload:function(event){
					chat.$refs.html.file = event.target.files[0];
					chat.$refs.html.sendMessage();
				},
				sendMessage: function () {

					if(!this.new_message && !this.file){
						$("#new_chat_message_txt").focus();
						return false;
					}
					const sendButtonClass = $(".send_chat_button");
					const sendButtonName = sendButtonClass.html();
					//Disable the button
					sendButtonClass.prop('disabled', true).html('<i class="fa fa-spinner fa-spin" style="font-size: 18px;"></i> Sending...');

					if (this.current_userlevel !='4' && this.current_userlevel !='7') {
						document.getElementById('message_frame').contentWindow.document.body.innerHTML="";

					}


					//Compile the message
					let new_message_data = new FormData();
					if(this.file){
						new_message_data.append('file', this.file);
					}
					new_message_data.append('description', this.new_message);

					if(chat.$refs.html.partner_user){
						new_message_data.append('partner_user_id', this.partner_user);
					}

					//Send to the api
					axios
						.post(this.siteURL()+'/portal/api/chat/'+this.student_unique_id,new_message_data,{
							headers: {
								'Content-Type': 'multipart/form-data'
							}
						})
						.then(response => {
							new_message = response.data.data;
							if(new_message.attachment){
								new_message.attachment.extension = "pdf";
							}
							chat.$refs.html.messages.push(new_message);
							$(".send_chat_button").prop('disabled', false).html(sendButtonName);
							document.getElementById("chat_file").value = "";
							this.file = "";

							setTimeout(function(){
								var messages_list = $("#communication_box .messages");
								messages_list.animate({ scrollTop: messages_list.prop("scrollHeight")}, 1000);
								if($("#partner_messages").length){
									const element = $('li.active')[0];
									element.scrollIntoView();
								}
							}, 100);
							this.new_message = "";
						})
						.catch(error => {
							$(".send_chat_button").prop('disabled', false).html('Send');
							console.log(error);
							alert(error.response.data.error)
							this.errored = true
						})
				},
				dropdownchatslist: function(){
					var x =  document.querySelector('#chat_wrap .dropdown-menu');
					if (x.style.display === "none") {
						x.style.display = "block";
					} else {
						x.style.display = "none";
					}
				},
				clearSearchItem(){
					this.searchItem = undefined
					this.searchInTheList('');

				},
				searchInTheList(searchText, currentPage){
					if(_.isUndefined(searchText)){
						this.filteredItems = _.filter(this.messages, function(message, k){
							return !message.selected
						})
					}
					else{
						// reset show date and show user info
						if (this.filteredItems[0]) {
							this.filteredItems[0].show_date = this.filteredItems[0].p_show_date ;
							this.filteredItems[0].show_user_info = this.filteredItems[0].p_show_user_info;
						}

						this.filteredItems = _.filter(this.messages, function(message, k){
							return !message.selected && (message.description.toLowerCase().indexOf(searchText.toLowerCase()) > -1 || message.date.toLowerCase().indexOf(searchText.toLowerCase()) > -1
								|| message.date_format.toLowerCase().indexOf(searchText.toLowerCase()) > -1)
						})

						if (this.filteredItems[0]) {
							this.filteredItems[0].p_show_date = this.filteredItems[0].show_date ;
							this.filteredItems[0].p_show_user_info = this.filteredItems[0].show_user_info;
							this.filteredItems[0].show_date = true;
							this.filteredItems[0].show_user_info = true;
						}
					}

				}
			},
		},
	},
})

