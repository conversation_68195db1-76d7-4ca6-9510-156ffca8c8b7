<?php

/**
 *
 */
class tutor_courses extends Controller
{



    public function index()
    {
        global $user_info, $school_info, $db;
		if ($user_info['type_id'] == 4){
			$data = array(
				'meta_title'=>'Access Denied',
				'view_file'=>'error_pages/permissions',
			);
			$this->view($this->layout,$data);
			exit();
		}
        $courses = new Courses;

        /** ===================================
         * Get the results
         * ====================================  */

        //Course Levels
        $course_args = array('paginate'=>true);

        if ($_GET['search']) {
            $course_levels_args['search'] = $_GET['search'];
        }

        //Table to use: sis_course_tutors holds the tutors
        //sis_course_schedule: link to the tutors 1 (db16888); #AND (db16888 ='46' OR db17213 ='46')
        // if ($user_info['partner']['id']) {
        //     if ($this->settings['applicants_partner_link'] == "programmes") {
        //         $course_levels_args['partner_id'] = $user_info['partner']['id'];
        //         $course_levels_args['show_based_on_partner_programmes'] = $user_info['partner']['id'];
        //     }
        // }

        $results = $courses->get($course_args);

        /** ===================================
         * Render View
         * ====================================  */
        $data = array(
            'meta_title' => 'Courses - ' . $school_info['title'],
            'view_file' => 'tutor_courses/index',
            'title' => 'Courses',
            'active_tab' => 'all',
            'results' => $results
        );
        $this->view($this->layout, $data);
    }


    public function info($username_id="")
    {
        global $user_info, $school_info, $db;
		
	    if ($user_info['type_id'] == 4){
		    $data = array(
			    'meta_title'=>'Access Denied',
			    'view_file'=>'error_pages/permissions',
		    );
		    $this->view($this->layout,$data);
		    exit();
	    }
		
        $courses = new ShortcoursesModel;


        $course_args = array('username_id'=>$username_id);
        $entry = $courses->get($course_args);

        //This should come form DB
        $bookings = array(
            array("first_name"=>"John", "last_name"=>"Doe","email"=>"<EMAIL>"),
            array("first_name"=>"Stacy", "last_name"=>"Doe","email"=>"<EMAIL>"),
            array("first_name"=>"Anna", "last_name"=>"Doe","email"=>"<EMAIL>"),
        );

        /** ===================================
         * Render View
         * ====================================  */
        $data = array(
            'meta_title' => 'Courses - ' . $school_info['title'],
            'view_file' => 'tutor_courses/details',
            'entry' => $entry,
            'bookings' => $bookings,
        );
        $this->view($this->layout, $data);
    }

    /**
     * @throws DateMalformedStringException
     * @throws DateInvalidTimeZoneException
     */
    public function attendees($scheduled_course_id=''): void
    {
        check_login();
        global $user_info,$school_info;
	    
	    if ($user_info['type_id'] == 4){
		    $data = array(
			    'meta_title'=>'Access Denied',
			    'view_file'=>'error_pages/permissions',
		    );
		    $this->view($this->layout,$data);
		    exit();
	    }
		
		
        $tutors = new CourseTutors;
        $attendees = $tutors->get_attendees($scheduled_course_id);
        $course_name = $attendees[0]['course_title'];
        $school_type = pull_field('form_schools', 'db30', "WHERE id ={$_SESSION['usergroup']}");
        $course_status = pull_field("sis_course_schedule", "db14959", "WHERE id= {$scheduled_course_id}");
        $startDate = pull_field("sis_course_schedule", "db14947", "WHERE id= {$scheduled_course_id}");
		$startTime = pull_field("sis_course_schedule", "db14948", "WHERE id= {$scheduled_course_id}");
		$timeSplit  = str_split($startTime, 2);
	    $finalEndTime = $timeSplit[0].':'.$timeSplit[1];
		
		$past_or_active = 'active';

        $defaultFormat= "Y-m-d H:i";
        $startDateWithTime =  $startDate. ' '. $finalEndTime;

        if (!empty($this->settings['tutor_dashboard']['course_widgets']['dateOnly'])){
            $defaultFormat = "Y-m-d";
            $startDateWithTime = $startDate;
        }

        //factor in timezone to handle BST for MRN usergroups
        $date = date($defaultFormat) ;
        if (pull_field('form_schools', 'db30', "WHERE id ={$_SESSION['usergroup']}") == 12) {
            $timeZoneID = pull_field('lead_preferences', 'db106496',"WHERE usergroup= {$_SESSION['usergroup']}");
            $timeZone = pull_field('system_timezone', 'timezone', "where id ={$timeZoneID}");
            // Set a date and time in BST
            $date = new DateTime('now', new DateTimeZone($timeZone));

            $bst =  $date->format($defaultFormat);

            // Convert to GMT (UTC)
            $date->setTimezone(new DateTimeZone('GMT'));

            // Output the date and time in GMT
            $gmt = $date->format($defaultFormat);

            if ($bst!=$gmt){
                $date = $bst;
            }else{
                $date = $gmt;
            }
        }

        if ( $date >=$startDateWithTime) {
            $past_or_active = 'past';
        }

        // print_r($attendees);

        $data = array(
            'meta_title'=> $course_name.' Attendees - '.$school_info['title'],
            'view_file'=>'tutor_courses/attendees',
            'attendee_category' => 'booking_attendees',
            'settings' => $this->settings,
            'attendees' => $attendees,
            'scheduled_course_id' => $scheduled_course_id,
            'school_type' =>$school_type,
            'course_status' => $course_status,
            'past_or_active' => $past_or_active,
            'sis_course_id' => $scheduled_course_id

        );
        // echo json_encode($data);
        $this->view($this->layout,$data);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws DateMalformedStringException
     * @throws DateInvalidTimeZoneException
     */
	public function session_attendees($scheduled_session_id=''): void
    {
        check_login();
        global $user_info,$school_info;
	    
	    if ($user_info['type_id'] == 4){
		    $data = array(
			    'meta_title'=>'Access Denied',
			    'view_file'=>'error_pages/permissions',
		    );
		    $this->view($this->layout,$data);
		    exit();
	    }
		
		
        $tutors = new CourseTutors;
        $attendees = $tutors->get_session_attendees($scheduled_session_id);
        $course_name = $attendees[0]['course_title'];
        $school_type = pull_field('form_schools', 'db30', "WHERE id ={$_SESSION['usergroup']}");
        $scheduled_course_id = pull_field("sis_scheduled_sessions", "rel_id", "WHERE id = {$scheduled_session_id}");
        $course_status = pull_field("sis_course_schedule", "db14959", "WHERE id= {$scheduled_course_id}");
	    $startDate = pull_field("sis_scheduled_sessions", "db59835", "WHERE id= {$scheduled_session_id}");
	    $startTime = pull_field("sis_scheduled_sessions", "db59836", "WHERE id= {$scheduled_session_id}");
	    $timeSplit  = str_split($startTime, 2);
	    $finalEndTime = $timeSplit[0].':'.$timeSplit[1];

	    $past_or_active = 'active';

        $defaultFormat= "Y-m-d H:i";
        $startDateWithTime =  $startDate. ' '. $finalEndTime;

        if (!empty($this->settings['tutor_dashboard']['course_widgets']['dateOnly'])){
            $defaultFormat = "Y-m-d";
            $startDateWithTime = $startDate;
        }

        //factor in timezone to handle BST for MRN usergroups
        $date = date($defaultFormat) ;
        if (pull_field('form_schools', 'db30', "WHERE id ={$_SESSION['usergroup']}") == 12) {
            $timeZoneID = pull_field('lead_preferences', 'db106496',"WHERE usergroup= {$_SESSION['usergroup']}");
            $timeZone = pull_field('system_timezone', 'timezone', "where id ={$timeZoneID}");
            // Set a date and time in BST
            $date = new DateTime('now', new DateTimeZone($timeZone));

            $bst =  $date->format($defaultFormat);

            // Convert to GMT (UTC)
            $date->setTimezone(new DateTimeZone('GMT'));

            // Output the date and time in GMT
            $gmt = $date->format($defaultFormat);

            if ($bst!=$gmt){
                $date = $bst;
            }else{
                $date = $gmt;
            }
        }

        if ( $date >=$startDateWithTime) {
            $past_or_active = 'past';
        }

        //   print_r($attendees);
        $data = array(
            'meta_title'=> $course_name.' Attendees - '.$school_info['title'],
            'view_file'=>'tutor_courses/attendees',
            'attendee_category' => 'session_attendees',
            'settings' => $this->settings,
            'attendees' => $attendees,
            'scheduled_session_id' => $scheduled_session_id,
            'school_type' =>$school_type,
            'course_status' => $course_status,
            'past_or_active' => $past_or_active,
            'sis_course_id' => $scheduled_course_id
        );
        // echo json_encode($data);
        $this->view($this->layout,$data);
    }

    public function courses($tutor_id=''){
        check_login();
        global $user_info,$school_info;
	    
	    if ($user_info['type_id'] == 4){
		    $data = array(
			    'meta_title'=>'Access Denied',
			    'view_file'=>'error_pages/permissions',
		    );
		    $this->view($this->layout,$data);
		    exit();
	    }
		
		
        $tutors = new CourseTutors;
        $courses = $tutors->get_courses($tutor_id);
        $data = array(
            'meta_title'=> 'Courses that you are approved to give',
            'view_file'=>'tutor_courses/courses',
            'settings' => $this->settings,
            'courses' => $courses
        );
        // echo json_encode($data);
        $this->view($this->layout,$data);
    }

    function get_attendee(){
//       echo "testing Post".$_POST['test'];
        $tutors = new CourseTutors;
        if($_POST['attendee_category']==='booking_attendees'){
            $attendee = $tutors->get_attendees($_POST['scheduled_course_id'],$_POST['scheduled_booking_id']);
        }else if($_POST['attendee_category']==='session_attendees'){
            $attendee = $tutors->get_session_attendees($_POST['scheduled_session_id'],$_POST['scheduled_booking_id']);
        }
        $attendee_response = array(
            'attendee' => $attendee,
            'success'=>true
        );
        echo json_encode($attendee_response);
    }
    public function get_attendee_on_edit(): void
    {
        $tutors = new CourseTutors;
        $attendee = $tutors->get_attendee_on_edit($_POST['attendee_id']);
        $attendee_response = array(
            'attendee' => $attendee,
            'success' => true
        );
        echo json_encode($attendee_response);
    }

    /**
     * @throws \Doctrine\DBAL\DBALException
     */
    function edit_attendee_data(){
        $tutors = new CourseTutors;
        $attendee = $tutors->edit_attendee_data($_POST);

        echo json_encode($attendee);
    }

    public function evaluations($scheduled_course_id)
    {
        check_login();
        global $user_info, $school_info;
	    if ($user_info['type_id'] == 4){
		    $data = array(
			    'meta_title'=>'Access Denied',
			    'view_file'=>'error_pages/permissions',
		    );
		    $this->view($this->layout,$data);
		    exit();
	    }
        $tutors = new CourseTutors;
        $courses_args = [
            'sch_course_id' => $scheduled_course_id,
            'school_id' => $this->school_info['id'],
            'paginate' => !empty($_GET['export']) ? false : true,
            'search' => $_GET['search'] ?: '',
            'order' => !empty($_GET['order']) ? $_GET['order'] : '',
        ];


        $evaluations = $tutors->evaluations($courses_args);
        $data = array(
            'meta_title' => 'Courses Evaluations or Feedback For '.$evaluations['course_name'],
            'view_file' => 'tutor_courses/evaluations',
            'settings' => $this->settings,
            'results' => $evaluations,
            'sch_course_id' => $scheduled_course_id
        );
        // echo json_encode($data);
        $this->view($this->layout, $data);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function get_goal_groups(){
        check_login();
        $tutors = new CourseTutors;
        $groups = $tutors->get_goal_groups();

        echo json_encode($groups);
    }

    /**
     * @throws \Doctrine\DBAL\Driver\Exception
     * @throws \Doctrine\DBAL\Exception
     */
    public function add_personal_goal(){
        check_login();
        $tutors = new CourseTutors;
        $goal = $tutors->add_personal_goal($_POST);
        echo $goal;
    }

    public function send_email_to_attendee(){
        $this->check_login();
        $tutors = new CourseTutors;
        $tutors->send_email_to_attendee($_POST);
    }

    public function send_sms_to_attendee()
    {
        $this->check_login();
        $messages = new CommunicationMessages;
        $subject = 'SMS to attendee from tutor';
        $sms_text = $_POST['sms_message'];
        $recipient_number = $_POST['student_mobile'];
        $student_id = $_POST['student_id'];
        $usergroup = $_SESSION['usergroup'];
        $send = 'Send';
        $student_name = pull_field('core_students', "db39", "WHERE id = {$student_id}");
        $sms_text= email_template_replace_values("{{first_name}}", $student_name, $sms_text);
        $sms_text= email_template_replace_values("{{name}}", $student_name, $sms_text);
        $sms = $messages->log_sms($template="", $subject, $sms_text, $recipient_number, $student_id, $usergroup, $send);

        echo json_encode($sms);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function get_student_mobile(){
        $this->check_login();
        $tutors = new CourseTutors;
        $mobile = $tutors->get_student_mobile($_POST);
        echo json_encode($mobile);
    }

    public function bulk_email_booking_attendees(){
        check_login();
        $tutors = new CourseTutors;
        $tutors->send_bulk_email_bookings($_POST);
    }

    public function bulk_email_session_attendees(){
        check_login();
        $tutors = new CourseTutors;
        $tutors->send_bulk_email_bookings($_POST);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function bulk_sms_booking_attendees(){
        check_login();
        $tutors = new CourseTutors;
        $results= $tutors->send_bulk_sms($_POST);
        echo json_encode($results);
    }

    /**
     * @throws \Doctrine\DBAL\Exception
     * @throws \Doctrine\DBAL\Driver\Exception
     */
    public function bulk_sms_session_attendees(){
        check_login();
        $tutors = new CourseTutors;
        $results= $tutors->send_bulk_sms($_POST);
        echo $results;
    }

    public function get_all_courses($user_id='')
    {

        check_login();
        $shortCourses = new ShortcoursesModel;

        $courses_args = [
            'paginate' => empty($_GET['export']),
            'search' => $_GET['search'] ?: '',
            'order' => !empty($_GET['order']) ? $_GET['order'] : '',
            'user_id' => $user_id
        ];
        $name = pull_field('form_users', "concat(db106, ' ', db111)", "where id={$user_id}");

        $courses = $shortCourses->get_mrn_bookings($courses_args);
        $data = array(
            'meta_title' => 'Booked Courses',
            'view_file' => 'tutor_courses/booked_courses',
            'settings' => $this->settings,
            'results' => $courses,
            'name' => $name
        );

        $this->view($this->layout, $data);
    }
	
	/**
	 * @throws \Doctrine\DBAL\Driver\Exception
	 * @throws \Doctrine\DBAL\Exception
	 */
	public function description($courseID){
		$args = [
			'courseID' => $courseID
		];
		$this->check_login();
		$tutors = new CourseTutors;
		$description = $tutors->courseDescription($args);
		echo json_encode($description);
	}
	
	public function course_materials($courseID= ''){
		
		if (empty($courseID)){
			$data = array(
				'meta_title'=>'Materials Not Found',
				'view_file'=>'error_pages/404',
			);
			$this->view($this->layout,$data);
			exit();
		}
		check_login();
		$courses = new Courses;
		$args = [
			'course_id' => $courseID,
			'active' => true
		];
		$allMaterials = $courses->get_course_materials($args);
		$courseName =  pull_field('core_courses', "db232", "WHERE id = {$courseID}");
		
		$data = array(
			'meta_title' => 'Courses Materials',
			'view_file' => 'tutor_courses/course_materials',
			'results' => $allMaterials,
			'name' => $courseName
		);
		
		$this->view($this->layout, $data);
	}

}

