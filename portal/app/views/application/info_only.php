<?php
global $db, $school_info;
$dbh = get_dbh();
$studentsModel = new Students;
$cms_pages = new FrontendPages;
$FormTemplatesModel = new FormTemplates;
$FieldsModel = new Fields;
$coursesModel = new Courses;
$application = $data['application'];
$application_pages = $data['application_pages'];
$partner_logged_id = true;
include(FRONTEND_PATH . 'app/views/applicants/inc_chat.php');
include(FRONTEND_PATH . 'app/views/applicants/inc_stages.php');
include(FRONTEND_PATH . 'app/views/applicants/inc_comment.php');
?>

    <nav aria-label="breadcrumb">
        <ol class="breadcrumb" style="background-color: transparent; padding-left: 0">
            <li class="breadcrumb-item"><a href="<?php echo front_end_url('course_levels/all/applicants'); ?>"><i
                            class="fa fa-angle-left"></i> Back to results</a></li>
            <li class="breadcrumb-item active"
                aria-current="page"><?php echo $data['application']['first_name'] . " " . $data['application']['last_name']; ?></li>
        </ol>
    </nav>


    <div id="profile_dialoag">
        <?php if (!$data['standard_view']) { ?>
            <button type="button" class="close pull-right" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        <?php } ?>

        <?php if ($this->settings['show_action_button'] == 1 && !empty($data['actions'])) { ?>
            <div class="dropdown float-right">
                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenu2"
                        data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Actions
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <?php foreach ($data['actions'] as $action) { ?>
                        <a class="dropdown-item <?php echo $action['class']; ?>" id="<?php echo $action['id']; ?>"
                           href="<?php echo $action['href']; ?>">
                            <?php echo $action['title']; ?>
                        </a>
                    <?php } ?>
                </div>
            </div>
        <?php } ?>

        <?php if ($application['user_info']['profile_picture']) { ?>
            <div class="profile_pic"
                 style="background-image: url('<?php echo $application['user_info']['profile_picture']; ?>');"></div>
        <?php } ?>

        <div class="clearfix"></div>

        <h3>
            <?php echo $application['first_name']; ?><?php echo $application['last_name']; ?>
        </h3>
        <hr>
        <div class="clearfix"></div>
        <?php

        if ($this->settings['applicant_popup']['checklist_to_view'] == "all") {
            $allowed_checklists[] = $application['application_route'];
        } else {
            if (count($this->settings['applicant_popup']['checklist_to_view'])) {
                foreach ($this->settings['applicant_popup']['checklist_to_view'] as $key => $value) {
                    $allowed_checklists[] = $key;
                }
            }
        }

        if ($application['id']) {
        if (in_array($application['application_route'], $allowed_checklists)) {
        $form_templates = new FormTemplates;
        $checklist_args = array('locked_fields_only' => 1, 'id' => $application['application_route']);
        if ($_GET['show_checklist_fields_sql']) {
            $checklist_args['show_fields_sql'] = true;
        }
        $checklist_fields = $form_templates->get($checklist_args);

        $template_answers_args = array(
            "rel_id" => $application['id'],
            "template_cat_abrv" => 'chk',
            "template_name" => $checklist_fields['slug'],
            'single_answer' => true
        );
        $template_answers = $form_templates->answers($template_answers_args); ?>
        <form class="section" action="<?php echo front_end_url('application/save_section'); ?>">

            <div id="application_progress_meter">
                <div class="box first <?php if ($application['date_submitted']) {
                    echo "already_submitted";
                } ?>" style="display: none;">
                    <div class="title">Stage 0</div>
                    <div class="half completion">
                        <div class="msg">
                            Application Completion
                            <div class="percentage"><?php if ($application['date_submitted']) {
                                    echo "100%";
                                } else {
                                    echo "-";
                                } ?></div>
                        </div>

                    </div>
                    <div class="half submission">
                        <div class="msg">
                            Application submitted
                        </div>

                        <div class="status">
                            <?php
                            if ($application['date_submitted']) {
                                echo '<i class="fa fa-check"></i>';
                            } else {
                                echo '<i class="fa fa-times"></i>';
                            }
                            ?>
                        </div>
                    </div>
                    <?php if (!$application['date_submitted']) { ?>
                        <a href="#" class="pending_data" style="display: none;">
                            <i class="fa fa-warning"></i> View Pending Data
                        </a>
                    <?php } ?>
                    <div class="clearfix"></div>
                </div>
                <?php
                $i = 0;
                foreach ($checklist_fields['fields'] as $field) {
                    if (in_array($field['id'], $this->settings['applicant_popup']['checklist_to_view'][$application['application_route']]) || ($this->settings['applicant_popup']['checklist_to_view'] == "all")) {
                        if ($field["type"] == "stage") {
                            $i++;

                            if ($template_answers[$field["db_field_name"]] == "on") {
                            }

                            //remove stage title "Stage number"
                            $stage_title = explode("-", $field["title"]);

                            $stage_number = $stage_title['0'];

                            if (strpos($stage_title[0], 'Stage') !== false) {
                                $stage_title = $stage_title[1];
                            }

                            ?>
                            <div class="box thickbox" title="Update Checklist">
                                <div class="title">Stage <?php echo $stage_number; ?></div>
                                <div class="msg"><?php echo $stage_title; ?></div>

                                <?php
                                if ($template_answers[$field["db_field_name"]] == "on") {
                                    echo '<div class="status green"><i class="fa fa-check"></i></div>';
                                } else {
                                    echo '<div class="status red"><i class="fa fa-times"></i></div>';
                                }
                                ?>

                            </div>
                        <?php }
                    }
                } ?>
                <div class="clearfix"></div>
            </div>

            <?php }
            } ?>
            <!--   internal comments-->
            <?php if ($this->settings['allow_comments'] == 1 && !empty($data['internal_comments'])) { ?>
                <div class="section">
                    <h4 class="section_title">Internal Comments </h4>
                    <?php foreach ($data['internal_comments'] as $internal_comment) { ?>

                        <div class="alert alert-info"><?php echo $internal_comment['db1129']; ?>
                            <hr>
                            <small class="pull-right">added on <?php echo $internal_comment['db39148']; ?>
                                by <?php echo $internal_comment['added_by'] ?> </small><br>
                        </div>

                        <div class="clearfix"></div>
                    <?php } ?>
                </div>
            <?php } ?>

            <?php
            if ($this->settings['applicant_popup']['general_info_viewing'] != 'none') {

                $blocked_general_fields = array("id", "has_submitted", "email_address", "intake", "on_short_course", "application_stage", "date_submitted", "checklist", "age", "cohort", "files_count", "level_of_entry", 'internal_reference', 'course_abbreviation_code', 'assigned', 'has_offer', 'has_made_a_payment', 'course_internal_code', 'department', 'has_been_withdrawn', 'has_been_rejected', 'references_count', 'course'); ?>
                <form class="section" action="<?php echo front_end_url('application/save_section'); ?>">
                    <?php if ($this->settings["application_editing"] != "yes" && !empty($this->settings['applicant_popup']['general_info_edit']) && $this->settings['applicant_popup']['general_info_edit'] != 'none') { ?>
                        <a href="#" class="edit_section">Edit section info</a>
                    <?php } ?>
                    <input type="button" class="btn btn-primary save_button btn-sm" value="Save Information">
                    <input type="hidden" name="form_type" value="general_information">
                    <input type="hidden" name="form_id" value="0">
                    <input type="hidden" name="student_id" value="<?php echo $application['id']; ?>">
                    <h4 class="section_title">General Information</h4>

                    <?php
                    foreach ($studentsModel->all_students_fields_dimensions(array('only_general_info' => 1)) as $key => $value) {
                        foreach ($value["General Information"] as $field) {
                            $show_item = true;
                            if (count($this->settings['applicant_popup']['general_info_viewing']) && $this->settings['applicant_popup']['general_info_viewing'] != 'all') {
                                if (!in_array($field['id'], $this->settings['applicant_popup']['general_info_viewing'])) {
                                    $show_item = false;
                                }
                            }

                            if ($show_item) {
                                $field_args = array('field' => $field, 'entry' => $application, 'school_info' => $this->school_info, 'return' => 'value');
                                $value = $studentsModel->students_field_td($field_args);


                                $editable = false;

                                if (!in_array($field['id'], $blocked_general_fields)) {
                                    $editable = true;
                                }

                                if ($editable) {

                                    if ($this->settings['applicant_popup']['general_info_edit'] == "all") {
                                        $editable = true;
                                    } else {
                                        if (in_array($field['id'], $this->settings['applicant_popup']['general_info_edit'])) {
                                            $editable = true;
                                        } else {
                                            $editable = false;
                                        }
                                    }
                                }

                                if ($field['id'] == "has_submitted") {
                                    if ($value == 1) {
                                        $value = "Yes";
                                    } else {
                                        $value = "No";
                                    }
                                } ?>
                                <div class="row answer_row <?php if ($editable) {
                                    echo 'editable_field';
                                } else {
                                    echo 'none_editable';
                                } ?>">
                                    <div class="col-sm-3">
                                    <span class="app_label">
                                        <?= translate(terminology($field['title'], $_SESSION['url'], $field['title'] . ' applicant portal.', true), $_SESSION['lang']) ?>
                                    </span>
                                    </div>
                                    <div class="col-sm-7">
                                        <span class="app_val"><?php echo $value; ?></span>

                                        <?php if ($editable) { ?>
                                            <div class="edit_field_wrap">
                                                <?php if ($field['id'] == "gender") { ?>
                                                    <?php
                                                    $gender_field = $FieldsModel->get(array('db_field_name' => 'db44'));
                                                    ?>
                                                    <select name="<?php echo $field['id']; ?>" class="form-control">
                                                        <option value="">Select...</option>
                                                        <?php foreach ($gender_field['options'] as $gender) { ?>
                                                            <option <?php if ($gender['title'] == $value) {
                                                                echo 'selected="selected"';
                                                            } ?> value="<?php echo $gender['title']; ?>"><?php echo $gender['title']; ?></option>
                                                        <?php } ?>
                                                    </select>

                                                <?php } elseif ($field['id'] == "source") { ?>
                                                    <?php
                                                    $gender_field = $FieldsModel->get(array('db_field_name' => 'db510'));
                                                    ?>
                                                    <select name="<?php echo $field['id']; ?>" class="form-control">
                                                        <option value="">Select source...</option>
                                                        <?php foreach ($gender_field['options'] as $gender) { ?>
                                                            <option <?php if ($gender['title'] == $value) {
                                                                echo 'selected="selected"';
                                                            } ?> value="<?php echo $gender['title']; ?>"><?php echo $gender['title']; ?></option>
                                                        <?php } ?>
                                                    </select>
                                                <?php } elseif (in_array($field['id'], array('course', 'second_course'))) { ?>
                                                    <?php
                                                    $courses = $coursesModel->get(array());
                                                    ?>
                                                    <select name="<?php echo $field['id']; ?>" class="form-control">
                                                        <option value="">Select course...</option>
                                                        <?php foreach ($courses as $course) { ?>
                                                            <option <?php if ($course['title'] == $value) {
                                                                echo 'selected="selected"';
                                                            } ?> value="<?php echo $course['id']; ?>"><?php echo $course['title']; ?></option>
                                                        <?php } ?>
                                                    </select>
                                                <?php } elseif ($field['id'] == "department") { ?>
                                                    <?php
                                                    $courses = $coursesModel->get_departments(array());
                                                    ?>
                                                    <select name="<?php echo $field['id']; ?>" class="form-control">
                                                        <option value="">Select...</option>
                                                        <?php foreach ($courses as $course) { ?>
                                                            <option <?php if ($course['title'] == $value) {
                                                                echo 'selected="selected"';
                                                            } ?> value="<?php echo $course['id']; ?>"><?php echo $course['title']; ?></option>
                                                        <?php } ?>
                                                    </select>
                                                <?php } elseif ($field['id'] == "date_of_birth") {
                                                    $value = str_replace("/", "-", $value); ?>

                                                    <input type="text" name="<?php echo $field['id']; ?>"
                                                           class="form-control"
                                                           value="<?php echo date("Y-m-d", strtotime($value)); ?>">
                                                <?php } elseif ($field['id'] == "period") { ?>
                                                    <?php
                                                    $period_field = $FieldsModel->get(array('db_field_name' => 'db1682'));
                                                    $period_figures = explode(",", $period_field['default']);
                                                    $period_sql = "SELECT id, " . $period_figures[1] . " FROM " . $period_figures[0] . " WHERE usergroup=" . $_SESSION['usergroup'];
                                                    dev_debug($period_sql);
                                                    $stmt = $dbh->prepare($period_sql);
                                                    $stmt->execute();
                                                    $periods = $stmt->fetchAll(PDO::FETCH_ASSOC);

                                                    ?>
                                                    <select name="<?php echo $field['id']; ?>" class="form-control">
                                                        <option value="">Select...</option>
                                                        <?php foreach ($periods as $period) { ?>
                                                            <option <?php if ($period[$period_figures[1]] == $value) {
                                                                echo 'selected="selected"';
                                                            } ?> value="<?php echo $period['id']; ?>"><?php echo $period[$period_figures[1]]; ?>

                                                            </option>
                                                        <?php } ?>
                                                    </select>
                                                <?php } elseif ($field['id'] == "db25618") { ?>
                                                    <?php
                                                    $gender_field = $FieldsModel->get(array('db_field_name' => 'db25618', 'school_id' => $school_info['id']));
                                                    ?>
                                                    <select name="<?php echo $field['id']; ?>" class="form-control">
                                                        <option value="">Select...</option>
                                                        <?php foreach ($gender_field['options'] as $gender) { ?>
                                                            <option <?php if ($gender['realvalue'] == $value) {
                                                                echo 'selected="selected"';
                                                            } ?> value="<?php echo $gender['realvalue']; ?>"><?php echo $gender['title']; ?></option>
                                                        <?php } ?>
                                                    </select>
                                                <?php } elseif ($field['id'] == "db28467") { ?>
                                                    <?php
                                                    $gender_field = $FieldsModel->get(array('db_field_name' => 'db28467', 'school_id' => $school_info['id']));
                                                    ?>
                                                    <select name="<?php echo $field['id']; ?>" class="form-control">
                                                        <option value="">Select...</option>
                                                        <?php foreach ($gender_field['options'] as $gender) { ?>
                                                            <option <?php if ($gender['realvalue'] == $value) {
                                                                echo 'selected="selected"';
                                                            } ?> value="<?php echo $gender['realvalue']; ?>"><?php echo $gender['title']; ?></option>
                                                        <?php } ?>
                                                    </select>
                                                <?php } elseif ($field['id'] == "country") { ?>
                                                    <?php
                                                    $country_field = $FieldsModel->get(array('db_field_name' => 'db763', 'school_id' => $school_info['id']));
                                                    $country_figures = explode(",", $country_field['default']);
                                                    $country_sql = "SELECT id, " . $country_figures[1] . " FROM " . $country_figures[0];
                                                    dev_debug($country_sql);
                                                    $stmt = $dbh->prepare($country_sql);
                                                    $stmt->execute();
                                                    $countries = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                                    ?>
                                                    <select name="<?php echo $field['id']; ?>" class="form-control">
                                                        <option value="">Select...</option>
                                                        <?php foreach ($countries as $country) { ?>
                                                            <option <?php if ($country[$country_figures[1]] == $value) {
                                                                echo 'selected="selected"';
                                                            } ?> value="<?php echo $country['id']; ?>"><?php echo $country[$country_figures[1]]; ?></option>
                                                        <?php } ?>
                                                    </select>
                                                <?php } else { ?>
                                                    <input type="text" name="<?php echo $field['id']; ?>"
                                                           class="form-control" value="<?php echo $value; ?>">
                                                <?php } ?>
                                            </div>
                                        <?php } ?>

                                    </div>
                                    <div class="col-sm-2">&nbsp;</div>
                                </div>
                            <?php }
                        }
                    } ?>

                </form>
            <?php }

            //Check if there is a restriction on which ages to show
            if (count($this->settings['applicant_popup']['pages_to_view'])) {
                foreach ($this->settings['applicant_popup']['pages_to_view'] as $key => $value) {
                    $allowed_forms[] = $key;
                }
            } else {
                foreach ($application_pages as $page) {
                    if ($page['new_form']) {
                        $allowed_forms[] = $page['new_form'];
                    } else {
                        $allowed_forms[] = $form_id;
                    }
                }
            }
            foreach ($application_pages as $page) {
                $i++;
                if ($page['new_form']) {
                    $form_id = $page['new_form'];
                } else {
                    $form_id = $page['form'];
                }

                if (in_array($form_id, $allowed_forms) || ($this->settings['applicant_popup']['forms_to_show'] == "all")) { ?>
                    <form class="section" action="<?php echo front_end_url('application/save_section'); ?>">
                        <?php
                        $forms_in_use = array();
                        $answers = array();
                        $form_fields = array();
                        $multi_page = "";
                        //Get the new forms
                        if ($page['new_form']) {
                            $form_args = array('form_id' => $page['new_form'], 'school_id' => $school_info['id']);
                            $form_fields = $FormTemplatesModel->get_custom_form_fields($form_args);

                            //Complie forms in use
                            foreach ($form_fields as $fi) {
                                if (!in_array($fi['table_id'], $forms_in_use)) {
                                    if ($fi['table_id']) {
                                        $forms_in_use[] = $fi['table_id'];
                                    }
                                }
                            }
                        } else {
                            //Get the old standard forms
                            if ($page['form']) {
                                $forms_in_use[] = $page['form'];
                                $form_args = array('id' => $page['form'], 'school_id' => $school_info['id'], 'locked_fields_only' => true);
                                $form = $FormTemplatesModel->get($form_args);
                                $form_fields = $form['fields'];
                                $multi_page = $page['multi_page'];
                            }
                        }

                        if ($multi_page) {
                            $query = "SELECT * FROM {$form['table_name']}  WHERE rel_id='{$application['id']}' AND (rec_archive is null or rec_archive ='') ";
                            $stmt = $dbh->prepare($query);
                            $stmt->execute();
                            $answers = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        } else {
                            if (count($forms_in_use)) {
                                $forms = $FormTemplatesModel->get(array('locked_fields_only' => true, 'basic_info' => 1, "id_in" => $forms_in_use, 'show_sql' => 1));
                                $form_left_joins = "";
                                foreach ($forms as $form_info) {
                                    $form_left_joins .= " LEFT JOIN " . $form_info['table_name'] . " ON " . $form_info['table_name'] . ".rel_id = `core_students`.`id`";
                                }
                                //Get the forms data
                                $query = "SELECT * FROM core_students $form_left_joins WHERE core_students.id='" . $application['id'] . "' AND (" . $form_info['table_name'] . ".rec_archive is null or " . $form_info['table_name'] . ".rec_archive = '') ORDER BY " . $form_info['table_name'] . ".id desc LIMIT 1";
                                $stmt = $dbh->prepare($query);
                                $stmt->execute();
                                $answers = $stmt->fetch(PDO::FETCH_ASSOC);
                            }
                        }
                        ?>

                        <?php if (($this->settings['applicant_popup']['pages_to_edit'] == "all") || count($this->settings['applicant_popup']['pages_to_edit'])) { ?>
                            <?php if (!$multi_page) {
                                if ($this->settings["application_editing"] != "yes" && !empty($this->settings['applicant_popup']['pages_to_edit'][$form_id])) { ?>
                                    <a href="#" class="edit_section">Edit section info</a>
                                <?php }
                            } ?>
                        <?php } ?>
                        <input type="button" class="btn btn-primary save_button btn-sm" value="Save Information">
                        <input type="hidden" name="form_type" value="<?php if ($page['new_form']) {
                            echo 'new';
                        } else {
                            echo 'old';
                        } ?>">
                        <input type="hidden" name="form_id" value="<?php echo $form_id; ?>">
                        <input type="hidden" name="student_id" value="<?php echo $application['id']; ?>">
                        <h4 class="section_title">
                            <?php echo str_replace("- ", "", $page['navigation_title']); ?>
                        </h4>

                        <?php
                        $fields_not_to_show = array("title", "warning", "textonly", "instruction", "subtitle");

                        //Show page info if its not a multi page
                        if ($multi_page) {
                            if ($page['form'] == "13") {
                                if ($this->settings['files']['allow_file_upload']) { ?>
                                    <a href="#" class="btn btn-primary pull-right btn-sm upload_new_file"
                                       style="margin-bottom: 15px;"
                                       ajaxUrl="<?php echo front_end_url("application/file_upload/" . $application['id']); ?>">Upload
                                        file</a>
                                <?php }
                                if (count($answers)) { ?>
                                    <p>File Uploaded: <strong><?php echo count($answers); ?></strong></p>
                                    <div id="docs_list">
                                        <table class="table table-dark table-hover table-striped table-sm">
                                            <thead>
                                            <tr>
                                                <th scope="col">Title</th>
                                                <th scope="col">Date</th>
                                                <th scope="col">Size</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <?php
                                            $studentsModel = new Students;
                                            $files = $studentsModel->get_files(array('student_id' => $application['id']));
                                            foreach ($files as $file) {
                                                ?>
                                                <tr class="doc">
                                                    <td scope="col"><i class="fa fa-file"
                                                                       style="color: #999; margin-right: 10px;"></i> <a
                                                                href="<?php echo $file['download_link']; ?>"
                                                                target="_blank"
                                                                stye><?php echo $file['title'] . "." . $file['extension']; ?></a>
                                                    </td>
                                                    <td scope="col"><?php echo date("F d, Y", strtotime($file['date'])); ?></td>
                                                    <td scope="col"><?php echo $file['size']; ?></td>
                                                    <td style="width: 150px;">
                                                        <?php if ($this->settings['files']['allow_file_deleting']) { ?>
                                                            <a href="#" delete_form_record="13"
                                                               id="<?php echo $file["id"]; ?>"
                                                               class="btn btn-default btn-sm">Delete</a>
                                                        <?php } ?>
                                                        <?php if ($this->settings['files']['allow_file_editing']) { ?>
                                                            <a href="#" form_modal="13" id="<?php echo $file["id"]; ?>"
                                                               class="btn btn-default btn-sm">Edit</a>
                                                        <?php } ?>


                                                    </td>
                                                </tr>

                                            <?php } ?>
                                            <tbody>
                                        </table>
                                    </div>
                                <?php } else { ?>
                                    <p>No Results Found</p>
                                <?php } ?>

                            <?php } else { ?>
                                <p>Found Results: <strong><?php echo count($answers); ?></strong></p>

                                <?php if (!empty($answers)) { ?>
                                    <div class="table-responsive">
                                        <table class="table table-dark table-hover table-striped table-sm">
                                            <thead>
                                            <tr>
                                                <?php

                                                foreach ($form_fields as $field) {

                                                    $show_item = true;
                                                    //check if the user level can view the fields or not
                                                    if ($this->settings['applicant_popup']['pages_to_view'] == "all") {
                                                    } elseif (count($this->settings['applicant_popup']['pages_to_view'][$form_id])) {
                                                        if (!in_array($field['id'], $this->settings['applicant_popup']['pages_to_view'][$form_id])) {
                                                            $show_item = false;
                                                        }
                                                    }

                                                    //Show item if user level is allowed
                                                    if ($show_item) {
                                                        if (!in_array($field['type'], $fields_not_to_show)) {
                                                            $title = strip_tags($field['title']);
                                                            $title = str_replace("&rsquo;", "'", $title);
                                                            $title = preg_replace("/&#?[a-z0-9]{2,8};/i", "", $title);
                                                            ?>
                                                            <th scope="col"><?php echo $title; ?></th>
                                                        <?php }
                                                    }
                                                } ?>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <?php

                                            foreach ($answers as $answer) { ?>
                                                <tr>
                                                    <?php foreach ($form_fields as $field) {
                                                        $show_item = true;
                                                        //check if the user level can view the fields or not
                                                        if ($this->settings['applicant_popup']['pages_to_view'] == "all") {
                                                        } elseif (count($this->settings['applicant_popup']['pages_to_view'][$form_id])) {
                                                            if (!in_array($field['id'], $this->settings['applicant_popup']['pages_to_view'][$form_id])) {
                                                                $show_item = false;
                                                            }
                                                        }

                                                        //Show item if user level is allowed
                                                        if ($show_item) {
                                                            if (!in_array($field['type'], $fields_not_to_show)) {
                                                                ?>
                                                                <td scope="row"><?php echo $answer[$field['db_field_name']]; ?></td>
                                                            <?php }
                                                        }
                                                    } ?>
                                                </tr>
                                            <?php } ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php } else { ?>
                                    <p>No Results Found</p>
                                <?php } ?>
                            <?php } ?>

                            <?php
                            //SHow the Normal page (Not a multi Page)
                        } else {
                            $i = 0;
                            $field_order_count = 0;
                            foreach ($form_fields as $field) {
                                if ($field['title']) {
                                    $show_item = true;
                                    //check if the user level can view the fields or not
                                    if ($this->settings['applicant_popup']['forms_to_show'] != "all") {
                                        if ($this->settings['applicant_popup']['pages_to_view'][$form_id] == "all") {
                                            $show_item = true;
                                        } elseif (count($this->settings['applicant_popup']['pages_to_view'][$form_id])) {
                                            if (!in_array($field['id'], $this->settings['applicant_popup']['pages_to_view'][$form_id])) {
                                                $show_item = false;
                                            }
                                        }
                                    }
                                    //Show item if allowed
                                    if ($show_item) {
                                        $title = strip_tags($field['title']);
                                        $title = str_replace("&rsquo;", "'", $title);
                                        $title = preg_replace("/&#?[a-z0-9]{2,8};/i", "", $title);

                                        if ($field['type'] == "title") { ?>
                                            <div class="row">
                                                <div class="col-sm-12">
                                                    <h5>
                                                        <?php echo $title; ?>
                                                    </h5>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                        if (!in_array($field['type'], $fields_not_to_show)) {
                                            $i++;

                                            $field["types_already_used"] = $types_already_used;
                                            $field["field_order_count"] = $field_order_count;
                                            $field["answer"] = $answers[$field['db_field_name']];
                                            $editable = false;

                                            if ($this->settings['applicant_popup']['pages_to_edit'] == "all") {
                                                $editable = true;
                                            } else {
                                                if ($this->settings['applicant_popup']['pages_to_edit'][$form_id] == "all") {
                                                    $editable = true;
                                                } else {
                                                    if (in_array($field['id'], $this->settings['applicant_popup']['pages_to_edit'][$form_id])) {
                                                        $editable = true;
                                                    } else {
                                                        $editable = false;
                                                    }
                                                }
                                            }
                                            ?>
                                            <div class="row answer_row <?php echo ($i % 2 == 0) ? 'row_color_1' : 'row_color_2'; ?> <?php echo ($editable) ? 'editable_field' : 'none_editable' ?>">
                                                <div class="col-sm-6">
                                                            <span class="app_label">
                                                                <?php echo $title; ?>
                                                            </span>
                                                </div>
                                                <div class="col-sm-6">
                                                    <?php
                                                    if ($field["type"] == "upload") {
                                                        $files_allowed = array("jpg", "gif", "pdf", "PDF", "png", "doc", "docx", "xlsx", "xls", "JPG", "GIF", "PNG", "jpeg", "JPEG", "ppt", "PPTX", "pptx", "PPT", "csv", "zip", "pub", "rtf", "tif", "TIF");
                                                        $filename = $answers[$field['db_field_name']];
                                                        $file_full_path = media_store . "$filename";
                                                        $filename_actual_name = "View The File";
                                                        $ext = pathinfo($file_full_path, PATHINFO_EXTENSION);

                                                        // check of file exists
                                                        $actual_file = encode($filename); //ready to send to downloader
                                                        error_log($file_full_path);

                                                        if (file_exists($file_full_path) && in_array($ext, $files_allowed)) {
                                                            $final_display_item = '<a href="' . $file_check . '"></a><a href="' . $this->engine_url('media/dl.php?fl=' . $actual_file) . '" alt="view file" title="view file" target="_blank">' . $filename_actual_name . ' (' . round(filesize($file_full_path) * .0009765625, 2) . 'KB)</a>
                										                <a href="' . $file_check . '"></a><a href="' . $this->engine_url('media/dl.php?fl=' . $actual_file . '&dl=1') . '" alt="view file" title="view file" target="_blank"><img src="' . $this->engine_url('images/arrow_down_alt.png') . '" alt="Download" title="Download"></a>';
                                                        } else {
                                                            // end check
                                                            $final_display_item = $answers[$field['db_field_name']];
                                                        }
                                                    } elseif ($field["type"] == "dynamic_list_group" || $field["type"] == "dynamic_list") {
                                                        $sql = explode('order', $field['default']);
                                                        $sql[0] = str_replace("[session]uid[/session]", $_SESSION['uid'], $sql[0]);
                                                        $sql[0] = str_replace("[SESSION]usergroup[/SESSION]", $_SESSION['usergroup'], $sql[0]);
                                                        $sql[0] = str_replace("[session]usergroup[/session]", $_SESSION['usergroup'], $sql[0]);
                                                        $sql[0] = str_replace("[SESSION]access[/SESSION]", $_SESSION['usergroup'], $sql[0]);
                                                        $sql[0] = str_replace("[session]access[/session]", $_SESSION['usergroup'], $sql[0]);
                                                        $sql[0] = str_replace("[SESSION]prog_course[/SESSION]", $_SESSION['prog_course'], $sql[0]);
                                                        $sql[0] = str_replace("[session]prog_course[/session]", $_SESSION['prog_course'], $sql[0]);
                                                        $sql[0] = str_replace("[session]student_id[/session]", $_SESSION['student_id'], $sql[0]);
                                                        $sql[0] = str_replace("[SESSION]student_id[/SESSION]", $_SESSION['student_id'], $sql[0]);
                                                        $sql[0] = str_replace("???", ",", $sql[0]);

                                                        $final_sql = explode(',', $field['default']);
                                                        $id = "id";

                                                        if (strpos($final_sql[1], "+") !== false) {
                                                            $select_array = explode("+", $final_sql[1]);

                                                            $search_found = [];

                                                            foreach ($select_array as $item) {
                                                                // check if we have found id or not
                                                                if (strpos($item, 'id') !== false) {
                                                                    $search_found[] = 'found';
                                                                } else {
                                                                    $search_found[] = 'not found';
                                                                }
                                                            }

                                                            if (in_array("found", $search_found, true)) {
                                                                $label = $select_array[1];
                                                                $id = $select_array[0];
                                                            } else {
                                                                $last = count($select_array) - 1;
                                                                $lst = explode('/*/', $select_array[$last]);
                                                                if (count($lst) > 1) {
                                                                    array_pop($select_array);
                                                                    $select_array[] = $lst[0];
                                                                }
                                                                $label = "CONCAT(" . implode(",' ',", $select_array) . ")";
                                                            }
                                                        } else {
                                                            $lst = explode('/*/', $query[1]);
                                                            if (count($lst) > 1) {
                                                                array_pop($select_array);
                                                                $final_sql[1] = $lst[0];
                                                            }
                                                            $label = $final_sql[1];
                                                        }
                                                        //TEMP FIX
                                                        if ($field['db_field_name'] == 'db57252') {
                                                            $id = 'core_partner.id';
                                                        }

                                                        $final_display_item = pull_field("{$sql[0]}", "{$label}", "AND {$id}={$answers[$field['db_field_name']]}");
                                                    } else {
                                                        $final_display_item = $answers[$field['db_field_name']];
                                                    }
                                                    ?>
                                                    <span class="app_val">
                                                                <?php echo str_replace('_', ' ', $final_display_item); ?>
                                                            </span>

                                                    <?php if ($editable) { ?>
                                                        <div class="edit_field_wrap">
                                                            <?php echo $FieldsModel->html($field); ?>
                                                        </div>
                                                    <?php } ?>

                                                </div>
                                            </div>
                                        <?php }
                                    }
                                }
                            }
                        } ?>
                    </form>
                <?php }
            } ?>
            <?php if (!empty($this->settings['file_types'])) { ?>
                <form class="section">
                    <h4 class="section_title">
                        Files
                    </h4>
                    <p>File Uploads:
                        <strong>
                            <?php echo count($data['uploads']); ?>
                        </strong>
                    </p>
                    <?php if (!empty($data['uploads'])) { ?>
                        <div id="docs_list">
                            <table class="table table-dark table-hover table-striped table-sm">
                                <thead>
                                <tr>
                                    <th scope="col">Date</th>
                                    <th scope="col">Title</th>
                                    <th scope="col">Type</th>
                                    <th scope="col">Size</th>
                                </tr>
                                </thead>
                                <tbody>
                                <?php
                                foreach ($data['uploads'] as $file) { ?>
                                    <tr class="doc">
                                        <td>
                                            <?php echo date("d/m/Y", strtotime($file['date'])); ?>
                                        </td>
                                        <td>
                                            <i class="fa fa-file" style="color: #999; margin-right: 10px;"></i>
                                            <a href="<?php echo $file['link']; ?>" target="_blank">
                                                <?php echo $file['title'] . "." . $file['extension']; ?>
                                            </a>
                                        </td>
                                        <td>
                                            <?php echo $file['category']; ?>
                                        </td>
                                        <td>
                                            <?php echo $file['size']; ?>
                                        </td>

                                    </tr>

                                <?php } ?>
                                <tbody>
                            </table>
                        </div>
                    <?php } else { ?>
                        <p>No Files Uploaded</p>
                    <?php } ?>
                </form>
            <?php } ?>

    </div>

<?php include(FRONTEND_PATH . 'app/views/application/inc_file_upload_modal.php'); ?>