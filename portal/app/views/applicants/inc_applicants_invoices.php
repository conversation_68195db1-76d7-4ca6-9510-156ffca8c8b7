<?php

require_once($_SERVER['DOCUMENT_ROOT'] . "/admin/app/models/invoices.php");
$invoiceModel=new InvoicesClass(); 

$offers_join = "";
$offers_condition = "";

//get invoices
$dbh = get_dbh();
check_login();
$invoices_sql = "SELECT  lead_invoice_settings.id,lead_invoice_settings.username_id,db14989
FROM lead_invoice_settings 
LEFT JOIN core_students on lead_invoice_settings.rel_id = core_students.id
$offers_join
WHERE lead_invoice_settings.usergroup = '" . $_SESSION['usergroup'] . "' 
AND (lead_invoice_settings.rec_archive is NULL OR lead_invoice_settings.rec_archive='')
$offers_condition
AND core_students.username_id ='" . $invoice_username_id . "' 
AND (" . $invoiceModel::lead_invoice_settings['doctype'] . " = 'invoice' 
or " . $invoiceModel::lead_invoice_settings['doctype'] . " = '' or " . $invoiceModel::lead_invoice_settings['doctype'] . " is NULL) 
AND (" . $invoiceModel::lead_invoice_settings['usertype'] . "='Applicant' OR " . $invoiceModel::lead_invoice_settings['usertype'] . "='' 
OR " . $invoiceModel::lead_invoice_settings['usertype'] . " IS NULL)";
dev_debug('invoices_sql' . $invoices_sql);
$sql = $dbh->prepare($invoices_sql);
$sql->execute();
$invoices = $sql->fetchAll(PDO::FETCH_ASSOC);


if (!empty($invoices)) { ?>

	<tr>
		<td><b><?= translate("Invoice(s)", $_SESSION['lang']) ?></b></td>
		<td>

			<?php foreach ($invoices as $invoice) {
				$i = 0; 
				$invoice_data= $invoiceModel->get(['id'=>$invoice['id'],"doctype" => "invoice",'get_items'=>true,'school_id'=>$_SESSION['usergroup'],'skip_addresses'=>true,'skip_refs'=>true]);
				//$payments = calculate_payments($invoice['username_id']);
				$payments_made = $invoice_data['payments'];
				$refunds_made = $invoice_data['refunds'];
				$amount_due = $invoice_data['amount_outstanding'];

				//new way of calculating payments outstanding
				$payments_outstanding = $invoice_data['amount_outstanding'];
                $disabled ="";
                // get the invoice item intake
                $invoice_cohort =  pull_field('lead_invoice_items', 'db20215', "WHERE rel_id ={$invoice['id']}");
                $deadline_preference =  pull_field('lead_preferences', 'db252128', "WHERE usergroup= {$_SESSION['usergroup']} ");
                if (!empty($invoice_cohort) && $deadline_preference == 'no'){
					$intake_data = explode('|', pull_field("dir_cohorts", "concat_ws('|',db32332,db32334,db25987,db25607)", "WHERE id={$invoice_cohort}"));
					if (!empty($intake_data) && $intake_data[0] != '0000-00-00'){
						$deadline_date = $intake_data[0]. ' '. $intake_data[1];
					}else{
						$deadline_date ="";
					}

					// check if limit has been reached
					$check_sql = "
                        select count(core_students.id) as count from core_students
                        left join sis_student_fees on core_students.id = sis_student_fees.rel_id
                        where core_students.usergroup = {$_SESSION['usergroup']}
                        and db1682 = '$invoice_cohort'
                        group by core_students.id
                        having sum(db1495) > 0
                    ";
                    dev_debug($check_sql);
					$sth9 = $dbh->prepare($check_sql);
					$sth9->execute();
					$booking_count = $sth9->fetch(PDO::FETCH_ASSOC);

                    $deadline_message= $capacity_reached = false;
					if (!empty($deadline_date) && date("Y-m-d Hi") > $deadline_date){
						$disabled = 'hide'; ?>
                        <span class="danger">
                            <?php
							echo translate(terminology('Deadline date for this course has passed', $_SESSION['url'], 'deadline date passed applicant portal', true),$_SESSION['lang']);#
							$deadline_message = true;
							?>
                        </span>
                        <?php
					}

                    if ($booking_count['count']>= $intake_data[2] && !empty($intake_data[2]) && empty($deadline_message)){
                        $disabled = 'hide';
                        $capacity_reached = true;
                        ?>
                        <span class="danger">
                            <?php
							echo translate(terminology('Maximum capacity for this course has been reached', $_SESSION['url'], 'max capacity reached applicant portal', true),$_SESSION['lang']);
							?>
                        </span>
                        <?php
                    }

                    if ($intake_data[3]!='yes' && empty($deadline_message) && empty($capacity_reached)){
                        $disabled='hide';
						echo translate(terminology('Course intake is now unavailable for bookings', $_SESSION['url'], 'unavailable course applicant portal', true), $_SESSION['lang']);
					}
				}

				if (date('Y-m-d', strtotime($invoice_data['due_date'])) < date('Y-m-d') && $payments_outstanding > 0 && !empty($invoice_data['due_date'])) {
					$btn_class = 'btn-danger';
					$btn_name = '(Overdue)';
				} else {
					$btn_class = 'btn-primary';
					$btn_name = '';
				} ?>

				<a style="margin: 0 0 5px 0;" target=" _blank" rel="noopener" href="<?php echo old_portal_url() ?>/invoice/<?php echo $invoice['username_id'] ?>"  class="btn btn-sm <?php echo $btn_class; ?>  <?php echo $disabled;?> ">
					<?= translate("View Invoice", $_SESSION['lang']) ?> <?php echo translate($btn_name, $_SESSION['lang']); ?>
				</a>
				</br>
			<?php }  ?>
		</td>
	</tr>
<?php } ?>